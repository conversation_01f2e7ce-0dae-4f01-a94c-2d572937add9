"""
管理员系统监控API路由
提供系统监控相关的API接口
主要功能：
1. 系统信息监控
2. 接口调用统计
3. 日志文件管理
4. 系统性能监控
"""

from fastapi import APIRouter, Body, Depends

from 依赖项.认证 import 获取当前管理员用户
from 数据.管理_统一入口 import (
    异步获取接口调用用户列表,
    异步获取接口调用统计,
    异步获取接口调用详情列表,
    异步获取日志文件列表,
    异步获取系统信息,
    异步读取日志文件内容,
)
from 数据模型.SuperAdmin_模型 import (
    接口统计请求,
    日志内容请求,
    日志文件请求,
    系统信息请求,
)
from 数据模型.响应模型 import 统一响应模型
from 日志 import 接口日志器, 错误日志器

# 创建系统监控路由
系统监控路由 = APIRouter(prefix="/admin/system", tags=["管理员-系统监控"])


# 注意：原有的获取时间范围条件方法已移至统一的时间范围工具中


# ==================== 系统信息监控 ====================


@系统监控路由.post("/info", summary="获取系统信息")
async def 获取系统信息接口(
    请求参数: 系统信息请求 = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取系统基本信息"""
    try:
        系统信息 = await 异步获取系统信息()
        return 统一响应模型.成功(数据=系统信息, 消息="获取系统信息成功")

    except Exception as e:
        错误日志器.error(f"获取系统信息失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取系统信息失败")


# ==================== 接口调用统计 ====================


@系统监控路由.post("/api/stats", summary="获取接口调用统计")
async def 获取接口统计接口(
    请求参数: 接口统计请求 = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取接口调用统计"""
    try:
        # 直接调用异步获取接口调用统计，传递正确的参数
        统计数据 = await 异步获取接口调用统计(
            时间段=请求参数.时间范围,
            开始日期=请求参数.开始日期,
            结束日期=请求参数.结束日期,
            page=getattr(请求参数, "page", 1),
            size=getattr(请求参数, "size", 10),
        )
        return 统一响应模型.成功(数据=统计数据, 消息="获取接口统计成功")

    except Exception as e:
        错误日志器.error(f"获取接口统计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取接口统计失败")


@系统监控路由.post("/api/details", summary="获取接口调用详情")
async def 获取接口详情接口(
    请求参数: dict = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取接口调用详情列表"""
    try:
        页码 = 请求参数.get("页码", 1)
        每页数量 = 请求参数.get("每页数量", 20)
        接口路径 = 请求参数.get("接口路径")

        详情列表 = await 异步获取接口调用详情列表(页码, 每页数量, 接口路径)
        return 统一响应模型.成功(数据=详情列表, 消息="获取接口详情成功")

    except Exception as e:
        错误日志器.error(f"获取接口详情失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取接口详情失败")


@系统监控路由.post("/api/users", summary="获取接口调用用户")
async def 获取接口用户接口(
    请求参数: dict = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取接口调用用户列表"""
    try:
        页码 = 请求参数.get("页码", 1)
        每页数量 = 请求参数.get("每页数量", 20)

        用户列表 = await 异步获取接口调用用户列表(页码, 每页数量)
        return 统一响应模型.成功(数据=用户列表, 消息="获取接口用户成功")

    except Exception as e:
        错误日志器.error(f"获取接口用户失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取接口用户失败")


# ==================== 日志文件管理 ====================


@系统监控路由.post("/logs/list", summary="获取日志文件列表")
async def 获取日志列表接口(
    请求参数: 日志文件请求 = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取日志文件列表"""
    try:
        日志列表 = await 异步获取日志文件列表()

        # 异步获取日志文件列表返回的是 List[str]，直接使用
        return 统一响应模型.成功(数据=日志列表, 消息="获取日志列表成功")

    except Exception as e:
        错误日志器.error(f"获取日志列表失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取日志列表失败")


@系统监控路由.post("/logs/content", summary="读取日志文件内容")
async def 读取日志内容接口(
    请求参数: 日志内容请求 = Body(...), 当前用户: dict = Depends(获取当前管理员用户)
):
    """读取日志文件内容"""
    try:
        # 异步读取日志文件内容只接受文件名和读取行数两个参数
        文件内容 = await 异步读取日志文件内容(
            请求参数.文件名, 请求参数.行数限制
        )

        # 如果有搜索关键词，在返回的内容中进行过滤
        if hasattr(请求参数, '搜索关键词') and 请求参数.搜索关键词:
            文件内容 = [行 for 行 in 文件内容 if 请求参数.搜索关键词 in 行]

        接口日志器.info(f"日志文件读取成功: {请求参数.文件名}")
        return 统一响应模型.成功(数据=文件内容, 消息="读取日志成功")

    except Exception as e:
        错误日志器.error(f"读取日志文件失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="读取日志文件失败")
