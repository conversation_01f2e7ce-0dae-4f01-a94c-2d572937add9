"""
管理员公司管理API路由
提供**超级管理员**对公司进行审核、查询等操作的API接口

⚠️ 注意：此模块与团队管理.py中的公司接口功能不重复
- 本模块：面向**超级管理员**，用于后台管理系统
- 团队管理.py：面向**普通用户**，用于CRM前端系统

主要功能：
1. 获取已审核公司列表
2. 获取待审核公司列表  
3. 批准/拒绝公司申请
4. 获取公司详情
"""

from fastapi import APIRouter, Depends
from typing import Optional
from pydantic import BaseModel, Field

# 导入依赖和模型
from 依赖项.认证 import 获取当前管理员用户
from 数据模型.响应模型 import 统一响应模型

# 导入数据层函数
from 数据.公司数据 import (
    获取公司列表 as 数据层获取公司列表, 
    审核公司 as 数据层审核公司,
    获取公司详情 as 数据层获取公司详情
)

# 导入审核相关模型
from 数据模型.团队模型 import 公司审核请求模型, 审核状态枚举

# 导入日志
from 日志 import 接口日志器, 错误日志器

# 创建API路由器
公司管理路由 = APIRouter(tags=["管理后台-公司管理"])


# =============== 请求模型定义 ===============

class 公司列表查询请求(BaseModel):
    """公司列表查询请求模型"""
    页码: int = Field(1, ge=1, description="页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词（公司名称、简称、代码）")
    公司状态: Optional[str] = Field(None, description="公司状态")
    审核状态: Optional[str] = Field(None, description="审核状态")


class 公司详情请求(BaseModel):
    """公司详情请求模型"""
    公司ID: int = Field(..., description="公司ID")



class 公司批准请求(BaseModel):
    """公司批准请求模型"""
    公司ID: int = Field(..., gt=0, description="公司ID")
    审核备注: Optional[str] = Field(None, description="审核备注")


class 公司拒绝请求(BaseModel):
    """公司拒绝请求模型"""
    公司ID: int = Field(..., gt=0, description="公司ID")
    审核备注: Optional[str] = Field(None, description="审核备注")


# =============== 公司查询接口 ===============

@公司管理路由.post(
    "/companies/reviewed",
    response_model=统一响应模型,
    summary="获取已审核公司列表",
    description="获取所有审核通过的公司列表"
)
async def 获取已审核公司列表(
    请求数据: 公司列表查询请求,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """获取已审核公司列表"""
    try:
        管理员ID = 当前管理员["id"]
        接口日志器.info(f"管理员 {管理员ID} 请求获取已审核公司列表，参数: {请求数据.model_dump()}")

        # 调用数据层获取已审核公司列表（审核状态=1表示审核通过）
        结果 = await 数据层获取公司列表(
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            搜索关键词=请求数据.搜索关键词,
            公司状态=请求数据.公司状态,
            审核状态=1  # 只获取审核通过的公司
        )

        接口日志器.info(f"管理员 {管理员ID} 获取已审核公司列表成功，返回 {len(结果.get('list', []))} 个公司")
        
        return 统一响应模型.成功(
            数据=结果,
            消息=f"获取已审核公司列表成功，共 {结果.get('total', 0)} 个公司"
        )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"获取已审核公司列表接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"获取已审核公司列表失败: {错误信息}")


@公司管理路由.post(
    "/companies/pending",
    response_model=统一响应模型,
    summary="获取待审核公司列表",
    description="获取所有待审核的公司列表"
)
async def 获取待审核公司列表(
    请求数据: 公司列表查询请求,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """获取待审核公司列表"""
    try:
        管理员ID = 当前管理员["id"]
        接口日志器.info(f"管理员 {管理员ID} 请求获取待审核公司列表，参数: {请求数据.model_dump()}")

        # 调用数据层获取待审核公司列表（审核状态=0表示待审核）
        结果 = await 数据层获取公司列表(
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            搜索关键词=请求数据.搜索关键词,
            公司状态=请求数据.公司状态,
            审核状态=0  # 只获取待审核的公司
        )

        接口日志器.info(f"管理员 {管理员ID} 获取待审核公司列表成功，返回 {len(结果.get('list', []))} 个公司")
        
        return 统一响应模型.成功(
            数据=结果,
            消息=f"获取待审核公司列表成功，共 {结果.get('total', 0)} 个公司"
        )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"获取待审核公司列表接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"获取待审核公司列表失败: {错误信息}")



# =============== 公司审核接口 ===============

@公司管理路由.post(
    "/companies/approve",
    response_model=统一响应模型,
    summary="批准公司申请",
    description="快捷批准公司申请（审核状态设为通过）"
)
async def 批准公司申请(
    请求数据: 公司批准请求,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """批准公司申请"""
    try:
        管理员ID = 当前管理员["id"]
        接口日志器.info(f"管理员 {管理员ID} 批准公司 {请求数据.公司ID}，备注: {请求数据.审核备注}")

        # 调用数据层审核公司，审核状态设为1（通过）
        结果 = await 数据层审核公司(
            公司ID=请求数据.公司ID,
            审核状态=1,  # 审核通过
            审核人ID=管理员ID,
            审核备注=请求数据.审核备注
        )

        if 结果.get("success"):
            接口日志器.info(f"管理员 {管理员ID} 批准公司 {请求数据.公司ID} 成功")
            return 统一响应模型.成功(
                数据={"公司ID": 请求数据.公司ID, "审核状态": 1},
                消息=结果.get("message", "公司批准成功")
            )
        else:
            return 统一响应模型.失败(
                状态码=400,
                消息=结果.get("message", "公司批准失败")
            )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"批准公司申请接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"批准公司申请失败: {错误信息}")


@公司管理路由.post(
    "/companies/reject",
    response_model=统一响应模型,
    summary="拒绝公司申请",
    description="拒绝公司申请（审核状态设为拒绝）"
)
async def 拒绝公司申请(
    请求数据: 公司拒绝请求,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """拒绝公司申请"""
    try:
        管理员ID = 当前管理员["id"]
        接口日志器.info(f"管理员 {管理员ID} 拒绝公司 {请求数据.公司ID}，备注: {请求数据.审核备注}")

        # 调用数据层审核公司，审核状态设为2（拒绝）
        结果 = await 数据层审核公司(
            公司ID=请求数据.公司ID,
            审核状态=2,  # 审核拒绝
            审核人ID=管理员ID,
            审核备注=请求数据.审核备注
        )

        if 结果.get("success"):
            接口日志器.info(f"管理员 {管理员ID} 拒绝公司 {请求数据.公司ID} 成功")
            return 统一响应模型.成功(
                数据={"公司ID": 请求数据.公司ID, "审核状态": 2},
                消息=结果.get("message", "公司拒绝成功")
            )
        else:
            return 统一响应模型.失败(
                状态码=400,
                消息=结果.get("message", "公司拒绝失败")
            )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"拒绝公司申请接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"拒绝公司申请失败: {错误信息}")


@公司管理路由.post(
    "/companies/detail",
    response_model=统一响应模型,
    summary="获取公司详情",
    description="获取指定公司的详细信息"
)
async def 获取公司详情(
    请求数据: 公司详情请求,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """获取公司详情"""
    try:
        管理员ID = 当前管理员["id"]
        接口日志器.info(f"管理员 {管理员ID} 请求获取公司 {请求数据.公司ID} 详情")

        # 调用数据层获取公司详情
        结果 = await 数据层获取公司详情(请求数据.公司ID)

        if 结果 and 结果.get("success"):
            接口日志器.info(f"管理员 {管理员ID} 获取公司 {请求数据.公司ID} 详情成功")
            return 统一响应模型.成功(
                数据=结果.get("data"),
                消息="获取公司详情成功"
            )
        else:
            return 统一响应模型.失败(
                状态码=404,
                消息=结果.get("message", "公司不存在") if 结果 else "公司不存在"
            )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"获取公司详情接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"获取公司详情失败: {错误信息}")


# =============== 通用审核接口（管理员使用） ===============

@公司管理路由.post(
    "/companies/approve-admin",
    response_model=统一响应模型,
    summary="管理员批准公司",
    description="管理员批准指定的公司申请"
)
async def 管理员批准公司(
    请求数据: 公司审核请求模型,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """管理员批准公司"""
    try:
        管理员ID = 当前管理员["id"]
        接口日志器.info(f"管理员 {管理员ID} 批准公司 {请求数据.公司ID}，备注: {请求数据.审核备注}")

        # 调用数据层审核公司，审核状态设为1（通过）
        结果 = await 数据层审核公司(
            公司ID=请求数据.公司ID,
            审核状态=审核状态枚举.审核通过.value,
            审核备注=请求数据.审核备注,
            审核人ID=管理员ID,
        )

        if 结果.get("success"):
            接口日志器.info(f"管理员 {管理员ID} 批准公司 {请求数据.公司ID} 成功")
            return 统一响应模型.成功(消息=结果.get("message", "公司批准成功"))
        else:
            return 统一响应模型.失败(
                状态码=400,
                消息=结果.get("message", "公司批准失败")
            )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"管理员批准公司接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"管理员批准公司失败: {错误信息}")


@公司管理路由.post(
    "/companies/reject-admin",
    response_model=统一响应模型,
    summary="管理员拒绝公司",
    description="管理员拒绝指定的公司申请"
)
async def 管理员拒绝公司(
    请求数据: 公司审核请求模型,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """管理员拒绝公司"""
    try:
        管理员ID = 当前管理员["id"]
        接口日志器.info(f"管理员 {管理员ID} 拒绝公司 {请求数据.公司ID}，备注: {请求数据.审核备注}")

        # 调用数据层审核公司，审核状态设为2（拒绝）
        结果 = await 数据层审核公司(
            公司ID=请求数据.公司ID,
            审核状态=审核状态枚举.审核不通过.value,
            审核备注=请求数据.审核备注,
            审核人ID=管理员ID,
        )

        if 结果.get("success"):
            接口日志器.info(f"管理员 {管理员ID} 拒绝公司 {请求数据.公司ID} 成功")
            return 统一响应模型.成功(消息=结果.get("message", "公司拒绝成功"))
        else:
            return 统一响应模型.失败(
                状态码=400,
                消息=结果.get("message", "公司拒绝失败")
            )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"管理员拒绝公司接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"管理员拒绝公司失败: {错误信息}")


@公司管理路由.post(
    "/companies/detail-admin",
    response_model=统一响应模型,
    summary="管理员获取公司详情",
    description="管理员获取指定公司的详细信息"
)
async def 管理员获取公司详情(
    请求数据: 公司详情请求,
    当前管理员: dict = Depends(获取当前管理员用户)
):
    """管理员获取公司详情"""
    try:
        管理员ID = 当前管理员["id"]
        公司ID = 请求数据.公司ID
        接口日志器.info(f"管理员 {管理员ID} 请求获取公司 {公司ID} 详情")

        # 调用数据层获取公司详情
        结果 = await 数据层获取公司详情(公司ID)

        if 结果 and 结果.get("success"):
            接口日志器.info(f"管理员 {管理员ID} 获取公司 {公司ID} 详情成功")
            return 统一响应模型.成功(
                数据=结果.get("data"),
                消息="获取公司详情成功"
            )
        else:
            return 统一响应模型.失败(
                状态码=404,
                消息=结果.get("message", "公司不存在") if 结果 else "公司不存在"
            )

    except Exception as e:
        错误信息 = str(e)
        错误日志器.error(f"管理员获取公司详情接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息=f"管理员获取公司详情失败: {错误信息}") 