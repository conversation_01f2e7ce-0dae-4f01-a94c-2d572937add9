import logging
import os
import sys
from logging.handlers import RotatingFileHandler

# 获取项目根目录
项目根目录 = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 日志存放目录 - 统一存放在根目录下的"系统日志"文件夹
日志目录 = os.path.join(项目根目录, "系统日志")
os.makedirs(日志目录, exist_ok=True)

# 屏蔽uvicorn和watchfiles的日志
for logger_name in ['watchfiles', 'watchgod', 'uvicorn.watchgod', 'uvicorn.reload', 'uvicorn.error']:
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.WARNING)
    logger.propagate = False

# 过滤器：过滤掉变更检测的日志
class 变更检测过滤器(logging.Filter):
    def filter(self, record):
        """过滤掉所有包含'change detected'的日志消息"""
        try:
            if hasattr(record, "getMessage"):
                消息 = record.getMessage()
            elif hasattr(record, "msg"):
                消息 = str(record.msg) if record.msg else ""
            else:
                return True
            return "change detected" not in 消息
        except Exception:
            return True

# 日志格式
默认格式化器 = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 简洁日志格式（用于控制台输出）
简洁格式化器 = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

# 全局过滤器实例
变更检测过滤器实例 = 变更检测过滤器()

# 配置根日志记录器
根记录器 = logging.getLogger()
根记录器.addFilter(变更检测过滤器实例)

# 定义全局共享的处理器 - 简化为2种主要日志
def 创建共享处理器():
    """创建全局共享的日志处理器 - 简化版本"""
    共享处理器 = {}
    
    # 📋 应用日志处理器 - 记录所有正常的业务操作和信息（INFO级别及以上）
    应用日志文件路径 = os.path.join(日志目录, "应用日志.log")
    应用日志处理器 = RotatingFileHandler(
        应用日志文件路径,
        maxBytes=50*1024*1024,  # 50MB
        backupCount=5,
        encoding='utf-8'
    )
    应用日志处理器.setFormatter(默认格式化器)
    应用日志处理器.setLevel(logging.INFO)
    # 设置轮换文件的中文命名格式
    应用日志处理器.namer = lambda name: name.replace('.log', '').replace('应用日志', '应用日志-备份') + '.log'
    共享处理器['应用日志'] = 应用日志处理器
    
    # ⚠️ 错误日志处理器 - 记录所有错误、异常和警告信息（WARNING级别及以上）
    错误日志文件路径 = os.path.join(日志目录, "错误日志.log")
    错误日志处理器 = RotatingFileHandler(
        错误日志文件路径,
        maxBytes=20*1024*1024,  # 20MB
        backupCount=3,
        encoding='utf-8'
    )
    错误日志处理器.setFormatter(默认格式化器)
    错误日志处理器.setLevel(logging.WARNING)
    # 设置轮换文件的中文命名格式
    错误日志处理器.namer = lambda name: name.replace('.log', '').replace('错误日志', '错误日志-备份') + '.log'
    共享处理器['错误日志'] = 错误日志处理器
    
    # 🖥️ 控制台处理器 - 在开发时显示日志（包含文件名和行号）
    控制台处理器 = logging.StreamHandler(sys.stdout)
    控制台处理器.setFormatter(默认格式化器)
    控制台处理器.setLevel(logging.INFO)
    共享处理器['控制台'] = 控制台处理器
    
    # 🐛 调试日志处理器 - 可选，用于开发调试（默认不启用）
    调试日志文件路径 = os.path.join(日志目录, "调试日志.log")
    调试日志处理器 = RotatingFileHandler(
        调试日志文件路径,
        maxBytes=100*1024*1024,  # 100MB
        backupCount=1,           # 只保留1个备份
        encoding='utf-8'
    )
    调试日志处理器.setFormatter(默认格式化器)
    调试日志处理器.setLevel(logging.DEBUG)
    调试日志处理器.namer = lambda name: name.replace('.log', '').replace('调试日志', '调试日志-备份') + '.log'
    共享处理器['调试日志'] = 调试日志处理器
    
    return 共享处理器

# 创建全局共享处理器
全局共享处理器 = 创建共享处理器()

class 日志记录器:
    """简化的日志记录器管理类"""
    
    _实例 = {}
    
    @classmethod
    def 获取记录器(cls, 名称="应用", 级别=logging.INFO, 输出到控制台=True, 
                启用调试日志=False):
        """
        获取或创建记录器 - 简化版本
        
        Args:
            名称: 记录器名称（如：应用、数据库、接口等）
            级别: 日志级别
            输出到控制台: 是否输出到控制台
            启用调试日志: 是否启用调试日志（仅开发环境使用）
        """
        # 如果记录器已存在，直接返回
        if 名称 in cls._实例:
            return cls._实例[名称]
        
        # 创建记录器
        记录器 = logging.getLogger(名称)
        记录器.setLevel(级别)
        记录器.propagate = False  # 避免日志重复
        
        # 清除已有处理器
        for 处理器 in 记录器.handlers[:]:
            记录器.removeHandler(处理器)
        
        # 添加过滤器
        记录器.addFilter(变更检测过滤器实例)
        
        # 📋 所有记录器都输出到应用日志
        记录器.addHandler(全局共享处理器['应用日志'])
        
        # ⚠️ 所有记录器都输出ERROR/WARNING级别日志到错误日志
        记录器.addHandler(全局共享处理器['错误日志'])
        
        # 🖥️ 添加控制台处理器（可选）
        if 输出到控制台:
            记录器.addHandler(全局共享处理器['控制台'])
        
        # 🐛 添加调试日志处理器（可选，仅开发环境）
        if 启用调试日志:
            记录器.addHandler(全局共享处理器['调试日志'])
        
        # 保存记录器实例
        cls._实例[名称] = 记录器
        return 记录器

# 创建统一的日志记录器架构 - 精简为两个核心日志器
应用日志器 = 日志记录器.获取记录器("应用", logging.INFO)
错误日志器 = 日志记录器.获取记录器("错误", logging.WARNING)

# 注意：兼容性别名已移至 __init__.py 中定义，避免循环导入

def 配置全局异常捕获():
    """配置全局未捕获异常的处理"""
    def 处理未捕获异常(exctype, value, traceback):
        错误日志器.critical("未捕获的异常", exc_info=(exctype, value, traceback))
        sys.__excepthook__(exctype, value, traceback)
    
    # 设置全局异常处理器
    sys.excepthook = 处理未捕获异常

# 初始化
配置全局异常捕获() 