"""
团队数据操作模块 - PostgreSQL版本
基于asyncpg实现的团队相关数据库操作

功能：
1. 团队基础CRUD操作
2. 团队成员管理
3. 团队权限管理
4. 团队统计和查询
"""

import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


# ==================== 团队基础操作 ====================

async def 创建团队(
    团队名称: str,
    公司ID: int,
    创建人id: int,
    团队代码: Optional[str] = None,
    最大成员数: int = 50
) -> Optional[int]:
    """
    创建新团队
    
    Args:
        团队名称: 团队名称
        公司ID: 所属公司ID
        创建人id: 创建人用户id
        团队代码: 团队代码（可选）
        最大成员数: 最大成员数
        
    Returns:
        新创建团队的ID，失败返回None
    """
    try:
        插入SQL = """
        INSERT INTO 团队表 (
            团队名称, 公司ID, 创建人id, 团队负责人id, 团队代码, 
            最大成员数, 当前成员数, 团队状态, created_at
        ) VALUES (
            $1, $2, $3, $3, $4, $5, 1, '正常', CURRENT_TIMESTAMP
        ) RETURNING id
        """
        
        结果 = await 异步连接池实例.执行查询(
            插入SQL, 
            (团队名称, 公司ID, 创建人id, 团队代码, 最大成员数)
        )
        
        if 结果:
            团队id = 结果[0]['id']
            数据库日志器.info(f"创建团队成功: ID={团队id}, 名称={团队名称}")
            
            # 自动将创建人添加为团队管理员
            await 添加团队成员(团队id, 创建人id, '管理员', 创建人id)
            
            return 团队id
        else:
            错误日志器.error(f"创建团队失败: 名称={团队名称}")
            return None
            
    except Exception as e:
        错误日志器.error(f"创建团队异常: 名称={团队名称}, 错误={str(e)}")
        return None


async def 获取团队信息(团队id: int) -> Optional[Dict[str, Any]]:
    """
    获取团队详细信息
    
    Args:
        团队id: 团队id
        
    Returns:
        团队信息字典，失败返回None
    """
    try:
        查询SQL = """
        SELECT 
            t.id, t.团队名称, t.公司ID, t.创建人id, t.团队负责人id,
            t.团队代码, t.最大成员数, t.当前成员数, t.团队状态,
            t.created_at, t.updated_at,
            c.公司名称,
            u1.用户名 as 创建人姓名,
            u2.用户名 as 负责人姓名
        FROM 团队表 t
        LEFT JOIN 公司表 c ON t.公司ID = c.id
        LEFT JOIN 用户表 u1 ON t.创建人id = u1.id
        LEFT JOIN 用户表 u2 ON t.团队负责人id = u2.id
        WHERE t.id = $1
        """
        
        结果 = await 异步连接池实例.执行查询(查询SQL, (团队id,))
        return 结果[0] if 结果 else None
        
    except Exception as e:
        错误日志器.error(f"获取团队信息异常: 团队id={团队id}, 错误={str(e)}")
        return None


async def 更新团队信息(
    团队id: int,
    更新字段: Dict[str, Any],
    操作人ID: int
) -> bool:
    """
    更新团队信息
    
    Args:
        团队id: 团队id
        更新字段: 要更新的字段字典
        操作人ID: 操作人ID
        
    Returns:
        是否更新成功
    """
    try:
        if not 更新字段:
            return True
            
        # 构建更新SQL
        字段列表 = []
        参数列表 = []
        参数索引 = 1
        
        for 字段名, 值 in 更新字段.items():
            if 字段名 in ['团队名称', '团队代码', '最大成员数', '团队状态', '团队负责人id']:
                字段列表.append(f'"{字段名}" = ${参数索引}')
                参数列表.append(值)
                参数索引 += 1
        
        if not 字段列表:
            数据库日志器.warning(f"更新团队信息时没有有效字段: 团队id={团队id}")
            return False
            
        # 添加更新时间
        字段列表.append("updated_at = CURRENT_TIMESTAMP")
        
        更新SQL = f"""
        UPDATE 团队表 
        SET {', '.join(字段列表)}
        WHERE id = $1
        """
        参数列表.append(团队id)
        
        影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)
        
        if 影响行数 > 0:
            数据库日志器.info(f"更新团队信息成功: 团队id={团队id}, 操作人={操作人ID}")
            return True
        else:
            数据库日志器.warning(f"更新团队信息未影响任何行: 团队id={团队id}")
            return False
            
    except Exception as e:
        错误日志器.error(f"更新团队信息异常: 团队id={团队id}, 错误={str(e)}")
        return False


async def 删除团队(团队id: int, 操作人ID: int) -> bool:
    """
    删除团队（软删除）
    
    Args:
        团队id: 团队id
        操作人ID: 操作人ID
        
    Returns:
        是否删除成功
    """
    try:
        更新SQL = """
        UPDATE 团队表 
        SET 团队状态 = '已删除', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        """
        
        影响行数 = await 异步连接池实例.执行更新(更新SQL, (团队id,))
        
        if 影响行数 > 0:
            数据库日志器.info(f"删除团队成功: 团队id={团队id}, 操作人={操作人ID}")
            return True
        else:
            数据库日志器.warning(f"删除团队未影响任何行: 团队id={团队id}")
            return False
            
    except Exception as e:
        错误日志器.error(f"删除团队异常: 团队id={团队id}, 错误={str(e)}")
        return False


# ==================== 团队成员管理 ====================

async def 添加团队成员(
    团队id: int,
    用户id: int,
    角色: str = '成员',
    操作人ID: Optional[int] = None
) -> bool:
    """
    添加团队成员
    
    Args:
        团队id: 团队id
        用户id: 用户id
        角色: 成员角色（管理员/成员）
        操作人ID: 操作人ID
        
    Returns:
        是否添加成功
    """
    try:
        # 检查是否已经是成员
        检查SQL = """
        SELECT id FROM 团队成员表 
        WHERE 团队id = $1 AND 用户id = $2 AND 状态 = '正常'
        """
        
        现有成员 = await 异步连接池实例.执行查询(检查SQL, (团队id, 用户id))
        if 现有成员:
            数据库日志器.warning(f"用户已是团队成员: 团队id={团队id}, 用户id={用户id}")
            return False
            
        # 添加成员
        插入SQL = """
        INSERT INTO 团队成员表 (团队id, 用户id, 角色, 加入时间, 状态, 操作人ID)
        VALUES ($1, $2, $3, CURRENT_TIMESTAMP, '正常', $4)
        """
        
        await 异步连接池实例.执行插入(插入SQL, (团队id, 用户id, 角色, 操作人ID))
        
        # 更新团队成员数
        await 更新团队成员数(团队id)
        
        数据库日志器.info(f"添加团队成员成功: 团队id={团队id}, 用户id={用户id}, 角色={角色}")
        return True
        
    except Exception as e:
        错误日志器.error(f"添加团队成员异常: 团队id={团队id}, 用户id={用户id}, 错误={str(e)}")
        return False


async def 移除团队成员(
    团队id: int,
    用户id: int,
    操作人ID: int
) -> bool:
    """
    移除团队成员
    
    Args:
        团队id: 团队id
        用户id: 用户id
        操作人ID: 操作人ID
        
    Returns:
        是否移除成功
    """
    try:
        更新SQL = """
        UPDATE 团队成员表 
        SET 状态 = '已移除', 移除时间 = CURRENT_TIMESTAMP, 操作人ID = $3
        WHERE 团队id = $1 AND 用户id = $2 AND 状态 = '正常'
        """
        
        影响行数 = await 异步连接池实例.执行更新(更新SQL, (团队id, 用户id, 操作人ID))
        
        if 影响行数 > 0:
            # 更新团队成员数
            await 更新团队成员数(团队id)
            
            数据库日志器.info(f"移除团队成员成功: 团队id={团队id}, 用户id={用户id}")
            return True
        else:
            数据库日志器.warning(f"移除团队成员未影响任何行: 团队id={团队id}, 用户id={用户id}")
            return False
            
    except Exception as e:
        错误日志器.error(f"移除团队成员异常: 团队id={团队id}, 用户id={用户id}, 错误={str(e)}")
        return False


async def 更新团队成员数(团队id: int) -> bool:
    """
    更新团队当前成员数
    
    Args:
        团队id: 团队id
        
    Returns:
        是否更新成功
    """
    try:
        # 统计当前成员数
        统计SQL = """
        SELECT COUNT(*) as 成员数 
        FROM 团队成员表 
        WHERE 团队id = $1 AND 状态 = '正常'
        """
        
        结果 = await 异步连接池实例.执行查询(统计SQL, (团队id,))
        成员数 = 结果[0]['成员数'] if 结果 else 0
        
        # 更新团队表
        更新SQL = """
        UPDATE 团队表 
        SET 当前成员数 = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        """
        
        await 异步连接池实例.执行更新(更新SQL, (成员数, 团队id))
        
        数据库日志器.debug(f"更新团队成员数成功: 团队id={团队id}, 成员数={成员数}")
        return True
        
    except Exception as e:
        错误日志器.error(f"更新团队成员数异常: 团队id={团队id}, 错误={str(e)}")
        return False


async def 获取团队成员列表(
    团队id: int,
    页码: int = 1,
    每页数量: int = 20,
    角色筛选: Optional[str] = None
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取团队成员列表

    Args:
        团队id: 团队id
        页码: 页码（从1开始）
        每页数量: 每页记录数
        角色筛选: 角色筛选（可选）

    Returns:
        (成员列表, 总数)
    """
    try:
        where_条件 = ["tm.团队id = $1", "tm.状态 = '正常'"]
        参数列表 = [团队id]
        参数索引 = 2

        if 角色筛选:
            where_条件.append(f"tm.角色 = ${参数索引}")
            参数列表.append(角色筛选)
            参数索引 += 1

        where_clause = " AND ".join(where_条件)

        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total
        FROM 团队成员表 tm
        WHERE {where_clause}
        """

        总数结果 = await 异步连接池实例.执行查询(计数SQL, 参数列表)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # {{ AURA-X: Fix - 修复PostgreSQL参数占位符重复使用问题. Source: PostgreSQL参数绑定最佳实践 }}
        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT
            tm.id, tm.团队id, tm.用户id, tm.角色, tm.加入时间,
            tm.状态, tm.操作人ID,
            u.用户名, u.手机号, u.邮箱
        FROM 团队成员表 tm
        LEFT JOIN 用户表 u ON tm.用户id = u.id
        WHERE {where_clause}
        ORDER BY tm.加入时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """

        # 添加分页参数
        参数列表.extend([每页数量, 偏移量])

        成员列表 = await 异步连接池实例.执行查询(查询SQL, 参数列表)

        数据库日志器.debug(f"获取团队成员列表成功: 团队id={团队id}, 总数={总数}")
        return 成员列表 or [], 总数

    except Exception as e:
        错误日志器.error(f"获取团队成员列表异常: 团队id={团队id}, 错误={str(e)}")
        return [], 0


async def 更新成员角色(
    团队id: int,
    用户id: int,
    新角色: str,
    操作人ID: int
) -> bool:
    """
    更新团队成员角色

    Args:
        团队id: 团队id
        用户id: 用户id
        新角色: 新角色
        操作人ID: 操作人ID

    Returns:
        是否更新成功
    """
    try:
        更新SQL = """
        UPDATE 团队成员表
        SET 角色 = $1, 操作人ID = $2, updated_at = CURRENT_TIMESTAMP
        WHERE 团队id = $3 AND 用户id = $4 AND 状态 = '正常'
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, (新角色, 操作人ID, 团队id, 用户id))

        if 影响行数 > 0:
            数据库日志器.info(f"更新成员角色成功: 团队id={团队id}, 用户id={用户id}, 新角色={新角色}")
            return True
        else:
            数据库日志器.warning(f"更新成员角色未影响任何行: 团队id={团队id}, 用户id={用户id}")
            return False

    except Exception as e:
        错误日志器.error(f"更新成员角色异常: 团队id={团队id}, 用户id={用户id}, 错误={str(e)}")
        return False


# ==================== 团队查询和统计 ====================

async def 获取用户团队列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取用户所属团队列表

    Args:
        用户id: 用户id
        页码: 页码（从1开始）
        每页数量: 每页记录数

    Returns:
        (团队列表, 总数)
    """
    try:
        # 查询总数
        计数SQL = """
        SELECT COUNT(*) as total
        FROM 团队成员表 tm
        INNER JOIN 团队表 t ON tm.团队id = t.id
        WHERE tm.用户id = $1 AND tm.状态 = '正常' AND t.团队状态 = '正常'
        """

        总数结果 = await 异步连接池实例.执行查询(计数SQL, (用户id,))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = """
        SELECT
            t.id, t.团队名称, t.公司ID, t.团队代码, t.当前成员数, t.最大成员数,
            t.团队状态, t.created_at,
            tm.角色, tm.加入时间,
            c.公司名称
        FROM 团队成员表 tm
        INNER JOIN 团队表 t ON tm.团队id = t.id
        LEFT JOIN 公司表 c ON t.公司ID = c.id
        WHERE tm.用户id = $1 AND tm.状态 = '正常' AND t.团队状态 = '正常'
        ORDER BY tm.加入时间 DESC
        LIMIT $2 OFFSET $3
        """

        团队列表 = await 异步连接池实例.执行查询(查询SQL, (用户id, 每页数量, 偏移量))

        数据库日志器.debug(f"获取用户团队列表成功: 用户id={用户id}, 总数={总数}")
        return 团队列表 or [], 总数

    except Exception as e:
        错误日志器.error(f"获取用户团队列表异常: 用户id={用户id}, 错误={str(e)}")
        return [], 0


async def 检查用户团队权限(
    用户id: int,
    团队id: int,
    需要角色: Optional[str] = None
) -> Dict[str, Any]:
    """
    检查用户在团队中的权限

    Args:
        用户id: 用户id
        团队id: 团队id
        需要角色: 需要的角色（可选）

    Returns:
        权限检查结果
    """
    try:
        查询SQL = """
        SELECT tm.角色, tm.状态, t.团队状态
        FROM 团队成员表 tm
        INNER JOIN 团队表 t ON tm.团队id = t.id
        WHERE tm.用户id = $1 AND tm.团队id = $2
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (用户id, 团队id))

        if not 结果:
            return {
                "是否成员": False,
                "角色": None,
                "权限检查": False,
                "错误信息": "不是团队成员"
            }

        成员信息 = 结果[0]

        是否成员 = 成员信息["状态"] == "正常" and 成员信息["团队状态"] == "正常"
        用户角色 = 成员信息["角色"]

        权限检查 = True
        if 需要角色:
            if 需要角色 == "管理员":
                权限检查 = 用户角色 == "管理员"
            elif 需要角色 == "成员":
                权限检查 = 用户角色 in ["管理员", "成员"]

        return {
            "是否成员": 是否成员,
            "角色": 用户角色,
            "权限检查": 权限检查 and 是否成员,
            "错误信息": None if 权限检查 and 是否成员 else "权限不足"
        }

    except Exception as e:
        错误日志器.error(f"检查用户团队权限异常: 用户id={用户id}, 团队id={团队id}, 错误={str(e)}")
        return {
            "是否成员": False,
            "角色": None,
            "权限检查": False,
            "错误信息": f"检查权限时发生错误: {str(e)}"
        }
