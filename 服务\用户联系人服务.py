"""
用户联系人服务层 - 业务逻辑层
负责处理用户联系人相关的业务逻辑，调用数据访问层进行数据操作
基于三层分离架构设计
"""

from typing import Dict, Any, Optional
from uuid import UUID

from 服务.基础服务 import 数据服务基类
from 数据.用户联系人数据访问层 import 用户联系人数据访问实例, 达人补充信息数据访问实例


class 用户联系人服务(数据服务基类):
    """用户联系人业务服务"""

    def __init__(self):
        super().__init__("用户联系人服务", 用户联系人数据访问实例)
        self.用户联系人数据访问 = 用户联系人数据访问实例
        self.达人补充信息数据访问 = 达人补充信息数据访问实例



    async def 关联联系人到达人补充信息(self, 补充信息id: int, 用户联系人id: UUID) -> Dict[str, Any]:
        """
        将用户联系人关联到达人补充信息

        参数:
            补充信息id: 用户达人补充信息表id
            用户联系人id: 用户联系人UUID

        返回:
            操作结果字典
        """
        # 参数验证
        验证错误 = self.验证必需参数(
            {"补充信息id": 补充信息id, "用户联系人id": 用户联系人id},
            ["补充信息id", "用户联系人id"]
        )
        if 验证错误:
            return self.构建失败响应(验证错误)

        try:
            # 调用数据访问层关联联系人
            关联成功 = await self.达人补充信息数据访问.关联用户联系人(补充信息id, 用户联系人id)

            if 关联成功:
                self.记录信息("关联联系人成功", 补充信息id=补充信息id, 联系人id=用户联系人id)
                return self.构建成功响应(None, "关联联系人成功")
            else:
                return self.构建失败响应("未找到对应的补充信息记录")

        except Exception as e:
            self.记录错误("关联联系人失败", e, 补充信息id=补充信息id, 联系人id=用户联系人id)
            return self.构建失败响应(f"关联联系人失败: {str(e)}")

    async def 创建联系人并关联到补充信息(self, 用户id: int, 姓名: str, 补充信息id: int) -> Dict[str, Any]:
        """
        创建用户联系人并关联到达人补充信息（事务操作）
        确保事务一致性：如果关联失败，则不创建联系人

        参数:
            用户id: 用户id
            姓名: 联系人姓名
            补充信息id: 用户达人补充信息表id

        返回:
            包含联系人UUID的结果字典
        """
        # 参数验证
        验证错误 = self.验证必需参数(
            {"用户id": 用户id, "姓名": 姓名, "补充信息id": 补充信息id},
            ["用户id", "姓名", "补充信息id"]
        )
        if 验证错误:
            return self.构建失败响应(验证错误)

        # 清理姓名参数
        清理后的姓名 = self.清理字符串参数(姓名)
        if not 清理后的姓名:
            return self.构建失败响应("联系人姓名不能为空")

        try:
            # 调用数据访问层创建联系人并关联
            联系人数据 = await self.达人补充信息数据访问.创建联系人并关联到补充信息(
                用户id, 清理后的姓名, 补充信息id
            )

            if 联系人数据:
                self.记录信息("创建联系人并关联成功", 用户id=用户id, 姓名=清理后的姓名, 补充信息id=补充信息id)
                return self.构建成功响应({
                    "用户联系人id": str(联系人数据["用户联系人id"]),
                    "姓名": 联系人数据["姓名"],
                    "用户表id": 联系人数据["用户表id"]
                }, "创建联系人并关联成功")
            else:
                return self.构建失败响应("创建联系人并关联失败")

        except Exception as e:
            self.记录错误("创建联系人并关联失败", e, 用户id=用户id, 姓名=清理后的姓名, 补充信息id=补充信息id)
            return self.构建失败响应(f"创建联系人并关联失败: {str(e)}")


    async def 查询用户联系人列表(self, 用户id: int, 关键词: Optional[str] = None) -> Dict[str, Any]:
        """
        查询用户的联系人列表

        参数:
            用户id: 用户id
            关键词: 搜索关键词（姓名或联系方式）

        返回:
            联系人列表
        """
        # 参数验证
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        try:
            # 调用数据访问层查询联系人列表
            结果 = await self.用户联系人数据访问.查询用户联系人_通过用户id(用户id, 关键词)


            return self.构建成功响应(结果, "查询联系人列表成功")

        except Exception as e:
            self.记录错误("查询联系人列表失败", e, 用户id=用户id)
            return self.构建失败响应(f"查询联系人列表失败: {str(e)}")


# 创建服务实例
用户联系人服务实例 = 用户联系人服务()
