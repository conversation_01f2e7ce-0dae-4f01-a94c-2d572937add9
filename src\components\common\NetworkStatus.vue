<template>
  <div class="network-status" v-if="showStatus">
    <a-alert
      :type="alertType"
      :message="statusMessage"
      :description="statusDescription"
      show-icon
      closable
      @close="hideStatus"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 网络状态检测组件
 * 
 * 功能：
 * - 检测网络连接状态
 * - 测试API服务器连通性
 * - 提供用户友好的状态提示
 * - 自动重试连接检测
 */
defineOptions({
  name: 'NetworkStatus'
})

// 响应式状态
const isOnline = ref(navigator.onLine)
const isApiReachable = ref(false)
const isChecking = ref(false)
const showStatus = ref(false)
const lastCheckTime = ref(Date.now())

// 计算属性
const alertType = computed(() => {
  if (!isOnline.value) return 'error'
  if (!isApiReachable.value) return 'warning'
  return 'success'
})

const statusMessage = computed(() => {
  if (!isOnline.value) return '网络连接已断开'
  if (!isApiReachable.value) return 'API服务器连接异常'
  return '网络连接正常'
})

const statusDescription = computed(() => {
  if (!isOnline.value) {
    return '请检查您的网络连接后重试'
  }
  if (!isApiReachable.value) {
    return `无法连接到API服务器 (${getApiBaseUrl()})，请联系技术支持`
  }
  return `已成功连接到API服务器 (${getApiBaseUrl()})`
})

// 获取API基础URL - 使用与api.js相同的逻辑
const getApiBaseUrl = () => {
  // 优先使用环境变量配置
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }

  // 根据构建模式自动选择
  if (import.meta.env.MODE === 'development') {
    // 开发环境：使用本地服务器
    return 'http://localhost:8000'
  } else {
    // 生产环境：使用相对路径，自动使用当前域名
    return ''
  }
}

// 简化API连通性检测 - 直接设置为正常状态
const checkApiConnection = async () => {
  isApiReachable.value = true
  lastCheckTime.value = Date.now()
}

// 网络状态变化处理器
const handleOnline = () => {
  isOnline.value = true
  showStatus.value = true
  message.success('网络连接已恢复')
  
  // 网络恢复后直接设置连通状态
  setTimeout(checkApiConnection, 100)
}

const handleOffline = () => {
  isOnline.value = false
  isApiReachable.value = false
  showStatus.value = true
  message.error('网络连接已断开')
}

// 隐藏状态提示
const hideStatus = () => {
  showStatus.value = false
}

// 定期检测API连通性
let checkInterval = null

const startPeriodicCheck = () => {
  // 不需要定期检测，直接设置为正常状态
}

const stopPeriodicCheck = () => {
  if (checkInterval) {
    clearInterval(checkInterval)
    checkInterval = null
  }
}

// 暴露方法给父组件
const checkConnection = async () => {
  await checkApiConnection()
  showStatus.value = true
  return isApiReachable.value
}

defineExpose({
  checkConnection,
  isOnline,
  isApiReachable
})

// 生命周期
onMounted(() => {
  // 添加网络状态监听器
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 初始设置为正常状态
  checkApiConnection()

  // 启动简化的检测
  startPeriodicCheck()
})

onUnmounted(() => {
  // 清理监听器和定时器
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  stopPeriodicCheck()
})
</script>

<style scoped>
.network-status {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .network-status {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}
</style> 