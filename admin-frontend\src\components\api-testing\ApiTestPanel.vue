<template>
  <div class="api-test-panel" v-if="selectedApi">
    <a-card :title="`测试接口: ${selectedApi.name}`" class="panel-card">
      <template #extra>
        <a-space>
          <a-button @click="generateApiDocs" type="default">
            <template #icon><file-text-outlined /></template>
            生成接口文档
          </a-button>
          <a-button type="primary" @click="handleSendRequest" :loading="isLoading">
            <template #icon><send-outlined /></template>
            发送请求
          </a-button>
          <a-button @click="resetFormAndResponse">
            <template #icon><clear-outlined /></template>
            重置
          </a-button>
        </a-space>
      </template>

      <p class="api-path">
        <strong>路径:</strong> 
        <a-tag color="purple" class="http-method-tag">{{ selectedApi.method }}</a-tag> 
        {{ selectedApi.path }}
        <a-tag 
          :color="selectedApi.requiresAuth ? 'blue' : 'green'" 
          class="auth-tag"
        >
          <template #icon>
            <lock-outlined v-if="selectedApi.requiresAuth" />
            <unlock-outlined v-else />
          </template>
          {{ selectedApi.requiresAuth ? '需要认证' : '无需认证' }}
        </a-tag>
      </p>
      <p class="api-description" v-if="selectedApi.description">{{ selectedApi.description }}</p>
      
      <a-divider />

      <!-- 请求配置区域 -->
      <a-tabs v-model:activeKey="activeRequestTab" class="request-tabs">
        <a-tab-pane key="params" tab="请求参数">
          <request-params-form />
        </a-tab-pane>
        <a-tab-pane key="headers" tab="请求头">
          <request-headers-form />
        </a-tab-pane>
        <a-tab-pane key="auth" tab="认证">
          <auth-management />
        </a-tab-pane>
      </a-tabs>

      <a-divider />

      <!-- 响应结果区域 -->
      <h3 class="response-title">响应结果</h3>
      <div class="response-status-bar" v-if="responseStatus !== null">
        <span :class="['status-tag', getStatusClass(responseStatus)]">
          HTTP: {{ responseStatus }}
        </span>
        <span :class="['status-tag', getStatusClass(responseFastApiStatus, true)]" v-if="responseFastApiStatus !== null">
          业务: {{ responseFastApiStatus }}
        </span>
        <span class="status-tag time-tag" v-if="responseTime !== null">时间: {{ responseTime }} ms</span>
        <span class="status-tag size-tag" v-if="responseSize !== null">大小: {{ formatBytes(responseSize) }}</span>
      </div>
       <p v-if="responseMessage" class="response-message">业务消息: {{ responseMessage }}</p>

      <a-tabs v-model:activeKey="activeResponseTab" class="response-tabs">
        <a-tab-pane key="body" tab="响应体">
          <response-display :content="responseData" type="body" />
        </a-tab-pane>
        <a-tab-pane key="headers" tab="响应头">
          <response-display :content="responseHeaders" type="headers" />
        </a-tab-pane>
        <a-tab-pane key="raw" tab="原始响应">
           <response-display :content="rawResponseForDisplay" type="raw" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
    
    <!-- 接口文档Modal -->
    <a-modal
      v-model:open="apiDocsModalVisible"
      title="接口文档"
      width="800px"
      :footer="null"
    >
      <div class="api-docs-content">
        <div class="docs-toolbar">
          <a-button type="primary" @click="copyApiDocs">
            <template #icon><copy-outlined /></template>
            复制文档
          </a-button>
        </div>
        <a-divider />
        <pre ref="apiDocsRef" class="api-docs-pre">{{ formattedApiDocs }}</pre>
      </div>
    </a-modal>
  </div>
  <div v-else class="api-test-panel-empty">
    <a-empty description="请从左侧选择一个接口进行测试" />
  </div>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {
  Button as AButton,
  Card as ACard,
  Divider as ADivider,
  Empty as AEmpty,
  message,
  Modal as AModal,
  Space as ASpace,
  TabPane as ATabPane,
  Tabs as ATabs,
  Tag as ATag
} from 'ant-design-vue';
import {
  ClearOutlined,
  CopyOutlined,
  FileTextOutlined,
  LockOutlined,
  SendOutlined,
  UnlockOutlined,
} from '@ant-design/icons-vue';
import {useApiTestingModule} from '@/store/apiTestingModule';
import RequestParamsForm from './RequestParamsForm.vue';
import RequestHeadersForm from './RequestHeadersForm.vue';
import ResponseDisplay from './ResponseDisplay.vue';
import AuthManagement from './AuthManagement.vue';

const store = useApiTestingModule();

const selectedApi = computed(() => store.getSelectedApiDetails);
const isLoading = computed(() => store.isLoading);
const mainAppToken = computed(() => store.mainAppToken); // 从store获取主应用token

// 响应数据
const responseStatus = computed(() => store.responseStatus);
const responseFastApiStatus = computed(() => store.responseFastApiStatus);
const responseMessage = computed(() => store.responseMessage);
const responseData = computed(() => store.responseData);
const responseHeaders = computed(() => store.responseHeaders);
const responseTime = computed(() => store.responseTime);
const responseSize = computed(() => store.responseSize);

// 用于显示整个Axios响应对象或错误对象，以供调试
const rawResponseForDisplay = computed(() => {
  if (store.responseData && store.responseData._rawAxiosResponse) {
    return store.responseData._rawAxiosResponse;
  } 
  if (store.responseData && store.responseData._rawAxiosError) {
     return store.responseData._rawAxiosError;
  }
  return store.responseData; // 降级显示responseData
});

const activeRequestTab = ref('params');
const activeResponseTab = ref('body');

// 接口文档相关
const apiDocsModalVisible = ref(false);
const formattedApiDocs = ref('');
const apiDocsRef = ref(null);

const handleSendRequest = async () => {
  if (!selectedApi.value) {
    message.error('请先选择一个接口');
    return;
  }
  await store.executeRequest();
  // 自动切换到响应体 Tab
  activeResponseTab.value = 'body'; 
};

const resetFormAndResponse = () => {
  if (selectedApi.value) {
    store.selectApi(selectedApi.value.id); // 重新选择以重置表单到默认值
  }
  store.clearResponse(); // 清空响应数据
  activeRequestTab.value = 'params';
  activeResponseTab.value = 'body';
  message.info('表单和响应已重置');
};

const getStatusClass = (status, isBusiness = false) => {
  if (status === null || status === undefined) return '';
  const s = Number(status);
  if (isBusiness) {
    return s === 100 ? 'status-success-biz' : 'status-error-biz';
  }
  if (s >= 200 && s < 300) return 'status-success';
  if (s >= 400 && s < 500) return 'status-warning';
  if (s >= 500) return 'status-error';
  return 'status-info';
};

const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// 当选择的API变化时，重置Tab
watch(selectedApi, () => {
  activeRequestTab.value = 'params';
  activeResponseTab.value = 'body';
});

const generateApiDocs = () => {
  if (!selectedApi.value) {
    message.error('请先选择一个接口');
    return;
  }

  const api = selectedApi.value;
  const hasResponse = responseStatus.value !== null;
  
  let docs = "API Documentation\n";
  docs += `Name: ${api.name}\n\n`;

  // 基本信息
  docs += "Section: Basic Information\n";
  docs += `Path: ${api.method} ${api.path}\n`;
  docs += `Authentication: ${api.requiresAuth ? '需要认证' : '无需认证'}\n`;
  if (api.description) {
    docs += `Description: ${api.description.replace(/\n/g, '\n             ')}\n`; // Indent multi-line descriptions
  }
  docs += '\n';

  // 请求参数
  if (api.params && api.params.length > 0) {
    docs += "Section: Request Parameters\n\n";
    
    const bodyParams = api.params.filter(p => !p.isQueryParam && !p.isPathParam && !p.isHeaderParam);
    const queryParams = api.params.filter(p => p.isQueryParam === true);
    const pathParams = api.params.filter(p => p.isPathParam === true);
    const headerParams = api.params.filter(p => p.isHeaderParam === true);

    const getParameterTypeText = (paramDetails) => {
      // 添加调试日志，特别关注"起始id"参数
      if (paramDetails.key === '起始id' || paramDetails.name === '起始id') {
        console.log('=== 起始id 参数调试信息（修复后）===');
        console.log('paramDetails:', paramDetails);
        console.log('paramDetails.rawSchema:', paramDetails.rawSchema);
        if (paramDetails.rawSchema) {
          console.log('rawSchema.type:', paramDetails.rawSchema.type);
          console.log('rawSchema.anyOf:', paramDetails.rawSchema.anyOf);
        }
      }
      
      let typeForTable;
      const propDetails = paramDetails.rawSchema || paramDetails;

      if (propDetails.anyOf && Array.isArray(propDetails.anyOf)) {
        const nonNullSchema = propDetails.anyOf.find(s => s.type !== 'null');
        if (nonNullSchema && nonNullSchema.type) {
          if (nonNullSchema.type === 'integer' || nonNullSchema.type === 'number') {
            typeForTable = 'number';
          } else if (nonNullSchema.type === 'string') {
            if (propDetails.format === 'date-time' || propDetails.format === 'date' || nonNullSchema.format === 'date-time' || nonNullSchema.format === 'date') {
              typeForTable = 'date';
            } else {
              typeForTable = 'string';
            }
          } else if (nonNullSchema.type === 'boolean') {
            typeForTable = 'boolean';
          } else if (nonNullSchema.type === 'array') {
            typeForTable = 'array';
             if (nonNullSchema.items && nonNullSchema.items.type) {
              typeForTable += ` of ${nonNullSchema.items.type}`;
            } else if (nonNullSchema.items && nonNullSchema.items.$ref) {
              typeForTable += ` of ${nonNullSchema.items.$ref.split('/').pop()}`;
            }
          } else {
            typeForTable = nonNullSchema.type; 
          }
        } else {
          typeForTable = propDetails.anyOf.map(s => s.type || 'unknown').join('/') || 'string';
        }
      } else if (propDetails.type) {
        if (propDetails.type === 'integer' || propDetails.type === 'number') {
          typeForTable = 'number';
        } else if (propDetails.type === 'string') {
          if (propDetails.format === 'date-time' || propDetails.format === 'date') {
            typeForTable = 'date';
          } else {
            typeForTable = 'string';
          }
        } else if (propDetails.type === 'boolean') {
          typeForTable = 'boolean';
        } else if (propDetails.type === 'array') {
          typeForTable = 'array';
          if (propDetails.items && propDetails.items.type) {
            typeForTable += ` of ${propDetails.items.type}`;
          } else if (propDetails.items && propDetails.items.$ref) {
            typeForTable += ` of ${propDetails.items.$ref.split('/').pop()}`;
          }
        } else {
          typeForTable = propDetails.type;
        }
      } else {
        typeForTable = 'string'; // Default fallback
      }
      
      return typeForTable;
    };
    
    // Body 参数
    if (bodyParams.length > 0) {
      docs += "Subsection: Body Parameters\n";
      bodyParams.forEach(param => {
        const propDetails = param.rawSchema || param;
        const typeText = getParameterTypeText(param);
        const defaultVal = param.defaultValue !== undefined ? 
          (typeof param.defaultValue === 'object' ? JSON.stringify(param.defaultValue) : String(param.defaultValue)) : 
          (propDetails.default !== undefined ? String(propDetails.default) : '-');
        const displayName = propDetails.title || param.name || param.key;
        const descriptionText = propDetails.description || param.description || param.placeholder || '-';
        const requiredText = param.required ? '是' : '否';

        docs += "  Parameter:\n";
        docs += `    Name: ${displayName}\n`;
        docs += `    Key: ${param.key}\n`;
        docs += `    Type: ${typeText}\n`;
        docs += `    Required: ${requiredText}\n`;
        docs += `    Default: ${defaultVal}\n`;
        docs += `    Description: ${descriptionText.replace(/\n/g, '\n                 ')}\n`;
      });
      docs += '\n';
    }
    
    // Query 参数
    if (queryParams.length > 0) {
      docs += "Subsection: Query Parameters\n";
      queryParams.forEach(param => {
        const propDetails = param.rawSchema || param;
        const typeText = getParameterTypeText(param); // Reuse type logic
        const defaultVal = param.defaultValue !== undefined ? String(param.defaultValue) : (propDetails.default !== undefined ? String(propDetails.default) : '-');
        const displayName = propDetails.title || param.name || param.key;
        const descriptionText = propDetails.description || param.description || param.placeholder || '-';
        const requiredText = param.required ? '是' : '否';

        docs += "  Parameter:\n";
        docs += `    Name: ${displayName}\n`;
        docs += `    Key: ${param.key}\n`;
        docs += `    Type: ${typeText}\n`;
        docs += `    Required: ${requiredText}\n`;
        docs += `    Default: ${defaultVal}\n`;
        docs += `    Description: ${descriptionText.replace(/\n/g, '\n                 ')}\n`;
      });
      docs += '\n';
    }
    
    // Path 参数
    if (pathParams.length > 0) {
      docs += "Subsection: Path Parameters\n";
      pathParams.forEach(param => {
        const propDetails = param.rawSchema || param;
        const typeText = getParameterTypeText(param); // Reuse type logic
        const displayName = propDetails.title || param.name || param.key;
        const descriptionText = propDetails.description || param.description || param.placeholder || '-';

        docs += "  Parameter:\n";
        docs += `    Name: ${displayName}\n`;
        docs += `    Key: ${param.key}\n`;
        docs += `    Type: ${typeText}\n`;
        docs += `    Required: 是\n`;
        docs += `    Description: ${descriptionText.replace(/\n/g, '\n                 ')}\n`;
      });
      docs += '\n';
    }
    
    // Header 参数
    if (headerParams.length > 0) {
      docs += "Subsection: Header Parameters\n";
      headerParams.forEach(param => {
        const propDetails = param.rawSchema || param;
        const typeText = getParameterTypeText(param); // Reuse type logic
        const defaultVal = param.defaultValue !== undefined ? String(param.defaultValue) : (propDetails.default !== undefined ? String(propDetails.default) : '-');
        const displayName = propDetails.title || param.name || param.key;
        const descriptionText = propDetails.description || param.description || param.placeholder || '-';
        const requiredText = param.required ? '是' : '否';

        docs += "  Parameter:\n";
        docs += `    Name: ${displayName}\n`;
        docs += `    Key: ${param.key}\n`;
        docs += `    Type: ${typeText}\n`;
        docs += `    Required: ${requiredText}\n`;
        docs += `    Default: ${defaultVal}\n`;
        docs += `    Description: ${descriptionText.replace(/\n/g, '\n                 ')}\n`;
      });
      docs += '\n';
    }
  }

  // 示例请求
  docs += "Section: Example Request\n";
  docs += `Method: ${api.method}\n`;
  docs += `Path: ${api.path}\n`;
  if (api.requiresAuth) {
    docs += "AuthenticationNote: 此接口需要有效的认证Token\n";
    docs += "Headers:\n  Authorization: Bearer YOUR_TOKEN_HERE\n";
  } else {
    docs += "AuthenticationNote: 无需认证\n";
  }
  
  const currentParams = store.currentParams || {};
  if (Object.keys(currentParams).length > 0) {
    if (api.method !== 'GET' && api.method !== 'DELETE') { // Assuming GET/DELETE typically don't have bodies from this form
      docs += "Body:\n";
      docs += `${JSON.stringify(currentParams, null, 2)}\n`;
    } else {
       // For GET/DELETE, parameters are usually query or path.
       // Path params are part of api.path. Query params are what's left in currentParams if not path.
       const queryExample = Object.entries(currentParams)
        .filter(([key]) => !(pathParams.find(pp => pp.key === key)))
        .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`).join('&');
      if (queryExample) {
        docs += `QueryStringNote: 例如: ${api.path}${api.path.includes('?') ? '&' : '?'}${queryExample}\n`;
      }
    }
  }
  docs += '\n';

  // 响应示例
  if (hasResponse) {
    docs += "Section: Example Response\n";
    docs += `HTTP Status: ${responseStatus.value}\n`;
    if (responseFastApiStatus.value !== null) {
      docs += `Business Status: ${responseFastApiStatus.value}\n`;
    }
    if (responseMessage.value) {
      docs += `Business Message: ${responseMessage.value.replace(/\n/g, '\n                  ')}\n`;
    }
    docs += "Body:\n";
    try {
      docs += `${JSON.stringify(responseData.value, null, 2)}\n`;
    } catch (e) {
      docs += `${JSON.stringify(responseData.value)}\n`;
    }
    docs += '\n';
  }

  // 注意事项
  docs += "Section: Notes\n";
  if (api.requiresAuth) {
    docs += "- 此接口需要有效的认证Token\n";
  }
  docs += "- 接口时间和大小限制请参考主要API文档\n";
  
  // 更新文档内容并显示Modal
  formattedApiDocs.value = docs;
  apiDocsModalVisible.value = true;
};

const copyApiDocs = async () => {
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(formattedApiDocs.value);
      message.success('文档已复制到剪贴板');
    } else {
      // 旧方法：创建临时文本区域
      const textArea = document.createElement('textarea');
      textArea.value = formattedApiDocs.value;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success('文档已复制到剪贴板');
    }
  } catch (err) {
    console.error('复制失败:', err);
    message.error('复制失败，请手动选择并复制文档内容');
  }
};
</script>

<style scoped>
.api-test-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.api-path {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.http-method-tag {
  margin-right: 8px;
  font-family: monospace;
  font-weight: bold;
}

.auth-tag {
  margin-left: 8px;
}

.api-description {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.65);
  white-space: pre-line;
}

.request-tabs {
  flex-grow: 0;
  margin-bottom: 16px;
}

.response-title {
  margin: 8px 0;
  font-size: 16px;
  font-weight: bold;
}

.response-status-bar {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.status-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.status-warning {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  color: #faad14;
}

.status-error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.status-info {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
}

.status-success-biz {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.status-error-biz {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.time-tag, .size-tag {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: rgba(0, 0, 0, 0.65);
}

.response-message {
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 8px;
  font-family: monospace;
  word-break: break-word;
}

.response-tabs {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.api-test-panel-empty {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border-radius: 4px;
}

.api-docs-content {
  max-height: 70vh;
  overflow-y: auto;
}

.docs-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.api-docs-pre {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 13px;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
}
</style>