"""
通告数据处理模块
提供通告相关的数据库操作功能
主要功能：
1. 通告CRUD操作
2. 通告列表查询
3. 通告状态管理
"""

import json
from datetime import datetime
from typing import Optional, List, Dict, Any

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器

async def 异步获取通告列表(
    页码: int = 1,
    页面大小: int = 10,
    类型筛选: Optional[str] = None,
    状态筛选: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取通告列表
    
    Args:
        页码: 页码，从1开始
        页面大小: 每页大小
        类型筛选: 类型筛选条件
        状态筛选: 状态筛选条件
        
    Returns:
        Dict: 包含success、data、message的字典
    """
    try:
        数据库日志器.info(f"获取通告列表 页码={页码}, 页面大小={页面大小}, 类型筛选={类型筛选}, 状态筛选={状态筛选}")
        
        # 构建查询条件
        条件列表 = []
        参数列表 = []
        
        if 类型筛选:
            条件列表.append("类型 = $1")
            参数列表.append(类型筛选)
            
        if 状态筛选:
            if 状态筛选 == "已发布":
                条件列表.append("已发布 = 1")
            elif 状态筛选 == "未发布":
                条件列表.append("已发布 = 0")
        
        WHERE条件 = " WHERE " + " AND ".join(条件列表) if 条件列表 else ""

        # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接池. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
        # 获取总数
        计数SQL = f"SELECT COUNT(*) as total FROM 通告{WHERE条件}"
        总数结果列表 = await 异步连接池实例.执行查询(计数SQL, 参数列表)
        总数 = 总数结果列表[0]['total'] if 总数结果列表 else 0

        # 计算偏移量
        偏移量 = (页码 - 1) * 页面大小

        # 获取列表数据
        查询SQL = f"""
        SELECT id, 类型, 标题, 创建时间, 更新时间, 已发布, 重要性,
               开始时间, 结束时间, 发布时间, 操作标识
        FROM 通告{WHERE条件}
        ORDER BY 重要性 DESC, 创建时间 DESC
        LIMIT $1 OFFSET $2
        """

        记录列表 = await 异步连接池实例.执行查询(查询SQL, 参数列表 + [页面大小, 偏移量])

        # 格式化数据
        通告列表 = []
        for 记录 in 记录列表:
            通告信息 = {
                "id": 记录["id"],
                "类型": 记录["类型"],
                "标题": 记录["标题"],
                "创建时间": 记录["创建时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["创建时间"] else None,
                "更新时间": 记录["更新时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["更新时间"] else None,
                "已发布": bool(记录["已发布"]),
                "重要性": 记录["重要性"],
                "开始时间": 记录["开始时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["开始时间"] else None,
                "结束时间": 记录["结束时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["结束时间"] else None,
                "发布时间": 记录["发布时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["发布时间"] else None,
                "操作标识": 记录["操作标识"]
            }
            通告列表.append(通告信息)

        # 计算分页信息
        总页数 = (总数 + 页面大小 - 1) // 页面大小

        数据库日志器.info(f"通告列表查询成功，共{总数}条记录，当前第{页码}页")

        return {
            "通告列表": 通告列表,
            "分页信息": {
                "当前页码": 页码,
                "页面大小": 页面大小,
                "总记录数": 总数,
                "总页数": 总页数
            }
        }

    except Exception as e:
        错误日志器.error(f"获取通告列表失败: {e}", exc_info=True)
        return {
            "通告列表": [],
            "分页信息": {
                "当前页码": 页码,
                "页面大小": 页面大小,
                "总记录数": 0,
                "总页数": 0
            }
        }

async def 异步获取通告详情(通告id: int) -> Dict[str, Any]:
    """
    获取通告详情
    
    Args:
        通告id: 通告id
        
    Returns:
        Dict: 包含success、data、message的字典
    """
    try:
        数据库日志器.info(f"获取通告详情 通告id={通告id}")
        
        async with 异步连接池实例.获取连接() as 连接:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            查询SQL = """
            SELECT id, 类型, 标题, 内容, 创建时间, 更新时间, 已发布, 重要性,
                   开始时间, 结束时间, 发布时间, 操作标识
            FROM 通告 WHERE id = $1
            """

            记录_原始 = await 连接.fetchrow(查询SQL, 通告id)

            if not 记录_原始:
                return None

            # 转换为字典
            记录 = dict(记录_原始)

            # 解析内容JSON
            内容数据 = []
            if 记录["内容"]:  # 内容字段
                try:
                    内容数据 = json.loads(记录["内容"])
                except json.JSONDecodeError:
                    # 如果不是JSON格式，当作纯文本处理
                    内容数据 = [{"类型": "文本", "内容": 记录["内容"]}]

            通告信息 = {
                "id": 记录["id"],
                "类型": 记录["类型"],
                "标题": 记录["标题"],
                "内容": 内容数据,
                "创建时间": 记录["创建时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["创建时间"] else None,
                "更新时间": 记录["更新时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["更新时间"] else None,
                "已发布": bool(记录["已发布"]),
                "重要性": 记录["重要性"],
                "开始时间": 记录["开始时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["开始时间"] else None,
                "结束时间": 记录["结束时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["结束时间"] else None,
                "发布时间": 记录["发布时间"].strftime('%Y-%m-%d %H:%M:%S') if 记录["发布时间"] else None,
                "操作标识": 记录["操作标识"]
            }

            数据库日志器.info(f"通告详情获取成功，通告id: {通告id}")

            return 通告信息

    except Exception as e:
        错误日志器.error(f"获取通告详情失败: {e}", exc_info=True)
        return None

async def 异步创建通告(
    标题: str,
    类型: str = "通知",
    重要性: int = 1,
    操作标识: int = 0,
    排序: int = 0,
    已发布: bool = False,
    内容: Optional[List[dict]] = None,
    开始时间: Optional[str] = None,
    结束时间: Optional[str] = None,
    创建者ID: int = None
) -> Dict[str, Any]:
    """
    创建通告
    
    Args:
        标题: 通告标题
        类型: 通告类型
        重要性: 重要性级别
        操作标识: 操作标识
        排序: 排序
        已发布: 是否已发布
        内容: 通告内容列表
        开始时间: 开始时间
        结束时间: 结束时间
        创建者ID: 创建者ID
        
    Returns:
        Dict: 包含success、data、message的字典
    """
    try:
        数据库日志器.info(f"创建通告：标题={标题}, 类型={类型}, 创建者ID={创建者ID}")
        
        # 处理内容JSON
        内容JSON = json.dumps(内容 or [], ensure_ascii=False) if 内容 else None
        
        # 处理时间字段
        开始时间对象 = None
        结束时间对象 = None
        发布时间对象 = None
        
        if 开始时间:
            try:
                开始时间对象 = datetime.strptime(开始时间, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                pass
                
        if 结束时间:
            try:
                结束时间对象 = datetime.strptime(结束时间, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                pass
        
        if 已发布:
            发布时间对象 = datetime.now()
        
        async with 异步连接池实例.获取连接() as 连接:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            插入SQL = """
            INSERT INTO 通告 (类型, 标题, 内容, 已发布, 重要性, 开始时间, 结束时间, 发布时间, 操作标识)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
            """

            通告id = await 连接.fetchval(插入SQL,
                类型, 标题, 内容JSON, 已发布, 重要性,
                开始时间对象, 结束时间对象, 发布时间对象, 操作标识
            )

            数据库日志器.info(f"通告创建成功，通告id: {通告id}")

            return {
                "success": True,
                "data": {"通告id": 通告id},
                "message": "通告创建成功"
            }
                
    except Exception as e:
        错误日志器.error(f"创建通告失败: {e}", exc_info=True)
        return {"success": False, "message": "创建通告失败"}

async def 异步更新通告(
    通告id: int,
    标题: str,
    类型: str = "通知",
    重要性: int = 1,
    操作标识: int = 0,
    排序: int = 0,
    已发布: bool = False,
    内容: Optional[List[dict]] = None,
    开始时间: Optional[str] = None,
    结束时间: Optional[str] = None,
    更新者ID: int = None
) -> Dict[str, Any]:
    """
    更新通告
    
    Args:
        通告id: 通告id
        其他参数同创建通告
        
    Returns:
        Dict: 包含success、data、message的字典
    """
    try:
        数据库日志器.info(f"更新通告：通告id={通告id}, 标题={标题}, 更新者ID={更新者ID}")
        
        # 处理内容JSON
        内容JSON = json.dumps(内容 or [], ensure_ascii=False) if 内容 else None
        
        # 处理时间字段
        开始时间对象 = None
        结束时间对象 = None
        发布时间对象 = None
        
        if 开始时间:
            try:
                开始时间对象 = datetime.strptime(开始时间, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                pass
                
        if 结束时间:
            try:
                结束时间对象 = datetime.strptime(结束时间, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                pass
        
        async with 异步连接池实例.获取连接() as 连接:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            # 先检查通告是否存在
            检查SQL = "SELECT id FROM 通告 WHERE id = $1"
            if not await 连接.fetchrow(检查SQL, 通告id):
                return {"success": False, "message": "通告不存在"}

            # 检查当前发布状态，如果从未发布变为已发布，设置发布时间
            if 已发布:
                状态检查SQL = "SELECT 已发布 FROM 通告 WHERE id = $1"
                当前状态 = await 连接.fetchrow(状态检查SQL, 通告id)
                if 当前状态 and not 当前状态[0]:  # 当前未发布，现在要发布
                    发布时间对象 = datetime.now()

            更新SQL = """
            UPDATE 通告 SET
                类型 = $1, 标题 = $2, 内容 = $3, 已发布 = $4, 重要性 = $5,
                开始时间 = $6, 结束时间 = $7, 操作标识 = $8, 更新时间 = $9
            """

            参数列表 = [
                类型, 标题, 内容JSON, 已发布, 重要性,
                开始时间对象, 结束时间对象, 操作标识, datetime.now()
            ]

            # 如果需要更新发布时间
            if 发布时间对象:
                更新SQL += ", 发布时间 = $10"
                参数列表.append(发布时间对象)

            更新SQL += " WHERE id = $" + str(len(参数列表) + 1)
            参数列表.append(通告id)

            await 连接.execute(更新SQL, *参数列表)

            数据库日志器.info(f"通告更新成功，通告id: {通告id}")

            return {
                "success": True,
                "data": {"通告id": 通告id},
                "message": "通告更新成功"
            }
                
    except Exception as e:
        错误日志器.error(f"更新通告失败: {e}", exc_info=True)
        return {"success": False, "message": "更新通告失败"}

async def 异步删除通告(通告id: int, 删除者ID: int) -> Dict[str, Any]:
    """
    删除通告（软删除）
    
    Args:
        通告id: 通告id
        删除者ID: 删除者ID
        
    Returns:
        Dict: 包含success、message的字典
    """
    try:
        数据库日志器.info(f"删除通告：通告id={通告id}, 删除者ID={删除者ID}")
        
        async with 异步连接池实例.获取连接() as 连接:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            # 检查通告是否存在
            检查SQL = "SELECT id FROM 通告 WHERE id = $1"
            if not await 连接.fetchrow(检查SQL, 通告id):
                return {"success": False, "message": "通告不存在"}

            # 执行删除
            删除SQL = "DELETE FROM 通告 WHERE id = $1"
            await 连接.execute(删除SQL, 通告id)

            数据库日志器.info(f"通告删除成功，通告id: {通告id}")

            return {
                "success": True,
                "message": "通告删除成功"
            }
                
    except Exception as e:
        错误日志器.error(f"删除通告失败: {e}", exc_info=True)
        return {"success": False, "message": "删除通告失败"} 