<template>
  <div class="dashboard">
    <div class="welcome-section">
      <a-card class="welcome-card custom-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ userStore.userName }}！</h2>
            <p>今天是一个美好的工作日，让我们开始高效的达人管理吧。</p>
          </div>
          <div class="welcome-time">
            {{ currentTime }}
          </div>
        </div>
      </a-card>
    </div>

    <div class="stats-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card custom-card">
            <a-statistic
              title="总达人数"
              :value="1128"
              :value-style="{ color: '#1890ff' }"
              :prefix="h(UserOutlined)"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card custom-card">
            <a-statistic
              title="本月新增"
              :value="93"
              :value-style="{ color: '#52c41a' }"
              :prefix="h(UserAddOutlined)"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card custom-card">
            <a-statistic
              title="活跃店铺"
              :value="45"
              :value-style="{ color: '#faad14' }"
              :prefix="h(ShopOutlined)"
            />
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card custom-card">
            <a-statistic
              title="团队成员"
              :value="12"
              :value-style="{ color: '#722ed1' }"
              :prefix="h(TeamOutlined)"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <div class="quick-actions">
      <a-card title="快捷操作" class="custom-card">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-button
              type="primary"
              size="large"
              block
              @click="navigateTo('/talent')"
              class="action-button"
            >
              <UserOutlined />
              <span>添加达人</span>
            </a-button>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-button
              size="large"
              block
              @click="navigateTo('/store')"
              class="action-button"
            >
              <ShopOutlined />
              <span>管理店铺</span>
            </a-button>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-button
              size="large"
              block
              @click="navigateTo('/friend')"
              class="action-button"
            >
              <TeamOutlined />
              <span>联系人</span>
            </a-button>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-button
              size="large"
              block
              @click="navigateTo('/team/dashboard')"
              class="action-button dashboard-button"
            >
              <BarChartOutlined />
              <span>数据看板</span>
            </a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 团队数据概览 -->
    <div class="team-overview-section">
      <a-card title="团队数据概览" class="custom-card">
        <template #extra>
          <a-button type="link" @click="navigateTo('/team/dashboard')">
            查看详细数据
            <RightOutlined />
          </a-button>
        </template>
        
        <a-row :gutter="[24, 16]">
          <a-col :xs="24" :sm="12" :lg="8">
            <div class="overview-item">
              <div class="overview-icon team-icon">
                <ApartmentOutlined />
              </div>
              <div class="overview-content">
                <div class="overview-title">我的团队</div>
                <div class="overview-value">{{ teamStats.myTeams }}</div>
                <div class="overview-desc">参与团队数</div>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :lg="8">
            <div class="overview-item">
              <div class="overview-icon member-icon">
                <UsergroupAddOutlined />
              </div>
              <div class="overview-content">
                <div class="overview-title">团队成员</div>
                <div class="overview-value">{{ teamStats.totalMembers }}</div>
                <div class="overview-desc">总成员数量</div>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :lg="8">
            <div class="overview-item">
              <div class="overview-icon activity-icon">
                <TrophyOutlined />
              </div>
              <div class="overview-content">
                <div class="overview-title">本月业绩</div>
                <div class="overview-value">{{ teamStats.monthlyPerformance }}%</div>
                <div class="overview-desc">目标达成率</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <div class="recent-activity">
      <a-card title="最近活动" class="custom-card">
        <a-timeline>
          <a-timeline-item color="blue">
            <p>添加了新客户 "张三"</p>
            <span class="time-text">2 小时前</span>
          </a-timeline-item>
          <a-timeline-item color="green">
            <p>更新了店铺 "时尚潮流" 的信息</p>
            <span class="time-text">5 小时前</span>
          </a-timeline-item>
          <a-timeline-item color="orange">
            <p>团队成员 "李四" 加入</p>
            <span class="time-text">1 天前</span>
          </a-timeline-item>
          <a-timeline-item>
            <p>系统维护完成</p>
            <span class="time-text">3 天前</span>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user'
import {
  UserOutlined,
  UserAddOutlined,
  ShopOutlined,
  TeamOutlined,
  ApartmentOutlined,
  BarChartOutlined,
  RightOutlined,
  UsergroupAddOutlined,
  TrophyOutlined
} from '@ant-design/icons-vue'

// 工作台页面组件
defineOptions({
  name: 'Dashboard'
})

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const currentTime = ref('')
const teamStats = ref({
  myTeams: 3,
  totalMembers: 15,
  monthlyPerformance: 87
})
let timeInterval = null

// 更新当前时间
const updateTime = () => {
  const now = new Date()
  const options = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
  currentTime.value = now.toLocaleDateString('zh-CN', options)
}

// 获取团队统计数据
const fetchTeamStats = async () => {
  try {
    // 这里可以调用API获取真实数据
    // const stats = await teamService.getMyTeamStats()
    // teamStats.value = stats
    
    // 暂时使用模拟数据
    teamStats.value = {
      myTeams: 3,
      totalMembers: 15,
      monthlyPerformance: 87
    }
  } catch (error) {
    console.error('获取团队统计数据失败:', error)
  }
}

// 导航到指定页面
const navigateTo = (path) => {
  router.push(path)
}

// 生命周期钩子
onMounted(() => {
  updateTime()
  fetchTeamStats()
  timeInterval = setInterval(updateTime, 60000) // 每分钟更新一次
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  color: white;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-text p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 16px;
}

.welcome-time {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-align: right;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quick-actions {
  margin-bottom: 24px;
}

.action-button {
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.3s;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.action-button .anticon {
  font-size: 18px;
}

.recent-activity {
  margin-bottom: 24px;
}

.time-text {
  color: #8c8c8c;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .welcome-time {
    text-align: left;
  }
  
  .welcome-text h2 {
    font-size: 20px;
  }
  
  .action-button {
    height: 50px;
  }
  
  .action-button .anticon {
    font-size: 16px;
  }
}

/* 卡片标题样式 */
:deep(.ant-card-head-title) {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

/* 统计数字样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

/* 时间线样式增强 */
:deep(.ant-timeline-item-content) {
  margin-left: 4px;
}

:deep(.ant-timeline-item-content p) {
  margin-bottom: 4px;
  color: #262626;
  font-weight: 500;
}

/* 欢迎卡片内容样式覆盖 */
.welcome-card :deep(.ant-card-body) {
  padding: 32px;
}

.welcome-card :deep(.ant-card-head) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-card :deep(.ant-card-head-title) {
  color: white;
}

.team-overview-section {
  margin-bottom: 24px;
}

.overview-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.overview-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.team-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.member-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.activity-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-content {
  flex: 1;
}

.overview-title {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
}

.overview-value {
  font-size: 28px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 4px;
}

.overview-desc {
  font-size: 13px;
  color: #8c8c8c;
}

.dashboard-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.dashboard-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  color: white;
}
</style> 