"""
用户团队业务指标路由
专门处理团队业务指标相关的API接口，包括微信、达人、样品等业务模块
"""

import traceback

from fastapi import APIRouter, Body, Depends

from 依赖项.认证 import 获取当前用户
from 数据模型.响应模型 import 统一响应模型
from 数据模型.团队管理模型 import (
    团队业务模块请求,
    团队成员排名请求,
)
from 日志 import 接口日志器, 错误日志器

# 创建路由器，不设置prefix，在main.py中统一设置
用户团队业务指标路由 = APIRouter(tags=["用户团队业务指标"])


async def 检查团队访问权限(
    团队id: int, 用户id: int, 错误消息: str = "无权限访问该团队"
):
    """统一的团队访问权限检查函数"""
    try:
        from 数据.团队数据看板 import 获取团队详情

        团队详情 = await 获取团队详情(团队id, 用户id, True)

        if not 团队详情.get("success"):
            return False, 统一响应模型.失败(状态码=403, 消息=错误消息)

        return True, None

    except Exception as e:
        错误日志器.error(
            f"检查团队访问权限失败: 团队id={团队id}, 用户id={用户id}, 错误={e}"
        )
        return False, 统一响应模型.失败(状态码=500, 消息="权限检查失败")


@用户团队业务指标路由.post(
    "/wechat-metrics",
    response_model=统一响应模型,
    summary="获取团队微信指标数据",
    description="获取团队微信模块的指标数据，包括微信好友统计、活跃度等",
)
async def 获取团队微信指标接口(
    请求数据: 团队业务模块请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队微信指标数据接口

    返回数据包括：
    - 微信账号总数
    - 好友总数和增长趋势
    - 活跃度统计
    - 成员微信运营排名
    """
    try:
        团队id = 请求数据.团队id
        用户id = 当前用户["id"]
        时间范围 = 请求数据.时间范围

        接口日志器.info(
            f"🔍 团队微信指标：开始获取团队 {团队id} 微信指标数据，用户: {用户id}"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队微信指标数据"
        )
        if not 有权限:
            return 权限错误响应

        # 导入团队数据看板服务
        from 服务.团队数据看板服务 import 团队数据看板服务实例

        # 获取微信指标数据
        微信指标数据 = await 团队数据看板服务实例.获取团队微信指标(团队id, 时间范围)

        if "error" in 微信指标数据:
            return 统一响应模型.失败(状态码=404, 消息=微信指标数据["error"])

        接口日志器.info(
            f"✅ 团队微信指标：用户 {用户id} 获取团队 {团队id} 微信指标数据成功"
        )
        return 统一响应模型.成功(数据=微信指标数据, 消息="获取团队微信指标数据成功")

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"团队微信指标接口异常 - 团队id: {请求数据.团队id}, 用户: {当前用户.get('id')}, 错误: {e}\n{错误详情}"
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队微信指标数据失败")


@用户团队业务指标路由.post(
    "/talent-metrics",
    response_model=统一响应模型,
    summary="获取团队达人指标数据",
    description="获取团队达人模块的指标数据，包括达人数量、质量分布等",
)
async def 获取团队达人指标接口(
    请求数据: 团队业务模块请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队达人指标数据接口

    返回数据包括：
    - 达人总数和分布
    - 有联系方式达人数
    - 达人质量分析
    - 成员达人管理排名
    """
    try:
        团队id = 请求数据.团队id
        用户id = 当前用户["id"]
        时间范围 = 请求数据.时间范围

        接口日志器.info(
            f"🔍 团队达人指标：开始获取团队 {团队id} 达人指标数据，用户: {用户id}"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队达人指标数据"
        )
        if not 有权限:
            return 权限错误响应

        # 导入团队数据看板服务
        from 服务.团队数据看板服务 import 团队数据看板服务实例

        # 获取达人指标数据
        达人指标数据 = await 团队数据看板服务实例.获取团队达人指标(团队id, 时间范围)

        if "error" in 达人指标数据:
            return 统一响应模型.失败(状态码=404, 消息=达人指标数据["error"])

        接口日志器.info(
            f"✅ 团队达人指标：用户 {用户id} 获取团队 {团队id} 达人指标数据成功"
        )
        return 统一响应模型.成功(数据=达人指标数据, 消息="获取团队达人指标数据成功")

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"团队达人指标接口异常 - 团队id: {请求数据.团队id}, 用户: {当前用户.get('id')}, 错误: {e}\n{错误详情}"
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队达人指标数据失败")


@用户团队业务指标路由.post(
    "/sample-metrics",
    response_model=统一响应模型,
    summary="获取团队样品指标数据",
    description="获取团队样品模块的指标数据，包括样品申请、发放统计等",
)
async def 获取团队样品指标接口(
    请求数据: 团队业务模块请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队样品指标数据接口

    返回数据包括：
    - 样品申请总数
    - 实际发放数量
    - 发放率和完成率
    - 成员样品管理排名
    """
    try:
        团队id = 请求数据.团队id
        用户id = 当前用户["id"]
        时间范围 = 请求数据.时间范围

        接口日志器.info(
            f"🔍 团队样品指标：开始获取团队 {团队id} 样品指标数据，用户: {用户id}"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队样品指标数据"
        )
        if not 有权限:
            return 权限错误响应

        # 导入团队数据看板服务
        from 服务.团队数据看板服务 import 团队数据看板服务实例

        # 获取样品指标数据
        样品指标数据 = await 团队数据看板服务实例.获取团队样品指标(团队id, 时间范围)

        if "error" in 样品指标数据:
            return 统一响应模型.失败(状态码=404, 消息=样品指标数据["error"])

        接口日志器.info(
            f"✅ 团队样品指标：用户 {用户id} 获取团队 {团队id} 样品指标数据成功"
        )
        return 统一响应模型.成功(数据=样品指标数据, 消息="获取团队样品指标数据成功")

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"团队样品指标接口异常 - 团队id: {请求数据.团队id}, 用户: {当前用户.get('id')}, 错误: {e}\n{错误详情}"
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队样品指标数据失败")


@用户团队业务指标路由.post(
    "/member-ranking",
    response_model=统一响应模型,
    summary="获取团队成员排名数据",
    description="获取团队成员绩效排名数据，支持多种排序方式",
)
async def 获取团队成员排名接口(
    请求数据: 团队成员排名请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队成员排名数据接口

    支持的排序方式：
    - 好友数量排名
    - 达人数量排名
    - 邀约数量排名
    - 综合绩效排名
    """
    try:
        团队id = 请求数据.团队id
        用户id = 当前用户["id"]
        时间范围 = 请求数据.时间范围
        排序方式 = 请求数据.排序方式
        限制数量 = 请求数据.限制数量
        模块类型 = getattr(请求数据, "模块类型", None)

        接口日志器.info(
            f"🔍 团队成员排名：开始获取团队 {团队id} 成员排名数据，用户: {用户id}，排序方式: {排序方式}，模块类型: {模块类型}"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队成员排名数据"
        )
        if not 有权限:
            return 权限错误响应

        # 导入团队数据看板服务
        from 服务.团队数据看板服务 import 团队数据看板服务实例

        # 获取成员排名数据
        排名数据 = await 团队数据看板服务实例.获取团队成员排名(
            团队id=团队id,
            时间范围=时间范围,
            排序方式=排序方式,
            限制数量=限制数量,
            模块类型=模块类型,
        )

        if "error" in 排名数据:
            return 统一响应模型.失败(状态码=404, 消息=排名数据["error"])

        接口日志器.info(
            f"✅ 团队成员排名：用户 {用户id} 获取团队 {团队id} 成员排名数据成功，返回 {len(排名数据.get('成员排名', []))} 条记录"
        )
        return 统一响应模型.成功(数据=排名数据, 消息="获取团队成员排名数据成功")

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"团队成员排名接口异常 - 团队id: {请求数据.团队id}, 用户: {当前用户.get('id')}, 错误: {e}\n{错误详情}"
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队成员排名数据失败")
