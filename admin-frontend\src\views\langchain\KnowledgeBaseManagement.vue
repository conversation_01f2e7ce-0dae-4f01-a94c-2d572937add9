<template>
  <div class="knowledge-base-management">
    <div class="page-header">
      <div class="header-content">
        <h1>LangChain知识库管理</h1>
        <p>管理员视图 - 创建、配置和管理智能体知识库</p>

      </div>
      <div class="header-actions">
        <a-button type="primary" @click="显示创建对话框">
          <template #icon><PlusOutlined /></template>
          创建知识库
        </a-button>
        <a-button @click="刷新列表">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <a-card class="search-card" size="small">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="搜索条件.搜索关键词"
            placeholder="搜索知识库名称"
            @press-enter="搜索知识库"
          >
            <template #prefix><SearchOutlined /></template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="搜索条件.知识库类型"
            placeholder="知识库类型"
            allow-clear
            @change="搜索知识库"
          >
            <a-select-option value="文档知识库">文档知识库</a-select-option>
            <a-select-option value="问答知识库">问答知识库</a-select-option>
            <a-select-option value="结构化知识库">结构化知识库</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-select
            v-model:value="搜索条件.是否公开"
            placeholder="公开状态"
            allow-clear
            @change="搜索知识库"
          >
            <a-select-option :value="true">公开</a-select-option>
            <a-select-option :value="false">私有</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-button type="primary" @click="搜索知识库">搜索</a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 知识库列表 -->
    <a-card class="list-card">
      <a-table
        :columns="表格列定义"
        :data-source="知识库列表"
        :loading="加载中"
        :pagination="分页配置"
        @change="处理表格变化"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'knowledge_info'">
            <div class="knowledge-info">
              <a-avatar :size="32" style="background-color: #52c41a">
                {{ record.知识库名称.charAt(0) }}
              </a-avatar>
              <div class="info-content">
                <div class="name">
                  <a @click="查看详情(record)" class="kb-name-link">
                    {{ record.知识库名称 }}
                  </a>
                </div>
                <div class="description">{{ record.知识库描述 || '暂无描述' }}</div>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'config_info'">
            <div class="config-info">
              <a-tag color="blue">
                {{ record.嵌入模型名称 || '未配置' }}
                <span v-if="record.嵌入模型提供商" class="provider-text">
                  ({{ record.嵌入模型提供商 }})
                </span>
              </a-tag>
              <div class="vector-status">
                <a-tag color="green">
                  PostgreSQL向量存储
                </a-tag>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'stats'">
            <div class="stats-info">
              <div class="stat-item">
                <span class="label">文档数:</span>
                <span class="value">{{ record.文档数量 || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="label">存储类型:</span>
                <span class="value">PostgreSQL</span>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="获取状态颜色(record.状态)">
              {{ 获取状态文本(record.状态) }}
            </a-tag>
          </template>



          <template v-if="column.key === 'actions'">
            <a-space>
              <a-popconfirm
                title="确定要删除这个知识库吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="删除知识库(record)"
              >
                <a-button type="primary" danger size="small">
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建知识库对话框 -->
    <a-modal
      v-model:open="创建对话框可见"
      title="创建LangChain知识库"
      width="600px"
      @ok="确认创建知识库"
      @cancel="取消创建"
      :confirm-loading="创建中"
    >
      <a-form
        ref="创建表单引用"
        :model="创建表单数据"
        :rules="创建表单规则"
        layout="vertical"
      >
        <a-form-item label="知识库名称" name="知识库名称">
          <a-input
            v-model:value="创建表单数据.知识库名称"
            placeholder="请输入知识库名称"
          />
        </a-form-item>

        <a-form-item label="知识库描述" name="知识库描述">
          <a-textarea
            v-model:value="创建表单数据.知识库描述"
            placeholder="请输入知识库描述"
            :rows="3"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="嵌入模型" name="嵌入模型">
              <a-select
                v-model:value="创建表单数据.嵌入模型"
                placeholder="选择嵌入模型"
                :loading="嵌入模型加载中"
                @focus="加载嵌入模型列表"
                @change="处理嵌入模型变更"
                :not-found-content="嵌入模型加载中 ? '加载中...' : '暂无可用模型'"
              >
                <a-select-option
                  v-for="model in 嵌入模型列表"
                  :key="model.id"
                  :value="model.id"
                  :disabled="model.启用状态 !== 1"
                >
                  <div class="model-option">
                    <span class="model-name">{{ model.显示名称 }}</span>
                    <a-tag
                      :color="model.启用状态 === 1 ? 'green' : 'orange'"
                      size="small"
                    >
                      {{ model.启用状态 === 1 ? '可用' : '不可用' }}
                    </a-tag>
                  </div>
                </a-select-option>
              </a-select>
              <div class="help-text">
                <small>选择用于文档向量化的嵌入模型，不同模型的向量维度可能不同</small>
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="向量维度" name="向量维度">
              <a-select
                v-model:value="创建表单数据.向量维度"
                placeholder="选择向量维度"
                style="width: 100%"
              >
                <a-select-option :value="64">64维</a-select-option>
                <a-select-option :value="128">128维</a-select-option>
                <a-select-option :value="256">256维</a-select-option>
                <a-select-option :value="512">512维</a-select-option>
                <a-select-option :value="768">768维</a-select-option>
                <a-select-option :value="1024">1024维（推荐）</a-select-option>
                <a-select-option :value="1536">1536维</a-select-option>
                <a-select-option :value="2048">2048维</a-select-option>
              </a-select>
              <div class="help-text">
                <small>text-embedding-v4支持多种维度，1024维为默认推荐值</small>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="配置信息">
          <a-textarea
            v-model:value="配置信息文本"
            placeholder="JSON格式的配置信息（可选）"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑功能已移至详情页面 -->

    <!-- 向量检索、文档管理、预览功能已移至详情页面 -->

  </div>
</template>

<script setup>
import knowledgeBaseService from '@/services/knowledgeBaseService'
import {
  DeleteOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

// ==================== 响应式数据 ====================

// 路由
const router = useRouter()

const 加载中 = ref(false)
const 创建中 = ref(false)
const 知识库列表 = ref([])
const 创建对话框可见 = ref(false)
const 创建表单引用 = ref()
// 配置验证错误已移至详情页面
const 嵌入模型列表 = ref([])
const 嵌入模型加载中 = ref(false)

// 所有复杂功能已移至详情页面

// 搜索条件
const 搜索条件 = reactive({
  搜索关键词: '',
  知识库类型: null,
  是否公开: null
})



// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 创建表单数据
const 创建表单数据 = reactive({
  知识库名称: '',
  知识库描述: '',
  嵌入模型: '',
  向量维度: 1024,  // 默认使用阿里云text-embedding-v4的维度
})

const 配置信息文本 = ref('{}')

// 编辑表单数据已移至详情页面

// 表单验证规则
const 创建表单规则 = {
  知识库名称: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 50, message: '知识库名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  嵌入模型: [
    { required: true, message: '请选择嵌入模型', trigger: 'change' }
  ],
  向量维度: [
    { required: true, message: '请选择向量维度', trigger: 'change' },
    {
      validator: (rule, value) => {
        const 支持的维度 = [64, 128, 256, 512, 768, 1024, 1536, 2048]
        if (!支持的维度.includes(value)) {
          return Promise.reject('请选择text-embedding-v4支持的向量维度')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

// 编辑表单规则已移至详情页面

// 表格列定义
const 表格列定义 = [
  {
    title: '知识库信息',
    key: 'knowledge_info',
    width: 300,
    fixed: 'left'
  },
  {
    title: '配置信息',
    key: 'config_info',
    width: 200
  },
  {
    title: '统计信息',
    key: 'stats',
    width: 150
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },

  {
    title: '创建时间',
    dataIndex: '创建时间',
    width: 180,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleString() : '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 300,
    fixed: 'right'
  }
]



// ==================== 计算属性 ====================

const 配置信息对象 = computed(() => {
  try {
    return JSON.parse(配置信息文本.value || '{}')
  } catch {
    return {}
  }
})

// ==================== 方法定义 ====================

/**
 * 加载嵌入模型列表
 */
const 加载嵌入模型列表 = async () => {
  if (嵌入模型列表.value.length > 0) {
    return // 已经加载过了
  }

  try {
    嵌入模型加载中.value = true
    console.log('🤖 加载嵌入模型列表')

    const response = await knowledgeBaseService.getVectorModels()

    if (response.success) {
      嵌入模型列表.value = response.data.模型列表 || []
      console.log('✅ 嵌入模型列表加载成功:', 嵌入模型列表.value.length)

      // 如果创建表单的嵌入模型为空，设置默认值
      if (!创建表单数据.嵌入模型 && 嵌入模型列表.value.length > 0) {
        const 默认模型 = 嵌入模型列表.value.find(model => model.启用状态 === 1)
        if (默认模型) {
          创建表单数据.嵌入模型 = 默认模型.id
        }
      }
    } else {
      console.error('❌ 加载嵌入模型列表失败:', response.error)
      message.error('加载嵌入模型列表失败')
    }
  } catch (error) {
    console.error('❌ 加载嵌入模型列表异常:', error)
    message.error('加载嵌入模型列表失败')
  } finally {
    嵌入模型加载中.value = false
  }
}

/**
 * 处理嵌入模型变更
 */
const 处理嵌入模型变更 = (modelId) => {
  const 选中模型 = 嵌入模型列表.value.find(m => m.id === modelId)
  if (选中模型) {
    // 设置默认维度为1024（text-embedding-v4推荐值）
    创建表单数据.向量维度 = 1024
    console.log(`🔧 模型变更: ${选中模型.显示名称}, 向量维度: ${创建表单数据.向量维度}`)
  }
}

/**
 * 获取知识库列表
 */
const 获取知识库列表 = async () => {
  try {
    加载中.value = true

    const 查询参数 = {
      页码: 分页配置.current,
      每页数量: 分页配置.pageSize,
      搜索关键词: 搜索条件.搜索关键词,
      知识库类型: 搜索条件.知识库类型,
      是否公开: 搜索条件.是否公开
    }

    console.log('🔍 获取知识库列表，参数:', 查询参数)

    const 响应 = await knowledgeBaseService.getKnowledgeBaseList(查询参数)

    console.log('📡 知识库列表响应:', 响应)

    if (响应.success) {
      知识库列表.value = 响应.data.知识库列表 || []
      分页配置.total = 响应.data.总数量 || 0

      console.log('✅ 知识库列表加载成功:', 知识库列表.value.length, '条记录')
    } else {
      message.error(响应.error || '获取知识库列表失败')
      知识库列表.value = []
      分页配置.total = 0
    }
  } catch (error) {
    console.error('❌ 获取知识库列表异常:', error)
    message.error('获取知识库列表失败')
    知识库列表.value = []
    分页配置.total = 0
  } finally {
    加载中.value = false
  }
}

/**
 * 搜索知识库
 */
const 搜索知识库 = () => {
  分页配置.current = 1
  获取知识库列表()
}

/**
 * 刷新列表
 */
const 刷新列表 = () => {
  获取知识库列表()
}

/**
 * 处理表格变化
 */
const 处理表格变化 = (pagination) => {
  分页配置.current = pagination.current
  分页配置.pageSize = pagination.pageSize
  获取知识库列表()
}

/**
 * 显示创建对话框
 */
const 显示创建对话框 = async () => {
  创建对话框可见.value = true

  // 加载嵌入模型列表
  await 加载嵌入模型列表()

  // 重置表单
  Object.assign(创建表单数据, {
    知识库名称: '',
    知识库描述: '',
    嵌入模型: 嵌入模型列表.value.length > 0 ? 嵌入模型列表.value.find(m => m.状态 === 'active')?.id || 嵌入模型列表.value[0]?.id : '',
    向量维度: 1024  // 默认使用阿里云text-embedding-v4的维度
  })
  配置信息文本.value = '{}'
}

/**
 * 取消创建
 */
const 取消创建 = () => {
  创建对话框可见.value = false
}

/**
 * 确认创建知识库
 */
const 确认创建知识库 = async () => {
  try {
    // 表单验证
    await 创建表单引用.value.validate()

    创建中.value = true

    const 知识库数据 = {
      知识库名称: 创建表单数据.知识库名称,
      知识库描述: 创建表单数据.知识库描述,
      嵌入模型id: 创建表单数据.嵌入模型,  // 修正字段名匹配后端
      向量维度: 创建表单数据.向量维度,
      是否公开: false  // 默认不公开
    }

    console.log('🚀 创建知识库，数据:', 知识库数据)

    const 响应 = await knowledgeBaseService.createKnowledgeBase(知识库数据)

    console.log('📡 创建知识库响应:', 响应)

    if (响应.success) {
      message.success('知识库创建成功')
      创建对话框可见.value = false
      获取知识库列表()
    } else {
      message.error(响应.error || '创建知识库失败')
    }
  } catch (error) {
    console.error('❌ 创建知识库异常:', error)
    message.error('创建知识库失败')
  } finally {
    创建中.value = false
  }
}

/**
 * 获取状态颜色
 */
const 获取状态颜色 = (状态) => {
  const 状态映射 = {
    'active': 'green',
    'inactive': 'red',
    'building': 'orange',
    'error': 'red'
  }
  return 状态映射[状态] || 'default'
}



/**
 * 获取状态文本
 */
const 获取状态文本 = (状态) => {
  const 状态映射 = {
    'active': '正常',
    'inactive': '停用',
    'building': '构建中',
    'error': '错误',
    'deleted': '已删除'
  }
  return 状态映射[状态] || '未知'
}

/**
 * 查看详情
 */
const 查看详情 = (record) => {
  if (!record.id) {
    message.error('知识id不存在，无法查看详情')
    return
  }
  router.push(`/langchain/knowledge-base/${record.id}`)
}

// ==================== 核心方法 ====================

/**
 * 删除知识库
 */
const 删除知识库 = async (record) => {
  try {
    console.log('删除知识库:', record)

    const 响应 = await knowledgeBaseService.deleteKnowledgeBase(record.id)

    if (响应.success) {
      message.success('知识库删除成功')
      获取知识库列表()
    } else {
      message.error(响应.error || '删除知识库失败')
    }
  } catch (error) {
    console.error('❌ 删除知识库异常:', error)
    message.error('删除知识库失败')
  }
}



// ==================== 生命周期 ====================

onMounted(() => {
  console.log('🎯 知识库管理页面已挂载')
  获取知识库列表()
})
</script>

<style scoped>
.knowledge-base-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}



.header-actions {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 16px;
}

.list-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.knowledge-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-content .name {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.kb-name-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
}

.kb-name-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.info-content .description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.config-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-info .dimension {
  font-size: 12px;
  color: #666;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.stat-item .label {
  color: #666;
}

.stat-item .value {
  font-weight: 500;
  color: #1a1a1a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-base-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    justify-content: flex-start;
  }
}

/* 编辑功能样式 */
.json-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.help-text {
  margin-top: 4px;
  color: #666;
}

.help-text small {
  font-size: 12px;
  line-height: 1.4;
}

.error-text {
  margin-top: 4px;
  color: #ff4d4f;
}

.error-text small {
  font-size: 12px;
}

/* 状态选择器样式 */
.ant-select-selection-item {
  display: flex;
  align-items: center;
}

/* 配置信息展示样式 */
.config-display {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-descriptions-title {
  font-weight: 500;
  margin-bottom: 12px;
}

/* 危险操作样式 */
.danger-item {
  color: #ff4d4f !important;
}

.danger-item:hover {
  background-color: #fff2f0 !important;
}

/* 嵌入模型选择器样式 */
.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 配置信息显示样式 */
.config-info .provider-text {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.model-name {
  flex: 1;
  margin-right: 8px;
}

.help-text {
  margin-top: 4px;
  color: #666;
}

.help-text small {
  font-size: 12px;
  line-height: 1.4;
}

/* 向量检索样式 */
.vector-search-container {
  max-height: 600px;
  overflow-y: auto;
}

.search-results {
  margin-top: 16px;
}

.result-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.document-name {
  color: #666;
  font-size: 12px;
  margin-left: auto;
}

.result-content {
  color: #333;
  line-height: 1.6;
  font-size: 14px;
  background: white;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.no-results {
  text-align: center;
  padding: 40px 0;
}
</style>