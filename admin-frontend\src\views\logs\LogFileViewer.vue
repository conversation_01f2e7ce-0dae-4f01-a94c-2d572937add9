<template>
  <div class="log-file-viewer">
    <!-- 页面头部 -->
    <div class="viewer-header">
      <h1>日志文件查看器</h1>
      <div class="header-actions">
        <a-space>
          <a-button @click="fetchAvailableLogFiles" :loading="loadingLogFiles" type="primary">
            <template #icon><ReloadOutlined /></template>
            刷新列表
          </a-button>
          <a-button @click="clearLogs" :disabled="!logContent">
            <template #icon><ClearOutlined /></template>
            清空显示
          </a-button>
          <a-switch v-model:checked="autoRefresh" checked-children="自动刷新" un-checked-children="手动刷新" />
        </a-space>
      </div>
    </div>

    <a-row :gutter="[16, 16]">
      <!-- 左侧文件列表 -->
      <a-col :xs="24" :md="8">
        <a-card title="日志文件" class="file-list-card">
          <template #extra>
            <a-badge :count="availableLogFiles.length" :number-style="{ backgroundColor: '#52c41a' }" />
          </template>
          
          <a-spin :spinning="loadingLogFiles">
            <div class="file-list">
              <div 
                v-for="file in availableLogFiles" 
                :key="file.name"
                :class="['file-item', { active: selectedLogFile === file.name }]"
                @click="selectFile(file)"
              >
                <div class="file-info">
                  <div class="file-name">
                    <FileTextOutlined />
                    {{ file.name }}
                  </div>
                  <div class="file-meta">
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                    <span class="file-time">{{ formatTime(file.lastModified) }}</span>
                  </div>
                </div>
                <div class="file-actions">
                  <a-tooltip title="下载文件">
                    <a-button 
                      type="text" 
                      size="small" 
                      @click.stop="downloadLogFile(file.name)"
                      :loading="downloading === file.name"
                    >
                      <template #icon><DownloadOutlined /></template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip title="删除文件">
                    <a-button 
                      type="text" 
                      size="small" 
                      danger
                      @click.stop="deleteLogFile(file.name)"
                    >
                      <template #icon><DeleteOutlined /></template>
                    </a-button>
                  </a-tooltip>
                </div>
              </div>
              
              <a-empty v-if="!loadingLogFiles && availableLogFiles.length === 0" 
                description="暂无日志文件" 
                :image="Empty.PRESENTED_IMAGE_SIMPLE" 
              />
            </div>
          </a-spin>
        </a-card>
      </a-col>
      
      <!-- 右侧内容显示 -->
      <a-col :xs="24" :md="16">
        <a-card class="content-card">
          <template #title>
            <div class="content-title">
              <span v-if="selectedLogFile">
                <FileTextOutlined /> {{ selectedLogFile }}
              </span>
              <span v-else>请选择日志文件</span>
            </div>
          </template>
          
          <template #extra v-if="selectedLogFile">
            <a-space>
              <a-tooltip title="实时模式">
                <a-switch 
                  v-model:checked="realTimeMode" 
                  checked-children="实时" 
                  un-checked-children="静态"
                  @change="toggleRealTimeMode"
                />
              </a-tooltip>
              <a-tooltip title="自动滚动">
                <a-switch 
                  v-model:checked="autoScroll" 
                  checked-children="滚动" 
                  un-checked-children="固定"
                />
              </a-tooltip>
            </a-space>
          </template>
          
          <!-- 搜索和过滤工具栏 -->
          <div v-if="selectedLogFile" class="toolbar">
            <a-row :gutter="[12, 12]">
              <a-col :xs="24" :sm="12">
                <a-input-search 
                  v-model:value="searchTerm" 
                  placeholder="搜索日志内容" 
                  enter-button="搜索" 
                  @search="highlightSearchTerm"
                  allow-clear
                >
                  <template #prefix><SearchOutlined /></template>
                </a-input-search>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-select 
                  v-model:value="logLevelFilter" 
                  placeholder="日志级别" 
                  style="width: 100%"
                  allow-clear
                  @change="filterByLevel"
                >
                  <a-select-option value="INFO">INFO</a-select-option>
                  <a-select-option value="WARN">WARN</a-select-option>
                  <a-select-option value="ERROR">ERROR</a-select-option>
                  <a-select-option value="DEBUG">DEBUG</a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-range-picker 
                  v-model:value="timeRange" 
                  style="width: 100%"
                  show-time
                  format="HH:mm:ss"
                  @change="filterByTime"
                  size="small"
                />
              </a-col>
            </a-row>
          </div>
          
          <!-- 日志内容显示 -->
          <div class="log-content-container">
            <a-spin :spinning="loadingLogContent">
              <div v-if="selectedLogFile" class="log-content">
                <!-- 统计信息 -->
                <div class="log-stats">
                  <a-space>
                    <a-tag color="blue">总行数: {{ totalLogLines }}</a-tag>
                    <a-tag color="green">显示行数: {{ displayLines }}</a-tag>
                    <a-tag v-if="searchTerm" color="orange">匹配: {{ matchedLines }}</a-tag>
                  </a-space>
                </div>
                
                <!-- 日志显示区域 -->
                <div 
                  ref="logContentRef" 
                  class="log-display" 
                  :class="{ 'real-time': realTimeMode }"
                >
                  <div 
                    v-for="(line, index) in displayedLogLines" 
                    :key="index"
                    :class="['log-line', getLogLevelClass(line)]"
                    @click="selectLine(index)"
                  >
                    <span class="line-number">{{ index + 1 }}</span>
                    <span class="line-content" v-html="highlightSearchTermForDisplay(line)"></span>
                  </div>
                  
                  <a-empty v-if="!loadingLogContent && displayedLogLines.length === 0" 
                    description="暂无日志内容" 
                    :image="Empty.PRESENTED_IMAGE_SIMPLE" 
                  />
                </div>
                
                <!-- 分页控制 -->
                <div v-if="!realTimeMode && totalLogLines > logLinesPerPage" class="pagination-container">
                  <a-pagination 
                    v-model:current="currentLogPage"
                    v-model:page-size="logLinesPerPage"
                    :total="totalLogLines"
                    :show-size-changer="true"
                    :show-quick-jumper="true"
                    :show-total="(total, range) => `第 ${range[0]}-${range[1]} 行，共 ${total} 行`"
                    @change="handleLogPageChange"
                  />
                </div>
              </div>
              
              <a-empty v-else 
                description="请从左侧选择要查看的日志文件" 
                :image="Empty.PRESENTED_IMAGE_SIMPLE" 
              />
            </a-spin>
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 日志行详情弹窗 -->
    <a-modal 
      v-model:open="lineDetailVisible" 
      title="日志行详情" 
      width="800px"
      :footer="null"
    >
      <div v-if="selectedLine" class="line-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="行号">{{ selectedLineIndex + 1 }}</a-descriptions-item>
          <a-descriptions-item label="时间戳">{{ extractTimestamp(selectedLine) }}</a-descriptions-item>
          <a-descriptions-item label="日志级别">{{ extractLogLevel(selectedLine) }}</a-descriptions-item>
          <a-descriptions-item label="模块">{{ extractModule(selectedLine) }}</a-descriptions-item>
          <a-descriptions-item label="完整内容">
            <pre class="line-content-detail">{{ selectedLine }}</pre>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue'
import {
  ClearOutlined,
  DeleteOutlined,
  DownloadOutlined,
  FileTextOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import {Empty, message, Modal} from 'ant-design-vue'
import { useSuperAdminRequest } from '@/composables/useApiRequest'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

// 响应式数据
const availableLogFiles = ref([])
const selectedLogFile = ref('')
const logContent = ref('')
const originalLogContent = ref('')
const searchTerm = ref('')
const logLevelFilter = ref('')
const timeRange = ref([])
const loadingLogFiles = ref(false)
const loadingLogContent = ref(false)
const downloading = ref('')
const autoRefresh = ref(false)
const realTimeMode = ref(false)
const autoScroll = ref(true)

// 分页相关
const currentLogPage = ref(1)
const logLinesPerPage = ref(100)

// 日志行详情
const lineDetailVisible = ref(false)
const selectedLine = ref('')
const selectedLineIndex = ref(0)

// 定时器
let refreshTimer = null
let realTimeTimer = null

// DOM 引用
const logContentRef = ref(null)

// API 调用
const { 执行API请求 } = useSuperAdminRequest()

// 计算属性
const logLines = computed(() => {
  return logContent.value ? logContent.value.split('\n').filter(line => line.trim()) : []
})

const totalLogLines = computed(() => logLines.value.length)

const displayedLogLines = computed(() => {
  if (realTimeMode.value) {
    return logLines.value.slice(-50) // 实时模式只显示最后50行
  }
  
  const start = (currentLogPage.value - 1) * logLinesPerPage.value
  const end = start + logLinesPerPage.value
  return logLines.value.slice(start, end)
})

const displayLines = computed(() => displayedLogLines.value.length)

const matchedLines = computed(() => {
  if (!searchTerm.value) return 0
  return logLines.value.filter(line => 
    line.toLowerCase().includes(searchTerm.value.toLowerCase())
  ).length
})

const highlightedLogContent = computed(() => {
  if (!searchTerm.value || !logContent.value) {
    return logContent.value
  }
  
  const regex = new RegExp(`(${searchTerm.value})`, 'gi')
  return logContent.value.replace(regex, '<mark>$1</mark>')
})

// 获取日志文件列表
const fetchAvailableLogFiles = async () => {
  loadingLogFiles.value = true
  try {
    const response = await fetchData('/log-files/list', 'POST', {})
    
    if (response.status === 100) {
      // 模拟数据
      availableLogFiles.value = [
        { 
          name: 'app.log', 
          size: 2621440, // 2.5MB in bytes
          lastModified: '2024-01-15T10:30:00Z',
          type: 'application'
        },
        { 
          name: 'error.log', 
          size: 1258291, // 1.2MB in bytes
          lastModified: '2024-01-15T09:45:00Z',
          type: 'error'
        },
        { 
          name: 'access.log', 
          size: 6082560, // 5.8MB in bytes
          lastModified: '2024-01-15T11:20:00Z',
          type: 'access'
        },
        { 
          name: 'debug.log', 
          size: 819200, // 800KB in bytes
          lastModified: '2024-01-15T08:15:00Z',
          type: 'debug'
        }
      ]
      message.success('日志文件列表获取成功')
    } else {
      message.error(response.message || '获取日志文件列表失败')
    }
  } catch (error) {
    console.error('获取日志文件列表失败:', error)
    message.error('获取日志文件列表失败')
    // 使用模拟数据作为后备
    availableLogFiles.value = [
      { name: 'app.log', size: 2621440, lastModified: '2024-01-15T10:30:00Z', type: 'application' },
      { name: 'error.log', size: 1258291, lastModified: '2024-01-15T09:45:00Z', type: 'error' }
    ]
  } finally {
    loadingLogFiles.value = false
  }
}

// 获取日志内容
const fetchLogContent = async (filename) => {
  loadingLogContent.value = true
  try {
    const response = await fetchData('/log-files/content', 'POST', { filename })
    
    if (response.status === 100) {
      // 模拟日志内容
      logContent.value = generateMockLogContent(filename)
      originalLogContent.value = logContent.value
      currentLogPage.value = 1
      message.success('日志内容加载成功')
    } else {
      message.error(response.message || '获取日志内容失败')
    }
  } catch (error) {
    console.error('获取日志内容失败:', error)
    message.error('获取日志内容失败')
    // 使用模拟数据作为后备
    logContent.value = generateMockLogContent(filename)
    originalLogContent.value = logContent.value
  } finally {
    loadingLogContent.value = false
    
    // 自动滚动到底部
    if (autoScroll.value) {
      nextTick(() => {
        scrollToBottom()
      })
    }
  }
}

// 生成模拟日志内容
const generateMockLogContent = (filename) => {
  const logTypes = ['INFO', 'WARN', 'ERROR', 'DEBUG']
  const modules = ['UserService', 'AuthController', 'DatabaseManager', 'EmailService', 'CacheManager', 'FileUploader']
  const messages = [
    '用户登录成功，用户id: 12345',
    '数据库连接建立成功',
    '邮件发送失败，收件人: <EMAIL>',
    '权限验证通过，角色: admin',
    '缓存更新完成，键: user_session_12345',
    '接口调用超时，URL: /api/users/profile',
    '文件上传成功，文件名: document.pdf',
    '数据库查询执行，耗时: 125ms',
    '用户注销，会话ID: abc123',
    '系统启动完成，端口: 8080'
  ]
  
  let content = ''
  const lineCount = filename.includes('error') ? 30 : 100
  
  for (let i = 0; i < lineCount; i++) {
    const timestamp = dayjs().subtract(i * 2, 'minute').format('YYYY-MM-DD HH:mm:ss.SSS')
    const level = logTypes[Math.floor(Math.random() * logTypes.length)]
    const module = modules[Math.floor(Math.random() * modules.length)]
    const message = messages[Math.floor(Math.random() * messages.length)]
    const threadId = Math.floor(Math.random() * 10) + 1
    
    content += `${timestamp} [${level}] [Thread-${threadId}] ${module}: ${message}\n`
  }
  
  return content
}

// 文件选择
const selectFile = (file) => {
  selectedLogFile.value = file.name
  fetchLogContent(file.name)
}



// 搜索日志内容
const searchLogContent = (keyword = searchTerm.value) => {
  if (!keyword || !originalLogContent.value) {
    logContent.value = originalLogContent.value
    return
  }
  
  const lines = originalLogContent.value.split('\n')
  const filteredLines = lines.filter(line => 
    line.toLowerCase().includes(keyword.toLowerCase())
  )
  
  if (filteredLines.length > 0) {
    logContent.value = filteredLines.join('\n')
    message.success(`找到 ${filteredLines.length} 条匹配记录`)
  } else {
    message.warning('未找到匹配的日志记录')
    logContent.value = originalLogContent.value
  }
  
  currentLogPage.value = 1
}

// 高亮搜索词（用于模板显示）
const highlightSearchTermForDisplay = (line) => {
  if (!searchTerm.value) return line
  
  const regex = new RegExp(`(${searchTerm.value})`, 'gi')
  return line.replace(regex, '<mark style="background-color: #fff3cd; padding: 1px 2px;">$1</mark>')
}

// 高亮搜索词
const highlightSearchTerm = () => {
  searchLogContent()
}

// 按日志级别过滤
const filterByLevel = (level) => {
  if (!level || !originalLogContent.value) {
    logContent.value = originalLogContent.value
    return
  }
  
  const lines = originalLogContent.value.split('\n')
  const filteredLines = lines.filter(line => 
    line.includes(`[${level}]`)
  )
  
  logContent.value = filteredLines.join('\n')
  currentLogPage.value = 1
  message.success(`已过滤 ${level} 级别日志，共 ${filteredLines.length} 条`)
}

// 按时间范围过滤
const filterByTime = (dates) => {
  if (!dates || dates.length !== 2 || !originalLogContent.value) {
    logContent.value = originalLogContent.value
    return
  }
  
  const [startTime, endTime] = dates
  const lines = originalLogContent.value.split('\n')
  const filteredLines = lines.filter(line => {
    const timestamp = extractTimestamp(line)
    if (!timestamp) return false
    
    const logTime = dayjs(timestamp)
    return logTime.isAfter(startTime) && logTime.isBefore(endTime)
  })
  
  logContent.value = filteredLines.join('\n')
  currentLogPage.value = 1
  message.success(`已过滤时间范围内日志，共 ${filteredLines.length} 条`)
}

// 清空日志显示
const clearLogs = () => {
  logContent.value = ''
  originalLogContent.value = ''
  selectedLogFile.value = ''
  searchTerm.value = ''
  logLevelFilter.value = ''
  timeRange.value = []
  currentLogPage.value = 1
  message.success('已清空日志显示')
}

// 切换实时模式
const toggleRealTimeMode = (enabled) => {
  if (enabled) {
    startRealTimeMode()
  } else {
    stopRealTimeMode()
  }
}

// 开始实时模式
const startRealTimeMode = () => {
  if (realTimeTimer) return
  
  realTimeTimer = setInterval(() => {
    if (selectedLogFile.value) {
      // 模拟新日志追加
      const newLine = generateNewLogLine()
      logContent.value += newLine + '\n'
      originalLogContent.value = logContent.value
      
      if (autoScroll.value) {
        nextTick(() => {
          scrollToBottom()
        })
      }
    }
  }, 3000) // 每3秒添加新日志
  
  message.success('已启用实时模式')
}

// 停止实时模式
const stopRealTimeMode = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer)
    realTimeTimer = null
    message.success('已停止实时模式')
  }
}

// 生成新的日志行
const generateNewLogLine = () => {
  const logTypes = ['INFO', 'WARN', 'ERROR', 'DEBUG']
  const modules = ['UserService', 'AuthController', 'DatabaseManager', 'EmailService']
  const messages = [
    '新用户注册成功',
    '接口调用完成',
    '缓存命中',
    '数据同步完成',
    '文件处理完成'
  ]
  
  const timestamp = dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')
  const level = logTypes[Math.floor(Math.random() * logTypes.length)]
  const module = modules[Math.floor(Math.random() * modules.length)]
  const message = messages[Math.floor(Math.random() * messages.length)]
  const threadId = Math.floor(Math.random() * 10) + 1
  
  return `${timestamp} [${level}] [Thread-${threadId}] ${module}: ${message}`
}

// 滚动到底部
const scrollToBottom = () => {
  if (logContentRef.value) {
    logContentRef.value.scrollTop = logContentRef.value.scrollHeight
  }
}

// 选择日志行
const selectLine = (index) => {
  selectedLineIndex.value = index
  selectedLine.value = displayedLogLines.value[index]
  lineDetailVisible.value = true
}

// 获取日志级别样式类
const getLogLevelClass = (line) => {
  if (line.includes('[ERROR]')) return 'log-error'
  if (line.includes('[WARN]')) return 'log-warn'
  if (line.includes('[INFO]')) return 'log-info'
  if (line.includes('[DEBUG]')) return 'log-debug'
  return ''
}

// 提取时间戳
const extractTimestamp = (line) => {
  const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})/)
  return match ? match[1] : ''
}

// 提取日志级别
const extractLogLevel = (line) => {
  const match = line.match(/\[(INFO|WARN|ERROR|DEBUG)\]/)
  return match ? match[1] : ''
}

// 提取模块名
const extractModule = (line) => {
  const match = line.match(/\] ([A-Za-z]+):/)
  return match ? match[1] : ''
}

// 下载日志文件
const downloadLogFile = async (filename = selectedLogFile.value) => {
  if (!filename) return
  
  downloading.value = filename
  try {
    // 模拟下载延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 获取要下载的内容
    const content = filename === selectedLogFile.value ? logContent.value : generateMockLogContent(filename)
    
    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    message.success(`文件 ${filename} 下载成功`)
  } catch (error) {
    console.error('下载失败:', error)
    message.error('下载失败')
  } finally {
    downloading.value = ''
  }
}

// 删除日志文件
const deleteLogFile = (filename) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除日志文件 "${filename}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟删除操作
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 从列表中移除
        availableLogFiles.value = availableLogFiles.value.filter(file => file.name !== filename)
        
        // 如果删除的是当前选中的文件，清空显示
        if (selectedLogFile.value === filename) {
          clearLogs()
        }
        
        message.success(`文件 ${filename} 删除成功`)
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 分页处理
const handleLogPageChange = (page, pageSize) => {
  currentLogPage.value = page
  logLinesPerPage.value = pageSize
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (timeStr) => {
  return dayjs(timeStr).format('MM-DD HH:mm')
}

// 设置自动刷新
const setupAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  
  if (autoRefresh.value) {
    refreshTimer = setInterval(() => {
      if (selectedLogFile.value && !realTimeMode.value) {
        fetchLogContent(selectedLogFile.value)
      }
    }, 30000) // 每30秒刷新一次
  }
}

// 监听自动刷新变化
watch(autoRefresh, setupAutoRefresh)

// 组件挂载
onMounted(() => {
  fetchAvailableLogFiles()
})

// 组件卸载
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  if (realTimeTimer) {
    clearInterval(realTimeTimer)
  }
})
</script>

<style scoped>
.log-file-viewer {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.viewer-header h1 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.header-actions .ant-space {
  gap: 12px !important;
}

/* 文件列表卡片 */
.file-list-card {
  height: calc(100vh - 200px);
  overflow: hidden;
}

.file-list-card .ant-card-body {
  padding: 16px;
  height: calc(100% - 57px);
  overflow-y: auto;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.file-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.file-item.active {
  border-color: #1890ff;
  background: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.file-name .anticon {
  color: #1890ff;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #8c8c8c;
}

.file-size {
  font-weight: 500;
}

.file-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-item:hover .file-actions {
  opacity: 1;
}

/* 内容卡片 */
.content-card {
  height: calc(100vh - 200px);
  overflow: hidden;
}

.content-card .ant-card-body {
  padding: 16px;
  height: calc(100% - 57px);
  display: flex;
  flex-direction: column;
}

.content-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.content-title .anticon {
  color: #1890ff;
}

/* 工具栏 */
.toolbar {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 日志内容容器 */
.log-content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.log-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 统计信息 */
.log-stats {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f0f0f0;
  border-radius: 4px;
}

/* 日志显示区域 */
.log-display {
  flex: 1;
  background: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  padding: 16px;
  border-radius: 6px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
}

.log-display.real-time {
  border-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.1);
}

.log-line {
  display: flex;
  margin-bottom: 2px;
  padding: 2px 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.log-line:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.line-number {
  width: 60px;
  color: #858585;
  text-align: right;
  margin-right: 12px;
  user-select: none;
  flex-shrink: 0;
}

.line-content {
  flex: 1;
  word-break: break-all;
}

/* 日志级别样式 */
.log-error .line-content {
  color: #ff6b6b;
}

.log-warn .line-content {
  color: #ffa726;
}

.log-info .line-content {
  color: #42a5f5;
}

.log-debug .line-content {
  color: #66bb6a;
}

/* 分页容器 */
.pagination-container {
  margin-top: 16px;
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 日志行详情弹窗 */
.line-detail .ant-descriptions {
  margin-top: 16px;
}

.line-content-detail {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

/* 搜索高亮 */
:deep(mark) {
  background-color: #fff3cd !important;
  color: #856404 !important;
  padding: 1px 2px !important;
  border-radius: 2px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-file-viewer {
    padding: 12px;
  }
  
  .viewer-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .viewer-header h1 {
    text-align: center;
  }
  
  .file-list-card,
  .content-card {
    height: auto;
    min-height: 400px;
  }
  
  .file-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .file-actions {
    opacity: 1;
    justify-content: center;
  }
  
  .log-display {
    font-size: 12px;
    padding: 12px;
  }
  
  .line-number {
    width: 40px;
    margin-right: 8px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-item,
.log-line {
  animation: fadeIn 0.3s ease;
}

/* 自定义滚动条 */
.log-display::-webkit-scrollbar,
.file-list::-webkit-scrollbar {
  width: 8px;
}

.log-display::-webkit-scrollbar-track,
.file-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.log-display::-webkit-scrollbar-thumb,
.file-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.log-display::-webkit-scrollbar-thumb:hover,
.file-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 加载状态 */
.ant-spin-nested-loading {
  height: 100%;
}

.ant-spin-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 空状态 */
.ant-empty {
  margin: auto;
}
</style>