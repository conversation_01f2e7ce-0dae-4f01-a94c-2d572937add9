/**
 * API工具函数
 * 提供统一的API响应处理和状态检查
 */

/**
 * 检查API响应是否成功
 * 支持新旧接口的不同状态码：100（新接口）、0和1（旧接口）
 * @param {Object} response - API响应对象
 * @returns {boolean} 是否成功
 */
export function isApiSuccess(response) {
  if (!response || typeof response.status !== 'number') {
    return false
  }
  
  // 支持多种成功状态码
  return [0, 1, 100].includes(response.status)
}

/**
 * 获取API响应数据
 * 统一处理不同的响应数据格式
 * @param {Object} 响应数据 - API响应对象（优化后的拦截器直接返回response.data）
 * @returns {any} 响应数据
 */
export function getApiData(响应数据) {
  if (!isApiSuccess(响应数据)) {
    return null;
  }

  // 优先返回data字段（规范格式）
  if (响应数据.data !== undefined) {
    return 响应数据.data;
  }

  // 检测到旧格式数据（业务数据放在message中）
  if (响应数据.message && typeof 响应数据.message !== 'string' && 响应数据.data === undefined) {
    if (import.meta.env.DEV) {
      console.warn('⚠️ API响应格式不规范: 业务数据放在message字段中而非data字段', 响应数据);
    }
    return 响应数据.message;
  }

  // 没有找到数据，返回null
  return null;
}

/**
 * 获取API错误消息
 * @param {Object} response - API响应对象
 * @param {string} defaultMessage - 默认错误消息
 * @returns {string} 错误消息
 */
export function getApiErrorMessage(response, defaultMessage = '操作失败') {
  if (!response) {
    return defaultMessage
  }
  
  return response.message || defaultMessage
}

/**
 * 通用API调用包装器
 * 统一处理API调用的成功和失败情况
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 * @param {string} options.successMessage - 成功消息
 * @param {string} options.errorMessage - 错误消息
 * @param {boolean} options.showLoading - 是否显示加载状态
 * @returns {Promise<any>} API响应数据
 */
export async function executeApiCall(apiCall, options = {}) {
  const {
    successMessage = '',
    errorMessage = '操作失败',
    showLoading = false
  } = options
  
  try {
    const response = await apiCall()
    
    if (isApiSuccess(response)) {
      if (successMessage) {
        // 这里可以添加成功提示，如果需要的话
        console.log('API调用成功:', successMessage)
      }
      return getApiData(response)
    } else {
      const errorMsg = getApiErrorMessage(response, errorMessage)
      throw new Error(errorMsg)
    }
  } catch (error) {
    console.error('API调用失败:', error)
    throw error
  }
}

/**
 * 检测API响应格式是否规范
 * @param {Object} response - API响应对象
 * @returns {boolean} 是否符合规范格式
 */
export function isStandardApiFormat(response) {
  if (!response || typeof response !== 'object') {
    return false;
  }
  
  // 检查是否包含必要的字段
  const hasStatus = typeof response.status === 'number';
  const hasMessage = typeof response.message === 'string';
  
  // 检查data字段存在（可以为null，但字段必须存在）
  const hasDataField = 'data' in response;
  
  return hasStatus && hasMessage && hasDataField;
}

/**
 * 检测并警告非规范的API响应格式
 * @param {Object} response - API响应对象
 * @param {string} apiName - API名称，用于日志
 */
export function warnIfNonStandardFormat(response, apiName = 'API') {
  if (!isStandardApiFormat(response) && import.meta.env.DEV) {
    const issues = [];
    
    if (!response || typeof response !== 'object') {
      issues.push('响应不是有效对象');
    } else {
      if (typeof response.status !== 'number') issues.push('缺少status字段或类型不是数字');
      if (typeof response.message !== 'string') issues.push('缺少message字段或类型不是字符串');
      if (!('data' in response)) issues.push('缺少data字段');
      
      // 检测业务数据是否错误地放在message中
      if (response.message && typeof response.message !== 'string' && !response.data) {
        issues.push('业务数据错误地放在message字段中');
      }
    }
    
    if (issues.length > 0) {
      console.warn(`⚠️ ${apiName} 响应格式不规范:`, issues.join(', '), response);
    }
  }
}

/**
 * 处理分页响应数据
 * 标准化分页数据格式
 * @param {Object} response - API响应对象
 * @param {string} listKey - 列表数据的字段名（默认为通用的中文字段名）
 * @param {string} totalKey - 总数字段名
 * @returns {Object} 标准化的分页数据
 */
export function processPaginationResponse(response, listKey = '', totalKey = '总数') {
  // 检测并警告非标准格式
  warnIfNonStandardFormat(response, '分页响应');
  
  if (!isApiSuccess(response)) {
    return {
      list: [],
      total: 0,
      success: false,
      message: getApiErrorMessage(response)
    };
  }
  
  // 获取数据，优先从data字段获取
  const data = getApiData(response);
  
  if (!data) {
    return {
      list: [],
      total: 0,
      success: false,
      message: '数据格式错误'
    };
  }
  
  // 自动检测列表字段名
  let list = [];
  if (listKey && data[listKey]) {
    list = data[listKey];
  } else {
    // 常见的列表字段名
    const possibleListKeys = ['列表', '数据列表', '成员列表', '团队列表', '邀请列表', 'list', 'data'];
    for (const key of possibleListKeys) {
      if (data[key] && Array.isArray(data[key])) {
        list = data[key];
        break;
      }
    }
  }
  
  const total = data[totalKey] || data.total || list.length || 0;
  
  return {
    list: Array.isArray(list) ? list : [],
    total: Number(total) || 0,
    success: true,
    message: '获取数据成功'
  };
}

/**
 * 处理表单提交响应
 * 标准化表单操作的响应处理
 * @param {Object} response - API响应对象
 * @param {string} successMessage - 成功消息
 * @param {string} errorMessage - 错误消息
 * @returns {Object} 处理结果
 */
export function processFormResponse(response, successMessage = '操作成功', errorMessage = '操作失败') {
  // 检测并警告非标准格式
  warnIfNonStandardFormat(response, '表单响应');
  
  if (isApiSuccess(response)) {
    return {
      success: true,
      message: successMessage,
      data: getApiData(response)
    };
  } else {
    return {
      success: false,
      message: getApiErrorMessage(response, errorMessage),
      data: null
    };
  }
}

/**
 * 批量处理API响应
 * 用于处理批量操作的响应
 * @param {Array} responses - API响应数组
 * @returns {Object} 批量处理结果
 */
export function processBatchResponse(responses) {
  if (!Array.isArray(responses)) {
    return { success: false, message: '响应格式错误' };
  }
  
  const results = responses.map(response => {
    // 检测每个响应的格式
    warnIfNonStandardFormat(response, '批量响应项');
    
    return {
      success: isApiSuccess(response),
      data: getApiData(response),
      message: getApiErrorMessage(response)
    };
  });
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  return {
    success: successCount > 0,
    successCount,
    totalCount,
    results,
    message: `成功 ${successCount} 项，失败 ${totalCount - successCount} 项`
  };
}

/**
 * 调试API响应的工具函数
 * 在开发环境下打印详细的响应信息
 * @param {Object} response - API响应对象
 * @param {string} apiName - API名称
 */
export function debugApiResponse(response, apiName = 'API') {
  if (import.meta.env.DEV) {
    console.group(`🔍 ${apiName} 响应调试信息`)
    console.log('状态码:', response?.status)
    console.log('是否成功:', isApiSuccess(response))
    console.log('响应数据:', getApiData(response))
    console.log('完整响应:', response)
    console.groupEnd()
  }
}

/**
 * API响应格式检测工具
 * 该工具用于全局检测API响应格式是否符合规范
 */
export const ApiFormatChecker = {
  /**
   * 已检测到的格式问题
   * @type {Array<{api: string, issues: string[]}>}
   */
  formatIssues: [],
  
  /**
   * 检测API响应格式并记录问题
   * @param {Object} response - API响应对象
   * @param {string} apiName - API名称
   */
  check(response, apiName) {
    // 开发环境下才检测
    if (!import.meta.env.DEV) return;
    
    const issues = [];
    
    if (!response || typeof response !== 'object') {
      issues.push('响应不是有效对象');
    } else {
      if (typeof response.status !== 'number') issues.push('缺少status字段或类型不是数字');
      if (typeof response.message !== 'string') issues.push('缺少message字段或类型不是字符串');
      if (!('data' in response)) issues.push('缺少data字段');
      
      // 检测业务数据是否错误地放在message中
      if (response.message && typeof response.message !== 'string' && !response.data) {
        issues.push('业务数据错误地放在message字段中');
      }
    }
    
    if (issues.length > 0) {
      this.formatIssues.push({
        api: apiName,
        issues,
        timestamp: new Date().toISOString(),
        sample: JSON.stringify(response).substring(0, 200) // 保存部分样本数据
      });
      
      console.warn(`⚠️ ${apiName} 响应格式不规范:`, issues.join(', '), response);
    }
  },
  
  /**
   * 获取所有检测到的格式问题
   * @returns {Array} 格式问题列表
   */
  getIssues() {
    return this.formatIssues;
  },
  
  /**
   * 清除所有记录的格式问题
   */
  clearIssues() {
    this.formatIssues = [];
  },
  
  /**
   * 生成格式问题报告
   * @returns {string} 格式化的报告
   */
  generateReport() {
    if (this.formatIssues.length === 0) {
      return '未检测到API格式问题';
    }
    
    let report = `# API格式问题报告\n`;
    report += `检测到 ${this.formatIssues.length} 个API格式问题\n\n`;
    
    this.formatIssues.forEach((issue, index) => {
      report += `## ${index + 1}. ${issue.api}\n`;
      report += `时间: ${issue.timestamp}\n`;
      report += `问题:\n`;
      issue.issues.forEach(i => report += `- ${i}\n`);
      report += `样本数据: ${issue.sample}\n\n`;
    });
    
    return report;
  }
};

// 全局接口钩子，用于在请求和响应时自动检测格式
export function setupApiFormatMonitoring(axiosInstance) {
  if (import.meta.env.DEV && axiosInstance) {
    axiosInstance.interceptors.response.use(
      (response) => {
        // 检测响应格式
        const url = response.config.url;
        const method = response.config.method?.toUpperCase();
        ApiFormatChecker.check(response.data, `${method} ${url}`);
        return response;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    
    console.log('API格式监控已启用');
  }
} 