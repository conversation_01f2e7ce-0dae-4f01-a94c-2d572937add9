"""
文本转换工具模块

包含各种文本格式转换的工具函数
"""
from typing import Any

def 对象转换为Markdown(数据: Any, 缩进级别: int = 0) -> str:
    """
    将对象转换为Markdown格式的文本
    
    参数:
        数据: 要转换的数据对象
        缩进级别: 当前缩进级别，用于嵌套对象
        
    返回:
        str: Markdown格式的文本
    """
    # 生成对应缩进级别的缩进
    缩进 = '  ' * 缩进级别
    
    # 如果是None，返回特殊表示
    if 数据 is None:
        return f"{缩进}无数据"
    
    # 如果是字符串，直接返回
    if isinstance(数据, str):
        return f"{缩进}{数据}"
    
    # 如果是数字或布尔值，转为字符串返回
    if isinstance(数据, (int, float, bool)):
        return f"{缩进}{str(数据)}"
    
    # 如果是列表
    if isinstance(数据, list):
        if not 数据:  # 空列表
            return f"{缩进}[]"
        
        结果 = ""
        for 项目 in 数据:
            # 如果列表项是简单类型，使用列表符号
            if isinstance(项目, (str, int, float, bool)) or 项目 is None:
                结果 += f"{缩进}- {str(项目)}\n"
            else:
                # 复杂类型，进行递归处理，增加缩进
                结果 += f"{缩进}- \n{对象转换为Markdown(项目, 缩进级别 + 1)}\n"
        
        return 结果.rstrip()
    
    # 如果是字典
    if isinstance(数据, dict):
        if not 数据:  # 空字典
            return f"{缩进}{{}}"
        
        结果 = ""
        for 键, 值 in 数据.items():
            # 添加键名作为标题
            结果 += f"{缩进}### {键}\n"
            
            # 根据值的类型进行递归处理
            if isinstance(值, (str, int, float, bool)) or 值 is None:
                结果 += f"{缩进}{str(值)}\n"
            elif isinstance(值, (dict, list)):
                结果 += f"{对象转换为Markdown(值, 缩进级别 + 1)}\n"
            
        return 结果.rstrip()
    
    # 其他类型，转为字符串返回
    return f"{缩进}{str(数据)}" 