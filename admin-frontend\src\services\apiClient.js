/**
 * 统一的HTTP客户端工厂
 * 提供标准化的HTTP请求客户端，支持不同配置需求
 *
 * 功能特性：
 * 1. 统一的客户端创建逻辑
 * 2. 可配置的拦截器和功能
 * 3. 业务状态码标准化处理
 * 4. 支持主应用和测试环境的不同需求
 */
import axios from 'axios';
import { useUserStore } from '../store';

/**
 * 标准化响应数据格式
 * 将后端的不同状态码统一为前端标准格式
 * @param {Object} 响应数据 - 原始响应数据
 * @returns {Object} 标准化后的响应数据
 */
const standardizeResponse = (响应数据) => {
  if (!响应数据 || typeof 响应数据 !== 'object') {
    return 响应数据;
  }

  // 创建响应数据的副本，避免修改原始数据
  const 标准化响应 = { ...响应数据 };

  // 统一业务状态码：将 1、0、100 都标准化为 100（成功状态）
  if ([1, 0, 100].includes(响应数据.status)) {
    标准化响应.status = 100;
  }

  return 标准化响应;
};

/**
 * 创建HTTP客户端实例工厂
 * @param {Object} options - 配置选项
 * @returns {Object} 配置好的axios实例
 */
const createHttpClient = (options = {}) => {
  const {
    baseURL = import.meta.env.VITE_API_BASE_URL || (import.meta.env.MODE === 'development' ? 'http://localhost:8000' : ''),
    timeout = 30000,
    enableAuth = true,
    enableLogging = true,
    enableStandardization = true,
    clientType = 'main' // main | testing
  } = options;

  const instance = axios.create({
    baseURL,
    timeout,
    headers: {
      'Content-Type': 'application/json',
    }
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加请求元数据
      config.metadata = { startTime: Date.now() };

      // 认证处理
      if (enableAuth) {
        const userStore = useUserStore();
        if (userStore.token) {
          config.headers['Authorization'] = `Bearer ${userStore.token}`;
        }
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 日志记录
      if (enableLogging) {
        const logPrefix = clientType === 'testing' ? '[API测试]' : '🌐';
        console.log(`${logPrefix} API请求: ${config.method?.toUpperCase()} ${config.url}`);
      }

      return config;
    },
    (error) => {
      if (enableLogging) {
        console.error('❌ 请求配置错误:', error);
      }
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // 计算响应时间
      const responseTime = Date.now() - response.config.metadata?.startTime;

      if (enableLogging) {
        const logPrefix = clientType === 'testing' ? '[API测试]' : '✅';
        console.log(`${logPrefix} API响应成功: ${response.config.method?.toUpperCase()} ${response.config.url} - ${responseTime}ms`);
      }

      // 为测试客户端添加额外元数据
      if (clientType === 'testing') {
        response.metadata = {
          responseTime,
          responseSize: JSON.stringify(response.data).length
        };
        return response; // 测试客户端返回完整响应
      }

      // 主应用客户端进行响应标准化
      if (enableStandardization) {
        const 原始响应数据 = response.data;
        const 标准化响应数据 = standardizeResponse(原始响应数据);
        return 标准化响应数据;
      }

      return response.data;
    },
    (error) => {
      if (enableAuth && enableLogging) {
        const userStore = useUserStore();
        console.error(`❌ API响应失败: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error);

        if (error.response) {
          const { status, data } = error.response;

          // 处理不同的HTTP状态码
          switch (status) {
            case 401: // 未授权
              console.warn('🔐 用户认证失败，清除本地认证信息');
              userStore.logout();
              error.shouldRedirectToLogin = true;
              break;
            case 403: // 禁止访问
              error.userFriendlyMessage = '您没有权限执行此操作';
              break;
            case 404:
              error.userFriendlyMessage = '请求的资源未找到';
              break;
            case 422: // 数据验证失败
              error.userFriendlyMessage = data?.message || '提交的数据格式不正确';
              if (data?.detail) {
                error.validationErrors = data.detail;
              }
              break;
            case 429: // 请求过于频繁
              error.userFriendlyMessage = '请求过于频繁，请稍后再试';
              break;
            case 500:
              error.userFriendlyMessage = '服务器内部错误，请稍后重试';
              break;
            case 502:
            case 503:
            case 504:
              error.userFriendlyMessage = '服务暂时不可用，请稍后重试';
              break;
            default:
              error.userFriendlyMessage = data?.message || error.message || '请求失败';
          }
        } else if (error.request) {
          error.userFriendlyMessage = '网络连接失败，请检查网络设置';
        } else {
          error.userFriendlyMessage = '请求配置错误: ' + error.message;
        }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// 创建主应用API客户端
const apiClient = createHttpClient({
  enableAuth: true,
  enableLogging: true,
  enableStandardization: true,
  clientType: 'main'
});

// 创建API测试专用客户端工厂函数
const createTestApiClient = (baseURL) => {
  return createHttpClient({
    baseURL,
    enableAuth: false,
    enableLogging: true,
    enableStandardization: false,
    clientType: 'testing'
  });
};

export default apiClient;

// 导出工厂函数和测试客户端创建函数
export { createHttpClient, createTestApiClient };