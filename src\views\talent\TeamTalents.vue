<template>
  <div class="team-talents">
    <!-- 页面头部：功能说明和开发状态 -->
    <div class="page-header">
      <h2>团队达人管理</h2>
      <p class="page-description">
        统一管理团队内所有成员认领的达人资源，提供团队达人概览、成员达人分布等功能
      </p>
    </div>

    <!-- 团队选择区域：核心功能入口 -->
    <a-card class="team-selector-card" :loading="loading.teams">
        <template #title>
        <team-outlined />
        选择团队
        </template>
        
      <a-row :gutter="[24, 16]">
        <!-- 团队选择下拉框：连接团队管理模块数据 -->
        <a-col :span="7">
          <a-form-item label="选择团队" class="team-select-item">
            <a-select
              v-model:value="selectedTeamId"
              placeholder="请选择要查看的团队"
              size="large"
              :loading="loading.teams"
              :disabled="teams.length === 0"
              show-search
              filter-option
              @change="handleTeamChange"
              @dropdown-visible-change="onTeamDropdownOpen"
            >
              <a-select-option 
                v-for="team in teams" 
                :key="team.id" 
                :value="team.id"
                :title="team.团队描述"
              >
                <div class="team-option">
                  <div class="team-name">{{ team.团队名称 }}</div>
                  <div class="team-info">
                    {{ team.当前成员数 }}/{{ team.最大成员数 }}人 
                    <a-tag 
                      :color="getTeamStatusColor(team.团队状态)" 
                      size="small"
                    >
                      {{ team.团队状态 }}
                    </a-tag>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        
        <!-- 团队列表刷新按钮 -->
        <a-col :span="1">
          <a-button 
            size="large"
            :loading="loading.teams"
            @click="loadUserTeams"
            title="重新加载团队列表"
          >
            <reload-outlined />
          </a-button>
        </a-col>

        <!-- 快速操作按钮：提升用户体验 -->
        <a-col :span="16">
          <a-space>
            <a-button 
              type="primary" 
              :disabled="!selectedTeamId"
              @click="refreshTeamData"
              :loading="loading.refresh"
            >
              <reload-outlined />
              刷新数据
            </a-button>
            
            <a-button 
              :disabled="!selectedTeamId"
              @click="exportTeamTalents"
            >
              <download-outlined />
              导出团队达人
            </a-button>
            
            <a-button 
              :disabled="!selectedTeamId"
              @click="showTeamSettings"
            >
              <setting-outlined />
              团队设置
            </a-button>
            
            <!-- 状态指示器 -->
            <a-tag v-if="teams.length > 0" color="success">
              已加载 {{ teams.length }} 个团队
            </a-tag>
            <a-tag v-else-if="!loading.teams" color="warning">
              未找到团队数据
            </a-tag>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 空状态提示：当用户没有团队时显示 -->
    <div v-if="!loading.teams && teams.length === 0" class="empty-state">
      <a-empty
        image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
        :image-style="{ height: '60px' }"
        description="您还没有加入任何团队"
      >
        <template #description>
          <span style="color: #999;">
            您还没有加入任何团队，请联系管理员邀请您加入团队后再使用此功能
          </span>
        </template>
        <a-button type="primary" @click="loadUserTeams">
          <reload-outlined />
          重新检查
        </a-button>
      </a-empty>
    </div>

    <!-- 平台切换器：统一的平台选择入口 -->
    <PlatformSwitcher
      v-if="teams.length > 0"
      :model-value="currentPlatform"
      :stats="platformStats"
      :show-stats="true"
      :show-description="false"
      @platform-change="handlePlatformChange"
      class="platform-switcher-section"
    />

    <!-- 团队达人统计概览：数据驱动的管理视图 -->
    <a-card
      v-if="selectedTeamId"
      class="stats-card"
      :loading="loading.stats"
      title="团队达人概览"
    >
      <template #extra>
        <a-button 
          type="link" 
          size="small"
          @click="showStatsDetail"
        >
          查看详细分析
        </a-button>
      </template>

      <a-row :gutter="[24, 16]">
        <!-- 核心指标：团队达人管理的关键数据 -->
        <a-col :span="6">
          <a-statistic
            title="团队达人总数"
            :value="teamStats.总达人数"
            suffix="个"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <user-outlined />
            </template>
          </a-statistic>
          </a-col>
        
          <a-col :span="6">
          <a-statistic
            title="活跃成员数"
            :value="teamStats.活跃成员数"
            suffix="人"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <team-outlined />
            </template>
          </a-statistic>
          </a-col>
        
          <a-col :span="6">
          <a-statistic
            title="本月新增"
            :value="teamStats.月新增数"
            suffix="个"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <plus-outlined />
            </template>
          </a-statistic>
          </a-col>
        
          <a-col :span="6">
          <a-statistic
            title="平均每人"
            :value="teamStats.平均每人达人数"
            suffix="个"
            :precision="1"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <bar-chart-outlined />
            </template>
          </a-statistic>
        </a-col>
      </a-row>

      <!-- 成员达人分布快览：管理层级视图 -->
      <a-divider>成员达人分布</a-divider>
      <div class="member-distribution">
        <a-row :gutter="16">
          <a-col
            v-for="member in memberStats.slice(0, 6)"
            :key="member.用户id"
            :span="4"
          >
            <div class="member-stat-item">
              <a-avatar
                :src="member.头像"
                :alt="member.用户名"
                size="small"
              >
                {{ member.用户名?.charAt(0) }}
              </a-avatar>
              <div class="member-name">{{ member.用户名 }}</div>
              <div class="member-count">{{ member.达人数量 }}个</div>
            </div>
          </a-col>
        </a-row>
        
        <a-button 
          v-if="memberStats.length > 6"
          type="link" 
          size="small"
          @click="showAllMemberStats"
        >
          查看全部 {{ memberStats.length }} 名成员
        </a-button>
      </div>
      </a-card>

    <!-- 团队达人列表：核心功能区域 -->
    <a-card 
      v-if="selectedTeamId"
      class="talents-list-card"
      :loading="loading.talents"
    >
      <template #title>
        <span>团队达人列表</span>
        <a-tag v-if="talentList.length > 0" color="blue" style="margin-left: 8px;">
          共 {{ pagination.total }} 个达人
        </a-tag>
      </template>

      <template #extra>
        <!-- 搜索和筛选：提升数据查找效率 -->
        <a-space>
          <!-- 成员筛选：按认领人查看 -->
          <a-select
            v-model:value="filters.memberId"
            placeholder="筛选成员"
            style="width: 150px"
            allow-clear
            @change="handleMemberFilter"
          >
            <a-select-option value="">全部成员</a-select-option>
            <a-select-option 
              v-for="member in teamMembers" 
              :key="member.用户id"
              :value="member.用户id"
            >
              {{ member.昵称 }}
            </a-select-option>
          </a-select>

          <!-- 关键词搜索：快速定位达人 -->
          <a-input-search
            v-model:value="filters.keyword"
            placeholder="搜索抖音号"
            style="width: 250px"
            @search="handleSearch"
            @change="handleSearchChange"
            allow-clear
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input-search>
        </a-space>
      </template>

      <!-- 达人列表表格：详细信息展示 -->
      <a-table
        :columns="talentColumns"
        :data-source="talentList"
        :pagination="paginationConfig"
        :loading="loading.talents"
        :scroll="{ x: 1200 }"
        size="middle"
        @change="handleTableChange"
      >
        <!-- 达人信息列：核心展示内容 -->
        <template #bodyCell="{ column, record }">
          <!-- 达人基本信息 -->
          <template v-if="column.key === 'talentInfo'">
            <div class="talent-info">
              <a-avatar
                :src="record.头像"
                :alt="record.昵称 || '达人'"
                size="large"
                shape="square"
              >
                {{ (record.昵称 || '达人').charAt(0) }}
              </a-avatar>
              <div class="talent-details">
                <div class="talent-name">
                  <a @click="viewTalentDetail(record)" class="talent-link">
                    {{ record.昵称 }}
                  </a>
                  <a-tag
                    v-if="record.抖音号 || record.视频号"
                    :color="record.平台 === '抖音' ? 'orange' : 'green'"
                    size="small"
                    :title="record.抖音号 || record.视频号"
                  >
                    {{ truncateAccount(record.抖音号 || record.视频号) }}
                  </a-tag>
                </div>
                <div class="talent-meta">
                  粉丝：{{ record.粉丝数文本 || formatNumber(record.粉丝数) }}
                  <a-divider type="vertical" />
                  城市：{{ record.城市 || '未知' }}
                </div>
              </div>
            </div>
          </template>

          <!-- 认领人信息 -->
          <template v-if="column.key === 'claimedBy'">
            <div class="claimed-by">
              <a-avatar
                :src="record.认领人头像"
                size="small"
              >
                {{ record.认领人姓名?.charAt(0) }}
              </a-avatar>
              <span class="claimer-name">{{ record.认领人姓名 }}</span>
            </div>
          </template>

          <!-- 认领时间 -->
          <template v-if="column.key === 'claimedTime'">
            <div class="claimed-time">
              <div>{{ formatDate(record.认领时间) }}</div>
              <div class="time-ago">{{ getTimeAgo(record.认领时间) }}</div>
            </div>
          </template>

          <!-- 操作列：管理功能 -->
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button 
                type="link" 
                size="small"
                @click="viewTalentDetail(record)"
              >
                查看详情
              </a-button>
              
              <a-dropdown>
                <a-button type="link" size="small">
                  更多操作
                  <down-outlined />
              </a-button>
                <template #overlay>
                  <a-menu @click="handleTalentAction">

                    <a-menu-item key="contact" :data-record="record">
                      <phone-outlined />
                      联系方式
                    </a-menu-item>
                    <a-menu-item key="note" :data-record="record">
                      <edit-outlined />
                      添加备注
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 空状态提示：优化用户体验 -->
    <a-card v-if="!selectedTeamId && !loading.teams">
      <a-empty description="请选择一个团队查看达人信息">
        <template #image>
          <team-outlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
        <a-button
          type="primary"
          @click="$router.push('/team/overview')"
        >
          前往团队管理
        </a-button>
          </a-empty>
      </a-card>



    <!-- 团队达人详细分析模态框 -->
    <TalentAnalysisModal 
      v-model:open="showAnalysisModal"
      :team-id="selectedTeamId"
      @close="showAnalysisModal = false"
    />

    <!-- 团队达人详情模态框 -->
    <TeamTalentDetailModal
      v-model:open="showTalentDetailModal"
      :talent="selectedTalent"
      :team-name="currentTeamName"
      :loading="detailModal.loading"
      :error="detailModal.error"

      @edit="handleTalentEdit"
      @refresh="refreshTeamData"
      @retry="handleDetailRetry"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  TeamOutlined,
  UserOutlined,
  GlobalOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
  PlusOutlined,
  BarChartOutlined,
  SearchOutlined,
  DownOutlined,

  PhoneOutlined,
  EditOutlined
} from '@ant-design/icons-vue'

// 导入服务
import { teamService } from '@/services/team'
import { teamBasicService } from '@/services/team/teamBasic'
import talentService from '@/services/talentService'
import teamTalentService from '@/services/team/teamTalent'

// 导入分析组件
import TalentAnalysisModal from '@/components/talent/TalentAnalysisModal.vue'
// 导入达人详情组件
import TeamTalentDetailModal from '@/components/talent/TeamTalentDetailModal.vue'
// 导入平台切换组件
import PlatformSwitcher from '@/components/talent/PlatformSwitcher.vue'

const router = useRouter()

// ==================== 响应式数据定义 ====================

/**
 * 加载状态管理
 * 用于控制各个模块的loading显示
 */
const loading = reactive({
  teams: false,      // 团队列表加载状态
  stats: false,      // 统计数据加载状态
  talents: false,    // 达人列表加载状态
  refresh: false     // 刷新操作状态
})

/**
 * 数据加载管理器 - 统一管理所有数据请求，避免重复调用
 */
const dataLoader = {
  // 当前正在进行的请求
  activeRequests: new Map(),

  /**
   * 加载团队达人数据 - 带去重机制
   * @param {string} reason - 调用原因，用于日志记录
   * @returns {Promise} 加载结果
   */
  async loadTalents(reason = 'unknown') {
    if (!selectedTeamId.value) {
      console.warn('⚠️ 未选择团队，跳过加载达人列表')
      return { success: false, reason: 'no_team' }
    }

    // 生成请求唯一标识
    const requestKey = `talents-${selectedTeamId.value}-${currentPlatform.value}-${pagination.current}-${pagination.pageSize}`

    // 如果已有相同请求在进行，返回该请求的Promise
    if (this.activeRequests.has(requestKey)) {
      console.log('⚠️ 检测到重复请求，复用现有请求:', reason)
      return this.activeRequests.get(requestKey)
    }

    // 创建新请求
    const requestPromise = this._doLoadTalents(reason, requestKey)
    this.activeRequests.set(requestKey, requestPromise)

    try {
      const result = await requestPromise
      return result
    } finally {
      // 请求完成后清理
      this.activeRequests.delete(requestKey)
    }
  },

  /**
   * 实际执行数据加载的内部方法
   */
  async _doLoadTalents(reason, requestKey) {
    console.log(`🔄 开始加载团队达人列表 [${reason}]:`, {
      teamId: selectedTeamId.value,
      platform: currentPlatform.value,
      page: pagination.current,
      requestKey
    })

    // 先验证团队访问权限
    const hasAccess = await validateTeamAccess(selectedTeamId.value)
    if (!hasAccess) {
      console.warn('⚠️ 用户无权限访问团队:', selectedTeamId.value)
      message.warning('您没有权限访问该团队的达人数据')
      talentList.value = []
      pagination.total = 0
      return { success: false, reason: 'no_access' }
    }

    loading.talents = true
    try {
      // 构建查询参数 - 统一使用一个接口，通过平台参数区分
      const params = {
        团队id: selectedTeamId.value,
        页码: pagination.current,
        每页数量: pagination.pageSize,
        成员id: filters.memberId,
        关键词: filters.keyword || null,
        排序字段: '认领时间',
        排序方式: 'desc',
        平台: currentPlatform.value === 'wechat' ? 'wechat' : 'douyin'  // 平台参数
      }

      // 统一调用团队达人列表接口，后端根据平台参数返回不同数据
      const response = await teamTalentService.getTeamTalentList(params)

      if (response.status === 100) {
        // {{ AURA-X: Fix - 适配后端中文字段名. Source: 前后端字段名统一最佳实践 }}
        talentList.value = response.data.达人列表 || []
        pagination.total = response.data.总数 || 0

        console.log(`✅ 团队达人列表加载成功 [${reason}]:`, {
          talentCount: talentList.value.length,
          total: pagination.total,
          currentPage: pagination.current
        })

        return { success: true, data: response.data }
      } else {
        console.warn('⚠️ 团队达人列表API返回失败:', response.message)
        message.warning(response.message || '获取团队达人列表失败')
        talentList.value = []
        pagination.total = 0
        return { success: false, reason: 'api_error', message: response.message }
      }
    } catch (error) {
      console.error(`❌ 加载团队达人列表失败 [${reason}]:`, error)
      handleApiError(error, '加载团队达人列表')
      talentList.value = []
      pagination.total = 0
      return { success: false, reason: 'exception', error }
    } finally {
      loading.talents = false
    }
  }
}

/**
 * 团队相关数据
 */
const teams = ref([])                    // 用户可访问的团队列表
const selectedTeamId = ref(null)         // 当前选中的团队id
const teamMembers = ref([])              // 团队成员列表

/**
 * 平台切换相关数据
 */
const currentPlatform = ref('douyin')    // 当前选中的平台
const platformStats = ref({
  douyin: { total: 0, claimed: 0, withContact: 0 },
  wechat: { total: 0, claimed: 0, withContact: 0 }
})

/**
 * 统计数据
 */
// {{ AURA-X: Fix - 适配后端中文字段名. Source: 前后端字段名统一最佳实践 }}
const teamStats = reactive({
  总达人数: 0,          // 团队达人总数
  活跃成员数: 0,        // 活跃成员数（有认领达人的成员）
  月新增数: 0,          // 本月新增达人数
  平均每人达人数: 0     // 平均每人认领达人数
})

/**
 * 成员达人分布统计
 */
const memberStats = ref([])

/**
 * 达人列表相关数据
 */
const talentList = ref([])              // 团队达人列表
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

/**
 * 筛选条件
 */
const filters = reactive({
  memberId: null,       // 按成员筛选
  keyword: ''           // 关键词搜索
})



/**
 * 分析模态框显示状态
 */
const showAnalysisModal = ref(false)

/**
 * 达人详情模态框相关数据
 */
const showTalentDetailModal = ref(false)    // 是否显示达人详情模态框
const selectedTalent = ref(null)            // 当前选中查看的达人

/**
 * 详情模态框状态管理
 */
const detailModal = reactive({
  loading: false,      // 详情加载状态
  error: ''           // 错误信息
})

// ==================== 计算属性 ====================

/**
 * 当前选中团队的名称
 */
const currentTeamName = computed(() => {
  if (!selectedTeamId.value) return ''
  const team = teams.value.find(t => t.id === selectedTeamId.value)
  return team?.团队名称 || ''
})

/**
 * 表格分页配置
 */
const paginationConfig = computed(() => ({
  ...pagination,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadTeamTalents('pagination')
  },
  onShowSizeChange: (_, size) => {
    pagination.current = 1
    pagination.pageSize = size
    loadTeamTalents('page_size_change')
  }
}))

/**
 * 达人列表表格列配置
 */
const talentColumns = [
  {
    title: '达人信息',
    key: 'talentInfo',
    width: 300,
    fixed: 'left'
  },
  {
    title: '认领人',
    key: 'claimedBy',
    width: 120,
    align: 'center'
  },
  {
    title: '认领时间',
    key: 'claimedTime',
    width: 150,
    sorter: true
  },
  {
    title: '粉丝数',
    dataIndex: '粉丝数',
    width: 100,
    align: 'right',
    sorter: true,
    customRender: ({ text }) => formatNumber(text)
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载后初始化数据
 */
onMounted(async () => {
  console.log('🚀 团队达人组件初始化')

  // 加载用户团队列表
  await loadUserTeams()

  // 验证当前团队权限状态（如果有预选团队）
  await validateCurrentTeamAccess()

  console.log('✅ 团队达人组件初始化完成')
})

/**
 * 监听团队选择变化 - 优化后的处理逻辑
 */
watch(selectedTeamId, async (newTeamId, oldTeamId) => {
  if (newTeamId) {
    console.log('🔄 团队选择变化:', { from: oldTeamId, to: newTeamId })

    // 先验证新选择的团队权限
    const hasAccess = await validateTeamAccess(newTeamId)
    if (!hasAccess) {
      console.warn('⚠️ 用户无权限访问新选择的团队:', newTeamId)
      message.warning('您没有权限访问该团队，已重置团队选择')
      selectedTeamId.value = null
      return
    }

    // 重置分页和筛选条件
    resetFiltersAndPagination()

    // 并行加载团队相关数据
    try {
      await Promise.all([
        loadTeamStats(),
        loadTeamMembers(),
        loadTeamTalents('team_change')  // 明确标识调用原因
      ])
      console.log('✅ 团队数据加载完成')
    } catch (error) {
      handleApiError(error, '加载团队数据')
    }
  } else {
    // 清空数据当没有选择团队时
    console.log('🔄 清空团队数据')
    Object.assign(teamStats, {
      总达人数: 0,
      活跃成员数: 0,
      月新增数: 0,
      平均每人达人数: 0
    })
    memberStats.value = []
    talentList.value = []
    pagination.total = 0
  }
})

// ==================== 数据加载方法 ====================

 /**
  * 加载用户团队列表
  * 从团队管理模块获取用户可访问的团队数据
  */
 const loadUserTeams = async () => {
   loading.teams = true
   try {
     console.log('🔄 开始加载用户团队列表...')

     // 首先尝试使用团队基础服务
     let response
     try {
       response = await teamService.getUserTeams({
         页码: 1,
         每页数量: 50,
         团队关系类型: 'all'
       })
     } catch (apiError) {
       console.warn('主要API调用失败，尝试备用方案:', apiError)
       // 备用方案：直接使用teamBasicService
       response = await teamBasicService.getUserTeams({
         页码: 1,
         每页数量: 50,
         团队关系类型: 'all'
       })
     }

     console.log('📋 团队列表接口响应:', response)

     if (response && response.status === 100) {
       // 处理响应数据，确保数据格式正确
       const teamList = response.data?.团队列表 || response.data?.teams || []

       if (teamList.length > 0) {
         // 转换数据格式以匹配前端需要的字段，只包含用户有权限的团队
         teams.value = teamList
           .filter(team => {
             // 过滤掉已解散或停用的团队
             const teamStatus = team.团队状态 || '正常'
             return teamStatus === '正常'
           })
           .map(team => ({
             id: team.团队id || team.id,
             团队名称: team.团队名称,
             团队代码: team.团队代码,
             团队描述: team.团队描述 || '',
             团队状态: team.团队状态 || '正常',
             当前成员数: team.当前成员数 || 0,
             最大成员数: team.最大成员数 || 100,
             公司名称: team.公司名称 || '',
             用户角色: team.用户角色 || '成员'
           }))

         if (teams.value.length > 0) {
           // 如果只有一个团队，自动选中
           if (teams.value.length === 1) {
             selectedTeamId.value = teams.value[0].id
           }

           console.log('✅ 团队列表加载成功:', teams.value.length, '个有效团队')
         } else {
           console.warn('⚠️ 过滤后没有可用的团队')
           teams.value = []
           message.warning('您当前没有可访问的团队，请联系管理员添加您到团队中')
         }
       } else {
         console.warn('⚠️ API返回的团队列表为空')
         teams.value = []
         message.warning('您当前没有加入任何团队，请联系管理员邀请您加入团队')
       }
     } else {
       console.error('❌ 团队列表接口返回非成功状态:', response)
       teams.value = []
       message.error('获取团队列表失败，请刷新页面重试')
     }
   } catch (error) {
     handleApiError(error, '加载团队列表')
     teams.value = []
   } finally {
     loading.teams = false
   }
 }

 /**
  * 加载团队统计数据
  * 获取团队达人的各项统计指标
  */
 const loadTeamStats = async () => {
   if (!selectedTeamId.value) return

   // 先验证团队访问权限
   const hasAccess = await validateTeamAccess(selectedTeamId.value)
   if (!hasAccess) {
     console.warn('⚠️ 用户无权限访问团队:', selectedTeamId.value)
     message.warning('您没有权限访问该团队的数据')
     // 重置统计数据
     Object.assign(teamStats, {
       总达人数: 0,
       活跃成员数: 0,
       月新增数: 0,
       平均每人达人数: 0
     })
     memberStats.value = []
     return
   }

   loading.stats = true
   try {
     console.log('🔄 开始加载团队达人统计，团队id:', selectedTeamId.value)

     // 根据当前平台调用不同的API
     let response
     if (currentPlatform.value === 'douyin') {
       response = await teamTalentService.getTeamTalentStats(selectedTeamId.value, {
         timeRange: '30d',
         includeInactive: false
       })
     } else if (currentPlatform.value === 'wechat') {
       response = await teamTalentService.getTeamWechatTalentStats(selectedTeamId.value, {
         timeRange: '30d',
         includeInactive: false
       })
     } else {
       throw new Error('不支持的平台类型')
     }

     if (response.status === 100) {
       const stats = response.data
       Object.assign(teamStats, {
         总达人数: stats.总达人数 || 0,
         活跃成员数: stats.活跃成员数 || 0,
         月新增数: stats.月新增数 || 0,
         平均每人达人数: stats.平均每人达人数 || 0
       })

       // 加载成员分布统计
       memberStats.value = stats.成员统计 || []

       // 更新平台统计数据
       updatePlatformStats()

       console.log('✅ 团队达人统计加载成功:', {
         总达人数: teamStats.总达人数,
         活跃成员数: teamStats.活跃成员数,
         memberCount: memberStats.value.length
       })
     } else {
       console.warn('⚠️ 团队达人统计API返回失败:', response.message)
       message.warning(response.message || '获取团队达人统计失败')
       // 重置为空数据而不是模拟数据
       Object.assign(teamStats, {
         总达人数: 0,
         活跃成员数: 0,
         月新增数: 0,
         平均每人达人数: 0
       })
       memberStats.value = []
     }
   } catch (error) {
     handleApiError(error, '获取团队达人统计')
     // 重置为空数据而不是模拟数据
     Object.assign(teamStats, {
       总达人数: 0,
       活跃成员数: 0,
       月新增数: 0,
       平均每人达人数: 0
     })
     memberStats.value = []
   } finally {
     loading.stats = false
   }
 }

/**
 * 加载团队成员列表
 * 用于成员筛选功能
 * 修复：使用正确的中文字段名与后端保持一致
 */
const loadTeamMembers = async () => {
  if (!selectedTeamId.value) {
    console.warn('loadTeamMembers: 未选择团队，跳过加载')
    return
  }
  
  try {
    console.log('🔄 开始加载团队成员列表，团队id:', selectedTeamId.value)
    
    const response = await teamService.getTeamMembers({
      团队id: selectedTeamId.value,
      成员状态: '正常', // 只获取正常状态的成员
      页码: 1,
      每页数量: 100 // 获取所有成员，用于筛选下拉框
    })
    
    console.log('🔄 团队成员接口响应:', response)
    
    if (response.status === 100) {
      // 直接使用服务层返回的格式化数据，避免重复处理
      teamMembers.value = response.data?.成员列表 || []
      
      console.log('✅ 团队成员加载成功，成员数量:', teamMembers.value.length)
    } else {
      console.warn('❌ 团队成员接口返回失败:', response.message)
      teamMembers.value = []
    }
  } catch (error) {
    handleApiError(error, '加载团队成员')
    teamMembers.value = []
  }
}

 /**
  * 加载团队达人列表 - 使用数据加载管理器
  * @param {string} reason - 调用原因
  */
 const loadTeamTalents = async (reason = 'manual') => {
   return await dataLoader.loadTalents(reason)
 }

 /**
  * 显示统计详情
  * 打开详细分析模态框，展示团队达人的全方位数据分析
  */
 const showStatsDetail = () => {
   console.log('打开团队达人详细分析，团队id:', selectedTeamId.value)
   if (!selectedTeamId.value) {
     message.warning('请先选择团队')
     return
   }
   
   // 打开分析模态框
   showAnalysisModal.value = true
 }
 
 /**
  * 显示所有成员统计
  */
 const showAllMemberStats = () => {
   // TODO: 打开成员统计详情对话框
   message.info('成员统计详情功能开发中')
 }
 
 /**
  * 显示联系方式
  */
 const showContactInfo = (talent) => {
   // TODO: 显示达人联系方式
   console.log('查看联系方式:', talent)
   message.info('联系方式功能开发中')
 }
 
 /**
  * 显示备注对话框
  */
 const showNoteModal = (talent) => {
   // TODO: 显示备注编辑对话框
   console.log('编辑备注:', talent)
   message.info('备注功能开发中')
 }

// ==================== 事件处理方法 ====================

/**
 * 团队下拉框打开时触发
 */
const onTeamDropdownOpen = (open) => {
  if (open) {
    console.log('团队下拉框打开，当前团队数据:', {
      teamsCount: teams.value.length,
      teams: teams.value,
      loading: loading.teams
    })
    
    // 如果没有团队数据且没有在加载中，重新加载
    if (teams.value.length === 0 && !loading.teams) {
      console.log('检测到团队列表为空，重新加载团队数据')
      loadUserTeams()
    }
  }
}

/**
 * 处理团队选择变化
 */
const handleTeamChange = (teamId) => {
  console.log('选择团队:', teamId)
  // 重置所有相关状态
  resetFiltersAndPagination()
  // selectedTeamId的watch会自动触发数据加载
}

/**
 * 处理成员筛选
 */
const handleMemberFilter = (memberId) => {
  filters.memberId = memberId
  pagination.current = 1
  loadTeamTalents('member_filter')
}

/**
 * 处理搜索
 */
const handleSearch = (value) => {
  filters.keyword = value
  pagination.current = 1
  loadTeamTalents('search')
}

/**
 * 处理搜索输入变化
 */
const handleSearchChange = (e) => {
  if (!e.target.value) {
    // 清空搜索时立即刷新
    pagination.current = 1
    loadTeamTalents('search_clear')
  }
}

/**
 * 处理表格变化（排序、分页等）
 */
const handleTableChange = (paginationInfo, filters, sorter) => {
  console.log('表格变化:', { paginationInfo, filters, sorter })

  // 处理分页
  if (paginationInfo) {
    pagination.current = paginationInfo.current
    pagination.pageSize = paginationInfo.pageSize
  }

  // 处理排序
  if (sorter && sorter.order) {
    // TODO: 实现排序逻辑
    console.log('排序:', sorter.field, sorter.order)
  }

  loadTeamTalents('table_change')
}

/**
 * 处理达人操作菜单点击
 */
const handleTalentAction = ({ key, domEvent }) => {
  const record = domEvent.target.closest('[data-record]')?.dataset.record
  if (!record) return
  
  const talent = JSON.parse(record)
  
  switch (key) {
    case 'contact':
      showContactInfo(talent)
      break
    case 'note':
      showNoteModal(talent)
      break
  }
}

// ==================== 功能方法 ====================

/**
 * 刷新团队数据
 */
const refreshTeamData = async () => {
  if (!selectedTeamId.value) return

  loading.refresh = true
  try {
    await Promise.all([
      loadTeamStats(),
      loadTeamMembers(),
      loadTeamTalents('refresh')
    ])
    message.success('数据刷新成功')
  } catch (error) {
    handleApiError(error, '数据刷新')
  } finally {
    loading.refresh = false
  }
}

/**
 * 处理平台切换 - 优化后的处理逻辑
 * @param {Object} platformInfo - 平台信息
 */
const handlePlatformChange = async (platformInfo) => {
  console.log('🔄 团队达人 - 平台切换:', platformInfo)

  // 更新当前平台
  currentPlatform.value = platformInfo.platform

  // 重置筛选条件
  filters.memberId = null
  filters.keyword = ''

  // 重置分页
  pagination.current = 1

  // 如果已选择团队，重新加载数据
  if (selectedTeamId.value) {
    try {
      await Promise.all([
        loadTeamStats(),
        loadTeamTalents('platform_change')  // 明确标识调用原因
      ])

      // 更新平台统计数据
      updatePlatformStats()

      console.log('✅ 平台切换数据加载完成')
    } catch (error) {
      handleApiError(error, '平台切换数据加载')
    }
  }
}

/**
 * 更新平台统计数据
 * 根据当前团队统计更新平台切换器显示的数据
 */
const updatePlatformStats = () => {
  if (currentPlatform.value === 'douyin') {
    platformStats.value.douyin = {
      total: teamStats.总达人数,
      claimed: teamStats.总达人数, // 团队达人都是已认领的
      withContact: Math.floor(teamStats.总达人数 * 0.6) // 估算有联系方式的比例
    }
  } else if (currentPlatform.value === 'wechat') {
    platformStats.value.wechat = {
      total: teamStats.总达人数,
      claimed: teamStats.总达人数, // 团队达人都是已认领的
      withContact: Math.floor(teamStats.总达人数 * 0.8) // 微信达人联系方式比例更高
    }
  }
}

 /**
  * 导出团队达人数据
  */
 const exportTeamTalents = async () => {
   if (!selectedTeamId.value) {
     message.warning('请先选择团队')
     return
   }
   
   try {
     message.loading('正在生成导出文件...', 2)
     
     const response = await teamTalentService.exportTeamTalents({
       团队id: selectedTeamId.value,
       导出格式: 'excel',
       字段列表: ['基本信息', '统计数据', '认领信息'],
       筛选条件: {
         成员id: filters.memberId,
         关键词: filters.keyword
       },
       导出范围: 'current_filter'
     })
     
     if (response.status === 100) {
       if (response.data.downloadUrl) {
         // 直接下载
         window.open(response.data.downloadUrl, '_blank')
         message.success('导出成功')
       } else {
         // 异步任务，提示用户
         message.success('导出任务已创建，请稍后查看下载中心')
       }
     } else {
       message.error(response.message || '导出失败')
     }
   } catch (error) {
     handleApiError(error, '导出团队达人数据')
   }
 }

/**
 * 显示团队设置
 */
const showTeamSettings = () => {
  router.push(`/team/detail/${selectedTeamId.value}`)
}

/**
 * 查看达人详情 - 显示完整的达人详情模态框
 */
const viewTalentDetail = async (talent) => {
  try {
    // 检查必要的参数
    if (!selectedTeamId.value || !talent?.达人id) {
      detailModal.error = '缺少团队或达人信息，无法获取详情'
      showTalentDetailModal.value = true
      return
    }

    // 先显示模态框和加载状态
    detailModal.loading = true
    detailModal.error = ''
    selectedTalent.value = null
    showTalentDetailModal.value = true

    // 调用团队达人详情API获取完整信息
    const response = await teamTalentService.getTalentDetail({
      团队id: selectedTeamId.value,
      达人id: talent.达人id
    })
    
    if (response.status === 100 && response.data) {
      // 设置完整的达人详情信息（使用data字段，不是message字段）
      selectedTalent.value = response.data
      detailModal.error = ''
      
      // 记录查看操作
      console.log('📋 查看达人详情:', {
        达人昵称: response.data.昵称 || '未知',
        抖音号: response.data.抖音号 || '未知',
        认领人: response.data.认领人昵称 || '未知',
        查看时间: new Date().toLocaleString()
      })
    } else {
      detailModal.error = response.message || '获取达人详情失败，请稍后重试'
      selectedTalent.value = null
    }
    
  } catch (error) {
    handleApiError(error, '查看达人详情')
    detailModal.error = '获取达人详情失败，请稍后重试'
    selectedTalent.value = null
  } finally {
    detailModal.loading = false
  }
}









/**
 * 处理从详情模态框发起的达人编辑
 */
const handleTalentEdit = (talent) => {
  // TODO: 实现达人信息编辑功能
  console.log('🔧 编辑达人信息:', talent)
  message.info('达人信息编辑功能开发中')
}

/**
 * 处理详情模态框的重试请求
 */
const handleDetailRetry = () => {
  // 从错误状态重新获取数据
  if (selectedTalent.value) {
    // 如果有达人信息，重新获取该达人的详情
    viewTalentDetail({ talentId: selectedTalent.value.达人id || selectedTalent.value.id })
  } else {
    // 如果没有达人信息，清除错误状态并关闭模态框
    detailModal.error = ''
    showTalentDetailModal.value = false
    message.warning('缺少达人信息，请重新选择达人查看详情')
  }
}

/**
 * 重置筛选条件和分页
 */
const resetFiltersAndPagination = () => {
  filters.memberId = null
  filters.keyword = ''
  pagination.current = 1
  pagination.total = 0
}

// ==================== 工具方法 ====================

/**
 * 获取团队状态颜色
 */
const getTeamStatusColor = (status) => {
  const colors = {
    '正常': 'green',
    '停用': 'orange',
    '解散': 'red'
  }
  return colors[status] || 'default'
}



/**
 * 格式化数字显示
 */
const formatNumber = (num) => {
  if (!num) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

/**
 * 截断账号显示 - 超过30个字符只显示前10位+省略号
 */
const truncateAccount = (account) => {
  if (!account) return ''
  if (account.length > 30) {
    return account.substring(0, 10) + '...'
  }
  return account
}

/**
 * 格式化日期显示
 */
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

/**
 * 获取相对时间显示
 */
const getTimeAgo = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now - date
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  return `${Math.floor(days / 30)}月前`
}

/**
 * 验证用户对团队的访问权限
 * 在调用团队达人API前进行权限预检查，避免403错误
 */
const validateTeamAccess = async (teamId) => {
  try {
    console.log('🔒 验证团队访问权限:', teamId)

    // 检查团队是否在用户的团队列表中
    const userTeam = teams.value.find(team => team.id === teamId)
    if (!userTeam) {
      console.warn('⚠️ 团队不在用户团队列表中:', teamId)
      return false
    }

    // 检查团队状态是否正常
    if (userTeam.团队状态 !== '正常') {
      console.warn('⚠️ 团队状态异常:', userTeam.团队状态)
      return false
    }

    console.log('✅ 团队访问权限验证通过')
    return true
  } catch (error) {
    console.error('❌ 团队权限验证失败:', error)
    return false
  }
}

/**
 * 检查并修复团队权限状态
 * 确保当前选择的团队用户有权限访问
 */
const validateCurrentTeamAccess = async () => {
  if (!selectedTeamId.value) return true

  console.log('🔍 检查当前团队权限状态:', selectedTeamId.value)

  const hasAccess = await validateTeamAccess(selectedTeamId.value)
  if (!hasAccess) {
    console.warn('⚠️ 当前选择的团队无权限访问，重置选择')
    selectedTeamId.value = null
    message.warning('检测到您对当前团队没有访问权限，已重置团队选择')
    return false
  }

  return true
}

/**
 * 统一的错误处理函数
 * 根据错误类型提供友好的用户提示
 */
const handleApiError = (error, operation = '操作') => {
  console.error(`❌ ${operation}失败:`, error)

  if (error.response) {
    const status = error.response.status
    const data = error.response.data

    switch (status) {
      case 401:
        message.error('登录已过期，请重新登录')
        // 可以在这里触发登录跳转
        break
      case 403:
        message.error('您没有权限执行此操作，请联系管理员')
        break
      case 404:
        message.error('请求的资源不存在')
        break
      case 500:
        message.error('服务器内部错误，请稍后重试')
        break
      default:
        message.error(data?.message || `${operation}失败，请稍后重试`)
    }
  } else if (error.code === 'ECONNABORTED') {
    message.error('请求超时，请检查网络连接')
  } else if (error.message) {
    message.error(error.message)
  } else {
    message.error(`${operation}失败，请稍后重试`)
  }
}



// ==================== 组件配置 ====================

defineOptions({
  name: 'TeamTalents'
})

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 TeamTalents 组件加载完成，数据加载管理器已就绪')
}
</script>

<style scoped>
/* ==================== 页面整体样式 ==================== */
.team-talents {
  padding: 0;
  min-height: 100vh;
  background: #f5f5f5;
}

/* 平台切换器样式 */
.platform-switcher-section {
  margin-bottom: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

/* 页面头部样式 */
.page-header {
  background: white;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

/* ==================== 卡片样式 ==================== */
.team-selector-card,
.stats-card,
.talents-list-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ==================== 团队选择区域样式 ==================== */
.team-select-item :deep(.ant-form-item) {
  margin-bottom: 0;
}

.team-option {
  padding: 4px 0;
}

.team-name {
  font-weight: 500;
  color: #262626;
}

.team-info {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* ==================== 统计数据样式 ==================== */
.member-distribution {
  margin-top: 16px;
}

.member-stat-item {
  text-align: center;
  padding: 12px 8px;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s;
}

.member-stat-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.member-name {
  font-size: 12px;
  color: #595959;
  margin: 8px 0 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-count {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

/* ==================== 达人列表样式 ==================== */
.talent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.talent-details {
  flex: 1;
  min-width: 0;
}

.talent-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.talent-link {
  font-weight: 500;
  color: #1890ff;
  text-decoration: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.talent-link:hover {
  color: #40a9ff;
}

.talent-meta {
  font-size: 12px;
  color: #8c8c8c;
}

/* 认领人信息样式 */
.claimed-by {
  display: flex;
  align-items: center;
  gap: 8px;
}

.claimer-name {
  font-size: 14px;
  color: #262626;
}

/* 认领时间样式 */
.claimed-time {
  text-align: center;
}

.time-ago {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1200px) {
  .team-talents {
    padding: 0 8px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .member-distribution .ant-col {
    margin-bottom: 8px;
  }
}

@media (max-width: 768px) {
  .team-selector-card .ant-row {
    flex-direction: column;
  }
  
  .stats-card .ant-row {
    flex-direction: column;
  }
  
  .stats-card .ant-col {
    margin-bottom: 16px;
  }
  
  .member-distribution .ant-row {
    flex-direction: column;
  }
}

/* ==================== 动画效果 ==================== */
.team-talents .ant-card {
  transition: all 0.3s ease;
}

.team-talents .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 表格滚动条样式 */
.ant-table-body::-webkit-scrollbar {
  height: 8px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 