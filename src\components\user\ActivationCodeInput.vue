<template>
  <div class="activation-code-input">
    <!-- 激活码输入卡片 -->
    <a-card 
      title="账户激活" 
      :bordered="false"
      class="activation-card"
    >
      <template #extra>
        <a-tag v-if="userInfo?.是否会员" color="green">
          <template #icon>
            <check-circle-outlined />
          </template>
          已激活
        </a-tag>
        <a-tag v-else color="orange">
          <template #icon>
            <clock-circle-outlined />
          </template>
          待激活
        </a-tag>
      </template>

      <div class="activation-content">
        <!-- 激活说明 -->
        <div class="activation-description">
          <a-alert
            message="激活账户获得更多权限"
            description="支持两种激活方式：① 输入16位激活码直接激活 ② 输入推荐人手机号激活（推荐人需为会员）"
            type="info"
            show-icon
            style="margin-bottom: 24px;"
          />
        </div>

        <!-- 激活码输入表单 -->
        <a-form
          ref="activationFormRef"
          :model="activationForm"
          :rules="activationRules"
          layout="vertical"
          @finish="handleActivation"
        >
          <a-form-item 
            label="激活码/推荐人手机号" 
            name="code"
          >
            <a-input
              v-model:value="activationForm.code"
              size="large"
              placeholder="请输入16位激活码或11位手机号"
              :prefix="h(KeyOutlined)"
              :maxlength="16"
              allow-clear
              @input="onInputChange"
            />
            <!-- 输入提示 -->
            <div class="input-hint" v-if="activationForm.code">
              <a-tag 
                :color="inputType === '激活码' ? 'blue' : inputType === '手机号' ? 'green' : 'red'"
                size="small"
                style="margin-top: 8px;"
              >
                {{ inputHintText }}
              </a-tag>
            </div>
          </a-form-item>

          <a-form-item>
            <a-button
              type="primary"
              size="large"
              html-type="submit"
              :loading="activating"
              :disabled="!isValidInput"
              block
              class="activation-button"
            >
              <template #icon>
                <rocket-outlined />
              </template>
              {{ activationButtonText }}
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 激活历史记录 -->
        <div v-if="activationHistory.length > 0" class="activation-history">
          <a-divider>激活记录</a-divider>
          <a-list
            :data-source="activationHistory"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span>{{ item.激活类型 }}</span>
                    <a-tag 
                      :color="item.状态 === '成功' ? 'green' : 'red'" 
                      size="small"
                      style="margin-left: 8px;"
                    >
                      {{ item.状态 }}
                    </a-tag>
                  </template>
                  <template #description>
                    {{ item.激活时间 }} - {{ item.描述 }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  KeyOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import { authAPI } from '@/services'
import { useUserStore } from '@/store/user'

/**
 * 组件属性定义
 */
const props = defineProps({
  // 是否显示激活历史
  showHistory: {
    type: Boolean,
    default: true
  },
  // 激活成功后的回调
  onActivationSuccess: {
    type: Function,
    default: null
  }
})

/**
 * 状态管理
 */
const userStore = useUserStore()
const userInfo = ref(userStore.userInfo)

// 表单引用
const activationFormRef = ref()

// 激活状态
const activating = ref(false)

// 激活表单数据
const activationForm = reactive({
  code: ''
})

// 激活历史记录
const activationHistory = ref([])

/**
 * 输入类型判断
 */
const inputType = computed(() => {
  const code = activationForm.code.trim()
  if (!code) return ''
  
  // 判断是否为16位激活码（数字和字母组合）
  if (code.length === 16 && /^[A-Za-z0-9]{16}$/.test(code)) {
    return '激活码'
  }
  
  // 判断是否为11位手机号（1开头，第二位是3-9）
  if (code.length === 11 && /^1[3-9]\d{9}$/.test(code)) {
    return '手机号'
  }
  
  return '无效'
})

/**
 * 输入提示文本
 */
const inputHintText = computed(() => {
  const code = activationForm.code.trim()
  if (!code) return ''
  
  switch (inputType.value) {
    case '激活码':
      return '识别为：16位激活码'
    case '手机号':
      return '识别为：推荐人手机号'
    case '无效':
      if (code.length < 11) {
        return '请输入11位手机号或16位激活码'
      } else if (code.length > 16) {
        return '输入过长，请检查格式'
      } else if (code.length === 11) {
        return '手机号格式错误（应为1开头，第二位3-9）'
      } else if (code.length === 16) {
        return '激活码格式错误（应为数字和字母组合）'
      } else {
        return '格式错误，请重新输入'
      }
    default:
      return ''
  }
})

/**
 * 激活按钮文本
 */
const activationButtonText = computed(() => {
  switch (inputType.value) {
    case '激活码':
      return '使用激活码激活'
    case '手机号':
      return '通过推荐人激活'
    default:
      return '立即激活'
  }
})

/**
 * 输入是否有效
 */
const isValidInput = computed(() => {
  return inputType.value === '激活码' || inputType.value === '手机号'
})

/**
 * 表单验证规则
 */
const activationRules = {
  code: [
    { required: true, message: '请输入激活码或推荐人手机号', trigger: 'blur' },
    { 
      validator: (rule, value) => {
        const trimmedValue = value.trim()
        
        // 16位激活码验证
        if (trimmedValue.length === 16) {
          if (!/^[A-Za-z0-9]{16}$/.test(trimmedValue)) {
            return Promise.reject('激活码应为16位数字和字母组合')
          }
          return Promise.resolve()
        }
        
        // 11位手机号验证
        if (trimmedValue.length === 11) {
          if (!/^1[3-9]\d{9}$/.test(trimmedValue)) {
            return Promise.reject('手机号格式错误，应为11位数字，以1开头，第二位为3-9')
          }
          return Promise.resolve()
        }
        
        return Promise.reject('请输入11位手机号或16位激活码')
      },
      trigger: 'blur'
    }
  ]
}

/**
 * 输入变化处理
 */
const onInputChange = () => {
  // 自动清除非法字符
  const code = activationForm.code
  if (code.length <= 11) {
    // 可能是手机号，只允许数字
    activationForm.code = code.replace(/[^\d]/g, '')
  } else {
    // 可能是激活码，只允许数字和字母
    activationForm.code = code.replace(/[^A-Za-z0-9]/g, '')
  }
}

/**
 * 处理激活
 */
const handleActivation = async () => {
  try {
    activating.value = true
    
    const response = await authAPI.activateAccount({
      code: activationForm.code.trim()
    })
    
    if (response.status === 200 || response.status === 100) {
      message.success('账户激活成功！')
      
      // 更新用户信息
      await userStore.fetchUserInfo()
      userInfo.value = userStore.userInfo
      
      // 添加到激活历史
      activationHistory.value.unshift({
        激活类型: response.data?.激活类型 || inputType.value,
        状态: '成功',
        激活时间: new Date().toLocaleString(),
        描述: response.message || '激活成功'
      })
      
      // 清空表单
      activationForm.code = ''
      
      // 执行成功回调
      if (props.onActivationSuccess) {
        props.onActivationSuccess(response)
      }
    } else {
      throw new Error(response.message || '激活失败')
    }
    
  } catch (error) {
    // 根据不同错误类型显示不同提示
    let errorMessage = error.消息 || error.message || '激活失败'
    let isInfoMessage = false // 判断是否为信息提示而非错误
    
    if (errorMessage?.includes('手机号不存在') || errorMessage?.includes('推荐人手机号不存在')) {
      errorMessage = '推荐人手机号不存在，请检查输入是否正确'
    } else if (errorMessage?.includes('不是会员') || errorMessage?.includes('不是付费会员')) {
      errorMessage = '推荐人不是付费会员，无法通过此手机号激活'
    } else if (errorMessage?.includes('已经被邀请') || errorMessage?.includes('无法再次邀请')) {
      errorMessage = '您已经被其他推荐人邀请过，每个用户只能通过一个推荐人激活'
      isInfoMessage = true // 这是正常的业务状态，不是错误
    } else if (errorMessage?.includes('无效的激活码')) {
      errorMessage = '无效的激活码，请检查激活码是否正确'
    } else if (errorMessage?.includes('已被使用')) {
      errorMessage = '激活码已被使用，请联系管理员'
    } else if (errorMessage?.includes('今日邀请次数已达上限')) {
      errorMessage = '推荐人今日邀请次数已达上限，请明天再试'
      isInfoMessage = true // 这是正常的限制，不是错误
    }
    
    // 只在真正的错误时记录日志
    if (!isInfoMessage) {
      console.error('激活失败:', error)
    }
    
    // 添加到激活历史
    activationHistory.value.unshift({
      激活类型: inputType.value || '未知',
      状态: isInfoMessage ? '信息' : '失败',
      激活时间: new Date().toLocaleString(),
      描述: errorMessage
    })
    
    // 根据消息类型使用不同的提示方式
    if (isInfoMessage) {
      message.info(errorMessage)
    } else {
      message.error(errorMessage)
    }
  } finally {
    activating.value = false
  }
}

/**
 * 组件挂载时初始化
 */
onMounted(() => {
  // 获取用户信息
  userInfo.value = userStore.userInfo
})

/**
 * 暴露给父组件的方法
 */
defineExpose({
  // 清空表单
  clearForm: () => {
    activationForm.code = ''
    activationFormRef.value?.resetFields()
  },
  // 设置激活码
  setCode: (code) => {
    activationForm.code = code
  }
})
</script>

<style scoped>
.activation-code-input {
  max-width: 600px;
  margin: 0 auto;
}

.activation-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.activation-content {
  padding: 8px 0;
}

.activation-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
}

.activation-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.activation-history {
  margin-top: 24px;
}

.activation-description {
  margin-bottom: 24px;
}

.input-hint {
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activation-code-input {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .activation-card {
    margin: 0;
  }
}
</style>
