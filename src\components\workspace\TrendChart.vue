<template>
  <div class="trend-chart-container">
    <!-- 图表控制面板 Chart Control Panel -->
    <div class="chart-controls">
      <!-- 第一行：基础选择器 -->
      <div class="control-row">
        <!-- 业务指标选择器（所有维度都显示） -->
        <div class="control-group">
          <label class="control-label">业务指标：</label>
          <a-select
            v-model:value="selectedMetric"
            style="width: 160px"
            placeholder="选择业务指标"
            @change="handleMetricChange"
            :disabled="loading"
            size="small"
          >
            <a-select-option
              v-for="metric in availableMetrics"
              :key="metric.key"
              :value="metric.key"
            >
              <component :is="getMetricIcon(metric.icon)" style="margin-right: 8px" />
              {{ metric.label }}
            </a-select-option>
          </a-select>
        </div>

        <div class="control-group">
          <label class="control-label">时间维度：</label>
          <a-select
            v-model:value="selectedTimeDimension"
            style="width: 100px"
            @change="handleTimeDimensionChange"
            :disabled="loading"
            size="small"
          >
            <a-select-option value="day">日维度</a-select-option>
            <a-select-option value="week">周维度</a-select-option>
            <a-select-option value="month">月维度</a-select-option>
            <a-select-option value="quarter">季度维度</a-select-option>
          </a-select>
        </div>
      </div>

      <!-- 第二行：指标选择（仅在多指标模式且非日维度下显示） -->
      <div v-if="isMultiMetricMode && selectedTimeDimension !== 'day'" class="control-row">
        <div class="control-group full-width">
          <label class="control-label">显示指标：</label>
          <a-checkbox-group
            v-model:value="visibleMetrics"
            @change="handleVisibleMetricsChange"
            class="metrics-checkbox-group"
          >
            <a-checkbox
              v-for="metric in selectedMetricOptions"
              :key="metric.key"
              :value="metric.key"
              class="metric-checkbox"
              size="small"
            >
              <span class="metric-color-dot" :style="{ backgroundColor: metric.color }"></span>
              {{ metric.label }}
            </a-checkbox>
          </a-checkbox-group>
        </div>
      </div>

      <!-- 日维度时显示说明文字 -->
      <div v-if="selectedTimeDimension === 'day'" class="control-row">
        <div class="control-group full-width">
          <div class="day-dimension-info">
            <span class="info-text">
              📊 日维度显示所选模块的今日vs昨日核心业务指标对比
            </span>
          </div>
        </div>
      </div>
    </div>

    <a-spin :spinning="loading || internalLoading" tip="加载图表数据...">
      <div
        ref="chartRef"
        class="chart-wrapper"
        :style="{ height: height + 'px' }"
      >
        <!-- 空状态提示 -->
        <div v-if="!loading && !internalLoading && (!chartData || !chartData.数据 || chartData.数据.length === 0)" class="empty-state">
          <a-empty description="暂无图表数据" />
        </div>
      </div>

      <!-- 图表统计信息 Chart Statistics -->
      <div v-if="chartData && !loading && !internalLoading" class="chart-stats">
        <div class="stats-row">
          <div class="stat-item">
            <span class="stat-label">总计</span>
            <span class="stat-value">{{ formatNumber(chartData.总计) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">平均值</span>
            <span class="stat-value">{{ formatNumber(chartData.平均值) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最大值</span>
            <span class="stat-value">{{ formatNumber(chartData.最大值) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最小值</span>
            <span class="stat-value">{{ formatNumber(chartData.最小值) }}</span>
          </div>
        </div>
      </div>

      <!-- 空状态 Empty State -->
      <div v-if="!chartData && !loading && !internalLoading" class="empty-state">
        <a-empty
          description="暂无图表数据"
          :image="h(BarChartOutlined)"
        />

      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick, h } from 'vue'
import {
  BarChartOutlined,
  WechatOutlined,
  MailOutlined,
  UserAddOutlined,
  SendOutlined,
  DatabaseOutlined,
  MessageOutlined,
  InteractionOutlined,
  UsergroupAddOutlined,
  ContactsOutlined,
  VideoCameraOutlined,
  TrophyOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import { workspaceService } from '@/services/workspaceService'
import { message } from 'ant-design-vue'

// Props definition
const props = defineProps({
  data: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  height: {
    type: Number,
    default: 300
  },
  theme: {
    type: String,
    default: 'light'
  },
  // 新增：业务模块数据，用于生成可选指标
  businessModules: {
    type: Array,
    default: () => []
  },
  // 新增：当前时间范围（可选，如果不传则使用内部管理）
  timeRange: {
    type: String,
    default: null
  }
})

// Emits definition
const emit = defineEmits(['metric-change', 'time-dimension-change', 'visible-metrics-change'])

// Refs
const chartRef = ref(null)
let chartInstance = null

// Reactive data
const selectedMetric = ref('wechat') // 默认选择微信运营指标
const selectedTimeDimension = ref('week') // 默认周维度
const visibleMetrics = ref([]) // 初始为空，在mounted时设置
const chartData = ref(null)

// 业务趋势分析独立的时间范围管理
const internalTimeRange = ref('本周')

// 动态业务指标配置 - 基于传入的businessModules数据
const availableMetrics = computed(() => {
  if (!props.businessModules || props.businessModules.length === 0) {
    // 如果没有传入businessModules，使用默认配置
    return [
      {
        key: 'wechat',
        label: '微信运营核心指标',
        icon: 'wechat',
        color: '#1890ff',
        subMetrics: [
          { key: 'wechat_accounts', label: '微信账号数量', color: '#1890ff' },
          { key: 'total_friends', label: '好友总数', color: '#52c41a' },
          { key: 'daily_new', label: '今日新增', color: '#fa8c16' },
          { key: 'friend_requests', label: '发送好友请求数', color: '#722ed1' },
          { key: 'stored_friends', label: '入库好友数', color: '#eb2f96' },
          { key: 'communication_friends', label: '沟通好友数', color: '#13c2c2' },
          { key: 'interaction_friends', label: '互动好友数', color: '#f5222d' }
        ]
      }
    ]
  }

  // 基于businessModules动态生成指标配置
  return props.businessModules.map(module => {
    // 根据指标卡片生成subMetrics
    const subMetrics = (module.metrics || []).map((metric, index) => ({
      key: generateMetricKey(module.key, metric.标题, index),
      label: metric.标题,
      color: metric.颜色 || module.color || '#1890ff'
    }))

    return {
      key: module.key,
      label: module.title,
      icon: getModuleIcon(module.key),
      color: module.color || '#1890ff',
      subMetrics: subMetrics
    }
  })
})

// 辅助函数：生成指标键值 - 复用核心业务指标的标准键名
const generateMetricKey = (moduleKey, metricTitle, index) => {
  console.log(`🔍 generateMetricKey - 模块: ${moduleKey}, 标题: ${metricTitle}, 索引: ${index}`);
  
  // 使用与核心业务指标完全一致的标准键名映射
  const titleKeyMap = {
    // 微信运营核心指标 - 与后端 异步工作台服务.获取微信运营核心指标 完全一致
    '微信账号': 'wechat_accounts',
    '微信账号数量': 'wechat_accounts',
    '好友总数': 'total_friends',
    '今日新增': 'daily_new',
    '昨日新增': 'daily_new',
    '本周新增': 'daily_new',
    '上周新增': 'daily_new',
    '本月新增': 'daily_new',
    '上月新增': 'daily_new',
    '本季度新增': 'daily_new',
    '上季度新增': 'daily_new',
    '期间新增': 'daily_new',
    '新增好友': 'daily_new',
    '今日新增好友': 'daily_new',
    '发送请求': 'friend_requests',
    '发送好友请求数': 'friend_requests',
    '入库好友': 'stored_friends',
    '入库好友数': 'stored_friends',
    '沟通好友': 'communication_friends',
    '沟通好友数': 'communication_friends',
    '互动好友': 'interaction_friends',
    '互动好友数': 'interaction_friends',
    
    // 达人管理指标 - 与后端标准键名一致
    '总邀约数': 'invitation_count',
    '邀约总数': 'invitation_count',
    '邀约数量': 'invitation_count',
    '意向合作': 'cooperation_count',
    '已建联': 'connection_count',
    '合作中': 'cooperation_active',
    '达人数量': 'talent_count',
    '合作数量': 'cooperation_count',
    '微信认领达人': 'wechat_talent_count',
    '抖音认领达人': 'douyin_talent_count',
    '联系方式获取': 'contact_acquired',
    '好友转化': 'friend_conversion',
    '总认领达人': 'total_talent_count',
    
    // 寄样模块指标 - 与后端标准键名一致
    '审批数量': 'approval_count',
    '申请通过数量': 'approval_count',
    '实际寄样数': 'actual_sample_count',
    '实际寄样数量': 'actual_sample_count',
    '已寄出数': 'delivered_count',
    '样品送达数量': 'delivered_count',
    '寄样数量': 'sample_count',
    '审核通过率': 'approval_rate',
    '成功率': 'success_rate'
  }

  // 优先使用标题映射
  const mappedKey = titleKeyMap[metricTitle];
  if (mappedKey) {
    console.log(`✅ 指标标题映射成功: "${metricTitle}" -> "${mappedKey}"`);
    return mappedKey;
  }
  
  // 如果没有找到映射，尝试模糊匹配
  for (const [key, value] of Object.entries(titleKeyMap)) {
    if (metricTitle.includes(key) || key.includes(metricTitle)) {
      console.log(`✅ 模糊匹配成功: "${metricTitle}" -> "${value}"`);
      return value;
    }
  }
  
  // 如果都没有匹配，记录警告并使用默认键名
  const defaultKey = `${moduleKey}_metric_${index}`;
  console.warn(`⚠️ 未找到匹配的指标键名: "${metricTitle}" -> "${defaultKey}"`);
  return defaultKey;
}

// 辅助函数：获取模块图标
const getModuleIcon = (moduleKey) => {
  const moduleIconMap = {
    'wechat': 'wechat',
    'invitation': 'mail',
    'talent': 'user-add',
    'sample': 'trophy'
  }
  return moduleIconMap[moduleKey] || 'bar-chart'
}

// 图标映射
const iconMap = {
  'wechat': WechatOutlined,
  'mail': MailOutlined,
  'user-add': UserAddOutlined,
  'send': SendOutlined,
  'database': DatabaseOutlined,
  'message': MessageOutlined,
  'interaction': InteractionOutlined,
  'usergroup-add': UsergroupAddOutlined,
  'contacts': ContactsOutlined,
  'video-camera': VideoCameraOutlined,
  'trophy': TrophyOutlined,
  'bar-chart': BarChartOutlined
}

// Computed properties
const isMultiMetricMode = computed(() => {
  // 所有维度都使用多指标模式，日维度有特殊的数据处理逻辑
  return true
})

// 当前使用的时间范围（优先使用内部管理的时间范围）
const currentTimeRange = computed(() => {
  return props.timeRange || internalTimeRange.value
})

const selectedMetricOptions = computed(() => {
  const metric = availableMetrics.value.find(m => m.key === selectedMetric.value)
  return metric ? metric.subMetrics : []
})

// Methods
const formatNumber = (value) => {
  if (value === null || value === undefined) return '0'
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return value.toString()
}

const getMetricIcon = (iconName) => {
  return iconMap[iconName] || BarChartOutlined
}

// 事件处理方法
const handleMetricChange = (value) => {
  selectedMetric.value = value
  // 重置可见指标为当前选择的指标的所有子指标（默认全选）
  const metric = availableMetrics.value.find(m => m.key === value)
  if (metric && metric.subMetrics.length > 0) {
    visibleMetrics.value = metric.subMetrics.map(sub => sub.key)
  }
  emit('metric-change', value)
  loadChartData()
}

const handleTimeDimensionChange = (value) => {
  selectedTimeDimension.value = value

  // 根据时间维度更新内部时间范围 - 支持日维度
  switch (value) {
    case 'day':
      internalTimeRange.value = '今日'
      break
    case 'week':
      internalTimeRange.value = '本周'
      break
    case 'month':
      internalTimeRange.value = '本月'
      break
    case 'quarter':
      internalTimeRange.value = '本季度'
      break
    default:
      // 默认使用周维度
      internalTimeRange.value = '本周'
      selectedTimeDimension.value = 'week'
      break
  }

  // 所有维度都设置可见指标
  const metric = availableMetrics.value.find(m => m.key === selectedMetric.value)
  if (metric && metric.subMetrics.length > 0) {
    visibleMetrics.value = metric.subMetrics.map(sub => sub.key)
  }

  emit('time-dimension-change', selectedTimeDimension.value)
  loadChartData()
}

const handleVisibleMetricsChange = (checkedValues) => {
  visibleMetrics.value = checkedValues
  emit('visible-metrics-change', checkedValues)
  updateChart()
}

const initChart = () => {
  console.log('🎨 initChart 开始')
  console.log('🎨 chartRef.value:', chartRef.value)
  console.log('🎨 chartData.value:', chartData.value)

  if (!chartRef.value || !chartData.value) {
    console.warn('⚠️ chartRef 或 chartData 为空，跳过初始化')
    return
  }

  // 确保容器有正确的尺寸
  const container = chartRef.value
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    console.warn('⚠️ 图表容器尺寸为0，延迟初始化')
    setTimeout(() => initChart(), 100)
    return
  }

  // Dispose existing chart instance
  if (chartInstance) {
    console.log('🎨 销毁现有图表实例')
    chartInstance.dispose()
  }

  // Create new chart instance
  console.log('🎨 创建新的图表实例')
  chartInstance = echarts.init(chartRef.value, props.theme)

  // Prepare chart data
  console.log('🎨 准备图表数据')
  const preparedData = prepareChartData()

  // Set chart options
  console.log('🎨 获取图表选项')
  const options = getChartOptions(preparedData)
  console.log('🎨 图表选项:', options)

  // Set options and render
  console.log('🎨 设置图表选项并渲染')
  chartInstance.setOption(options)

  // 确保图表正确渲染
  setTimeout(() => {
    if (chartInstance) {
      console.log('🎨 图表重绘')
      chartInstance.resize()
    }
  }, 100)

  // Handle resize
  window.addEventListener('resize', handleResize)
  console.log('✅ initChart 完成')
}

// 内部loading状态
const internalLoading = ref(false)

// 转换单个模块数据为日维度柱状图数据
const convertSingleModuleToDayChart = (单模块数据) => {
  console.log('🔄 convertSingleModuleToDayChart - 输入数据:', 单模块数据)
  
  try {
    const 模块名称 = 单模块数据.模块名称
    const 今日模块数据 = 单模块数据.今日[模块名称]
    const 昨日模块数据 = 单模块数据.昨日[模块名称]
    
    if (!今日模块数据?.指标卡片 || !昨日模块数据?.指标卡片) {
      console.warn('⚠️ 模块数据为空或缺少指标卡片')
      return null
    }
    
    // 获取今日的指标作为基准
    const 今日指标卡片 = 今日模块数据.指标卡片
    const 昨日指标卡片 = 昨日模块数据.指标卡片
    
    // 构建指标数据
    const 今日数据 = []
    const 昨日数据 = []
    const 指标标签 = []
    
    // 辅助函数：清理标题中的时间前缀
    const 清理标题前缀 = (标题) => {
      // 移除时间前缀，如"今日"、"昨日"、"期间"、"本周"等
      return 标题.replace(/^(今日|昨日|期间|本周|上周|本月|上月|本季度|上季度)/, '')
    }
    
    今日指标卡片.forEach(今日指标 => {
      const 原始标题 = 今日指标.标题
      const 清理后标题 = 清理标题前缀(原始标题)
      const 今日数值 = 今日指标.数值 || 0
      
      // 在昨日数据中查找对应的指标（支持灵活匹配）
      const 昨日指标 = 昨日指标卡片.find(item => {
        const 昨日清理后标题 = 清理标题前缀(item.标题)
        return 昨日清理后标题 === 清理后标题 || 
               item.标题 === 原始标题 ||
               清理后标题.includes('总邀约数') && 昨日清理后标题.includes('总邀约数') ||
               清理后标题.includes('总认领达人') && 昨日清理后标题.includes('总认领达人') ||
               清理后标题.includes('微信认领达人') && 昨日清理后标题.includes('微信认领达人') ||
               清理后标题.includes('抖音认领达人') && 昨日清理后标题.includes('抖音认领达人') ||
               清理后标题.includes('联系方式获取') && 昨日清理后标题.includes('联系方式获取') ||
               清理后标题.includes('好友转化') && 昨日清理后标题.includes('好友转化')
      })
      
      const 昨日数值 = 昨日指标?.数值 || 0
      
      今日数据.push(今日数值)
      昨日数据.push(昨日数值)
      指标标签.push(清理后标题) // 使用清理后的标题作为图表标签
      
      console.log(`📊 指标匹配: ${原始标题} -> ${清理后标题}, 今日: ${今日数值}, 昨日: ${昨日数值}`)
    })
    
    // 计算统计信息
    const 今日总计 = 今日数据.reduce((sum, val) => sum + val, 0)
    const 昨日总计 = 昨日数据.reduce((sum, val) => sum + val, 0)
    const 总计 = 今日总计 + 昨日总计
    
    // 构造图表数据
    const chartData = {
      类型: 'bar',
      标题: `${模块名称} - 今日vs昨日对比`,
      日期列表: 指标标签,
      指标系列: [
        {
          name: '今日',
          data: 今日数据,
          color: '#1890ff'
        },
        {
          name: '昨日',
          data: 昨日数据,
          color: '#52c41a'
        }
      ],
      统计信息: {
        总计: 总计,
        平均值: 总计 / (指标标签.length * 2),
        最大值: Math.max(...今日数据, ...昨日数据),
        最小值: Math.min(...今日数据, ...昨日数据)
      }
    }
    
    console.log('✅ convertSingleModuleToDayChart - 输出数据:', chartData)
    return chartData
  } catch (error) {
    console.error('❌ convertSingleModuleToDayChart - 转换失败:', error)
    return null
  }
}

// 转换所有指标数据为日维度柱状图数据
const convertAllMetricsToDayChart = (合并数据) => {
  console.log('🔄 convertAllMetricsToDayChart - 输入数据:', 合并数据)
  
  try {
    // 定义15个核心业务指标的标准配置
    const 指标配置 = [
      // 微信运营核心指标（6个）
      { key: 'wechat_accounts', label: '微信账号数', module: '微信运营核心指标', color: '#1890ff' },
      { key: 'total_friends', label: '好友总数', module: '微信运营核心指标', color: '#52c41a' },
      { key: 'daily_new', label: '新增好友', module: '微信运营核心指标', color: '#fa8c16' },
      { key: 'friend_requests', label: '发送请求', module: '微信运营核心指标', color: '#722ed1' },
      { key: 'communication_friends', label: '沟通好友', module: '微信运营核心指标', color: '#13c2c2' },
      { key: 'interaction_friends', label: '互动好友', module: '微信运营核心指标', color: '#f5222d' },
      
      // 达人管理指标（6个）
      { key: 'invitation_count', label: '总邀约数', module: '达人管理', color: '#fa8c16' },
      { key: 'total_talent_count', label: '总认领达人', module: '达人管理', color: '#1890ff' },
      { key: 'wechat_talent_count', label: '微信认领达人', module: '达人管理', color: '#52c41a' },
      { key: 'douyin_talent_count', label: '抖音认领达人', module: '达人管理', color: '#722ed1' },
      { key: 'contact_acquired', label: '联系方式获取', module: '达人管理', color: '#13c2c2' },
      { key: 'friend_conversion', label: '好友转化', module: '达人管理', color: '#f5222d' },
      
      // 寄样模块指标（3个）
      { key: 'approval_count', label: '申请通过数量', module: '寄样统计', color: '#eb2f96' },
      { key: 'actual_sample_count', label: '实际寄样数量', module: '寄样统计', color: '#1890ff' },
      { key: 'delivered_count', label: '样品送达数量', module: '寄样统计', color: '#52c41a' }
    ]
    
    // 提取指标数据的辅助函数
    const 提取指标数据 = (模块数据, 指标配置) => {
      if (!模块数据 || !模块数据.指标卡片) {
        console.warn(`⚠️ 模块数据为空或缺少指标卡片: ${指标配置.module}`)
        return 0
      }
      
      const 指标卡片 = 模块数据.指标卡片
      
      // 通过标题匹配找到对应的指标
      const 匹配指标 = 指标卡片.find(item => {
        const 标题 = item.标题 || ''
        // 使用模糊匹配，支持多种标题格式
        return 标题.includes(指标配置.label) || 
               指标配置.label.includes(标题) ||
               (指标配置.label === '新增好友' && (标题.includes('今日新增') || 标题.includes('新增好友'))) ||
               (指标配置.label === '发送请求' && (标题.includes('发送请求') || 标题.includes('发送好友请求'))) ||
               (指标配置.label === '沟通好友' && (标题.includes('沟通好友') || 标题.includes('入库好友'))) ||
               (指标配置.label === '互动好友' && 标题.includes('互动好友')) ||
               (指标配置.label === '总认领达人' && (标题.includes('总认领') || 标题.includes('达人数量'))) ||
               (指标配置.label === '微信认领达人' && 标题.includes('微信认领')) ||
               (指标配置.label === '抖音认领达人' && 标题.includes('抖音认领')) ||
               (指标配置.label === '联系方式获取' && 标题.includes('联系方式')) ||
               (指标配置.label === '好友转化' && 标题.includes('好友转化')) ||
               (指标配置.label === '申请通过数量' && (标题.includes('申请通过') || 标题.includes('审批数量'))) ||
               (指标配置.label === '实际寄样数量' && (标题.includes('实际寄样') || 标题.includes('寄样数量'))) ||
               (指标配置.label === '样品送达数量' && (标题.includes('样品送达') || 标题.includes('送达数量')))
      })
      
      if (匹配指标) {
        console.log(`✅ 找到匹配指标: ${指标配置.label} -> ${匹配指标.标题}, 数值: ${匹配指标.数值}`)
        return 匹配指标.数值 || 0
      } else {
        console.warn(`⚠️ 未找到匹配指标: ${指标配置.label}`)
        console.log('📋 可用指标:', 指标卡片.map(item => ({ 标题: item.标题, 数值: item.数值 })))
        return 0
      }
    }
    
    // 生成今日和昨日的数据
    const 今日数据 = []
    const 昨日数据 = []
    const 指标标签 = []
    
    指标配置.forEach(config => {
      const 今日值 = 提取指标数据(合并数据.今日[config.module], config)
      const 昨日值 = 提取指标数据(合并数据.昨日[config.module], config)
      
      今日数据.push(今日值)
      昨日数据.push(昨日值)
      指标标签.push(config.label)
    })
    
    // 计算统计信息
    const 今日总计 = 今日数据.reduce((sum, val) => sum + val, 0)
    const 昨日总计 = 昨日数据.reduce((sum, val) => sum + val, 0)
    const 总计 = 今日总计 + 昨日总计
    
    // 构造图表数据
    const chartData = {
      类型: 'bar',
      标题: '今日vs昨日业务指标对比',
      日期列表: 指标标签,
      指标系列: [
        {
          name: '今日',
          data: 今日数据,
          color: '#1890ff'
        },
        {
          name: '昨日',
          data: 昨日数据,
          color: '#52c41a'
        }
      ],
      统计信息: {
        总计: 总计,
        平均值: 总计 / (指标标签.length * 2),
        最大值: Math.max(...今日数据, ...昨日数据),
        最小值: Math.min(...今日数据, ...昨日数据)
      }
    }
    
    console.log('✅ convertAllMetricsToDayChart - 输出数据:', chartData)
    return chartData
  } catch (error) {
    console.error('❌ convertAllMetricsToDayChart - 转换失败:', error)
    return null
  }
}

// 加载图表数据
const loadChartData = async () => {
  try {
    internalLoading.value = true

    if (visibleMetrics.value.length === 0) {
      // 尝试初始化默认指标
      const defaultMetric = availableMetrics.value.find(m => m.key === selectedMetric.value)
      if (defaultMetric && defaultMetric.subMetrics.length > 0) {
        visibleMetrics.value = defaultMetric.subMetrics.map(sub => sub.key)
      } else {
        chartData.value = null
        return
      }
    }

    console.log('🚀 开始加载图表数据')
    console.log('🚀 isMultiMetricMode.value:', isMultiMetricMode.value)
    console.log('🚀 selectedMetric.value:', selectedMetric.value)
    console.log('🚀 visibleMetrics.value:', visibleMetrics.value)
    console.log('🚀 selectedTimeDimension.value:', selectedTimeDimension.value)

    // 日维度特殊处理：显示今日vs昨日所有15个指标的对比柱状图
    if (selectedTimeDimension.value === 'day') {
      console.log('📊 日维度模式，获取今日vs昨日所有指标对比数据')
      await loadAllMetricsForDayDimension()
      return
    }

    if (isMultiMetricMode.value) {
      // 其他情况：调用多指标趋势API
      console.log('📊 调用多指标趋势API，参数:', {
        businessModule: selectedMetric.value,
        metricList: visibleMetrics.value,
        timeDimension: selectedTimeDimension.value,
        timeRange: props.timeRange
      })

      const response = await workspaceService.getMultiMetricTrends({
        businessModule: selectedMetric.value,
        metricList: visibleMetrics.value,
        timeDimension: selectedTimeDimension.value,
        timeRange: currentTimeRange.value
      })

      if (response && response.status === 100 && response.data) {
        const apiData = response.data
        chartData.value = convertMultiMetricApiData(apiData)
      } else {
        throw new Error('多指标API响应格式错误')
      }
    } else {
      // 单指标模式：调用原有趋势API
      const response = await workspaceService.getTrends({
        dataType: 'invitation',
        timeRange: currentTimeRange.value
      })

      if (response && response.status === 100 && response.data) {
        chartData.value = convertSingleMetricApiData(response.data)
      } else {
        throw new Error('单指标API响应格式错误')
      }
    }

    nextTick(() => {
      if (chartData.value) {
        updateChart()
      }
    })
  } catch (error) {
    console.error('加载图表数据失败:', error)
    message.error('加载图表数据失败: ' + error.message)

    // 失败时显示空状态
    chartData.value = null
  } finally {
    internalLoading.value = false
  }
}

// 加载日维度所有指标数据（今日vs昨日对比）
const loadAllMetricsForDayDimension = async () => {
  try {
    console.log('🔄 开始加载日维度指标数据，选中模块:', selectedMetric.value)
    
    let 今日数据 = null
    let 昨日数据 = null
    let 模块名称 = ''
    
    // 根据选中的业务模块获取对应的数据
    if (selectedMetric.value === 'wechat') {
      // 微信运营核心指标
      const [今日微信数据, 昨日微信数据] = await Promise.all([
        workspaceService.getWechatCoreMetrics({ timeRange: '今日' }),
        workspaceService.getWechatCoreMetrics({ timeRange: '昨日' })
      ])
      
      今日数据 = 验证API响应(今日微信数据, '今日微信核心指标')
      昨日数据 = 验证API响应(昨日微信数据, '昨日微信核心指标')
      模块名称 = '微信运营核心指标'
      
    } else if (selectedMetric.value === 'talent') {
      // 达人管理数据 - 使用分平台统计API获取完整的6个指标
      const [今日达人数据, 昨日达人数据] = await Promise.all([
        workspaceService.getTalentManagementByPlatform({ timeRange: '今日' }),
        workspaceService.getTalentManagementByPlatform({ timeRange: '昨日' })
      ])
      
      今日数据 = 验证API响应(今日达人数据, '今日达人管理')
      昨日数据 = 验证API响应(昨日达人数据, '昨日达人管理')
      模块名称 = '达人管理'
      
    } else if (selectedMetric.value === 'sample') {
      // 寄样统计数据
      const [今日寄样数据, 昨日寄样数据] = await Promise.all([
        workspaceService.getSampleMetrics({ timeRange: '今日' }),
        workspaceService.getSampleMetrics({ timeRange: '昨日' })
      ])
      
      今日数据 = 验证API响应(今日寄样数据, '今日寄样统计')
      昨日数据 = 验证API响应(昨日寄样数据, '昨日寄样统计')
      模块名称 = '寄样统计'
    }

    // 验证API响应的辅助函数
    function 验证API响应(response, apiName) {
      if (!response || response.status !== 100 || !response.data) {
        throw new Error(`${apiName} API响应格式错误`)
      }
      return response.data
    }

    console.log('✅ API调用完成')
    console.log('📊 今日数据:', 今日数据)
    console.log('📊 昨日数据:', 昨日数据)

    // 构造单模块数据格式
    const 单模块数据 = {
      今日: { [模块名称]: 今日数据 },
      昨日: { [模块名称]: 昨日数据 },
      模块名称: 模块名称
    }

    console.log('📊 单模块数据:', 单模块数据)

    // 转换为图表数据格式
    chartData.value = convertSingleModuleToDayChart(单模块数据)

    console.log('✅ 日维度图表数据生成完成:', chartData.value)

    nextTick(() => {
      if (chartData.value) {
        updateChart()
      }
    })
  } catch (error) {
    console.error('❌ 加载日维度指标数据失败:', error)
    throw error
  }
}

// 转换多指标API数据格式
const convertMultiMetricApiData = (apiData) => {
  console.log('🔄 convertMultiMetricApiData - 输入数据:', apiData)

  try {
    const { 图表数据 } = apiData

    if (!图表数据) {
      console.error('❌ 图表数据为空')
      return null
    }

    console.log('📊 图表数据:', 图表数据)
    console.log('📅 日期列表:', 图表数据.日期列表)
    console.log('📈 指标系列:', 图表数据.指标系列)

    const result = {
      标题: 图表数据.标题,
      类型: selectedTimeDimension.value === 'day' ? 'bar' : 'line',
      数据: 图表数据.日期列表?.map((date, index) => ({
        日期: date,
        数值: 图表数据.指标系列?.[0]?.data?.[index] || 0,
        标签: `${图表数据.指标系列?.[0]?.data?.[index] || 0}`
      })) || [],
      指标系列: 图表数据.指标系列 || [],
      总计: 图表数据.统计信息?.总计 || 0,
      平均值: 图表数据.统计信息?.平均值 || 0,
      最大值: 图表数据.统计信息?.最大值 || 0,
      最小值: 图表数据.统计信息?.最小值 || 0
    }

    console.log('✅ 转换后的数据:', result)
    return result
  } catch (error) {
    console.error('❌ 数据转换失败:', error)
    return null
  }
}

// 转换单指标API数据格式
const convertSingleMetricApiData = (apiData) => {
  // 处理原有API返回的数据格式
  if (apiData.邀约趋势) {
    const 趋势数据 = apiData.邀约趋势
    return {
      标题: 趋势数据.标题 || '邀约趋势',
      类型: 趋势数据.类型 || 'line',
      数据: 趋势数据.数据 || [],
      总计: 趋势数据.总计 || 0,
      平均值: 趋势数据.平均值 || 0,
      最大值: 趋势数据.最大值 || 0,
      最小值: 趋势数据.最小值 || 0
    }
  }

  return null
}

const getSelectedMetricLabel = () => {
  const metric = availableMetrics.value.find(m => m.key === selectedMetric.value)
  return metric ? metric.label : '业务趋势'
}



const prepareChartData = () => {
  if (!chartData.value) {
    console.warn('⚠️ 图表数据为空，返回默认数据结构')
    return { dates: [], values: [], series: [] }
  }

  // 检查是否为日维度的柱状图数据（新的今日vs昨日对比格式）
  if (chartData.value.类型 === 'bar' && chartData.value.日期列表 && chartData.value.指标系列) {
    console.log('📊 日维度柱状图数据处理（今日vs昨日）')
    const dates = chartData.value.日期列表
    const series = chartData.value.指标系列.map(seriesData => ({
      name: seriesData.name,
      data: seriesData.data,
      color: seriesData.color
    }))
    
    console.log('📊 日维度柱状图数据:', { dates, series })
    return { dates, series }
  }

  // 检查是否有传统的数据格式
  if (!chartData.value.数据) {
    console.warn('⚠️ 图表数据格式不支持，返回默认数据结构')
    return { dates: [], values: [], series: [] }
  }

  const dates = chartData.value.数据.map(item => item.日期)
  console.log('📊 准备图表数据 - 日期列表:', dates)

  if (isMultiMetricMode.value && chartData.value.指标系列 && chartData.value.指标系列.length > 1) {
    // 多指标模式：使用API返回的指标系列数据
    const series = chartData.value.指标系列
      .filter(seriesData => visibleMetrics.value.includes(seriesData.key))
      .map(seriesData => ({
        name: seriesData.name,
        data: seriesData.data,
        color: seriesData.color
      }))

    console.log('📊 多指标模式数据:', series)
    return { dates, series }
  } else if (isMultiMetricMode.value && visibleMetrics.value.length > 1) {
    // 多指标模式但没有API数据：返回空数据
    console.log('📊 多指标模式但无数据')
    return { dates, series: [] }
  } else {
    // 单指标模式
    const values = chartData.value.数据.map(item => item.数值 || 0)
    console.log('📊 单指标模式数据:', values)
    return { dates, values }
  }
}

const getChartOptions = (preparedData) => {
  const isLineChart = chartData.value?.类型 === 'line'
  const isBarChart = chartData.value?.类型 === 'bar'
  const isMultiSeries = preparedData.series && preparedData.series.length > 0
  
  // 日维度时使用柱状图，其他维度使用线图
  const isDayDimension = selectedTimeDimension.value === 'day'
  const chartType = isDayDimension ? 'bar' : 'line'

  // 检查是否所有数据都为0
  let allDataIsZero = false
  if (isMultiSeries) {
    allDataIsZero = preparedData.series.every(series =>
      series.data.every(value => value === 0)
    )
  } else {
    allDataIsZero = preparedData.values && preparedData.values.every(value => value === 0)
  }

  console.log('📊 数据分析 - 所有数据是否为0:', allDataIsZero)
  console.log('📊 图表类型:', chartType, '是否日维度:', isDayDimension)

  const baseOptions = {
    title: {
      text: chartData.value?.标题 || '业务趋势分析',
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#262626'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d9d9d9',
      borderWidth: 1,
      textStyle: {
        color: '#262626'
      },
      formatter: function(params) {
        // 日维度柱状图的特殊处理
        if (isDayDimension && params.length === 2) {
          const 指标名称 = params[0].axisValue
          const 今日数值 = params.find(p => p.seriesName === '今日')?.value || 0
          const 昨日数值 = params.find(p => p.seriesName === '昨日')?.value || 0
          
          // 计算变化趋势
          const 变化值 = 今日数值 - 昨日数值
          const 变化率 = 昨日数值 === 0 ? (今日数值 > 0 ? 100 : 0) : ((变化值 / 昨日数值) * 100)
          const 趋势图标 = 变化值 > 0 ? '↗' : (变化值 < 0 ? '↘' : '→')
          const 趋势颜色 = 变化值 > 0 ? '#52c41a' : (变化值 < 0 ? '#ff4d4f' : '#1890ff')
          
          return `
            <div style="padding: 8px;">
              <div style="font-weight: 600; margin-bottom: 8px;">${指标名称}</div>
              <div style="margin-bottom: 4px;">
                <span style="color: #1890ff;">●</span>
                <span style="margin-left: 4px;">今日: ${formatNumber(今日数值)}</span>
              </div>
              <div style="margin-bottom: 8px;">
                <span style="color: #52c41a;">●</span>
                <span style="margin-left: 4px;">昨日: ${formatNumber(昨日数值)}</span>
              </div>
              <div style="padding-top: 4px; border-top: 1px solid #f0f0f0;">
                <span style="color: ${趋势颜色};">${趋势图标}</span>
                <span style="margin-left: 4px; color: ${趋势颜色};">
                  ${变化值 > 0 ? '+' : ''}${formatNumber(变化值)} 
                  (${变化率.toFixed(1)}%)
                </span>
              </div>
            </div>
          `
        }
        
        // 其他情况的默认处理
        let content = `<div style="padding: 4px 0;">
          <div style="margin-bottom: 8px; font-weight: 600;">${params[0].axisValue}</div>`

        params.forEach(param => {
          content += `
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%;"></span>
              <span>${param.seriesName}: ${formatNumber(param.value)}</span>
            </div>`
        })

        content += '</div>'
        return content
      }
    },
    legend: isMultiSeries ? {
      top: 'top',
      right: 'right',
      orient: 'horizontal',
      textStyle: {
        color: '#8c8c8c',
        fontSize: 12
      }
    } : undefined,
    grid: {
      left: '3%',
      right: '4%',
      bottom: isDayDimension ? '15%' : '10%', // 日维度时增加底部空间
      top: isMultiSeries ? '20%' : '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: preparedData.dates,
      axisLine: {
        lineStyle: {
          color: '#d9d9d9'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#8c8c8c',
        fontSize: 12,
        // 日维度柱状图时，标签可能较长，需要旋转显示
        rotate: isDayDimension && preparedData.dates.length > 8 ? 45 : 0,
        interval: isDayDimension && preparedData.dates.length > 10 ? 'auto' : 0,
        formatter: function(value) {
          // 日维度时，如果标签过长，进行截断处理
          if (isDayDimension && value.length > 6) {
            return value.length > 6 ? value.substring(0, 6) + '...' : value
          }
          return value
        }
      }
    },
    yAxis: {
      type: 'value',
      min: allDataIsZero ? -0.5 : function(value) {
        // 如果最小值大于0，从0开始显示
        return value.min > 0 ? 0 : Math.floor(value.min)
      },
      max: allDataIsZero ? 2 : function(value) {
        // 确保最大值至少比最小值大1
        return Math.max(Math.ceil(value.max), (value.min || 0) + 1)
      },
      minInterval: 1, // 确保Y轴间隔至少为1，避免小数
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#8c8c8c',
        fontSize: 12,
        formatter: function(value) {
          // 确保显示整数
          return Math.round(value).toString()
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      },
      // 当所有数据为0时，添加0值参考线
      ...(allDataIsZero ? {
        axisPointer: {
          show: true,
          lineStyle: {
            color: '#1890ff',
            width: 2,
            type: 'solid'
          }
        }
      } : {})
    }
  }

  // 根据是否为多系列设置不同的series配置
  if (isMultiSeries) {
    console.log('🎨 配置多系列图表')
    baseOptions.series = preparedData.series.map(seriesData => {
      const seriesConfig = {
        name: seriesData.name,
        type: chartType,
        data: seriesData.data,
        smooth: chartType === 'line',
        symbol: chartType === 'line' ? 'circle' : 'none',
        symbolSize: chartType === 'line' ? (allDataIsZero ? 8 : 6) : 0,
        showSymbol: chartType === 'line', // 线图显示节点，柱状图不显示
        lineStyle: chartType === 'line' ? {
          width: 3,
          color: seriesData.color
        } : undefined,
        itemStyle: {
          color: seriesData.color,
          borderRadius: chartType === 'bar' ? [4, 4, 0, 0] : 0 // 柱状图圆角
        },
        emphasis: {
          focus: 'series'
        },
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
      console.log('🎨 系列配置:', seriesConfig)
      return seriesConfig
    })
    console.log('🎨 最终多系列配置:', baseOptions.series)
  } else {
    baseOptions.series = [{
      name: chartData.value?.标题 || '数据',
      type: chartType,
      data: preparedData.values,
      smooth: chartType === 'line',
      symbol: chartType === 'line' ? 'circle' : 'none',
      symbolSize: isLineChart ? 6 : 0,
      lineStyle: isLineChart ? {
        width: 3,
        color: '#1890ff'
      } : undefined,
      // 确保0值点也能显示
      showSymbol: true,
      symbolSize: isLineChart ? (allDataIsZero ? 8 : 6) : 0,
      itemStyle: {
        color: '#1890ff',
        borderRadius: isBarChart ? [4, 4, 0, 0] : 0
      },
      areaStyle: isLineChart ? {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
            { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
          ]
        }
      } : undefined,
      emphasis: {
        focus: 'series',
        itemStyle: {
          color: '#40a9ff'
        }
      },
      animationDuration: 1000,
      animationEasing: 'cubicOut',
      // 当数据全为0时，添加标记线
      ...(allDataIsZero ? {
        markLine: {
          silent: true,
          lineStyle: {
            color: '#1890ff',
            width: 2,
            type: 'solid',
            opacity: 0.6
          },
          data: [{
            yAxis: 0,
            label: {
              show: true,
              position: 'end',
              formatter: '基准线 (0)',
              color: '#1890ff',
              fontSize: 12
            }
          }]
        }
      } : {})
    }]
  }

  return baseOptions
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const updateChart = () => {
  if (!chartInstance) {
    initChart()
    return
  }

  if (!chartData.value) return

  const preparedData = prepareChartData()
  const options = getChartOptions(preparedData)

  chartInstance.setOption(options, true)
}

// Watchers
watch(() => props.data, () => {
  nextTick(() => {
    if (props.data) {
      chartData.value = props.data
      updateChart()
    }
  })
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    nextTick(() => {
      if (chartData.value) {
        initChart()
      } else {
        loadChartData()
      }
    })
  }
})

// 移除对props.timeRange的监听，因为现在使用独立的时间范围管理

// 监听props.data的变化
watch(() => props.data, (newData) => {
  if (newData && !props.loading) {
    chartData.value = newData
    nextTick(() => {
      updateChart()
    })
  }
}, { deep: true })

// 初始化可见指标的函数
const initializeVisibleMetrics = () => {
  console.log('🔧 初始化可见指标')
  console.log('🔧 selectedMetric.value:', selectedMetric.value)
  console.log('🔧 availableMetrics.value:', availableMetrics.value)

  // 如果当前选择的指标不存在，选择第一个可用的指标
  if (availableMetrics.value.length > 0) {
    const currentMetricExists = availableMetrics.value.some(m => m.key === selectedMetric.value)
    if (!currentMetricExists) {
      selectedMetric.value = availableMetrics.value[0].key
      console.log('🔧 切换到第一个可用指标:', selectedMetric.value)
    }
  }

  const defaultMetric = availableMetrics.value.find(m => m.key === selectedMetric.value)
  console.log('🔧 defaultMetric:', defaultMetric)

  if (defaultMetric && defaultMetric.subMetrics.length > 0) {
    visibleMetrics.value = defaultMetric.subMetrics.map(sub => sub.key)
    console.log('🔧 设置 visibleMetrics.value:', visibleMetrics.value)
  } else {
    console.warn('⚠️ 没有找到默认指标或子指标为空')
    visibleMetrics.value = []
  }
}

// 监听businessModules变化，重新初始化指标
watch(() => props.businessModules, (newModules) => {
  if (newModules && newModules.length > 0) {
    console.log('📊 businessModules 变化，重新初始化指标')
    nextTick(() => {
      initializeVisibleMetrics()
    })
  }
}, { deep: true, immediate: true })

// Lifecycle
onMounted(() => {
  console.log('🚀 TrendChart mounted')

  // 初始化可见指标
  initializeVisibleMetrics()

  nextTick(() => {
    console.log('🚀 nextTick - props.data:', props.data)
    console.log('🚀 nextTick - props.loading:', props.loading)
    console.log('🚀 nextTick - props.businessModules:', props.businessModules)

    if (props.data && !props.loading) {
      console.log('✅ 使用传入的props数据')
      chartData.value = props.data
      initChart()
    } else if (!props.loading) {
      console.log('📡 props数据为空，调用API获取数据')
      loadChartData()
    } else {
      console.log('⏳ 组件正在加载中，等待数据')
    }
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.trend-chart-container {
  width: 100%;
  height: 100%;
}

/* 图表控制面板样式 */
.chart-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.full-width {
  width: 100%;
}

.control-label {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  white-space: nowrap;
}

.metrics-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 100%;
}

.metric-checkbox {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 3px;
  background: #fff;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
  font-size: 12px;
}

.metric-checkbox:hover {
  border-color: #40a9ff;
  background: #f6ffed;
}

.metric-color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  min-height: 450px;
  max-height: 600px;
  position: relative;
  overflow: hidden;
}

.chart-stats {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.day-dimension-info {
  text-align: center;
  color: #8c8c8c;
  font-size: 14px;
  padding: 10px 0;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.day-dimension-info .info-text {
  display: inline-block;
  padding: 6px 12px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;
  font-size: 13px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .chart-controls {
    gap: 10px;
    padding: 10px 12px;
  }

  .control-row {
    gap: 12px;
  }

  .control-group {
    flex-wrap: wrap;
  }

  .metrics-checkbox-group {
    gap: 6px;
  }

  .chart-wrapper {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .chart-controls {
    padding: 12px;
    gap: 8px;
  }

  .control-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .stats-row {
    flex-wrap: wrap;
    gap: 12px;
  }

  .stat-item {
    min-width: calc(50% - 6px);
  }

  .stat-value {
    font-size: 14px;
  }

  .chart-wrapper {
    min-height: 350px;
  }
}
</style>
