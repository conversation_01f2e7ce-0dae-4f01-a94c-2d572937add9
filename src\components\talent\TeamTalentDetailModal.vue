<template>
  <!-- 团队达人详情模态框 - 简化版 -->
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="900px"
    :footer="null"
    @cancel="handleClose"
    class="team-talent-detail-modal"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="modal-loading-state">
      <div class="loading-content">
        <a-spin size="large" />
        <p>正在加载达人详细信息...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="modal-error-state">
      <div class="error-content">
        <div class="error-icon">
          <exclamation-circle-outlined style="font-size: 48px; color: #ff4d4f;" />
        </div>
        <h3>数据加载失败</h3>
        <p class="error-message">{{ error }}</p>
        <div class="error-actions">
          <a-button type="primary" @click="handleRetry">重新加载</a-button>
          <a-button @click="handleClose">关闭</a-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!talent || Object.keys(talent).length === 0" class="modal-empty-state">
      <div class="empty-content">
        <div class="empty-icon">
          <file-search-outlined style="font-size: 64px; color: #d9d9d9;" />
        </div>
        <h3>暂无达人信息</h3>
        <p class="empty-description">
          该达人的详细信息暂时无法获取，可能的原因：
        </p>
        <ul class="reason-list">
          <li>达人数据尚未同步</li>
          <li>达人信息正在更新中</li>
          <li>网络连接异常</li>
        </ul>
        <div class="empty-actions">
          <a-button type="primary" @click="handleRefresh" :loading="refreshing">
            <reload-outlined />
            刷新数据
          </a-button>
          <a-button @click="handleContactSupport">
            <customer-service-outlined />
            联系技术支持
          </a-button>
          <a-button @click="handleClose">关闭</a-button>
        </div>
        <div class="help-tip">
          <info-circle-outlined />
          <span>如果问题持续存在，请联系系统管理员</span>
        </div>
      </div>
    </div>

    <!-- 正常数据显示 -->
    <div class="team-talent-detail-content" v-else>
      <!-- 达人基本信息头部 -->
      <div class="talent-header-section">
        <div class="talent-basic-info">
          <!-- 头像和基本信息 -->
          <div class="avatar-info">
            <a-avatar
              :size="60"
              :src="talent.头像"
              :alt="talent.昵称 || '达人'"
            >
              <template #icon v-if="!talent.头像">
                <user-outlined />
              </template>
            </a-avatar>
            
            <div class="basic-info">
              <h3 class="talent-name">{{ talent.昵称 || '未知昵称' }}</h3>
              <div class="talent-account">
                <video-camera-outlined />
                <span>{{ talent.抖音号 || '未知抖音号' }}</span>
              </div>
              <div class="talent-uid" v-if="talent.UID">
                <number-outlined />
                <span>UID: {{ talent.UID }}</span>
              </div>
            </div>
          </div>
          
          <!-- 状态标签 -->
          <div class="status-tags">
            <a-tag color="green" size="large">
              <safety-outlined />
              正常
            </a-tag>
            
            <a-tag color="blue" size="large" v-if="talent.认领人昵称">
              <user-outlined />
              认领人：{{ talent.认领人昵称 }}
            </a-tag>
          </div>
        </div>
      </div>

      <!-- 详细信息标签页 -->
      <a-tabs v-model:active-key="activeTab" class="talent-detail-tabs">
        <!-- 数据统计页 -->
        <a-tab-pane key="stats" tab="📊 数据统计">
          <div class="stats-section">
            <a-row :gutter="[16, 16]">
              <!-- 核心数据指标 -->
              <a-col :span="6">
                <a-card class="stat-card">
                  <a-statistic 
                    title="粉丝数" 
                    :value="formatStatValue(talent.粉丝数)"
                  >
                    <template #prefix>
                      <team-outlined style="color: #1890ff;" />
                    </template>
                  </a-statistic>
                </a-card>
              </a-col>
              
              <a-col :span="6">
                <a-card class="stat-card">
                  <a-statistic 
                    title="关注数" 
                    :value="formatStatValue(talent.关注数)"
                  >
                    <template #prefix>
                      <heart-outlined style="color: #52c41a;" />
                    </template>
                  </a-statistic>
                </a-card>
              </a-col>
              
              <a-col :span="6">
                <a-card class="stat-card">
                  <a-statistic 
                    title="获赞数" 
                    :value="formatStatValue(talent.获赞数)"
                  >
                    <template #prefix>
                      <like-outlined style="color: #faad14;" />
                    </template>
                  </a-statistic>
                </a-card>
              </a-col>
              
              <a-col :span="6">
                <a-card class="stat-card">
                  <a-statistic 
                    title="作品数" 
                    :value="formatStatValue(talent.作品数)"
                  >
                    <template #prefix>
                      <play-circle-outlined style="color: #722ed1;" />
                    </template>
                  </a-statistic>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 基本信息页 -->
        <a-tab-pane key="info" tab="📋 基本信息">
          <div class="info-section">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="达人昵称">
                {{ talent.昵称 || '-' }}
              </a-descriptions-item>
              
              <a-descriptions-item label="抖音号">
                {{ talent.抖音号 || '-' }}
              </a-descriptions-item>
              
              <a-descriptions-item label="UID">
                {{ talent.UID || '-' }}
              </a-descriptions-item>
              
              <a-descriptions-item label="认领人">
                {{ talent.认领人昵称 || '-' }}
              </a-descriptions-item>
              
              <a-descriptions-item label="认领时间">
                {{ formatDateTime(talent.认领时间) || '-' }}
              </a-descriptions-item>
              
              <a-descriptions-item label="所属团队">
                {{ teamName || '-' }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>

        <!-- 管理操作页 -->
        <a-tab-pane key="management" tab="⚙️ 管理操作">
          <div class="management-section">
            <a-space direction="vertical" style="width: 100%;" :size="16">
              <!-- 操作按钮组 -->
              <a-card title="快速操作" size="small">
                <a-space wrap>

                  
                  <a-button 
                    :icon="h(EditOutlined)"
                    @click="handleEditInfo"
                  >
                    编辑信息
                  </a-button>
                  
                  <a-button 
                    :icon="h(MessageOutlined)"
                    @click="handleAddNote"
                  >
                    添加备注
                  </a-button>
                  
                  <a-button 
                    :icon="h(DownloadOutlined)"
                    @click="handleExportData"
                  >
                    导出数据
                  </a-button>
                </a-space>
              </a-card>
            </a-space>
          </div>
        </a-tab-pane>

        <!-- 高级分析页（仅粉丝数>1万的达人显示） -->
        <a-tab-pane key="analysis" tab="📈 高级分析" v-if="showAdvancedAnalysis">
          <div class="analysis-section">
            <a-space direction="vertical" style="width: 100%;" :size="16">
              <!-- 商业价值分析卡片 -->
              <a-card title="商业价值分析" size="small">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <div class="influence-item">
                      <div class="influence-title">影响力指数</div>
                      <div class="influence-value">{{ getInfluenceScore() }}</div>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div class="influence-item">
                      <div class="influence-title">互动率</div>
                      <div class="influence-value">{{ calculateEngagementRate() }}%</div>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div class="influence-item">
                      <div class="influence-title">商业价值评级</div>
                      <div class="influence-value">
                        <a-rate 
                          :value="getCommercialValue()" 
                          disabled 
                          :count="5"
                        />
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </a-card>

              <!-- 合作建议卡片 -->
              <a-card title="合作建议" size="small">
                <div class="cooperation-suggestions">
                  <a-tag 
                    v-for="suggestion in getCooperationSuggestions()" 
                    :key="suggestion" 
                    color="blue"
                    style="margin: 4px;"
                  >
                    {{ suggestion }}
                  </a-tag>
                </div>
              </a-card>
            </a-space>
          </div>
        </a-tab-pane>
      </a-tabs>

      <!-- 底部操作区 -->
      <div class="modal-footer">
        <a-space>
          <a-button @click="handleClose">关闭</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
/**
 * 团队达人详情模态框组件
 * 
 * 核心功能：
 * - 【数据展示】四个标签页分别展示：统计数据、基本信息、管理操作、高级分析
 * - 【智能显示】高级分析仅对粉丝数>1万的达人显示，避免信息冗余
 * - 【数据格式化】自动格式化大数字为易读形式（如1.4w、500k）
 * - 【管理集成】提供编辑、备注等达人管理功能
 * - 【商业价值】基于粉丝数自动计算1-5星评级和合作建议
 * 
 * 数据来源：
 * - 优先使用后端API返回的完整达人详情数据（中文字段名）
 * - 支持前端备用计算逻辑，确保功能稳定性
 * - 高级分析：仅对高价值达人（粉丝数>1万）显示
 * 
 * 使用方式：
 * <TeamTalentDetailModal
 *   v-model:open="showModal"
 *   :talent="talentData"
 *   :team-name="teamName"

 *   @edit="handleEdit"
 * />
 */

import { ref, computed, h, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  UserOutlined,
  VideoCameraOutlined,
  NumberOutlined,
  SafetyOutlined,
  TeamOutlined,
  HeartOutlined,
  LikeOutlined,
  PlayCircleOutlined,

  EditOutlined,
  MessageOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  FileSearchOutlined,
  ReloadOutlined,
  CustomerServiceOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

// ==================== Props & Emits ====================
const props = defineProps({
  /** 是否显示模态框 */
  open: {
    type: Boolean,
    default: false
  },
  /** 达人数据对象 */
  talent: {
    type: Object,
    default: () => ({})
  },
  /** 团队名称 */
  teamName: {
    type: String,
    default: ''
  },
  /** 是否只读模式 */
  readonly: {
    type: Boolean,
    default: false
  },
  /** 加载状态 */
  loading: {
    type: Boolean,
    default: false
  },
  /** 错误信息 */
  error: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:open', 'edit', 'refresh', 'retry'])

// ==================== 响应式数据 ====================
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const activeTab = ref('stats')
const refreshing = ref(false)

// 调试：监听talent数据变化，便于排查问题
watch(() => props.talent, (newTalent) => {
  if (newTalent && Object.keys(newTalent).length > 0) {
    console.log('🔍 TeamTalentDetailModal 接收到的talent数据:', newTalent)
    console.log('📊 数据字段详细检查:', {
      '昵称': newTalent.昵称,
      '粉丝数': newTalent.粉丝数,
      '粉丝数类型': typeof newTalent.粉丝数,
      '关注数': newTalent.关注数,
      '关注数类型': typeof newTalent.关注数,
      '获赞数': newTalent.获赞数,
      '获赞数类型': typeof newTalent.获赞数,
      '作品数': newTalent.作品数,
      '作品数类型': typeof newTalent.作品数,
      '头像': newTalent.头像,
      'UID': newTalent.UID,
      '认领人昵称': newTalent.认领人昵称
    })
    
    // 特别检查可能导致[object Object]的数据
    const problematicFields = []
    if (typeof newTalent.粉丝数 === 'object') problematicFields.push('粉丝数')
    if (typeof newTalent.关注数 === 'object') problematicFields.push('关注数')
    if (typeof newTalent.获赞数 === 'object') problematicFields.push('获赞数')
    if (typeof newTalent.作品数 === 'object') problematicFields.push('作品数')
    
    if (problematicFields.length > 0) {
      console.warn('⚠️ 发现对象类型的数值字段:', problematicFields, newTalent)
    }
  }
}, { immediate: true, deep: true })

// 模态框标题
const modalTitle = computed(() => {
  const name = props.talent?.昵称 || '达人'
  return `${name} - 详细信息`
})

// 是否显示高级分析（只有高价值达人才显示）
const showAdvancedAnalysis = computed(() => {
  return (props.talent?.粉丝数 || 0) > 10000
})



// ==================== 工具方法 ====================

/**
 * 格式化统计数值显示
 * 将大数字格式化为易读格式（如1.4w、500k）
 * 修复：确保处理各种数据类型，避免显示[object Object]
 */
const formatStatValue = (value) => {
  // 处理各种可能的数据类型
  let numValue = 0
  
  if (value === null || value === undefined || value === '') {
    numValue = 0
  } else if (typeof value === 'object') {
    // 如果是对象，尝试获取数值
    console.warn('统计数值格式异常，收到对象类型:', value)
    numValue = 0
  } else {
    // 转换为数字
    numValue = Number(value)
    if (isNaN(numValue)) {
      console.warn('统计数值转换失败:', value)
      numValue = 0
    }
  }
  
  // 格式化显示
  if (numValue === 0) return '0'
  if (numValue >= 10000) {
    return (numValue / 10000).toFixed(1) + 'w'
  }
  if (numValue >= 1000) {
    return (numValue / 1000).toFixed(1) + 'k'
  }
  return numValue.toString()
}

/**
 * 格式化日期时间显示
 */
const formatDateTime = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN')
}

/**
 * 计算粉丝互动率
 * 优先使用后端计算的互动率，备用前端计算逻辑
 */
const calculateEngagementRate = () => {
  // 优先使用后端返回的互动率
  if (props.talent?.互动率 !== undefined) {
    return props.talent.互动率
  }
  
  // 备用计算逻辑
  const fans = props.talent?.粉丝数 || 0
  const likes = props.talent?.获赞数 || 0
  if (fans === 0) return '0'
  return ((likes / fans) * 100).toFixed(2)
}

/**
 * 获取影响力指数
 * 优先使用后端计算的活跃度评分，备用前端计算逻辑
 */
const getInfluenceScore = () => {
  // 优先使用后端返回的活跃度评分
  if (props.talent?.活跃度评分 !== undefined) {
    return props.talent.活跃度评分
  }
  
  // 备用计算逻辑
  const fans = props.talent?.粉丝数 || 0
  const works = props.talent?.作品数 || 1
  const score = Math.min(100, Math.round((fans / 10000) * 10 + (works / 100) * 20))
  return score
}

/**
 * 获取商业价值评级（1-5星）
 * 优先使用后端计算的商业价值星级，备用前端计算逻辑
 */
const getCommercialValue = () => {
  // 优先使用后端返回的商业价值星级
  if (props.talent?.商业价值星级 !== undefined) {
    return props.talent.商业价值星级
  }
  
  // 备用计算逻辑
  const fans = props.talent?.粉丝数 || 0
  if (fans >= 1000000) return 5
  if (fans >= 500000) return 4
  if (fans >= 100000) return 3
  if (fans >= 50000) return 2
  return 1
}

/**
 * 获取合作建议列表
 * 优先使用后端返回的建议，否则基于粉丝数生成
 */
const getCooperationSuggestions = () => {
  // 优先使用后端返回的合作建议
  if (props.talent?.合作建议 && Array.isArray(props.talent.合作建议)) {
    return props.talent.合作建议
  }
  
  // 前端备用逻辑：基于粉丝数生成建议
  const fans = props.talent?.粉丝数 || 0
  const suggestions = []
  
  if (fans >= 100000) {
    suggestions.push('适合品牌代言')
    suggestions.push('适合产品推广')
  } else if (fans >= 50000) {
    suggestions.push('适合产品推广')
    suggestions.push('适合内容营销')
  } else if (fans >= 10000) {
    suggestions.push('适合内容营销')
    suggestions.push('适合垂直合作')
  } else {
    suggestions.push('建议观察发展')
  }
  
  return suggestions
}

// ==================== 事件处理 ====================

/**
 * 关闭模态框
 */
const handleClose = () => {
  visible.value = false
}



/**
 * 编辑达人信息
 */
const handleEditInfo = () => {
  emit('edit', props.talent)
  message.info('编辑达人信息功能已触发')
}

/**
 * 添加备注
 */
const handleAddNote = () => {
  // TODO: 实现添加备注功能
  message.info('添加备注功能开发中')
}

/**
 * 导出数据
 */
const handleExportData = () => {
  // TODO: 实现数据导出功能
  message.info('数据导出功能开发中')
}

/**
 * 重试加载数据
 */
const handleRetry = () => {
  emit('retry')
}

/**
 * 刷新数据
 */
const handleRefresh = async () => {
  refreshing.value = true
  try {
    emit('refresh')
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟刷新延迟
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败，请重试')
  } finally {
    refreshing.value = false
  }
}

/**
 * 联系技术支持
 */
const handleContactSupport = () => {
  // 可以打开在线客服、发送邮件或跳转到帮助页面
  message.info('正在为您跳转到技术支持页面...')
  // 这里可以添加实际的联系支持逻辑
  // window.open('mailto:<EMAIL>')
  // 或者
  // router.push('/support')
}
</script>

<style scoped>
/* 团队达人详情模态框样式 */
.team-talent-detail-modal {
  /* 模态框内容区域 */
  .team-talent-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  /* 加载状态样式 */
  .modal-loading-state {
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fafafa;

    .loading-content {
      text-align: center;
      
      p {
        margin-top: 16px;
        color: #666;
        font-size: 16px;
      }
    }
  }

  /* 错误状态样式 */
  .modal-error-state {
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fafafa;

    .error-content {
      text-align: center;
      max-width: 400px;
      
      .error-icon {
        margin-bottom: 20px;
      }
      
      h3 {
        color: #262626;
        font-size: 20px;
        margin-bottom: 12px;
      }
      
      .error-message {
        color: #8c8c8c;
        font-size: 14px;
        margin-bottom: 24px;
        line-height: 1.5;
      }
      
      .error-actions {
        .ant-btn {
          margin: 0 8px;
        }
      }
    }
  }

  /* 空状态样式 */
  .modal-empty-state {
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fafafa;

    .empty-content {
      text-align: center;
      max-width: 500px;
      padding: 40px 20px;
      
      .empty-icon {
        margin-bottom: 24px;
      }
      
      h3 {
        color: #262626;
        font-size: 20px;
        margin-bottom: 12px;
      }
      
      .empty-description {
        color: #8c8c8c;
        font-size: 14px;
        margin-bottom: 16px;
        line-height: 1.5;
      }
      
      .reason-list {
        text-align: left;
        max-width: 300px;
        margin: 0 auto 24px;
        color: #595959;
        
        li {
          margin-bottom: 8px;
          padding-left: 8px;
          position: relative;
          
          &:before {
            content: '•';
            color: #d9d9d9;
            position: absolute;
            left: 0;
          }
        }
      }
      
      .empty-actions {
        margin-bottom: 20px;
        
        .ant-btn {
          margin: 0 8px 8px;
        }
      }
      
      .help-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #8c8c8c;
        font-size: 12px;
        padding: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        
        span {
          line-height: 1.4;
        }
      }
    }
  }

  /* 达人头部信息区域 */
  .talent-header-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;

    .talent-basic-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .avatar-info {
        display: flex;
        gap: 12px;

        .basic-info {
          .talent-name {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
          }

          .talent-account,
          .talent-uid {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 4px;
            color: #666;
            font-size: 14px;
          }
        }
      }

      .status-tags {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: flex-end;
      }
    }
  }

  /* 标签页样式 */
  .talent-detail-tabs {
    /* 统计数据页面 */
    .stats-section {
      .stat-card {
        text-align: center;
        
        :deep(.ant-statistic-title) {
          font-size: 14px;
          color: #666;
        }
        
        :deep(.ant-statistic-content) {
          font-size: 20px;
          font-weight: 600;
        }
      }
    }

    /* 基本信息页面 */
    .info-section {
      :deep(.ant-descriptions) {
        background: white;
      }
    }

    /* 高级分析页面 */
    .analysis-section {
      .influence-item {
        text-align: center;
        padding: 16px;
        background: #f9f9f9;
        border-radius: 8px;

        .influence-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .influence-value {
          font-size: 18px;
          font-weight: 600;
          color: #1890ff;
        }
      }
    }
  }

  /* 底部操作区域 */
  .modal-footer {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}
</style>