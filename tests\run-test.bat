@echo off
echo 🚀 开始CRM系统压力测试
echo 📊 目标URL: https://invite.limob.cn

REM 检查K6是否安装
k6 version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ K6未安装，请运行: npm install -g k6
    pause
    exit /b 1
)

echo ✅ K6工具检查通过

REM 获取测试类型参数
set TEST_TYPE=%1
if "%TEST_TYPE%"=="" set TEST_TYPE=light

echo 📋 测试类型: %TEST_TYPE%

REM 确保结果目录存在
if not exist "tests\results" mkdir "tests\results"

REM 生成输出文件名
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set OUTPUT_FILE=tests\results\k6-result-%timestamp%.json

echo 🎯 开始执行%TEST_TYPE%压力测试...
echo 📄 结果将保存到: %OUTPUT_FILE%

REM 运行K6测试
k6 run --out json=%OUTPUT_FILE% --tag testType=%TEST_TYPE% tests\k6\crm-stress-test.js

if %errorlevel% equ 0 (
    echo ✅ 测试执行完成
    echo 📁 详细结果请查看: %OUTPUT_FILE%
) else (
    echo ❌ 测试执行失败
)

pause
