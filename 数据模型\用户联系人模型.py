from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class 用户联系人响应模型(BaseModel):
    """用户联系人响应模型"""
    用户联系人id: UUID = Field(..., description="联系人UUID")
    姓名: str = Field(..., description="联系人姓名")
    用户表id: int = Field(..., description="所属用户id")


class 关联用户联系人请求模型(BaseModel):
    """关联用户联系人到达人补充信息请求模型"""
    补充信息id: int = Field(..., description="用户达人补充信息表id")
    用户联系人id: UUID = Field(..., description="用户联系人UUID")


class 创建联系人并关联请求模型(BaseModel):
    """创建用户联系人并关联到达人补充信息请求模型"""
    姓名: str = Field(..., max_length=50, description="联系人姓名")
    补充信息id: int = Field(..., description="用户达人补充信息表id")


class 查询用户联系人请求模型(BaseModel):
    """查询用户联系人列表请求模型"""
    关键词: Optional[str] = Field(None, max_length=100, description="搜索关键词（姓名或联系方式）")
