<template>
  <div class="request-headers-form">
    <a-form layout="vertical">
      <div v-for="(header, index) in localHeaders" :key="index" class="header-row">
        <a-row :gutter="16" align="middle">
          <a-col :span="10">
            <a-form-item>
              <a-input 
                v-model:value="header.key" 
                placeholder="头部名称 (e.g., Content-Type)"
                :disabled="header.isDefault && !header.editable"
                @change="updateStoreHeaderKey(index, header.key, header.originalKey)"
              />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item>
              <a-input 
                v-model:value="header.value" 
                placeholder="头部值 (e.g., application/json)"
                :disabled="header.isDefault && !header.editable"
                @change="store.updateHeader(header.key, header.value)" 
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-button 
              danger 
              type="text" 
              @click="removeLocalHeader(index)" 
              :disabled="header.isDefault && !header.editable"
            >
              <template #icon><delete-outlined /></template>
            </a-button>
          </a-col>
        </a-row>
      </div>

      <a-button type="dashed" @click="addLocalHeader" style="width: 100%; margin-top: 8px;">
        <template #icon><plus-outlined /></template>
        添加请求头
      </a-button>

      <a-empty v-if="localHeaders.length === 0 && !hasDefaultUneditableHeaders" description="暂无请求头，可手动添加" style="margin-top: 16px;"/>
      <p v-if="hasDefaultUneditableHeaders" style="font-size: 12px; color: #888; margin-top: 10px;">
        提示：某些默认请求头 (如 Content-Type) 可能由系统自动管理且不可编辑。
      </p>
    </a-form>
  </div>
</template>

<script setup>
import {computed, nextTick, ref, watch} from 'vue';
import {
  Button as AButton,
  Col as ACol,
  Empty as AEmpty,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  message,
  Row as ARow
} from 'ant-design-vue';
import {DeleteOutlined, PlusOutlined} from '@ant-design/icons-vue';
import {useApiTestingModule} from '@/store/apiTestingModule';

const store = useApiTestingModule();

const selectedApi = computed(() => store.getSelectedApiDetails);

// 本地状态，用于动态增删，并通过watch同步回store
// 每个对象结构: { key: string, value: string, isDefault: boolean, editable: boolean, originalKey?: string }
const localHeaders = ref([]);

const hasDefaultUneditableHeaders = computed(() => {
    return selectedApi.value?.headers?.some(h => h.editable === false) || false;
});

const initializeLocalHeaders = () => {
  const newLocalHeaders = [];
  const storeHeaders = store.currentHeaders || {};
  const defaultApiHeaders = selectedApi.value?.headers || [];

  // 1. 添加接口定义中的头部 (包括不可编辑的)
  defaultApiHeaders.forEach(defHeader => {
    newLocalHeaders.push({
      key: defHeader.key,
      originalKey: defHeader.key, // 保存原始key用于更新store中的旧key
      value: storeHeaders.hasOwnProperty(defHeader.key) ? storeHeaders[defHeader.key] : defHeader.value, // 优先使用store中的值
      isDefault: true,
      editable: defHeader.editable !== false // 默认为可编辑
    });
  });

  // 2. 添加store中存在但接口定义中没有的自定义头部
  Object.entries(storeHeaders).forEach(([key, value]) => {
    if (!newLocalHeaders.find(h => h.key === key)) {
      newLocalHeaders.push({
        key: key,
        originalKey: key,
        value: value,
        isDefault: false,
        editable: true
      });
    }
  });
  
  localHeaders.value = newLocalHeaders;
};

watch(() => store.currentHeaders, (newStoreHeaders) => {
    // 当store中的currentHeaders变化时（例如通过selectApi action初始化或外部修改）
    // 重新初始化本地localHeaders以保持同步
    // 这是一个简化的同步，更复杂的场景可能需要深比较和合并
    initializeLocalHeaders();
}, { immediate: true, deep: true });


const addLocalHeader = () => {
  localHeaders.value.push({ key: '', value: '', isDefault: false, editable: true });
  nextTick(() => {
      // 让新增的输入框获取焦点等操作可以在这里执行
  });
};

const removeLocalHeader = (index) => {
  const headerToRemove = localHeaders.value[index];
  if (headerToRemove.isDefault && !headerToRemove.editable) {
      message.warn('该默认请求头不可移除。');
      return;
  }
  store.removeHeader(headerToRemove.key); // 从store中删除
  localHeaders.value.splice(index, 1); // 从本地列表中删除
};

const updateStoreHeaderKey = (index, newKey, oldKey) => {
    const header = localHeaders.value[index];
    if (header.isDefault && !header.editable) return; // 不可编辑的默认头部不允许改key

    if (newKey === oldKey) return; // key没变

    // 检查新key是否已存在（非自身）
    if (localHeaders.value.some((h, i) => i !== index && h.key === newKey)) {
        message.error(`请求头名称 "${newKey}" 已存在。`);
        header.key = oldKey; // 恢复旧值
        return;
    }

    // 更新store: 先移除旧的，再添加新的（如果newKey有效）
    if (oldKey) {
        store.removeHeader(oldKey);
    }
    if (newKey) {
        store.updateHeader(newKey, header.value);
        header.originalKey = newKey; // 更新originalKey，以便下次修改
    } else {
        // 如果新key为空，则相当于只删除了旧key
        header.originalKey = '';
    }
};

// 初始化时也调用一次
initializeLocalHeaders();

</script>

<style scoped>
.request-headers-form {
  padding: 8px;
}
.header-row .ant-form-item {
  margin-bottom: 8px; /* 减少行内表单项间距 */
}
</style> 