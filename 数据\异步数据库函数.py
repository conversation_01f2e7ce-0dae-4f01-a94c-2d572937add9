import asyncio
import secrets
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import Request

import 状态
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 导入统一日志系统
from 日志 import 数据库日志器, 错误日志器


async def 异步更新用户邀请人(用户id: int, 邀请人id: int) -> bool:
    """更新用户的邀请人信息"""
    try:
        更新SQL = "UPDATE 用户表 SET 邀请人 = $1 WHERE id = $2"
        await 异步连接池实例.执行更新(更新SQL, (邀请人id, 用户id))
        数据库日志器.info(f"成功更新用户{用户id}的邀请人为{邀请人id}")
        return True
    except Exception as e:
        错误日志器.error(f"更新用户邀请人失败: {e}", exc_info=True)
        return False


async def 异步更新用户邀请关联信息(用户id: int, 邀请人id: int) -> bool:
    """更新用户邀请关联信息（统一函数）"""
    return await 异步更新用户邀请人(用户id, 邀请人id)

# =============================================================================
# 验证码系统
# =============================================================================


class 异步验证码管理器:
    def __init__(self):
        self.缓存: Dict[str, dict] = {}
        self.锁 = asyncio.Lock()  # 使用异步锁
        self.最大容量 = 1000  # 防止内存溢出

    async def 存储验证码(self, phone: str, code: str, 场景: str, 有效期秒数: int = 600):
        """异步存储验证码"""
        async with self.锁:
            # 自动清理10%的旧数据
            if len(self.缓存) > self.最大容量 * 0.9:
                await self._自动清理()

            key = f"{场景}:{phone}"
            expire_time = time.time() + 有效期秒数

            self.缓存[key] = {"code": code, "expire": expire_time}
        print("验证码列表", self.缓存)

    async def 验证验证码(self, phone: str, code: str, 场景: str) -> dict:
        """异步验证验证码，返回详细状态"""
        print(f"验证验证码: {phone}, {code}, {场景}")
        key = f"{场景}:{phone}"

        async with self.锁:
            print("验证码列表", self.缓存)
            stored = self.缓存.get(key)
            print(f"获取验证码: {key}, {stored}")
            if not stored:
                print(f"验证码不存在: {key}")
                return {
                    "valid": False,
                    "status": 状态.短信服务.验证码错误,
                    "message": "验证码不存在",
                }

            # 自动清理过期
            if time.time() > stored["expire"]:
                print(f"当前时间: {time.time()}, 存储的过期时间: {stored['expire']}")
                print(f"验证码已过期: {key}")
                del self.缓存[key]
                return {
                    "valid": False,
                    "status": 状态.短信服务.验证码过期,
                    "message": "验证码已过期",
                }

            try:
                # 安全比较
                code_match = secrets.compare_digest(code, stored["code"])
                if code_match:
                    print(f"验证码验证成功: {key}")
                    del self.缓存[key]  # 验证成功后删除
                    return {
                        "valid": True,
                        "status": 状态.通用.成功_旧,
                        "message": "验证码正确",
                    }
                return {
                    "valid": False,
                    "status": 状态.短信服务.验证码错误,
                    "message": "验证码错误",
                }
            except Exception as e:
                print(f"验证码验证过程中出现异常: {str(e)}")
                return {
                    "valid": False,
                    "status": 状态.通用.服务器错误,
                    "message": f"验证码验证异常: {str(e)}",
                }

    async def _自动清理(self):
        now = time.time()
        to_delete = []
        # 找出过期的
        for key, data in self.缓存.items():
            if data["expire"] < now:
                to_delete.append(key)
        # 清理超过最大容量的
        if len(self.缓存) > self.最大容量:
            excess = len(self.缓存) - self.最大容量
            to_delete.extend(list(self.缓存.keys())[:excess])
        # 执行删除
        for key in to_delete:
            del self.缓存[key]


# 全局单例实例
异步验证码系统 = 异步验证码管理器()


# =============================================================================
# 订单相关功能
# =============================================================================


async def 异步获取用户订单_id(订单id: int, 用户id: int) -> Optional[Dict]:
    """异步获取用户指定订单"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT * FROM 支付订单表 WHERE id = $1 AND 用户id = $2", (订单id, 用户id)
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步获取订单异常: {str(e)}")
        return None


# =============================================================================
# 微信好友相关功能
# =============================================================================


async def 异步根据id获取微信好友信息(我方微信号id: int, 识别id: int) -> Optional[Dict]:
    """
    异步根据我方微信号id和识别id获取微信好友信息

    注意：微信好友表没有主键id字段，使用我方微信号id+识别id作为唯一标识
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT * FROM 微信好友表 WHERE 我方微信号id = $1 AND 识别id = $2",
            (我方微信号id, 识别id)
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"异步根据id获取微信好友信息异常: {str(e)}")
        return None


async def 异步添加微信好友并生成识别id(
    我方微信号id: int, 对方微信号id: int
) -> dict:
    """异步添加微信好友并生成识别id"""
    try:

        # 检查好友关系是否已存在
        现有关系 = await 异步连接池实例.执行查询(
            """
            SELECT 识别id FROM 微信好友表
            WHERE 我方微信号id = $1 AND 对方微信号id = $2
            """,
            (我方微信号id, 对方微信号id)
        )

        if 现有关系:
            return {
                "识别id": 现有关系[0]["识别id"],
                "是否新增": False
            }

        # 创建新的好友关系
        结果 = await 异步连接池实例.执行查询(
            """
            INSERT INTO 微信好友表 (我方微信号id, 对方微信号id, 创建时间)
            VALUES ($1, $2, NOW())
            RETURNING 识别id
            """,
            (我方微信号id, 对方微信号id)
        )

        if 结果:
            return {
                "识别id": 结果[0]["识别id"],
                "是否新增": True
            }
        else:
            return {
                "识别id": None,
                "是否新增": False
            }

    except Exception as e:
        错误日志器.error(f"异步添加微信好友异常: {str(e)}", exc_info=True)
        return {
            "识别id": None,
            "是否新增": False
        }


# =============================================================================
# 店铺相关功能
# =============================================================================


async def 异步获取店铺id(shop_id: str) -> Optional[int]:
    """根据shop_id获取店铺id"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 店铺 WHERE shop_id = $1", (shop_id,)
        )
        return 结果[0]["id"] if 结果 else None
    except Exception as e:
        print(f"异步获取店铺id异常: {str(e)}")
        return None


async def 异步插入店铺数据(shop_id: str, shop_name: str, avatar: str) -> Optional[int]:
    """异步插入店铺数据"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            INSERT INTO 店铺 (shop_name, shop_id, avatar, 创建时间)
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
            RETURNING id
            """,
            (shop_name, shop_id, avatar),
        )
        return 结果[0]["id"] if 结果 else None
    except Exception as e:
        print(f"异步插入店铺数据异常: {str(e)}")
        return None


# =============================================================================
# 微信对接进度相关功能
# =============================================================================


async def 异步获取或创建微信id(微信号: str) -> Dict[str, Any]:
    """获取或创建微信id，使用PostgreSQL的CTE和UPSERT优雅实现"""
    try:
        # 使用PostgreSQL的WITH CTE + INSERT ON CONFLICT优雅解决方案
        # 1. 先尝试大小写不敏感查询现有记录
        # 2. 如果不存在，插入新记录（保持原始大小写）
        # 3. 使用RETURNING返回统一结果
        结果 = await 异步连接池实例.执行查询(
            """
            WITH existing AS (
                SELECT id, false as 是否新增
                FROM 微信信息表
                WHERE LOWER(微信号) = LOWER($1)
                LIMIT 1
            ),
            inserted AS (
                INSERT INTO 微信信息表 (微信号, 修改时间)
                SELECT $1, CURRENT_TIMESTAMP
                WHERE NOT EXISTS (SELECT 1 FROM existing)
                ON CONFLICT (LOWER(微信号)) DO NOTHING
                RETURNING id, true as 是否新增
            )
            SELECT id, 是否新增 FROM existing
            UNION ALL
            SELECT id, 是否新增 FROM inserted
            """,
            (微信号,),
        )

        if 结果:
            return {
                "id": 结果[0]["id"],
                "是否新增": 结果[0]["是否新增"]
            }
        else:
            raise Exception("获取或创建微信id失败")

    except Exception as e:
        print(f"异步获取或创建微信id异常: {str(e)}")
        raise


async def 异步更新微信对接进度(数据: Dict) -> bool:
    """更新微信对接进度"""
    try:
        from datetime import datetime

        # 构建更新SQL
        更新字段 = []
        参数列表 = []
        参数索引 = 1

        字段映射 = {
            "好友状态": "好友状态",
            "回复状态": "回复状态",
            "意向状态": "意向状态",
            "样品状态": "样品状态",
            "排期状态": "排期状态",
            "开播状态": "开播状态",
            "排期开始时间": "排期开始时间",
            "排期结束时间": "排期结束时间",
        }

        for 字段名, 数据库字段 in 字段映射.items():
            if 字段名 in 数据:
                更新字段.append(f"{数据库字段} = ${参数索引}")
                参数列表.append(数据[字段名])
                参数索引 += 1

        if not 更新字段:
            return True  # 没有需要更新的字段

        # 添加更新时间
        更新字段.append(f"更新时间 = ${参数索引}")
        参数列表.append(datetime.now())
        参数索引 += 1

        # 添加WHERE条件参数
        参数列表.extend(
            [数据["我方微信号id"], 数据["对方微信号id"], 数据["合作产品id"]]
        )

        更新SQL = f"""
        UPDATE 微信产品对接进度表
        SET {", ".join(更新字段)}
        WHERE 我方微信号id = $1 AND 对方微信号id = $2 AND 合作产品id = $3
        """

        await 异步连接池实例.执行更新(更新SQL, tuple(参数列表))
        return True

    except Exception as e:
        print(f"异步更新微信对接进度异常: {str(e)}")
        return False


async def 异步查询微信对接进度(我方微信号: str, 对方微信号: str) -> List[Dict]:
    """查询微信对接进度"""
    try:
        查询SQL = """
        SELECT p.*,
               w1.微信号 as 我方微信号,
               w2.微信号 as 对方微信号
        FROM 微信产品对接进度表 p
        LEFT JOIN 微信信息表 w1 ON p.我方微信号id = w1.id
        LEFT JOIN 微信信息表 w2 ON p.对方微信号id = w2.id
        WHERE w1.微信号 = $1 AND w2.微信号 = $2
        ORDER BY p.创建时间 DESC
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (我方微信号, 对方微信号))
        return 结果 or []

    except Exception as e:
        print(f"异步查询微信对接进度异常: {str(e)}")
        return []


# =============================================================================
# 用户店铺关联功能
# =============================================================================


async def 异步关联用户店铺(用户id: int, 店铺id: int) -> bool:
    """异步关联用户和店铺"""
    try:
        # 检查关联是否已存在
        现有关联 = await 异步连接池实例.执行查询(
            "SELECT 用户id FROM 用户_店铺 WHERE 用户id = $1 AND 店铺id = $2",
            (用户id, 店铺id),
        )

        if 现有关联:
            print(f"用户 {用户id} 和店铺 {店铺id} 的关联已存在")
            return True

        # 创建新关联
        await 异步连接池实例.执行更新(
            "INSERT INTO 用户_店铺 (用户id, 店铺id, 创建时间) VALUES ($1, $2, $3)",
            (用户id, 店铺id, int(time.time())),
        )
        return True
    except Exception as e:
        print(f"异步关联用户店铺异常: {str(e)}")
        return False


# =============================================================================
# 激活码相关功能
# =============================================================================


async def 异步查询激活码信息(code: str) -> Optional[Dict]:
    """异步查询激活码信息"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id, 激活码, 激活码类型表id, 激活用户id, 备注, 是否为一次性激活, 创建时间, 使用时间
            FROM
                激活码表
            WHERE
                激活码 = $1
            """,
            (code,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步查询激活码信息异常: {str(e)}")
        return None


async def 异步查询激活码类型(类型id: int) -> Optional[Dict]:
    """异步查询激活码类型"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id, 名称, 描述, 价格, 会员表id, 会员天数
            FROM
                激活码类型表
            WHERE
                id = $1
            """,
            (类型id,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步查询激活码类型异常: {str(e)}")
        return None


async def 异步设置用户指定权限时间(用户id: int, 会员id: int, 增加秒数: int) -> bool:
    """异步设置用户会员时间 - 基于会员关联表的新架构"""
    try:
        会员记录 = await 异步连接池实例.执行查询(
            """
            SELECT
                id, 到期时间
            FROM
                用户_会员_关联表
            WHERE
                用户id = $1 AND 会员id = $2
            """,
            (用户id, 会员id),
        )

        if 会员记录:
            # 用户已有该会员，更新到期时间
            现有记录 = 会员记录[0]
            现有到期时间 = 现有记录["到期时间"]

            # 如果现有到期时间已过期，从当前时间开始计算
            当前时间 = datetime.now()
            if 现有到期时间 < 当前时间:
                新到期时间 = 当前时间 + timedelta(seconds=增加秒数)
            else:
                新到期时间 = 现有到期时间 + timedelta(seconds=增加秒数)

            await 异步连接池实例.执行更新(
                """
                UPDATE 用户_会员_关联表
                SET 到期时间 = $1
                WHERE id = $2
                """,
                (新到期时间, 现有记录["id"]),
            )
            print(f"更新用户 {用户id} 的会员 {会员id} 到期时间为 {新到期时间}")
        else:
            # 用户没有该会员，创建新记录
            新到期时间 = datetime.now() + timedelta(seconds=增加秒数)
            await 异步连接池实例.执行插入(
                """
                INSERT INTO 用户_会员_关联表 (用户id, 会员id, 到期时间)
                VALUES ($1, $2, $3)
                """,
                (用户id, 会员id, 新到期时间),
            )
            print(f"为用户 {用户id} 创建会员 {会员id}，到期时间为 {新到期时间}")

        return True
    except Exception as e:
        print(f"异步设置用户会员时间异常: {str(e)}")
        return False


async def 异步查询激活记录(激活码id: int) -> Optional[Dict]:
    """
    异步查询激活记录

    Args:
        激活码id: 激活码id

    Returns:
        激活记录字典或None
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT id, 激活码id, 用户id, 激活时间
            FROM 激活记录表
            WHERE 激活码id = $1
            """,
            (激活码id,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步查询激活记录异常: {str(e)}")
        return None


async def 异步插入激活记录(激活码id: int, 用户id: int) -> bool:
    """
    异步插入激活记录

    Args:
        激活码id: 激活码id
        用户id: 用户id

    Returns:
        是否插入成功
    """
    try:
        await 异步连接池实例.执行插入(
            """
            INSERT INTO 激活记录表 (激活码id, 用户id, 激活时间)
            VALUES ($1, $2, $3)
            """,
            (激活码id, 用户id, int(time.time())),
        )
        return True
    except Exception as e:
        print(f"异步插入激活记录异常: {str(e)}")
        return False


async def 异步设置激活码使用状态(code: str, 用户id: int) -> bool:
    """异步设置激活码使用状态（仅用于一次性激活码）"""
    try:
        await 异步连接池实例.执行更新(
            """
            UPDATE 激活码表
            SET 激活用户id = $1, 使用时间 = $2
            WHERE 激活码 = $3
            """,
            (用户id, datetime.now(), code),
        )
        return True
    except Exception as e:
        print(f"异步设置激活码使用状态异常: {str(e)}")
        return False


async def 异步查询用户指定会员到期时间(用户id: int, 会员id: int) -> Optional[datetime]:
    """异步查询用户指定会员到期时间 - 兼容新旧权限架构"""
    try:
        # 优先查询新表：用户_会员_关联表
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT 到期时间
            FROM 用户_会员_关联表
            WHERE 用户id = $1 AND 会员id = $2
            """,
            (用户id, 会员id),
        )

        if 结果:
            到期时间戳 = 结果[0]["到期时间"]
            return datetime.fromtimestamp(到期时间戳)

        # 如果新表没有数据，查询旧表：用户权限表
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT 到期时间
            FROM 用户权限表
            WHERE user_id = $1 AND permission_id = $2
            """,
            (用户id, 会员id),
        )

        if 结果:
            到期时间戳 = 结果[0]["到期时间"]
            return datetime.fromtimestamp(到期时间戳)

        return None
    except Exception as e:
        print(f"异步查询用户指定会员到期时间异常: {str(e)}")
        return None


# =============================================================================
# 推广相关功能
# =============================================================================


async def 异步获取推广用户列表(
    当前用户id: int, 页码: int, 每页数量: int, 查询昵称或手机号: Optional[str] = None
) -> Dict[str, Any]:
    """
    异步获取指定用户id推广的用户列表，支持分页和关键词搜索。

    Args:
        当前用户id: 推广人的用户id
        页码: 页码，从1开始
        每页数量: 每页显示的用户数量
        查询昵称或手机号: 可选的搜索关键词

    Returns:
        包含用户列表和总数的字典
    """
    try:
        # 构建基础查询条件
        where_conditions = ["u.邀请人 = $1"]
        params = [当前用户id]
        param_count = 1

        # 添加搜索条件
        if 查询昵称或手机号:
            param_count += 1
            where_conditions.append(
                f"(u.昵称 ILIKE ${param_count} OR u.手机号 ILIKE ${param_count})"
            )
            params.append(f"%{查询昵称或手机号}%")

        where_clause = " AND ".join(where_conditions)

        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM 用户表 u
        WHERE {where_clause}
        """
        count_result = await 异步连接池实例.执行查询(count_sql, params)
        总记录数 = count_result[0]["total"] if count_result else 0

        # 计算偏移量
        偏移量 = (页码 - 1) * 每页数量

        # 查询用户列表
        param_count += 1
        limit_param = param_count
        param_count += 1
        offset_param = param_count

        list_sql = f"""
        SELECT u.id, u.昵称, u.手机号, u.created_at, u.level
        FROM 用户表 u
        WHERE {where_clause}
        ORDER BY u.created_at DESC
        LIMIT ${limit_param} OFFSET ${offset_param}
        """
        params.extend([每页数量, 偏移量])

        用户列表 = await 异步连接池实例.执行查询(list_sql, params)

        # 处理用户列表数据
        处理后的用户列表 = []
        for 用户 in 用户列表:
            处理后的用户 = {
                "id": 用户["id"],
                "昵称": 用户["昵称"] or "",
                "手机号": 用户["手机号"] or "",
                "注册时间": 用户["created_at"].strftime("%Y-%m-%d %H:%M:%S")
                if 用户["created_at"]
                else "",
                "等级": 用户["level"] or 1,
            }
            处理后的用户列表.append(处理后的用户)

        return {"用户列表": 处理后的用户列表, "total_count": 总记录数}

    except Exception as e:
        错误日志器.error(f"异步获取推广用户列表异常: {str(e)}", exc_info=True)
        return {"用户列表": [], "total_count": 0}


# =============================================================================
# 登录记录相关功能
# =============================================================================


async def 异步更新用户最后登录时间(
    用户id: int, request: Optional[Request] = None
) -> bool:
    """异步更新用户最后登录时间，并记录到用户登陆记录表，包含IP归属地查询"""
    try:
        # 获取客户端IP
        客户端IP = "未知"
        if request:
            客户端IP = request.client.host if request.client else "未知"
            # 检查是否通过代理
            if "x-forwarded-for" in request.headers:
                客户端IP = request.headers["x-forwarded-for"].split(",")[0].strip()
            elif "x-real-ip" in request.headers:
                客户端IP = request.headers["x-real-ip"]

        当前时间 = datetime.now()

        # 查询IP归属地
        try:
            from 工具.IP归属地查询 import 查询IP归属地

            IP归属地 = 查询IP归属地(客户端IP)
        except Exception as ip_error:
            错误日志器.warning(f"查询IP归属地失败: {ip_error}")
            IP归属地 = "查询失败"

        # 插入登录记录到用户登陆记录表（不传递id，让数据库自动生成）
        await 异步连接池实例.执行插入(
            """
            INSERT INTO 用户登陆记录表 (用户id, 登陆时间, ip地址, ip归属地)
            VALUES ($1, $2, $3, $4)
            """,
            (用户id, 当前时间, 客户端IP, IP归属地),
        )

        数据库日志器.info(
            f"用户 {用户id} 登录记录更新成功，IP: {客户端IP}, 归属地: {IP归属地}"
        )
        return True

    except Exception as e:
        错误日志器.error(
            f"异步更新用户最后登录时间失败 (用户id: {用户id}): {e}", exc_info=True
        )
        return False


# =============================================================================
# 别名函数（向后兼容）
# =============================================================================

# 为了保持向后兼容性，提供一些别名函数
异步验证码管理器实例 = 异步验证码系统
