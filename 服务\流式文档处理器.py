"""
流式文档处理器 - 分段读取大文件，最小化内存使用
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict

# 配置日志
流式处理日志器 = logging.getLogger("LangChain.流式处理器")


class 流式文档处理器:
    """流式文档处理器 - 内存友好的大文件处理"""

    def __init__(self):
        # 根据文件大小动态调整分段大小
        self.分段配置 = {
            "小文件": {"阈值": 1024 * 1024, "分段大小": 512 * 1024},  # < 1MB: 512KB分段
            "中文件": {
                "阈值": 10 * 1024 * 1024,
                "分段大小": 1024 * 1024,
            },  # < 10MB: 1MB分段
            "大文件": {
                "阈值": 50 * 1024 * 1024,
                "分段大小": 2048 * 1024,
            },  # < 50MB: 2MB分段
            "超大文件": {
                "阈值": float("inf"),
                "分段大小": 4096 * 1024,
            },  # >= 50MB: 4MB分段
        }

        # 支持的文本文件格式
        self.文本文件格式 = {".txt", ".md", ".csv", ".json", ".log"}

    def 获取分段配置(self, 文件大小: int) -> Dict[str, Any]:
        """根据文件大小获取分段配置"""
        for 配置名, 配置 in self.分段配置.items():
            if 文件大小 <= 配置["阈值"]:
                return {
                    "类型": 配置名,
                    "分段大小": 配置["分段大小"],
                    "预计分段数": max(
                        1, (文件大小 + 配置["分段大小"] - 1) // 配置["分段大小"]
                    ),
                }

        # 默认配置
        return self.分段配置["超大文件"]

    def 是否支持流式处理(self, 文件路径: str) -> bool:
        """检查文件是否支持流式处理"""
        扩展名 = Path(文件路径).suffix.lower()
        return 扩展名 in self.文本文件格式

    def 流式读取文档(self, 文件路径: str):
        """流式读取文档内容"""
        try:
            文件大小 = os.path.getsize(文件路径)
            分段配置 = self.获取分段配置(文件大小)
            分段大小 = 分段配置["分段大小"]

            流式处理日志器.info(
                f"开始流式读取: {文件路径}, 大小: {文件大小}, 分段: {分段配置['类型']}"
            )

            已读取字节 = 0
            分段索引 = 0
            总内容长度 = 0

            # 尝试不同的编码
            编码列表 = ["utf-8", "gbk", "gb2312", "utf-16"]

            for 编码 in 编码列表:
                try:
                    with open(文件路径, "r", encoding=编码, buffering=8192) as 文件:
                        while True:
                            # 分段读取
                            分段内容 = 文件.read(分段大小)
                            if not 分段内容:
                                break

                            已读取字节 += len(分段内容.encode(编码))
                            总内容长度 += len(分段内容)
                            分段索引 += 1

                            流式处理日志器.debug(
                                f"读取分段 {分段索引}: {len(分段内容)} 字符"
                            )

                            # 生成分段内容
                            yield 分段内容

                            # 内存使用检查点
                            if 分段索引 % 10 == 0:
                                流式处理日志器.debug(
                                    f"已处理 {分段索引} 个分段, 进度: {已读取字节 / 文件大小 * 100:.1f}%"
                                )

                    # 成功读取完成，记录统计信息
                    流式处理日志器.info(
                        f"流式读取完成: 文件大小={文件大小}, 内容长度={总内容长度}, "
                        f"分段数量={分段索引}, 使用编码={编码}"
                    )
                    break  # 成功读取，跳出编码尝试循环

                except UnicodeDecodeError:
                    流式处理日志器.debug(f"编码 {编码} 失败，尝试下一个")
                    continue

            # 所有编码都失败
            raise Exception("无法识别文件编码")

        except Exception as e:
            流式处理日志器.error(f"流式读取失败: {str(e)}")
            # 返回空生成器而不是字典，保持类型一致性
            yield from []

    def 流式分块处理(
        self,
        文件路径: str,
        分块策略: str = "智能递归分块",
        分块大小: int = 1000,
        分块重叠: int = 200,
    ):
        """流式分块处理"""
        try:
            from langchain.schema import Document
            from langchain_text_splitters import (
                CharacterTextSplitter,
                RecursiveCharacterTextSplitter,
            )

            # 根据分块策略创建对应的文本分割器 - 与常规处理器保持一致
            if 分块策略 == "行级精准分块":
                # 行级精准分块：直接按行处理，不需要传统的文本分割器
                yield from self._流式行级分块处理(文件路径, 分块重叠)
                return
            elif 分块策略 == "智能递归分块":
                # 智能递归分块 - 通用策略，按段落、句子递归分割
                基础分隔符 = [
                    "\n\n",
                    "\n",
                    "。",
                    "！",
                    "？",
                    "；",
                    ".",
                    "!",
                    "?",
                    ";",
                    " ",
                    "",
                ]
                文本分割器 = RecursiveCharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    is_separator_regex=False,
                    separators=基础分隔符,
                    keep_separator=False,
                )
            elif 分块策略 == "语义优化分块":
                # 语义优化分块 - 中文优化，适合列表和结构化数据
                语义分隔符 = [
                    "\n\n",
                    "\n",
                    "。",
                    "！",
                    "？",
                    "；",
                    "，",
                    "、",
                    ".",
                    "!",
                    "?",
                    ";",
                    ",",
                    " ",
                    "",
                ]
                文本分割器 = RecursiveCharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    is_separator_regex=False,
                    separators=语义分隔符,
                    keep_separator=False,
                )
            elif 分块策略 == "固定大小分块":
                # 固定大小分块 - 严格按字符数分割，不考虑语义
                文本分割器 = CharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    separator=" ",
                )
            else:
                # 未知策略，使用默认智能递归分块
                流式处理日志器.warning(
                    f"⚠️ 未知分块策略: {分块策略}，使用默认智能递归分块"
                )
                基础分隔符 = ["\n\n", "\n", "。", "！", "？", " ", ""]
                文本分割器 = RecursiveCharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    is_separator_regex=False,
                    separators=基础分隔符,
                    keep_separator=False,
                )

            累积内容 = ""
            累积长度 = 0
            分块索引 = 0
            总分块数 = 0

            # 流式读取文档
            文档生成器 = self.流式读取文档(文件路径)
            try:
                for 分段内容 in 文档生成器:
                    累积内容 += 分段内容
                    累积长度 += len(分段内容)

                # 当累积内容达到一定长度时进行分块
                if 累积长度 >= 分块大小 * 3:  # 保留足够内容用于重叠
                    # 创建文档对象并分块
                    文档对象 = Document(page_content=累积内容)
                    分块列表 = 文本分割器.split_documents([文档对象])

                    # 处理分块（保留最后一个分块用于重叠）
                    for 分块 in 分块列表[:-1]:
                        分块信息 = {
                            "分块索引": 分块索引,
                            "分块内容": 分块.page_content.strip(),
                            "分块长度": len(分块.page_content.strip()),
                            "元数据": {
                                "分块策略": 分块策略,
                                "分块大小": 分块大小,
                                "分块重叠": 分块重叠,
                                "流式处理": True,
                            },
                        }

                        if 分块信息["分块内容"]:  # 跳过空分块
                            yield 分块信息
                            分块索引 += 1
                            总分块数 += 1

                    # 保留最后一个分块作为下次的开始（实现重叠）
                    if 分块列表:
                        累积内容 = 分块列表[-1].page_content
                        累积长度 = len(累积内容)
                    else:
                        累积内容 = ""
                        累积长度 = 0

            except Exception as stream_error:
                流式处理日志器.error(f"流式分块处理异常: {str(stream_error)}")
                return

            # 处理剩余内容
            if 累积内容.strip():
                文档对象 = Document(page_content=累积内容)
                分块列表 = 文本分割器.split_documents([文档对象])

                for 分块 in 分块列表:
                    分块信息 = {
                        "分块索引": 分块索引,
                        "分块内容": 分块.page_content.strip(),
                        "分块长度": len(分块.page_content.strip()),
                        "元数据": {
                            "分块策略": 分块策略,
                            "分块大小": 分块大小,
                            "分块重叠": 分块重叠,
                            "流式处理": True,
                        },
                    }

                    if 分块信息["分块内容"]:
                        yield 分块信息
                        分块索引 += 1
                        总分块数 += 1

            return {"success": True, "总分块数": 总分块数, "处理方式": "流式分块"}

        except Exception as e:
            流式处理日志器.error(f"流式分块处理失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _流式行级分块处理(self, 文件路径: str, 分块重叠: int = 0):
        """专门的流式行级分块处理方法"""
        try:
            分块索引 = 0
            总分块数 = 0

            # 流式读取文档并按行处理
            文档生成器 = self.流式读取文档(文件路径)
            累积内容 = ""

            try:
                for 分段内容 in 文档生成器:
                    累积内容 += 分段内容

                    # 按行分割累积内容
                    行列表 = 累积内容.split("\n")

                    # 保留最后一行（可能不完整），处理其他完整行
                    完整行列表 = 行列表[:-1]
                    累积内容 = 行列表[-1]  # 保留最后一行作为下次的开始

                    # 处理每一行
                    for 行号, 行内容 in enumerate(完整行列表):
                        行内容 = 行内容.strip()
                        if 行内容:  # 跳过空行
                            分块信息 = {
                                "分块索引": 分块索引,
                                "分块内容": 行内容,
                                "分块长度": len(行内容),
                                "元数据": {
                                    "分块策略": "行级精准分块",
                                    "分块重叠": 分块重叠,
                                    "流式处理": True,
                                    "chunk_type": "line",
                                    "line_number": 分块索引 + 1,
                                    "chunk_size": len(行内容),
                                },
                            }

                            yield 分块信息
                            分块索引 += 1
                            总分块数 += 1

            except Exception as stream_error:
                流式处理日志器.error(f"流式行级分块处理异常: {str(stream_error)}")
                raise stream_error

            # 处理最后剩余的内容
            if 累积内容.strip():
                分块信息 = {
                    "分块索引": 分块索引,
                    "分块内容": 累积内容.strip(),
                    "分块长度": len(累积内容.strip()),
                    "元数据": {
                        "分块策略": "行级精准分块",
                        "分块重叠": 分块重叠,
                        "流式处理": True,
                        "chunk_type": "line",
                        "line_number": 分块索引 + 1,
                        "chunk_size": len(累积内容.strip()),
                    },
                }

                yield 分块信息
                总分块数 += 1

            流式处理日志器.info(f"✅ 流式行级分块处理完成: {总分块数}个分块")

        except Exception as e:
            流式处理日志器.error(f"流式行级分块处理失败: {str(e)}")
            # 生成器函数中的异常处理，不能return，只能抛出异常
            raise e

    def 获取内存使用建议(self, 文件大小: int) -> Dict[str, Any]:
        """获取内存使用建议"""
        分段配置 = self.获取分段配置(文件大小)

        # 估算内存使用
        预计内存使用 = 分段配置["分段大小"] * 2  # 考虑重叠和处理缓冲

        return {
            "文件类型": 分段配置["类型"],
            "预计内存使用": f"{预计内存使用 / 1024 / 1024:.1f} MB",
            "分段大小": f"{分段配置['分段大小'] / 1024:.0f} KB",
            "预计分段数": 分段配置.get("预计分段数", 1),
            "建议": self._获取处理建议(文件大小),
        }

    def _获取处理建议(self, 文件大小: int) -> str:
        """获取处理建议"""
        if 文件大小 < 1024 * 1024:  # < 1MB
            return "小文件，可以正常处理"
        elif 文件大小 < 10 * 1024 * 1024:  # < 10MB
            return "中等文件，使用流式处理优化内存"
        elif 文件大小 < 50 * 1024 * 1024:  # < 50MB
            return "大文件，强烈建议使用流式处理"
        else:  # >= 50MB
            return "超大文件，必须使用流式处理，建议分批上传"


# 创建全局实例
流式文档处理器实例 = 流式文档处理器()
