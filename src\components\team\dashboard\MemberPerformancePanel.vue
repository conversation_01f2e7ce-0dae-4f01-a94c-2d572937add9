<template>
  <div class="member-performance-panel">
    <a-card :loading="loading" :bordered="false">
      <template #title>
        <div class="panel-header">
          <span class="panel-title">成员绩效</span>
          <div class="header-actions">
            <a-select
              v-model:value="timeRange"
              style="width: 120px; margin-right: 8px"
              :disabled="loading"
            >
              <a-select-option value="today">今日</a-select-option>
              <a-select-option value="week">本周</a-select-option>
              <a-select-option value="month">本月</a-select-option>
              <a-select-option value="quarter">本季度</a-select-option>
            </a-select>
            <a-tooltip title="刷新数据">
              <a-button type="text" :disabled="loading" @click="refreshData">
                <template #icon><ReloadOutlined :spin="loading" /></template>
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </template>
      
      <!-- 模块化标签页 -->
      <a-tabs
        v-model:activeKey="activeModule"
        @change="handleModuleChange"
        class="performance-tabs"
        :tab-bar-style="{ marginBottom: '16px' }"
        animated
      >
        <!-- 微信运营模块 -->
        <a-tab-pane key="wechat" tab="微信运营">
          <template #tab>
            <span class="tab-title">
              <WechatOutlined />
              微信运营
              <a-badge
                :count="wechatMetricsCount"
                :number-style="{ backgroundColor: '#1890ff', fontSize: '10px' }"
                style="margin-left: 4px"
              />
            </span>
          </template>

          <a-table
            :dataSource="memberData"
            :columns="wechatColumns"
            :pagination="{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }"
            :rowKey="record => record.用户id"
            size="middle"
            :scroll="{ x: 1100, y: 400 }"
            :loading="loading"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <component
                :is="renderTableCell"
                :column="column"
                :record="record"
              />
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 达人管理模块 -->
        <a-tab-pane key="talent" tab="达人管理">
          <template #tab>
            <span class="tab-title">
              <StarOutlined />
              达人管理
              <a-badge
                :count="talentMetricsCount"
                :number-style="{ backgroundColor: '#fa8c16', fontSize: '10px' }"
                style="margin-left: 4px"
              />
            </span>
          </template>

          <a-table
            :dataSource="memberData"
            :columns="talentColumns"
            :pagination="{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }"
            :rowKey="record => record.用户id"
            size="middle"
            :scroll="{ x: 1150, y: 400 }"
            :loading="loading"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <component
                :is="renderTableCell"
                :column="column"
                :record="record"
              />
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 寄样管理模块 -->
        <a-tab-pane key="sample" tab="寄样管理">
          <template #tab>
            <span class="tab-title">
              <GiftOutlined />
              寄样管理
              <a-badge
                :count="sampleMetricsCount"
                :number-style="{ backgroundColor: '#eb2f96', fontSize: '10px' }"
                style="margin-left: 4px"
              />
            </span>
          </template>

          <a-table
            :dataSource="memberData"
            :columns="sampleColumns"
            :pagination="{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }"
            :rowKey="record => record.用户id"
            size="middle"
            :scroll="{ x: 1000, y: 400 }"
            :loading="loading"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <component
                :is="renderTableCell"
                :column="column"
                :record="record"
              />
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>

      <!-- 无数据提示 -->
      <a-empty v-if="memberData.length === 0 && !loading" description="暂无成员数据" />


    </a-card>
  </div>
</template>

<script setup>
import {
    CalendarOutlined,
    CheckCircleOutlined,
    CommentOutlined,
    ContactsOutlined,
    DatabaseOutlined,
    GiftOutlined,
    MailOutlined,
    MessageOutlined,
    ReloadOutlined,
    RiseOutlined,
    SendOutlined,
    StarOutlined,
    SwapOutlined,
    TeamOutlined,
    TrophyOutlined,
    UserAddOutlined,
    VideoCameraOutlined,
    WechatOutlined
} from '@ant-design/icons-vue'
import { Avatar, Badge, Statistic, Tag } from 'ant-design-vue'
import { computed, h, onMounted, ref, watch } from 'vue'
import { teamDashboardService } from '../../../services/team/teamDashboard'

// 定义组件属性
const props = defineProps({
  teamId: {
    type: [Number, String],
    required: true,
    validator: (value) => {
      if (value === null || value === undefined) {
        console.warn('MemberPerformancePanel: teamId不能为null或undefined')
        return false
      }
      return true
    }
  },
  // 共享数据属性
  sharedData: {
    type: Object,
    default: null
  },
  // 成员排名数据（新增）
  memberRanking: {
    type: Object,
    default: null
  }
})

// 定义事件
const emit = defineEmits(['refresh-completed'])

// 响应式数据
const loading = ref(false)
const memberData = ref([])
const timeRange = ref('month')

// 模块化标签页相关
const activeModule = ref('wechat') // 默认显示微信运营模块

// 各模块指标数量
const wechatMetricsCount = computed(() => 9) // 微信运营核心指标数量
const talentMetricsCount = computed(() => 8) // 达人管理指标数量
const sampleMetricsCount = computed(() => 6) // 寄样管理指标数量

// 基础列定义（所有模块共用）
const baseColumns = [
  {
    title: '排名',
    dataIndex: 'ranking',
    key: 'ranking',
    fixed: 'left',
    width: 70,
    sorter: (a, b) => a.排名 - b.排名,
    defaultSortOrder: 'ascend'
  },
  {
    title: '成员',
    dataIndex: 'member',
    key: 'member',
    fixed: 'left',
    width: 180
  },
  {
    title: '绩效等级',
    dataIndex: 'performanceLevel',
    key: 'performanceLevel',
    width: 100,
    filters: [
      { text: '优秀', value: '优秀' },
      { text: '良好', value: '良好' },
      { text: '一般', value: '一般' },
      { text: '待提升', value: '待提升' }
    ],
    onFilter: (value, record) => record.绩效等级 === value
  }
]

// 微信运营模块列定义
const wechatColumns = computed(() => [
  ...baseColumns,
  {
    title: '微信账号',
    dataIndex: 'wechatCount',
    key: 'wechatCount',
    width: 100,
    sorter: (a, b) => (a.微信账号 || 0) - (b.微信账号 || 0)
  },
  {
    title: '好友总数',
    dataIndex: 'friends',
    key: 'friends',
    width: 120,
    sorter: (a, b) => (a.好友总数 || 0) - (b.好友总数 || 0)
  },
  {
    title: '今日新增',
    dataIndex: 'todayAdded',
    key: 'todayAdded',
    width: 100,
    sorter: (a, b) => (a.今日新增 || 0) - (b.今日新增 || 0)
  },
  {
    title: '发送请求数',
    dataIndex: 'friendRequests',
    key: 'friendRequests',
    width: 120,
    sorter: (a, b) => (a.发送好友请求数 || 0) - (b.发送好友请求数 || 0)
  },
  {
    title: '入库好友数',
    dataIndex: 'storedFriends',
    key: 'storedFriends',
    width: 120,
    sorter: (a, b) => (a.入库好友数 || 0) - (b.入库好友数 || 0)
  },
  {
    title: '沟通好友数',
    dataIndex: 'communicationFriends',
    key: 'communicationFriends',
    width: 120,
    sorter: (a, b) => (a.沟通好友数 || 0) - (b.沟通好友数 || 0)
  },
  {
    title: '互动好友数',
    dataIndex: 'interactionFriends',
    key: 'interactionFriends',
    width: 120,
    sorter: (a, b) => (a.互动好友数 || 0) - (b.互动好友数 || 0)
  },
  {
    title: '本周新增',
    dataIndex: 'weeklyNewFriends',
    key: 'weeklyNewFriends',
    width: 120,
    sorter: (a, b) => (a.本周新增 || 0) - (b.本周新增 || 0)
  },
  {
    title: '本月新增',
    dataIndex: 'monthlyNewFriends',
    key: 'monthlyNewFriends',
    width: 120,
    sorter: (a, b) => (a.本月新增 || 0) - (b.本月新增 || 0)
  }
])

// 达人管理模块列定义
const talentColumns = computed(() => [
  ...baseColumns,
  {
    title: '总邀约数',
    dataIndex: 'totalInvitations',
    key: 'totalInvitations',
    width: 100,
    sorter: (a, b) => (a.总邀约数 || 0) - (b.总邀约数 || 0)
  },
  {
    title: '认领达人数',
    dataIndex: 'talentCount',
    key: 'talentCount',
    width: 120,
    sorter: (a, b) => (a.认领达人数 || 0) - (b.认领达人数 || 0)
  },
  {
    title: '微信认领达人',
    dataIndex: 'wechatTalent',
    key: 'wechatTalent',
    width: 130,
    sorter: (a, b) => (a.微信认领达人 || 0) - (b.微信认领达人 || 0)
  },
  {
    title: '抖音认领达人',
    dataIndex: 'douyinTalent',
    key: 'douyinTalent',
    width: 130,
    sorter: (a, b) => (a.抖音认领达人 || 0) - (b.抖音认领达人 || 0)
  },
  {
    title: '联系方式获取',
    dataIndex: 'contactObtained',
    key: 'contactObtained',
    width: 130,
    sorter: (a, b) => (a.联系方式获取 || 0) - (b.联系方式获取 || 0)
  },
  {
    title: '好友转化',
    dataIndex: 'friendConversion',
    key: 'friendConversion',
    width: 100,
    sorter: (a, b) => (a.好友转化 || 0) - (b.好友转化 || 0)
  },
  {
    title: '今日新增达人',
    dataIndex: 'todayAddedTalent',
    key: 'todayAddedTalent',
    width: 130,
    sorter: (a, b) => (a.今日新增达人 || 0) - (b.今日新增达人 || 0)
  },
  {
    title: '本周新增达人',
    dataIndex: 'weeklyAddedTalent',
    key: 'weeklyAddedTalent',
    width: 130,
    sorter: (a, b) => (a.本周新增达人 || 0) - (b.本周新增达人 || 0)
  }
])

// 寄样管理模块列定义
const sampleColumns = computed(() => [
  ...baseColumns,
  {
    title: '申请通过数量',
    dataIndex: 'approvedSamples',
    key: 'approvedSamples',
    width: 130,
    sorter: (a, b) => (a.申请通过数量 || 0) - (b.申请通过数量 || 0)
  },
  {
    title: '实际寄样数量',
    dataIndex: 'actualSamples',
    key: 'actualSamples',
    width: 130,
    sorter: (a, b) => (a.实际寄样数量 || 0) - (b.实际寄样数量 || 0)
  },
  {
    title: '样品送达数量',
    dataIndex: 'deliveredSamples',
    key: 'deliveredSamples',
    width: 130,
    sorter: (a, b) => (a.样品送达数量 || 0) - (b.样品送达数量 || 0)
  },
  {
    title: '好友转化率',
    dataIndex: 'friendConversionRate',
    key: 'friendConversionRate',
    width: 120,
    sorter: (a, b) => (a.好友转化率 || 0) - (b.好友转化率 || 0)
  },
  {
    title: '邀约成功率',
    dataIndex: 'invitationSuccessRate',
    key: 'invitationSuccessRate',
    width: 120,
    sorter: (a, b) => (a.邀约成功率 || 0) - (b.邀约成功率 || 0)
  },
  {
    title: '样品发放率',
    dataIndex: 'sampleDeliveryRate',
    key: 'sampleDeliveryRate',
    width: 120,
    sorter: (a, b) => (a.样品发放率 || 0) - (b.样品发放率 || 0)
  }
])





// 处理成员排名数据
const processMemberRanking = (rankingData) => {
  if (!rankingData || !Array.isArray(rankingData.成员排名)) {
    memberData.value = []
    return
  }

  console.log('🔄 MemberPerformancePanel: 处理排名数据', {
    module: activeModule.value,
    memberCount: rankingData.成员排名.length
  })

  // 直接使用后端返回的数据
  memberData.value = rankingData.成员排名

}



// 获取排名样式
const getRankingStyle = (ranking) => {
  if (ranking === 1) {
    return { backgroundColor: '#f6d55c', color: '#333' } // 金色
  } else if (ranking === 2) {
    return { backgroundColor: '#c0c0c0', color: '#333' } // 银色
  } else if (ranking === 3) {
    return { backgroundColor: '#cd7f32', color: '#fff' } // 铜色
  } else {
    return { backgroundColor: '#1890ff', color: '#fff' } // 默认蓝色
  }
}

// 获取绩效等级颜色
const getPerformanceLevelColor = (level) => {
  switch (level) {
    case '优秀':
      return 'green'
    case '良好':
      return 'blue'
    case '一般':
      return 'orange'
    case '待提升':
      return 'red'
    default:
      return 'default'
  }
}

// 获取用户头像颜色
const getUserAvatarColor = (username) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068', '#108ee9']
  const index = username ? username.charCodeAt(0) % colors.length : 0
  return colors[index]
}

// 模块切换处理（实现模块化数据加载）
const handleModuleChange = async (key) => {
  if (loading.value) {
    console.log('🔄 MemberPerformancePanel: 数据加载中，忽略模块切换')
    return
  }

  const moduleNames = {
    'wechat': '微信运营',
    'talent': '达人管理',
    'sample': '寄样管理'
  }

  console.log('🔄 MemberPerformancePanel: 模块切换开始', {
    from: activeModule.value,
    to: key,
    moduleName: moduleNames[key]
  })

  // 更新当前模块
  const previousModule = activeModule.value
  activeModule.value = key

  // 清空当前数据，显示加载状态
  memberData.value = []

  try {
    // 重新获取对应模块的数据
    await fetchData()

    console.log('✅ MemberPerformancePanel: 模块切换完成', {
      module: key,
      moduleName: moduleNames[key],
      dataLoaded: memberData.value.length > 0,
      memberCount: memberData.value.length
    })
  } catch (error) {
    console.error('❌ MemberPerformancePanel: 模块切换失败', {
      module: key,
      moduleName: moduleNames[key],
      error: error.message
    })

    // 发生错误时回退到之前的模块
    activeModule.value = previousModule
  }
}

// 表格单元格渲染函数
const renderTableCell = (props) => {
  const { column, record } = props

  // 排名列
  if (column.dataIndex === 'ranking') {
    return h('div', { class: 'ranking-cell' }, [
      h(Badge, {
        count: record.排名,
        numberStyle: getRankingStyle(record.排名),
        showZero: true
      })
    ])
  }

  // 成员信息列
  if (column.dataIndex === 'member') {
    return h('div', { class: 'member-info' }, [
      h(Avatar, {
        size: 32,
        style: { backgroundColor: getUserAvatarColor(record.用户名) }
      }, () => record.用户名?.charAt(0) || 'U'),
      h('div', { class: 'member-details' }, [
        h('div', { class: 'member-name' }, record.用户名 || '未知用户'),
        h('div', { class: 'member-role' }, record.角色 || '团队成员')
      ])
    ])
  }

  // 绩效等级列
  if (column.dataIndex === 'performanceLevel') {
    const performanceLevel = record.绩效等级 || '一般'
    return h(Tag, {
      color: getPerformanceLevelColor(performanceLevel)
    }, () => performanceLevel) // 使用函数插槽避免null值警告
  }

  // 数值类型列的通用渲染
  const numericColumns = {
    // 微信运营指标
    wechatCount: { field: '微信账号', icon: WechatOutlined, color: '#1890ff' },
    friends: { field: '好友总数', icon: TeamOutlined, color: '#52c41a' },
    todayAdded: { field: '今日新增', icon: UserAddOutlined, color: '#fa8c16' },
    friendRequests: { field: '发送好友请求数', icon: SendOutlined, color: '#722ed1' },
    storedFriends: { field: '入库好友数', icon: DatabaseOutlined, color: '#13c2c2' },
    communicationFriends: { field: '沟通好友数', icon: MessageOutlined, color: '#fa8c16' },
    interactionFriends: { field: '互动好友数', icon: CommentOutlined, color: '#52c41a' },
    weeklyNewFriends: { field: '本周新增', icon: CalendarOutlined, color: '#fa8c16' },
    monthlyNewFriends: { field: '本月新增', icon: CalendarOutlined, color: '#722ed1' },

    // 达人管理指标
    totalInvitations: { field: '总邀约数', icon: MailOutlined, color: '#1890ff' },
    talentCount: { field: '认领达人数', icon: StarOutlined, color: '#722ed1' },
    wechatTalent: { field: '微信认领达人', icon: WechatOutlined, color: '#1890ff' },
    douyinTalent: { field: '抖音认领达人', icon: VideoCameraOutlined, color: '#eb2f96' },
    contactObtained: { field: '联系方式获取', icon: ContactsOutlined, color: '#13c2c2' },
    friendConversion: { field: '好友转化', icon: SwapOutlined, color: '#52c41a' },
    todayAddedTalent: { field: '今日新增达人', icon: UserAddOutlined, color: '#fa8c16' },
    weeklyAddedTalent: { field: '本周新增达人', icon: CalendarOutlined, color: '#fa8c16' },

    // 寄样管理指标
    approvedSamples: { field: '申请通过数量', icon: CheckCircleOutlined, color: '#52c41a' },
    actualSamples: { field: '实际寄样数量', icon: SendOutlined, color: '#1890ff' },
    deliveredSamples: { field: '样品送达数量', icon: GiftOutlined, color: '#fa8c16' },

    // 衍生指标（百分比）
    friendConversionRate: { field: '好友转化率', icon: RiseOutlined, color: '#52c41a', suffix: '%', precision: 1 },
    invitationSuccessRate: { field: '邀约成功率', icon: TrophyOutlined, color: '#1890ff', suffix: '%', precision: 1 },
    sampleDeliveryRate: { field: '样品发放率', icon: CheckCircleOutlined, color: '#fa8c16', suffix: '%', precision: 1 }
  }

  const columnConfig = numericColumns[column.dataIndex]
  if (columnConfig) {
    return h(Statistic, {
      value: record[columnConfig.field] || 0,
      precision: columnConfig.precision || 0,
      valueStyle: { fontSize: '14px', color: columnConfig.color },
      suffix: columnConfig.suffix || '',
      prefix: h(columnConfig.icon)
    })
  }

  // 默认渲染
  return record[column.dataIndex] || '-'
}

// 方法
const fetchData = async () => {
  // 检查teamId是否有效
  if (!props.teamId || props.teamId === null || props.teamId === undefined) {
    console.warn('MemberPerformancePanel: teamId无效，跳过数据获取')
    loading.value = false
    return
  }

  loading.value = true
  try {
    // 映射前端模块名到后端参数
    const moduleTypeMap = {
      'wechat': '微信运营',
      'talent': '达人管理',
      'sample': '寄样管理'
    }

    // 构建请求参数，确保模块类型正确传递
    const requestParams = {
      timeRange: timeRange.value,
      sortBy: '好友总数',
      limit: 50
    }

    // 只有当选择了具体模块时才传递模块类型参数
    if (activeModule.value && moduleTypeMap[activeModule.value]) {
      requestParams.moduleType = moduleTypeMap[activeModule.value]
    }

    console.log('🔄 MemberPerformancePanel: 模块化数据加载', {
      teamId: props.teamId,
      module: activeModule.value,
      moduleType: requestParams.moduleType,
      timeRange: timeRange.value,
      requestParams
    })

    const response = await teamDashboardService.getTeamMemberRanking(props.teamId, requestParams)

    // 直接处理后端返回的数据
    if (response && Array.isArray(response.成员排名)) {
      processMemberRanking(response)
      console.log('✅ MemberPerformancePanel: 模块数据加载成功', {
        module: activeModule.value,
        moduleType: requestParams.moduleType,
        memberCount: memberData.value.length
      })
    } else {
      console.error('❌ MemberPerformancePanel: 数据格式错误', response)
      memberData.value = []
    }

  } catch (error) {
    console.error('❌ MemberPerformancePanel: 获取成员绩效数据失败:', error)
    memberData.value = []
  } finally {
    loading.value = false
    // 通知父组件刷新完成
    const moduleTypeMap = {
      'wechat': '微信运营',
      'talent': '达人管理',
      'sample': '寄样管理'
    }
    emit('refresh-completed', {
      component: 'member-performance',
      module: activeModule.value,
      moduleType: moduleTypeMap[activeModule.value],
      success: memberData.value.length > 0,
      memberCount: memberData.value.length,
      timestamp: Date.now()
    })
  }
}

// 刷新数据方法
const refreshData = async () => {
  console.log('MemberPerformancePanel: 手动刷新数据')
  await fetchData()
}

// 监听时间范围变化
watch(timeRange, () => {
  console.log('🔄 MemberPerformancePanel: 时间范围变化，重新加载数据', timeRange.value)
  fetchData()
})

// 生命周期钩子
onMounted(() => {
  console.log('🔄 MemberPerformancePanel: 组件挂载，初始化数据加载')
  fetchData()
})
</script>

<style scoped>
.member-performance-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  /* 添加响应式容器支持 */
  container-type: inline-size;
  width: 100%;
  max-width: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  /* 改进响应式布局 */
  flex-wrap: wrap;
  gap: 12px;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
  /* 响应式字体调整 */
  white-space: nowrap;
}

.time-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  /* 响应式调整 */
  flex-shrink: 0;
}

.time-label {
  font-size: 14px;
  color: #666;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  /* 响应式调整 */
  flex-shrink: 0;
}

/* 简约标签页样式 - 改进响应式设计 */
.performance-tabs {
  height: 100%;
  background: #fff;
  /* 确保容器适应 */
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.performance-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 16px;
  background: #fafafa;
  padding: 4px;
  border-radius: 4px;
  /* 响应式调整 */
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}

.performance-tabs :deep(.ant-tabs-nav-wrap) {
  /* 确保标签页不会溢出 */
  overflow: hidden;
}

.performance-tabs :deep(.ant-tabs-nav-list) {
  /* 支持水平滚动 */
  display: flex;
  flex-wrap: nowrap;
  width: max-content;
  min-width: 100%;
}

.performance-tabs :deep(.ant-tabs-tab) {
  margin-right: 4px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.2s ease;
  /* 响应式调整 */
  flex-shrink: 0;
  white-space: nowrap;
}

.performance-tabs :deep(.ant-tabs-tab:hover) {
  background: #f0f0f0;
}

.performance-tabs :deep(.ant-tabs-tab-active) {
  background: #1890ff;
  color: #fff;
  border-radius: 6px 6px 0 0;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.performance-tabs :deep(.ant-tabs-tab-active .tab-title) {
  color: #fff;
  font-weight: 600;
}

.performance-tabs :deep(.ant-tabs-tab:hover:not(.ant-tabs-tab-active)) {
  background: #f0f0f0;
  border-radius: 6px 6px 0 0;
}

.performance-tabs :deep(.ant-tabs-tab:hover:not(.ant-tabs-tab-active) .tab-title) {
  color: #1890ff;
}

.performance-tabs :deep(.ant-tabs-content-holder) {
  height: calc(100% - 60px);
  padding: 0;
  /* 响应式调整 */
  overflow: hidden;
}

.performance-tabs :deep(.ant-tabs-tabpane) {
  height: 100%;
  /* 确保内容不会溢出 */
  overflow: hidden;
}

.tab-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  font-size: 14px;
  /* 响应式调整 */
  white-space: nowrap;
}

.tab-title .anticon {
  font-size: 14px;
  /* 响应式调整 */
  flex-shrink: 0;
}

/* 表格响应式优化 */
.performance-tabs :deep(.ant-table-wrapper) {
  /* 确保表格容器适应 */
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.performance-tabs :deep(.ant-table) {
  /* 表格基础样式 */
  width: 100%;
  font-size: 14px;
}

.performance-tabs :deep(.ant-table-container) {
  /* 表格容器优化 */
  width: 100%;
  max-width: 100%;
}

.performance-tabs :deep(.ant-table-body) {
  /* 表格主体优化 */
  overflow-x: auto;
  overflow-y: auto;
  max-height: 400px;
}

.performance-tabs :deep(.ant-table-thead > tr > th) {
  background: #f5f5f5;
  color: #262626;
  font-weight: 600;
  text-align: center;
  border-bottom: 2px solid #e8e8e8;
  /* 响应式调整 */
  padding: 12px 8px;
  white-space: nowrap;
  font-size: 13px;
}

.performance-tabs :deep(.ant-table-tbody > tr:hover > td) {
  background: #f9f9f9;
}

.performance-tabs :deep(.ant-table-tbody > tr > td) {
  text-align: center;
  padding: 12px 8px;
  /* 响应式调整 */
  white-space: nowrap;
  font-size: 13px;
}

/* 数值统计样式 */
.performance-tabs :deep(.ant-statistic) {
  text-align: center;
}

.performance-tabs :deep(.ant-statistic-content) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  /* 响应式调整 */
  flex-wrap: wrap;
}

.performance-tabs :deep(.ant-statistic-content-value) {
  font-size: 14px;
  font-weight: 600;
}

.performance-tabs :deep(.ant-statistic-content-suffix) {
  font-size: 12px;
  margin-left: 2px;
}

/* 排名徽章样式 */
.ranking-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ranking-cell .ant-badge {
  display: inline-flex;
}

.ranking-cell .ant-badge-count {
  min-width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 50%;
  font-weight: 600;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 成员信息样式 */
.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-start;
  text-align: left;
  /* 响应式调整 */
  min-width: 0;
  flex: 1;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  /* 响应式调整 */
  min-width: 0;
  flex: 1;
}

.member-name {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
  /* 响应式调整 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-role {
  font-size: 12px;
  color: #8c8c8c;
  /* 响应式调整 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 加载状态样式 */
.performance-tabs :deep(.ant-spin-nested-loading) {
  min-height: 300px;
}

/* 空数据状态样式 */
.performance-tabs :deep(.ant-empty) {
  margin: 40px 0;
}

/* 分页器响应式优化 */
.performance-tabs :deep(.ant-pagination) {
  margin-top: 16px;
  text-align: center;
  /* 响应式调整 */
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.performance-tabs :deep(.ant-pagination-total-text) {
  font-size: 12px;
  /* 响应式调整 */
  white-space: nowrap;
}

/* 容器查询支持 */
@container (max-width: 900px) {
  .performance-tabs :deep(.ant-table-thead > tr > th) {
    font-size: 12px;
    padding: 8px 6px;
  }
  
  .performance-tabs :deep(.ant-table-tbody > tr > td) {
    font-size: 12px;
    padding: 8px 6px;
  }
  
  .tab-title {
    font-size: 13px;
  }
  
  .tab-title .anticon {
    font-size: 13px;
  }
}

@container (max-width: 600px) {
  .performance-tabs :deep(.ant-tabs-tab) {
    padding: 6px 12px;
  }
  
  .tab-title {
    font-size: 12px;
  }
  
  .tab-title .anticon {
    font-size: 12px;
  }
  
  .performance-tabs :deep(.ant-table-thead > tr > th) {
    font-size: 11px;
    padding: 6px 4px;
  }
  
  .performance-tabs :deep(.ant-table-tbody > tr > td) {
    font-size: 11px;
    padding: 6px 4px;
  }
}

/* 响应式设计 - 精细化断点控制 */

/* 大屏幕 (1400px+) */
@media (min-width: 1400px) {
  .panel-title {
    font-size: 18px;
  }
  
  .performance-tabs :deep(.ant-tabs-tab) {
    padding: 10px 20px;
  }
  
  .tab-title {
    font-size: 15px;
  }
  
  .tab-title .anticon {
    font-size: 15px;
  }
  
  .performance-tabs :deep(.ant-table-thead > tr > th) {
    font-size: 14px;
    padding: 14px 10px;
  }
  
  .performance-tabs :deep(.ant-table-tbody > tr > td) {
    font-size: 14px;
    padding: 14px 10px;
  }
}

/* 中等屏幕 (1200px - 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .panel-header {
    gap: 16px;
  }
  
  .performance-tabs :deep(.ant-table-scroll) {
    overflow-x: auto;
  }
}

/* 平板屏幕 (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .panel-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .performance-tabs :deep(.ant-tabs-nav) {
    padding: 3px;
  }
  
  .performance-tabs :deep(.ant-tabs-tab) {
    padding: 8px 14px;
  }
  
  .tab-title {
    font-size: 13px;
  }
  
  .tab-title .anticon {
    font-size: 13px;
  }
  
  .performance-tabs :deep(.ant-table-thead > tr > th) {
    font-size: 12px;
    padding: 10px 6px;
  }
  
  .performance-tabs :deep(.ant-table-tbody > tr > td) {
    font-size: 12px;
    padding: 10px 6px;
  }
  
  .performance-tabs :deep(.ant-table-scroll) {
    overflow-x: auto;
  }
  
  .member-info {
    gap: 8px;
  }
  
  .member-name {
    font-size: 13px;
  }
  
  .member-role {
    font-size: 11px;
  }
}

/* 移动端 (最大767px) */
@media (max-width: 767px) {
  .member-performance-panel {
    padding: 0;
  }
  
  .panel-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .panel-title {
    font-size: 16px;
    text-align: center;
  }
  
  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .performance-tabs :deep(.ant-tabs-nav) {
    padding: 2px;
    margin-bottom: 12px;
    /* 支持水平滚动 */
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
  }
  
  .performance-tabs :deep(.ant-tabs-nav-wrap) {
    overflow: visible;
  }
  
  .performance-tabs :deep(.ant-tabs-nav-list) {
    min-width: max-content;
  }
  
  .performance-tabs :deep(.ant-tabs-tab) {
    padding: 6px 12px;
    margin-right: 2px;
    flex-shrink: 0;
  }
  
  .tab-title {
    font-size: 12px;
    gap: 4px;
  }
  
  .tab-title .anticon {
    font-size: 12px;
  }
  
  .performance-tabs :deep(.ant-tabs-content-holder) {
    height: calc(100% - 50px);
  }
  
  /* 表格移动端优化 */
  .performance-tabs :deep(.ant-table-wrapper) {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .performance-tabs :deep(.ant-table) {
    min-width: 800px; /* 确保表格有最小宽度 */
  }
  
  .performance-tabs :deep(.ant-table-thead > tr > th) {
    font-size: 11px;
    padding: 8px 4px;
    white-space: nowrap;
  }
  
  .performance-tabs :deep(.ant-table-tbody > tr > td) {
    font-size: 11px;
    padding: 8px 4px;
    white-space: nowrap;
  }
  
  .performance-tabs :deep(.ant-table-body) {
    max-height: 300px;
  }
  
  .member-info {
    gap: 6px;
  }
  
  .member-name {
    font-size: 12px;
  }
  
  .member-role {
    font-size: 10px;
  }
  
  .ranking-cell .ant-badge-count {
    min-width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
  }
  
  /* 分页器移动端优化 */
  .performance-tabs :deep(.ant-pagination) {
    margin-top: 12px;
    justify-content: center;
  }
  
  .performance-tabs :deep(.ant-pagination-total-text) {
    display: none; /* 移动端隐藏总数显示 */
  }
  
  .performance-tabs :deep(.ant-pagination-options) {
    display: none; /* 移动端隐藏页面大小选择器 */
  }
  
  .performance-tabs :deep(.ant-pagination-jump-prev),
  .performance-tabs :deep(.ant-pagination-jump-next) {
    display: none; /* 移动端隐藏快速跳转 */
  }
}

/* 小屏幕移动端 (最大479px) */
@media (max-width: 479px) {
  .panel-title {
    font-size: 14px;
  }
  
  .performance-tabs :deep(.ant-tabs-tab) {
    padding: 4px 8px;
  }
  
  .tab-title {
    font-size: 11px;
    gap: 2px;
  }
  
  .tab-title .anticon {
    font-size: 11px;
  }
  
  .performance-tabs :deep(.ant-table) {
    min-width: 700px; /* 小屏幕下适当减小最小宽度 */
  }
  
  .performance-tabs :deep(.ant-table-thead > tr > th) {
    font-size: 10px;
    padding: 6px 2px;
  }
  
  .performance-tabs :deep(.ant-table-tbody > tr > td) {
    font-size: 10px;
    padding: 6px 2px;
  }
  
  .member-name {
    font-size: 11px;
  }
  
  .member-role {
    font-size: 9px;
  }
  
  .ranking-cell .ant-badge-count {
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    font-size: 9px;
  }
}

/* 针对容器查询的回退方案 */
@supports not (container-type: inline-size) {
  /* 当不支持容器查询时的回退样式 */
  @media (max-width: 900px) {
    .performance-tabs :deep(.ant-table-thead > tr > th) {
      font-size: 12px;
      padding: 8px 6px;
    }
    
    .performance-tabs :deep(.ant-table-tbody > tr > td) {
      font-size: 12px;
      padding: 8px 6px;
    }
  }
  
  @media (max-width: 600px) {
    .tab-title {
      font-size: 12px;
    }
    
    .performance-tabs :deep(.ant-table-thead > tr > th) {
      font-size: 11px;
      padding: 6px 4px;
    }
    
    .performance-tabs :deep(.ant-table-tbody > tr > td) {
      font-size: 11px;
      padding: 6px 4px;
    }
  }
}

/* 横屏移动设备优化 */
@media (max-width: 1024px) and (orientation: landscape) {
  .performance-tabs :deep(.ant-table-body) {
    max-height: 250px;
  }
  
  .performance-tabs :deep(.ant-tabs-content-holder) {
    height: calc(100% - 45px);
  }
}

/* 打印样式优化 */
@media print {
  .member-performance-panel {
    background: white;
    box-shadow: none;
  }
  
  .header-actions {
    display: none;
  }
  
  .performance-tabs :deep(.ant-tabs-nav) {
    display: none;
  }
  
  .performance-tabs :deep(.ant-table) {
    font-size: 12px;
  }
  
  .performance-tabs :deep(.ant-pagination) {
    display: none;
  }
}
</style> 