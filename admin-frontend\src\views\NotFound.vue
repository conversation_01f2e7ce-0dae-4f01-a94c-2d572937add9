<template>
  <div class="notfound-container">
    <div class="notfound-svg">
      <!-- 简洁SVG插画 -->
      <svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="80" cy="80" r="80" fill="#f5f5f5"/>
        <ellipse cx="80" cy="120" rx="40" ry="10" fill="#e0e0e0"/>
        <rect x="50" y="60" width="60" height="30" rx="8" fill="#bfbfbf"/>
        <rect x="60" y="70" width="10" height="10" rx="2" fill="#fff"/>
        <rect x="90" y="70" width="10" height="10" rx="2" fill="#fff"/>
        <rect x="70" y="80" width="20" height="5" rx="2.5" fill="#fff"/>
      </svg>
    </div>
    <h2>404 - 页面未找到</h2>
    <p>抱歉，您访问的页面不存在或已被删除。</p>
    <a-button type="primary" @click="goHome" style="margin-top: 16px;">返回首页</a-button>
  </div>
</template>

<script setup>
import {useRouter} from 'vue-router';

const router = useRouter();
const goHome = () => {
  router.push('/');
};
</script>

<style scoped>
.notfound-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40px 0;
}
.notfound-svg {
  margin-bottom: 24px;
}
h2 {
  color: #333;
  margin-bottom: 8px;
}
p {
  color: #888;
  margin-bottom: 16px;
}
</style> 