import traceback
from datetime import date, datetime
from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.响应模型 import 统一响应模型
from 日志 import 应用日志器, 错误日志器

仪表板路由 = APIRouter(tags=["仪表板"])


# 寄样统计请求模型
class 寄样统计请求模型(BaseModel):
    """寄样统计数据请求模型"""

    时间范围: str = Field(
        default="本月",
        description="时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）",
        example="本月",
    )
    开始日期: Optional[date] = Field(
        default=None, description="开始日期(自定义时间范围时使用)"
    )
    结束日期: Optional[date] = Field(
        default=None, description="结束日期(自定义时间范围时使用)"
    )
    用户id: Optional[int] = Field(default=None, description="用户id")


def 获取用户id(请求对象, 当前用户: dict) -> int:
    """统一获取用户id的逻辑"""
    return (
        请求对象.用户id
        if hasattr(请求对象, "用户id") and 请求对象.用户id
        else 当前用户["id"]
    )


async def 处理接口异常(
    异常: Exception, 接口名称: str, 用户id: int = None, 请求参数: dict = None
):
    """统一的异常处理逻辑"""
    错误信息 = f"{接口名称}失败: {str(异常)}"
    错误详情 = traceback.format_exc()

    # 记录详细的错误日志
    错误日志器.error(f"[{接口名称}] 用户id: {用户id} - {错误信息}")
    错误日志器.error(f"[{接口名称}] 请求参数: {请求参数}")
    错误日志器.error(f"[{接口名称}] 错误详情: {错误详情}")

    return 统一响应模型.失败(500, 错误信息)


# ==================== 寄样统计接口 ====================


@仪表板路由.post(
    "/sample-metrics",
    summary="获取寄样统计数据",
    description="获取样品申请、寄样、送达等核心指标数据",
)
async def 获取寄样统计数据(请求: 寄样统计请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取寄样统计数据

    功能：
        获取样品寄送相关的核心业务指标数据

    参数:
    - **时间范围**: 时间范围 ('yesterday'=昨日, '1d'=今日, '7d'=7日, '30d'=30日, '90d'=90日, 'custom'=自定义)
    - **开始日期**: 自定义开始日期 (时间范围为custom时使用)
    - **结束日期**: 自定义结束日期 (时间范围为custom时使用)
    - **用户id**: 用户id (可选，不传则使用当前登录用户)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 寄样统计数据，包含:
        - 用户申请样品数量: 已通过审核的样品申请数量
        - 用户实际寄样数量: 已发货状态的样品数量
        - 用户样品送达数量: 已签收的样品数量
        - 指标卡片: 格式化的指标卡片数据
    """
    用户id = 获取用户id(请求, 用户)
    接口名称 = "获取寄样统计数据"

    try:
        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 开始处理请求")
        应用日志器.debug(f"[{接口名称}] 请求参数: 时间范围={请求.时间范围}")

        # 参数验证
        if 请求.时间范围 == "custom" and (not 请求.开始日期 or not 请求.结束日期):
            return 统一响应模型.失败(400, "自定义时间范围时必须提供开始日期和结束日期")

        # 调用服务获取寄样统计数据
        统计结果 = await 获取寄样统计数据_服务(
            用户id=用户id,
            时间范围=请求.时间范围,
            开始日期=请求.开始日期,
            结束日期=请求.结束日期,
        )

        应用日志器.info(f"[{接口名称}] 用户id: {用户id} - 成功获取寄样统计数据")
        return 统一响应模型.成功(统计结果, "获取寄样统计数据成功")

    except Exception as e:
        return await 处理接口异常(
            e,
            接口名称,
            用户id,
            {
                "时间范围": 请求.时间范围,
                "开始日期": 请求.开始日期,
                "结束日期": 请求.结束日期,
            },
        )


# ==================== 寄样统计服务函数 ====================


async def 获取寄样统计数据_服务(
    用户id: int,
    时间范围: str = "本月",
    开始日期: Optional[date] = None,
    结束日期: Optional[date] = None,
):
    """
    获取寄样统计数据服务函数

    参数:
        用户id: 用户id
        时间范围: 时间范围（支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度、自定义）
        开始日期: 开始日期
        结束日期: 结束日期

    返回:
        寄样统计数据字典
    """
    try:
        # 使用统一的时间范围工具解析时间范围
        from 工具.时间范围工具 import 时间范围工具

        开始时间, 结束时间 = 时间范围工具.解析时间范围(时间范围, 开始日期, 结束日期)

        # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
        # 查询用户相关的样品数据（通过用户产品表关联）
        统计查询 = """
        SELECT
            COUNT(CASE WHEN s.用户审核状态 = 1 AND s.审核时间 >= $1 AND s.审核时间 <= $2 THEN 1 END) as 申请通过数量,
            COUNT(CASE WHEN s.快递状态 > 0 AND s.快递状态变更时间 >= $3 AND s.快递状态变更时间 <= $4 THEN 1 END) as 实际寄样数量,
            COUNT(CASE WHEN s.快递状态 = 2 AND s.快递状态变更时间 >= $5 AND s.快递状态变更时间 <= $6 THEN 1 END) as 样品送达数量,
            COUNT(*) as 总申请数量
        FROM 样品信息记录表 s
        LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
        WHERE p.用户id = $7
        """

        统计结果列表 = await 异步连接池实例.执行查询(
            统计查询,
            (
                开始时间,
                结束时间,  # 申请通过数量时间参数
                开始时间,
                结束时间,  # 实际寄样数量时间参数
                开始时间,
                结束时间,  # 样品送达数量时间参数
                用户id,
            )
        )
        统计结果 = 统计结果列表[0] if 统计结果列表 else None

        if not 统计结果:
            统计结果 = {
                "申请通过数量": 0,
                "实际寄样数量": 0,
                "样品送达数量": 0,
                "总申请数量": 0,
            }

        # 获取时间范围对应的前缀
        def _获取时间范围前缀(时间范围: str) -> str:
            """获取时间范围对应的前缀"""
            时间前缀映射 = {
                "昨日": "昨日",
                "今日": "今日",
                "本周": "本周",
                "上周": "上周",
                "本月": "本月",
                "上月": "上月",
                "本季度": "本季度",
                "上季度": "上季度",
                "自定义": "期间",
            }
            return 时间前缀映射.get(时间范围, "期间")

        时间前缀 = _获取时间范围前缀(时间范围)

        # 构建指标卡片数据
        指标卡片 = [
            {
                "标题": f"{时间前缀}申请通过数量",
                "数值": 统计结果["申请通过数量"],
                "格式化数值": str(统计结果["申请通过数量"]),
                "趋势": f"+{统计结果['申请通过数量']}",
                "趋势数值": 统计结果["申请通过数量"],
                "趋势类型": "up" if 统计结果["申请通过数量"] > 0 else "stable",
                "图标": "CheckCircleOutlined",
                "颜色": "#52c41a",
                "描述": f"{时间前缀}已通过审核的样品申请",
            },
            {
                "标题": f"{时间前缀}实际寄样数量",
                "数值": 统计结果["实际寄样数量"],
                "格式化数值": str(统计结果["实际寄样数量"]),
                "趋势": f"+{统计结果['实际寄样数量']}",
                "趋势数值": 统计结果["实际寄样数量"],
                "趋势类型": "up" if 统计结果["实际寄样数量"] > 0 else "stable",
                "图标": "SendOutlined",
                "颜色": "#1890ff",
                "描述": f"{时间前缀}已发货的样品数量",
            },
            {
                "标题": f"{时间前缀}样品送达数量",
                "数值": 统计结果["样品送达数量"],
                "格式化数值": str(统计结果["样品送达数量"]),
                "趋势": f"+{统计结果['样品送达数量']}",
                "趋势数值": 统计结果["样品送达数量"],
                "趋势类型": "up" if 统计结果["样品送达数量"] > 0 else "stable",
                "图标": "GiftOutlined",
                "颜色": "#fa8c16",
                "描述": f"{时间前缀}用户已签收的样品",
            },
        ]

        # 计算转化率
        申请通过率 = 0
        寄样完成率 = 0
        送达成功率 = 0

        if 统计结果["总申请数量"] > 0:
            申请通过率 = round(
                (统计结果["申请通过数量"] / 统计结果["总申请数量"]) * 100, 1
            )

        if 统计结果["申请通过数量"] > 0:
            寄样完成率 = round(
                (统计结果["实际寄样数量"] / 统计结果["申请通过数量"]) * 100, 1
            )

        if 统计结果["实际寄样数量"] > 0:
            送达成功率 = round(
                (统计结果["样品送达数量"] / 统计结果["实际寄样数量"]) * 100, 1
            )

        # 构建返回数据
        返回数据 = {
            "用户申请样品数量": 统计结果["申请通过数量"],
            "用户实际寄样数量": 统计结果["实际寄样数量"],
            "用户样品送达数量": 统计结果["样品送达数量"],
            "总申请数量": 统计结果["总申请数量"],
            "申请通过率": 申请通过率,
            "寄样完成率": 寄样完成率,
            "送达成功率": 送达成功率,
            "指标卡片": 指标卡片,
            "时间范围": 时间范围,
            "统计时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

        应用日志器.info(
            f"寄样统计数据获取成功: 用户id={用户id}, 申请通过={统计结果['申请通过数量']}, 实际寄样={统计结果['实际寄样数量']}, 样品送达={统计结果['样品送达数量']}"
        )

        return 返回数据

    except Exception as e:
        错误日志器.error(f"获取寄样统计数据失败: {str(e)}")
        raise e


# 时间范围解析函数已删除，现在使用统一的时间范围工具
