import json
from datetime import datetime
from typing import Dict, Any, List, Tuple

from pydantic import ValidationError

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.微信小店模型 import 微信小店达人写入模型, 微信达人公海筛选模型
from 日志.日志配置 import logger


async def 写入或更新微信小店达人(达人数据: 微信小店达人写入模型) -> Dict[str, Any]:
    """
    将微信小店达人信息写入数据库，并处理更新时无变化的场景。
    如果达人已存在（通过finderUsername判断），则更新信息；否则，插入新记录。

    Args:
        达人数据 (微信小店达人写入模型): 包含达人信息的Pydantic模型。

    Returns:
        Dict[str, Any]: 包含操作结果（插入、更新、无变化）和达人id的字典。
    
    Raises:
        ValueError: 数据校验失败时抛出。
        IOError: 数据库操作失败时抛出。
    """
    try:
        async with 异步连接池实例.获取连接() as conn:
            # 检查达人是否已存在
            existing_talent_dict = await conn.fetchrow(
                "SELECT * FROM kol.微信达人表 WHERE finderUsername = $1",
                达人数据.finderUsername
            )

            # 准备要写入的数据，排除未设置的字段
            update_data = 达人数据.dict(exclude_unset=True)

            # 将列表转换为JSON字符串以便比较和存储
            for key in ['内容类型', '带货类目']:
                if key in update_data and isinstance(update_data[key], list):
                    update_data[key] = json.dumps(update_data[key], ensure_ascii=False)

            if existing_talent_dict:
                # 更新逻辑
                talent_id = existing_talent_dict['id']

                # 检查是否有实际变化
                has_changes = False
                for key, value in update_data.items():
                    # 数据库返回的json是str，需要统一比较
                    existing_value = existing_talent_dict.get(key)
                    if isinstance(existing_value, list):
                         existing_value = json.dumps(existing_value, ensure_ascii=False)

                    if str(value) != str(existing_value):
                        has_changes = True
                        break

                if not has_changes:
                    logger.info(f"微信小店达人 '{达人数据.finderUsername}' 信息无变化，无需更新。")
                    return {"操作": "无变化", "id": talent_id}

                # 有变化，执行更新
                update_data['更新时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                update_data.pop('finderUsername', None) # 不更新唯一标识

                set_clause = ", ".join([f'"{key}" = ${i+1}' for i, key in enumerate(update_data)])
                values = list(update_data.values())
                values.append(talent_id)

                update_sql = f"UPDATE kol.微信达人表 SET {set_clause} WHERE id = ${len(update_data)+1}"

                await conn.execute(update_sql, *values)
                logger.info(f"成功更新微信小店达人: {达人数据.finderUsername}")
                return {"操作": "更新", "id": talent_id}
            else:
                # 插入新的达人记录
                update_data['创建时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                update_data['更新时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                columns = ", ".join([f'"{key}"' for key in update_data.keys()])
                placeholders = ", ".join([f"${i+1}" for i in range(len(update_data))])
                values = list(update_data.values())

                insert_sql = f"INSERT INTO kol.微信达人表 ({columns}) VALUES ({placeholders}) RETURNING id"

                new_id = await conn.fetchval(insert_sql, *values)
                logger.info(f"成功插入新的微信小店达人: {达人数据.finderUsername}, ID: {new_id}")
                return {"操作": "插入", "id": new_id}
    except ValidationError as e:
        logger.error(f"微信小店达人数据校验失败: {e}")
        raise ValueError(f"数据格式错误: {e}")
    except Exception as e:
        logger.error(f"写入或更新微信小店达人时发生数据库错误: {e}", exc_info=True)
        # 统一抛出IOError，由服务层处理
        raise IOError(f"数据库操作失败: {e}")


async def 根据finderUsername获取微信小店达人(finderUsername: str) -> Dict[str, Any] | None:
    """根据finderUsername从kol.微信达人表获取达人信息"""
    # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:1735372800). }}
    # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
    return await 异步连接池实例.执行单行查询(
        "SELECT * FROM kol.微信达人表 WHERE finderUsername = $1",
        (finderUsername,)
    )


async def 获取或创建联系方式(联系方式: str, 类型: str) -> int:
    """
    在kol.联系方式表中获取或创建一条联系方式记录，并返回其ID。
    如果联系方式已存在，直接返回id；否则，插入新记录再返回id。
    """
    # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:1735372800). }}
    # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
    # 检查联系方式是否存在
    record = await 异步连接池实例.执行单行查询(
        "SELECT id FROM kol.联系方式表 WHERE 联系方式 = $1 AND 类型 = $2",
        (联系方式, 类型)
    )
    if record:
        return record['id']

    # 不存在则插入新记录
    new_id = await 异步连接池实例.执行单值查询(
        "INSERT INTO kol.联系方式表 (联系方式, 类型, 创建时间, 更新时间) VALUES ($1, $2, $3, $4) RETURNING id",
        (联系方式, 类型, datetime.now(), datetime.now())
    )
    return new_id


async def 关联微信小店达人与联系方式(微信达人id: int, 联系方式id: int):
    """在kol.微信达人联系方式关联表中创建关联记录，如果关联已存在则不进行任何操作。"""
    async with 异步连接池实例.获取连接() as conn:
        try:
            await conn.execute(
                "INSERT INTO kol.微信达人联系方式关联表 (微信达人id, 联系方式id) VALUES ($1, $2)",
                微信达人id, 联系方式id
            )
        except Exception:
            # 假设发生主键冲突（即关联已存在），则静默处理
            pass


async def 检查用户是否已认领微信小店达人(用户id: int, 微信达人id: int) -> bool:
    """在invite.用户达人关联表中检查用户是否已认领该达人"""
    async with 异步连接池实例.获取连接() as conn:
        result = await conn.fetchrow(
            "SELECT 1 FROM invite.用户达人关联表 WHERE 用户id = $1 AND 达人id = $2 AND 平台 = '微信' AND 状态 = 1",
            用户id, 微信达人id
        )
        return result is not None


async def 为用户认领微信小店达人(用户id: int, 微信达人id: int):
    """在invite.用户达人关联表中为用户创建认领记录"""
    async with 异步连接池实例.获取连接() as conn:
        await conn.execute(
            "INSERT INTO invite.用户达人关联表 (用户id, 达人id, 平台, 认领时间, 状态) VALUES ($1, $2, '微信', $3, 1)",
            用户id, 微信达人id, datetime.now()
        )


async def 获取微信小店达人公海列表(筛选条件: 微信达人公海筛选模型) -> List[Dict[str, Any]]:
    """
    高效获取未被认领的微信小店达人列表（公海），并支持多种筛选条件。
    使用keyset分页（基于ID）以提高性能。
    """
    async with 异步连接池实例.获取连接() as conn:
        # 基础查询语句
        query_parts = [
            "SELECT t1.id, t1.昵称, t1.内容类型, t1.带货类目, t1.粉丝数文本, t1.GMV文本, t1.有无联系方式",
            "FROM kol.微信达人表 t1",
            "LEFT JOIN invite.用户达人关联表 t2 ON t1.id = t2.达人id AND t2.平台 = '微信' AND t2.状态 = 1",
            "WHERE t2.达人id IS NULL"
        ]
        params = []

        # --- 动态构建筛选条件 ---
        param_index = 1
        if 筛选条件.昵称:
            query_parts.append(f"AND t1.昵称 LIKE ${param_index}")
            params.append(f"%{筛选条件.昵称}%")
            param_index += 1

        # 高效筛选：PostgreSQL使用jsonb操作符
        if 筛选条件.内容类型:
            query_parts.append(f"AND t1.内容类型::jsonb @> ${param_index}::jsonb")
            params.append(f'[{{"name": "{筛选条件.内容类型}"}}]')
            param_index += 1

        if 筛选条件.带货类目:
            query_parts.append(f"AND t1.带货类目::jsonb @> ${param_index}::jsonb")
            params.append(f'[{{"topCatName": "{筛选条件.带货类目}"}}]')
            param_index += 1

        if 筛选条件.粉丝数文本:
            query_parts.append(f"AND t1.粉丝数文本 = ${param_index}")
            params.append(筛选条件.粉丝数文本)
            param_index += 1

        if 筛选条件.GMV文本:
            query_parts.append(f"AND t1.GMV文本 = ${param_index}")
            params.append(筛选条件.GMV文本)
            param_index += 1

        if 筛选条件.有无联系方式 is not None:
            query_parts.append(f"AND t1.有无联系方式 = ${param_index}")
            params.append(1 if 筛选条件.有无联系方式 else 0)
            param_index += 1

        # --- 分页逻辑 ---
        if 筛选条件.lastId:
            query_parts.append(f"AND t1.id > ${param_index}")
            params.append(筛选条件.lastId)
            param_index += 1

        query_parts.append(f"ORDER BY t1.id ASC LIMIT ${param_index}")
        params.append(筛选条件.每页条数)

        # 组合并执行查询
        final_query = " ".join(query_parts)
        records = await conn.fetch(final_query, *params)

        # 将JSON字符串字段转换为Python对象
        result = []
        for record in records:
            record_dict = dict(record)
            if record_dict.get('内容类型'):
                try:
                    record_dict['内容类型'] = json.loads(record_dict['内容类型'])
                except (json.JSONDecodeError, TypeError):
                    record_dict['内容类型'] = [] # 或者其他默认值
            if record_dict.get('带货类目'):
                try:
                    record_dict['带货类目'] = json.loads(record_dict['带货类目'])
                except (json.JSONDecodeError, TypeError):
                    record_dict['带货类目'] = [] # 或者其他默认值
            result.append(record_dict)

        return result


async def 获取用户认领的微信小店达人列表(用户id: int, 页码: int, 每页条数: int) -> Tuple[List[Dict[str, Any]], int]:
    """获取指定用户已认领的微信小店达人列表"""
    async with 异步连接池实例.获取连接() as conn:
        # 计算总数
        total = await conn.fetchval(
            "SELECT COUNT(*) FROM invite.用户达人关联表 WHERE 用户id = $1 AND 平台 = '微信' AND 状态 = 1",
            用户id
        )

        # 获取分页数据
        offset = (页码 - 1) * 每页条数
        records = await conn.fetch("""
            SELECT t1.*
            FROM kol.微信达人表 t1
            JOIN invite.用户达人关联表 t2 ON t1.id = t2.达人id
            WHERE t2.用户id = $1 AND t2.平台 = '微信' AND t2.状态 = 1
            LIMIT $2 OFFSET $3
        """, 用户id, 每页条数, offset)

        return [dict(record) for record in records], total


async def 根据id获取微信小店达人(id: int) -> Dict[str, Any] | None:
    """根据id从kol.微信达人表获取达人信息"""
    async with 异步连接池实例.获取连接() as conn:
        result = await conn.fetchrow(
            "SELECT * FROM kol.微信达人表 WHERE id = $1",
            id
        )
        return dict(result) if result else None


async def 获取微信小店达人关联的联系方式(微信达人id: int) -> List[Dict[str, Any]]:
    """获取达人所有关联的联系方式"""
    async with 异步连接池实例.获取连接() as conn:
        records = await conn.fetch("""
            SELECT c.类型, c.联系方式
            FROM kol.联系方式表 c
            JOIN kol.微信达人联系方式关联表 r ON c.id = r.联系方式id
            WHERE r.微信达人id = $1
        """, 微信达人id)
        return [dict(record) for record in records]


async def 取消认领微信小店达人(用户id: int, 微信达人id: int) -> int:
    """从invite.用户达人关联表中更新用户的认领记录状态"""
    async with 异步连接池实例.获取连接() as conn:
        result = await conn.execute(
            "UPDATE invite.用户达人关联表 SET 状态 = 0 WHERE 用户id = $1 AND 达人id = $2 AND 平台 = '微信' AND 状态 = 1",
            用户id, 微信达人id
        )
        # PostgreSQL的execute返回类似"UPDATE 1"的字符串，提取影响的行数
        return int(result.split()[-1]) if result.startswith('UPDATE') else 0
 