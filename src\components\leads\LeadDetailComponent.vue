<template>
  <div class="lead-detail">
    <!-- 基础信息 -->
    <a-card title="基础信息" style="margin-bottom: 16px;">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="达人名称">
          <span class="lead-name">{{ lead.displayName }}</span>
          <a-tag v-if="lead.isHighValue" color="gold" style="margin-left: 8px;">
            高价值
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="地域信息">
          <span>
            <environment-outlined />
            {{ lead.displayLocation }}
          </span>
        </a-descriptions-item>
        <a-descriptions-item label="类别">
          <span>
            <tag-outlined />
            {{ lead.displayCategory }}
          </span>
        </a-descriptions-item>
        <a-descriptions-item label="粉丝数">
          <a-statistic 
            :value="lead.displayFansCount" 
            :value-style="{ fontSize: '16px', fontWeight: 'bold' }"
          />
        </a-descriptions-item>
        <a-descriptions-item label="线索来源">
          {{ lead.线索来源 }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ formatDate(lead.更新时间) }}
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    
    <!-- 联系方式信息 -->
    <a-card title="联系方式" style="margin-bottom: 16px;">
      <div v-if="lead.hasContact" class="contact-section">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="联系方式类型">
            <a-tag color="green">
              <phone-outlined />
              {{ lead.关联_联系方式类型 || '未知' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="联系方式内容">
            <div class="contact-content">
              <span>{{ lead.关联_联系方式内容 }}</span>
              <a-button 
                type="link" 
                size="small" 
                @click="copyContact"
                style="margin-left: 8px;"
              >
                <copy-outlined />
                复制
              </a-button>
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <div v-else class="no-contact">
        <a-empty description="暂无联系方式信息">
          <template #image>
            <phone-outlined style="font-size: 48px; color: #d9d9d9;" />
          </template>
        </a-empty>
      </div>
    </a-card>
    
    <!-- 详细信息 -->
    <a-card title="详细信息" style="margin-bottom: 16px;">
      <div class="detail-info">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item
            v-for="(value, key) in getFormattedInfo(lead.信息)"
            :key="key"
            :label="getFieldLabel(key)"
            :span="shouldSpanFullWidth(value) ? 2 : 1"
          >
            <span v-if="Array.isArray(value)">
              <a-tag
                v-for="(item, index) in value"
                :key="index"
                :color="getTagColor(index)"
              >
                {{ item }}
              </a-tag>
            </span>
            <span v-else-if="typeof value === 'object' && value !== null">
              <a-descriptions :column="1" size="small" bordered>
                <a-descriptions-item
                  v-for="(subValue, subKey) in value"
                  :key="subKey"
                  :label="getFieldLabel(subKey)"
                >
                  {{ subValue }}
                </a-descriptions-item>
              </a-descriptions>
            </span>
            <span v-else>{{ formatValue(value) }}</span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-card>
    

    
    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button type="primary" @click="editLead">
          <edit-outlined />
          编辑线索
        </a-button>
        <a-button @click="exportLead">
          <export-outlined />
          导出
        </a-button>
        <a-button @click="refreshDetail">
          <reload-outlined />
          刷新
        </a-button>
        <a-button @click="$emit('close')">
          关闭
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  EnvironmentOutlined,
  TagOutlined,
  PhoneOutlined,
  CopyOutlined,
  EditOutlined,
  ExportOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'

// 定义props
const props = defineProps({
  lead: {
    type: Object,
    required: true
  }
})

// 定义emits
const emit = defineEmits(['update', 'close'])

// 计算属性 - 已移除价值评估相关功能

// 方法
function formatDate(dateString) {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 字段标签映射
const fieldLabels = {
  '名称': '达人名称',
  '昵称': '达人昵称',
  '抖音号': '抖音账号',
  '抖音粉丝数': '粉丝数量',
  '粉丝数': '粉丝数量',
  '城市': '所在城市',
  '省份': '所在省份',
  '类别': '内容类别',
  '分类': '内容分类',
  '简介': '个人简介',
  '介绍': '个人介绍',
  '作品数': '作品数量',
  '获赞数': '获赞总数',
  '关注数': '关注数量',
  '年龄': '年龄',
  '性别': '性别',
  '认证': '认证状态',
  '标签': '标签',
  '联系方式': '联系方式',
  '微信': '微信号',
  '电话': '电话号码',
  '邮箱': '邮箱地址'
}

function getFieldLabel(key) {
  return fieldLabels[key] || key
}

function getFormattedInfo(info) {
  if (!info) return {}

  // 如果是字符串，尝试解析为JSON
  if (typeof info === 'string') {
    try {
      return JSON.parse(info)
    } catch (e) {
      return { '原始信息': info }
    }
  }

  return info
}

function shouldSpanFullWidth(value) {
  if (typeof value === 'string' && value.length > 50) return true
  if (typeof value === 'object' && value !== null) return true
  return false
}

function getTagColor(index) {
  const colors = ['blue', 'green', 'orange', 'red', 'purple', 'cyan']
  return colors[index % colors.length]
}

function formatValue(value) {
  if (value === null || value === undefined) return '未知'
  if (typeof value === 'number') {
    // 格式化数字显示
    if (value >= 10000) {
      return (value / 10000).toFixed(1) + '万'
    }
    return value.toLocaleString()
  }
  return String(value)
}



async function copyContact() {
  try {
    await navigator.clipboard.writeText(props.lead.关联_联系方式内容)
    message.success('联系方式已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

function editLead() {
  emit('update', props.lead)
}

function exportLead() {
  // TODO: 实现单个线索导出
  message.info('导出功能开发中')
}

function refreshDetail() {
  // TODO: 实现详情刷新
  message.info('刷新功能开发中')
}
</script>

<style scoped>
.lead-detail {
  padding: 0;
}

.lead-name {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.contact-section {
  /* 联系方式样式 */
}

.contact-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.no-contact {
  text-align: center;
  padding: 40px 0;
}

.detail-info h4 {
  margin-bottom: 12px;
  color: #262626;
}

.json-info {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.value-assessment {
  /* 价值评估样式 */
}

.value-level,
.contact-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #8c8c8c;
}

.value-factors h4 {
  margin-bottom: 16px;
  color: #262626;
}

.factor-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.factor-label {
  font-size: 12px;
  color: #8c8c8c;
}

.factor-value {
  font-weight: 500;
  color: #262626;
}

.action-buttons {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 描述列表样式优化 */
:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background: #fafafa;
}

:deep(.ant-descriptions-item-content) {
  background: #fff;
}
</style>
