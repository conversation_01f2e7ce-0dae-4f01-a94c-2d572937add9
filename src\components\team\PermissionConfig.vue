<template>
  <div class="permission-config">
    <a-spin :spinning="loading">
      <!-- 自定义权限模式 -->
      <div class="custom-permissions">
        <!-- 权限搜索和批量操作 -->
        <div class="permission-toolbar">
          <a-row :gutter="16" align="middle">
            <a-col :span="12">
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索权限..."
                allow-clear
                @search="filterPermissions"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="12">
              <a-space>
                <a-button @click="selectAllPermissions" size="small">
                  全选
                </a-button>
                <a-button @click="clearAllPermissions" size="small">
                  清空
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </div>

        <div v-if="filteredCategories.length > 0">
          <a-row :gutter="[16, 16]">
            <a-col
              v-for="category in filteredCategories"
              :key="category.分类ID || category.id"
              :span="layout === 'grid' ? 12 : 24"
            >
              <a-card 
                :title="category.分类名称 || category.name" 
                size="small" 
                class="permission-category-card"
              >
                <template #extra>
                  <a-space>
                    <a-tag :color="getCategoryColor(category)">
                      {{ getSelectedCount(category) }}/{{ category.权限列表.length }}
                    </a-tag>
                    <a-button 
                      type="link" 
                      size="small" 
                      @click="toggleCategoryAll(category)"
                    >
                      {{ isCategoryAllSelected(category) ? '取消全选' : '全选' }}
                    </a-button>
                  </a-space>
                </template>
                
                <a-checkbox-group 
                  v-model:value="selectedPermissions" 
                  @change="handlePermissionChange"
                >
                  <div
                    v-for="permission in category.权限列表 || category.permissions"
                    :key="permission.权限名称 || permission.name"
                    class="permission-item"
                  >
                    <a-checkbox 
                      :value="permission.权限名称 || permission.name"
                      :disabled="isPermissionDisabled(permission)"
                    >
                      <div class="permission-info">
                        <span class="permission-name">
                          {{ permission.权限名称 || permission.name }}
                        </span>
                        <a-typography-text 
                          v-if="showDescription && (permission.权限描述 || permission.description)" 
                          type="secondary" 
                          class="permission-desc"
                        >
                          {{ permission.权限描述 || permission.description }}
                        </a-typography-text>
                      </div>
                    </a-checkbox>
                  </div>
                </a-checkbox-group>
              </a-card>
            </a-col>
          </a-row>
        </div>
        
        <!-- 加载状态 -->
        <div v-else class="loading-permissions">
          <a-spin tip="加载权限配置中...">
            <div style="height: 200px;"></div>
          </a-spin>
        </div>
      </div>
      
      <!-- 权限总览 -->
      <div v-if="showSummary" class="permission-summary">
        <a-divider>权限总览</a-divider>
        <a-tag 
          v-for="permission in selectedPermissionDetails"
          :key="permission.权限名称 || permission.name"
          color="blue"
          style="margin: 4px"
        >
          {{ permission.权限名称 || permission.name }}
        </a-tag>
        <div v-if="selectedPermissions.length === 0" class="no-permissions">
          <a-typography-text type="secondary">
            暂未选择任何权限
          </a-typography-text>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { teamPermissionService } from '../../services/team/teamPermission'

defineOptions({
  name: 'PermissionConfig'
})

const props = defineProps({
  // 基础配置
  modelValue: {
    type: Array,
    default: () => []
  },
  team: {
    type: Object,
    required: true
  },
  
  // UI配置
  layout: {
    type: String,
    default: 'grid', // 'grid' | 'list'
    validator: value => ['grid', 'list'].includes(value)
  },
  showDescription: {
    type: Boolean,
    default: true
  },
  showSummary: {
    type: Boolean,
    default: true
  },
  showModeSelector: {
    type: Boolean,
    default: false
  },
  
  // 功能配置
  disabledPermissions: {
    type: Array,
    default: () => []
  },
  defaultMode: {
    type: String,
    default: 'custom', // 'preset' | 'custom'
    validator: value => ['preset', 'custom'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'permissions-change'])

// 响应式数据
const selectedPermissions = ref([...props.modelValue])
const permissionCategories = ref([])
const loading = ref(false)
const searchKeyword = ref('')

// 计算属性
const teamId = computed(() => props.team?.团队id || props.team?.id)

// 过滤后的权限分类
const filteredCategories = computed(() => {
  if (!searchKeyword.value.trim()) {
    return permissionCategories.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return permissionCategories.value
    .map(category => ({
      ...category,
      权限列表: (category.权限列表 || []).filter(permission => 
        (permission.权限名称 || '').toLowerCase().includes(keyword) ||
        (permission.权限描述 || '').toLowerCase().includes(keyword)
      )
    }))
    .filter(category => category.权限列表.length > 0)
})

const selectedPermissionDetails = computed(() => {
  const allPermissions = []
  permissionCategories.value.forEach(category => {
    const permissions = category.权限列表 || category.permissions || []
    allPermissions.push(...permissions)
  })
  
  return allPermissions.filter(permission => 
    selectedPermissions.value.includes(permission.权限名称 || permission.name)
  )
})

// 方法
const loadPermissions = async () => {
  if (!teamId.value) return
  
  loading.value = true
  try {
    // 调用权限服务获取所有权限列表
    const response = await teamPermissionService.getPermissionList()

    if (response && response.status === 100) {
      // 获取按分类组织的权限数据
      const permissionData = response.data
      
      // 格式化数据结构以适配组件
      const formattedCategories = Object.keys(permissionData).map(categoryName => ({
        分类名称: categoryName,
        权限列表: permissionData[categoryName].map(p => ({
          权限名称: p.权限名称, 
          权限描述: p.权限描述 || `${p.权限名称}权限`
        }))
      }))
      
      // 设置权限分类数据
      permissionCategories.value = formattedCategories
      
      console.log('权限配置加载成功，共加载', 
        formattedCategories.reduce((total, cat) => total + cat.权限列表.length, 0), 
        '项权限，分', formattedCategories.length, '个分类')
    } else {
      // 权限加载失败，提示用户
      message.error('权限列表加载失败: ' + (response?.message || '服务器返回错误'))
    }
  } catch (error) {
    // 处理异常情况
    message.error('加载权限配置失败: ' + (error.message || '网络异常'))
    console.error('加载权限配置失败:', error)
  } finally {
    loading.value = false
  }
}

// 权限选择处理
const handlePermissionChange = () => {
  emit('update:modelValue', selectedPermissions.value)
  emit('permissions-change', selectedPermissions.value)
}

const toggleCategoryAll = (category) => {
  const categoryPermissions = (category.权限列表 || []).map(p => p.权限名称 || p.name)
  const allSelected = isCategoryAllSelected(category)
  
  if (allSelected) {
    // 如果全部已选，则取消全选
    selectedPermissions.value = selectedPermissions.value.filter(p => !categoryPermissions.includes(p))
  } else {
    // 否则，添加所有未选中的
    categoryPermissions.forEach(p => {
      if (!selectedPermissions.value.includes(p)) {
        selectedPermissions.value.push(p)
      }
    })
  }
  handlePermissionChange()
}

const selectAllPermissions = () => {
  const allPermissionNames = []
  permissionCategories.value.forEach(category => {
    (category.权限列表 || []).forEach(p => {
      allPermissionNames.push(p.权限名称 || p.name)
    })
  })
  selectedPermissions.value = [...new Set(allPermissionNames)]
  handlePermissionChange()
}

const clearAllPermissions = () => {
  selectedPermissions.value = []
  handlePermissionChange()
}

// 权限项是否禁用
const isPermissionDisabled = (permission) => {
  return props.disabledPermissions.includes(permission.权限名称 || permission.name)
}

// 获取分类下的选中数量
const getSelectedCount = (category) => {
  const categoryPermissions = (category.权限列表 || []).map(p => p.权限名称 || p.name)
  return selectedPermissions.value.filter(p => categoryPermissions.includes(p)).length
}

// 判断分类是否全部选中
const isCategoryAllSelected = (category) => {
  const categoryPermissions = (category.权限列表 || []).map(p => p.权限名称 || p.name)
  return categoryPermissions.every(p => selectedPermissions.value.includes(p))
}

const getCategoryColor = (category) => {
  const colors = ['#f50', '#2db7f5', '#87d068', '#108ee9', '#eb2f96']
  const hash = (category.分类名称 || category.name).split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  return colors[hash % colors.length]
}

// 搜索权限
const filterPermissions = () => {
  // 搜索功能由计算属性 filteredCategories 自动处理
}

// 监听
watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(selectedPermissions.value)) {
    selectedPermissions.value = [...newValue]
  }
}, { deep: true })

watch(teamId, (newId) => {
  if (newId) {
    loadPermissions()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (teamId.value) {
    loadPermissions()
  }
})

// 暴露方法给父组件
defineExpose({
  loadPermissions
})
</script>

<style scoped>
.permission-config {
  width: 100%;
}

.permission-toolbar {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.permission-category-card {
  margin-bottom: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  height: 100%;
}

.permission-category-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.permission-item {
  display: block;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.permission-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.permission-info {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.permission-name {
  font-weight: 500;
  color: #262626;
}

.permission-desc {
  font-size: 12px;
  margin-top: 4px;
  color: #8c8c8c;
}

.permission-summary {
  margin-top: 24px;
  padding-top: 16px;
}

.no-permissions {
  text-align: center;
  padding: 20px;
}

.loading-permissions {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .permission-config :deep(.ant-col) {
    span: 24;
  }
}
</style> 