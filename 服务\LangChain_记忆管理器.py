"""
LangChain记忆管理器

功能：
1. 对话历史管理
2. 上下文窗口控制
3. 记忆压缩和总结
4. 会话状态管理
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# LangChain记忆组件
LANGCHAIN_AVAILABLE = False
try:
    from langchain.memory import ConversationSummaryBufferMemory  # type: ignore
    from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage  # type: ignore
    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 创建占位符类
    class ConversationBufferMemory:  # type: ignore
        def __init__(self, **kwargs):  # type: ignore
            pass

    class ConversationSummaryBufferMemory:  # type: ignore
        def __init__(self, **kwargs):  # type: ignore
            pass

    class BaseMessage:  # type: ignore
        def __init__(self, content: str = ""):
            self.content = content

    class HumanMessage(BaseMessage):  # type: ignore
        pass

    class AIMessage(BaseMessage):  # type: ignore
        pass

    class SystemMessage(BaseMessage):  # type: ignore
        pass

    class BaseChatMessageHistory:  # type: ignore
        pass

# 配置日志
记忆日志器 = logging.getLogger("LangChain.记忆管理器")

class 简单对话历史:
    """简单的对话历史实现"""
    
    def __init__(self):
        self.messages: List[Dict[str, str]] = []
    
    def add_user_message(self, message: str):
        self.messages.append({"role": "user", "content": message, "timestamp": datetime.now().isoformat()})
    
    def add_ai_message(self, message: str):
        self.messages.append({"role": "assistant", "content": message, "timestamp": datetime.now().isoformat()})
    
    def add_system_message(self, message: str):
        self.messages.append({"role": "system", "content": message, "timestamp": datetime.now().isoformat()})
    
    def get_messages(self) -> List[Dict[str, str]]:
        return self.messages.copy()
    
    def clear(self):
        self.messages.clear()
    
    def get_last_n_messages(self, n: int) -> List[Dict[str, str]]:
        return self.messages[-n:] if n > 0 else []

class LangChain记忆管理器:
    """LangChain记忆管理器"""
    
    def __init__(self):
        self.会话记忆 = {}  # 会话ID -> 记忆实例
        self.会话配置 = {}  # 会话ID -> 配置
        self.已初始化 = False
        self.默认窗口大小 = 10
        self.最大会话数 = 1000
        self.会话过期时间 = timedelta(hours=24)
    
    async def 初始化(self):
        """初始化记忆管理器"""
        try:
            self.已初始化 = True
            记忆日志器.info("记忆管理器初始化成功")
            return True
            
        except Exception as e:
            记忆日志器.error(f"记忆管理器初始化失败: {str(e)}")
            return False
    
    async def 创建会话记忆(self, 会话id: str, 配置: Optional[Dict[str, Any]] = None) -> bool:
        """创建新的会话记忆"""
        try:
            if not self.已初始化:
                await self.初始化()
            
            # 清理过期会话
            await self._清理过期会话()
            
            # 检查会话数量限制
            if len(self.会话记忆) >= self.最大会话数:
                await self._清理最旧会话()
            
            # 设置默认配置
            默认配置 = {
                "窗口大小": self.默认窗口大小,
                "记忆类型": "buffer",  # buffer, summary_buffer
                "最大令牌数": 4000,
                "创建时间": datetime.now(),
                "最后访问时间": datetime.now()
            }
            
            if 配置:
                默认配置.update(配置)
            
            self.会话配置[会话id] = 默认配置
            
            # 创建记忆实例
            if LANGCHAIN_AVAILABLE and 默认配置["记忆类型"] == "summary_buffer":
                # 使用总结缓冲记忆（需要LLM支持）
                try:
                    self.会话记忆[会话id] = ConversationSummaryBufferMemory(
                        max_token_limit=默认配置["最大令牌数"],  # type: ignore
                        return_messages=True  # type: ignore
                    )
                except Exception:
                    # 如果参数不支持，使用简化版本
                    self.会话记忆[会话id] = ConversationSummaryBufferMemory()
            else:
                # 使用简单对话历史
                self.会话记忆[会话id] = 简单对话历史()
            
            记忆日志器.info(f"创建会话记忆成功: {会话id}")
            return True
            
        except Exception as e:
            记忆日志器.error(f"创建会话记忆失败 {会话id}: {str(e)}")
            return False
    
    async def 获取会话记忆(self, 会话id: str) -> Optional[Any]:
        """获取会话记忆"""
        try:
            if 会话id not in self.会话记忆:
                # 自动创建会话记忆
                await self.创建会话记忆(会话id)
            
            # 更新最后访问时间
            if 会话id in self.会话配置:
                self.会话配置[会话id]["最后访问时间"] = datetime.now()
            
            return self.会话记忆.get(会话id)
            
        except Exception as e:
            记忆日志器.error(f"获取会话记忆失败 {会话id}: {str(e)}")
            return None
    
    async def 添加用户消息(self, 会话id: str, 消息: str) -> bool:
        """添加用户消息到会话记忆"""
        try:
            记忆实例 = await self.获取会话记忆(会话id)
            if not 记忆实例:
                return False
            
            if isinstance(记忆实例, 简单对话历史):
                记忆实例.add_user_message(消息)
            elif LANGCHAIN_AVAILABLE and hasattr(记忆实例, 'chat_memory'):
                记忆实例.chat_memory.add_user_message(消息)
            
            # 检查窗口大小限制
            await self._检查窗口大小(会话id)
            
            记忆日志器.debug(f"添加用户消息成功: {会话id}")
            return True
            
        except Exception as e:
            记忆日志器.error(f"添加用户消息失败 {会话id}: {str(e)}")
            return False
    
    async def 添加AI消息(self, 会话id: str, 消息: str) -> bool:
        """添加AI消息到会话记忆"""
        try:
            记忆实例 = await self.获取会话记忆(会话id)
            if not 记忆实例:
                return False
            
            if isinstance(记忆实例, 简单对话历史):
                记忆实例.add_ai_message(消息)
            elif LANGCHAIN_AVAILABLE and hasattr(记忆实例, 'chat_memory'):
                记忆实例.chat_memory.add_ai_message(消息)
            
            # 检查窗口大小限制
            await self._检查窗口大小(会话id)
            
            记忆日志器.debug(f"添加AI消息成功: {会话id}")
            return True
            
        except Exception as e:
            记忆日志器.error(f"添加AI消息失败 {会话id}: {str(e)}")
            return False
    
    async def 获取对话历史(self, 会话id: str, 最近n条: Optional[int] = None) -> List[Dict[str, str]]:
        """获取对话历史"""
        try:
            记忆实例 = await self.获取会话记忆(会话id)
            if not 记忆实例:
                return []
            
            if isinstance(记忆实例, 简单对话历史):
                if 最近n条:
                    return 记忆实例.get_last_n_messages(最近n条)
                return 记忆实例.get_messages()
            
            elif LANGCHAIN_AVAILABLE and hasattr(记忆实例, 'chat_memory'):
                历史消息列表 = []
                for 单条消息 in 记忆实例.chat_memory.messages:
                    if isinstance(单条消息, HumanMessage):
                        历史消息列表.append({"role": "user", "content": getattr(单条消息, 'content', str(单条消息))})
                    elif isinstance(单条消息, AIMessage):
                        历史消息列表.append({"role": "assistant", "content": getattr(单条消息, 'content', str(单条消息))})
                    elif isinstance(单条消息, SystemMessage):
                        历史消息列表.append({"role": "system", "content": getattr(单条消息, 'content', str(单条消息))})

                if 最近n条:
                    return 历史消息列表[-最近n条:]
                return 历史消息列表
            
            return []
            
        except Exception as e:
            记忆日志器.error(f"获取对话历史失败 {会话id}: {str(e)}")
            return []
    
    async def 清除会话记忆(self, 会话id: str) -> bool:
        """清除会话记忆"""
        try:
            if 会话id in self.会话记忆:
                记忆实例 = self.会话记忆[会话id]
                
                if isinstance(记忆实例, 简单对话历史):
                    记忆实例.clear()
                elif LANGCHAIN_AVAILABLE and hasattr(记忆实例, 'clear'):
                    记忆实例.clear()
                
                记忆日志器.info(f"清除会话记忆成功: {会话id}")
                return True
            
            return False
            
        except Exception as e:
            记忆日志器.error(f"清除会话记忆失败 {会话id}: {str(e)}")
            return False
    
    async def 删除会话(self, 会话id: str) -> bool:
        """删除整个会话"""
        try:
            if 会话id in self.会话记忆:
                del self.会话记忆[会话id]
            
            if 会话id in self.会话配置:
                del self.会话配置[会话id]
            
            记忆日志器.info(f"删除会话成功: {会话id}")
            return True
            
        except Exception as e:
            记忆日志器.error(f"删除会话失败 {会话id}: {str(e)}")
            return False
    
    async def _检查窗口大小(self, 会话id: str):
        """检查并维护窗口大小"""
        try:
            配置 = self.会话配置.get(会话id, {})
            窗口大小 = 配置.get("窗口大小", self.默认窗口大小)
            
            记忆实例 = self.会话记忆.get(会话id)
            if not 记忆实例:
                return
            
            if isinstance(记忆实例, 简单对话历史):
                消息列表 = 记忆实例.get_messages()
                if len(消息列表) > 窗口大小 * 2:  # 用户+AI消息对
                    # 保留最近的消息
                    保留消息 = 消息列表[-(窗口大小 * 2):]
                    记忆实例.clear()
                    记忆实例.messages = 保留消息
            
        except Exception as e:
            记忆日志器.error(f"检查窗口大小失败 {会话id}: {str(e)}")
    
    async def _清理过期会话(self):
        """清理过期会话"""
        try:
            当前时间 = datetime.now()
            过期会话 = []
            
            for 会话id, 配置 in self.会话配置.items():
                最后访问时间 = 配置.get("最后访问时间", 配置.get("创建时间", 当前时间))
                if 当前时间 - 最后访问时间 > self.会话过期时间:
                    过期会话.append(会话id)
            
            for 会话id in 过期会话:
                await self.删除会话(会话id)
            
            if 过期会话:
                记忆日志器.info(f"清理过期会话: {len(过期会话)} 个")
            
        except Exception as e:
            记忆日志器.error(f"清理过期会话失败: {str(e)}")
    
    async def _清理最旧会话(self):
        """清理最旧的会话"""
        try:
            if len(self.会话记忆) < self.最大会话数:
                return
            
            # 按最后访问时间排序
            会话列表 = []
            for 会话id, 配置 in self.会话配置.items():
                最后访问时间 = 配置.get("最后访问时间", 配置.get("创建时间", datetime.now()))
                会话列表.append((会话id, 最后访问时间))
            
            会话列表.sort(key=lambda x: x[1])
            
            # 删除最旧的会话
            需要删除数量 = len(self.会话记忆) - self.最大会话数 + 1
            for i in range(需要删除数量):
                if i < len(会话列表):
                    会话id = 会话列表[i][0]
                    await self.删除会话(会话id)
            
            记忆日志器.info(f"清理最旧会话: {需要删除数量} 个")
            
        except Exception as e:
            记忆日志器.error(f"清理最旧会话失败: {str(e)}")
    
    async def 获取会话统计(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        try:
            总会话数 = len(self.会话记忆)
            总消息数 = 0
            
            for 会话id in self.会话记忆:
                对话历史 = await self.获取对话历史(会话id)
                总消息数 += len(对话历史)
            
            return {
                "总会话数": 总会话数,
                "总消息数": 总消息数,
                "平均每会话消息数": 总消息数 / 总会话数 if 总会话数 > 0 else 0,
                "最大会话数": self.最大会话数,
                "默认窗口大小": self.默认窗口大小
            }
            
        except Exception as e:
            记忆日志器.error(f"获取会话统计失败: {str(e)}")
            return {}
    
    def 获取状态(self) -> Dict[str, Any]:
        """获取记忆管理器状态"""
        return {
            "已初始化": self.已初始化,
            "活跃会话数": len(self.会话记忆),
            "LangChain可用": LANGCHAIN_AVAILABLE,
            "最大会话数": self.最大会话数,
            "默认窗口大小": self.默认窗口大小
        }

# 创建全局记忆管理器实例
LangChain记忆管理器实例 = LangChain记忆管理器()
