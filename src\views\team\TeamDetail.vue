<template>
  <div class="team-detail">
    <!-- 面包屑导航 -->
    <a-breadcrumb class="breadcrumb">
      <a-breadcrumb-item>
        <router-link to="/team/overview">团队总览</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>{{ teamInfo.团队名称 || '团队详情' }}</a-breadcrumb-item>
    </a-breadcrumb>

    <!-- 团队头部信息区域 -->
    <a-card class="team-header-card" :loading="loading">
      <div class="team-header">
        <div class="team-avatar-section">
          <a-avatar 
            :size="80"
            :style="{ backgroundColor: getTeamAvatarColor(teamInfo.团队名称) }"
          >
            {{ (teamInfo.团队名称 || 'T').charAt(0) }}
          </a-avatar>
        </div>
        
        <div class="team-info-section">
          <div class="team-title-row">
            <h1 class="team-name">{{ teamInfo.团队名称 }}</h1>
            <div class="team-tags">
                          <a-tag v-if="permissionStatus?.用户角色" :color="getRoleColor(permissionStatus.用户角色)" size="large">
              {{ permissionStatus.用户角色 }}
            </a-tag>
              <a-tag v-if="teamInfo.团队状态" :color="getStatusColor(teamInfo.团队状态)" size="large">
                {{ teamInfo.团队状态 }}
              </a-tag>
            </div>
          </div>
          
          <p v-if="teamInfo.团队描述" class="team-description">
            {{ teamInfo.团队描述 }}
          </p>
          
          <div class="team-meta-info">
            <div class="meta-item">
              <UserOutlined />
              <span>{{ teamInfo.当前成员数 || 0 }}/{{ teamInfo.最大成员数 || 100 }} 成员</span>
            </div>
            <div v-if="teamInfo.公司名称" class="meta-item">
              <BankOutlined />
              <span>{{ teamInfo.公司名称 }}</span>
            </div>
            <div class="meta-item">
              <CalendarOutlined />
              <span>创建于 {{ formatDate(teamInfo.创建时间) }}</span>
            </div>
          </div>
        </div>
        
        <div class="team-actions-section">
          <!-- 邀请成员 - 突出显示 -->
          <a-button 
            v-if="canInviteMembers(teamInfo)"
            type="primary" 
            size="large"
            :disabled="!canInviteMembers(teamInfo)"
            @click="handleInviteMember"
            class="invite-btn"
          >
            <template #icon><UserAddOutlined /></template>
            邀请成员
          </a-button>
          
          <!-- 显示邀请权限状态提示 -->
          <a-tooltip v-if="!canInviteMembers(teamInfo)" title="您没有邀请成员的权限">
            <a-button 
              type="default" 
              size="large"
              disabled
              class="invite-btn-disabled"
            >
              <template #icon><UserAddOutlined /></template>
              邀请成员
            </a-button>
          </a-tooltip>
          
          <!-- 其他快捷操作 -->
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleQuickAction">
                <a-menu-item 
                  key="editTeam"
                  v-if="hasEnhancedPermission(teamInfo, 'team_manage')"
                >
                  <EditOutlined />
                  编辑团队
                </a-menu-item>
                <a-menu-item 
                  key="managePermissions" 
                  v-if="hasEnhancedPermission(teamInfo, 'permission_manage')"
                >
                  <SafetyCertificateOutlined />
                  权限管理
                </a-menu-item>
                <a-menu-divider v-if="isFounder(teamInfo) || !isFounder(teamInfo)" />
                <!-- 创始人操作 - 只有创建人才能看到这些操作 -->
                <template v-if="isFounder(teamInfo)">
                  <a-menu-item key="smartLeaveTeam">
                    <LogoutOutlined />
                    智能退出
                  </a-menu-item>
                  <a-menu-item key="transferOwnership">
                    <SwapOutlined />
                    转移所有权
                  </a-menu-item>
                  <a-menu-item key="dissolveTeam" danger>
                    <DeleteOutlined />
                    解散团队
                  </a-menu-item>
                </template>
                <!-- 非创始人操作 - 普通成员的操作 -->
                <template v-else>
                  <a-menu-item key="leaveTeam">
                    <LogoutOutlined />
                    退出团队
                  </a-menu-item>
                </template>
              </a-menu>
            </template>
            <a-button size="large">
              更多操作
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </div>
      </div>
    </a-card>

    <!-- 功能模块标签页 -->
    <a-card class="content-card">
      <a-tabs v-model:activeKey="activeTab" type="card" size="large">
        <!-- 团队概况 -->
        <a-tab-pane key="overview" tab="团队概况">
          <team-overview-tab 
            :team="teamInfo" 
            :loading="loading" 
            @refresh="loadTeamDetail" 
          />
        </a-tab-pane>
        
        <!-- 成员管理 -->
        <a-tab-pane key="members" tab="成员管理">
          <!-- 邀请成员提示 -->
          <div v-if="canInviteMembers(teamInfo)" class="invite-tip">
            <a-alert
              type="info"
              show-icon
              message="您可以邀请新成员加入团队"
              description="点击上方的邀请成员按钮开始邀请流程"
              closable
            />
          </div>
          
          <team-members-tab 
            :team="teamInfo" 
            :can-invite="canInviteMembers(teamInfo)"
            :can-manage="hasEnhancedPermission(teamInfo, 'member_manage')"
            @invite-member="handleInviteMember"
            @refresh="loadTeamDetail"
          />
        </a-tab-pane>
        
        <!-- 邀请管理 -->
        <a-tab-pane 
          key="invitations" 
          tab="邀请管理"
          v-if="hasEnhancedPermission(teamInfo, '邀请成员')"
        >
          <invitation-list-tab 
            :team="teamInfo" 
            @refresh="loadTeamDetail"
          />
        </a-tab-pane>
        
        <!-- 团队设置 -->
        <a-tab-pane 
          key="settings" 
          tab="团队设置"
          v-if="hasEnhancedPermission(teamInfo, 'team_manage')"
        >
          <team-settings-tab 
            :team="teamInfo" 
            @refresh="loadTeamDetail"
            @team-updated="handleTeamUpdated"
          />
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 邀请成员弹窗 -->
    <invite-member-modal
      v-model:open="showInviteModal"
      :team="teamInfo"
      @success="handleInviteSuccess"
    />

    <!-- 权限管理弹窗 -->
    <permission-modal
      v-model:open="showPermissionModal"
      :team="teamInfo"
      @success="handlePermissionSuccess"
    />

    <!-- 转移所有权弹窗 -->
    <transfer-ownership-modal
      v-model="showTransferModal"
      :team-info="transferModalData.teamInfo"
      :available-members="transferModalData.availableMembers"
      @success="handleTransferSuccess"
      @cancel="handleTransferCancel"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { 
  UserOutlined,
  BankOutlined,
  CalendarOutlined,
  UserAddOutlined,
  EditOutlined,
  SafetyCertificateOutlined,
  SwapOutlined,
  DeleteOutlined,
  LogoutOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
// 导入服务层
import teamService from '../../services/team'
import { teamBasicService } from '../../services/team/teamBasic'
import { usePermissions } from '../../composables/usePermissions'
// 导入用户状态管理
import { useUserStore } from '../../store/user'

// 异步导入组件 - 提高页面加载性能
const InviteMemberModal = defineAsyncComponent(() => import('../../components/team/InviteMemberModal.vue'))
const PermissionModal = defineAsyncComponent(() => import('../../components/team/PermissionModal.vue'))
const TransferOwnershipModal = defineAsyncComponent(() => import('../../components/team/TransferOwnershipModal.vue'))
const TeamOverviewTab = defineAsyncComponent(() => import('../../components/team/TeamOverviewTab.vue'))
const TeamMembersTab = defineAsyncComponent(() => import('../../components/team/TeamMembersTab.vue'))
const InvitationListTab = defineAsyncComponent(() => import('../../components/team/InvitationListTab.vue'))
const TeamSettingsTab = defineAsyncComponent(() => import('../../components/team/TeamSettingsTab.vue'))

defineOptions({
  name: 'TeamDetail'
})

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const { hasPermission } = usePermissions()

// 响应式数据
const loading = ref(false)
const teamInfo = reactive({})
const activeTab = ref('members') // 默认显示成员管理页面
const permissionStatus = ref(null) // 用户在当前团队的权限状态

// 模态框控制状态
const showInviteModal = ref(false)
const showPermissionModal = ref(false)
const showTransferModal = ref(false)

// 转移所有权相关数据
const transferModalData = ref({
  teamInfo: {},
  availableMembers: []
})

/**
 * 从路由参数获取团队id
 * 确保ID为有效的数字类型
 */
const teamId = computed(() => {
  const id = parseInt(route.params.teamId)
  return isNaN(id) ? null : id
})

/**
 * 检查用户是否可以邀请成员
 * 基于权限状态接口的数据进行判断
 * @param {Object} team - 团队信息对象
 * @returns {boolean} 是否可以邀请成员
 */
const canInviteMembers = (team) => {
  if (!team || !permissionStatus.value) return false
  
  // 构建增强的团队对象，包含权限状态信息
  const enhancedTeam = {
    ...team,
    权限状态: permissionStatus.value,
    当前用户id: userStore.userInfo?.id || '当前登录用户'
  }
  
  return hasPermission(enhancedTeam, '邀请成员')
}

/**
 * 检查用户是否具有特定权限
 * @param {Object} team - 团队信息对象
 * @param {string} permissionCode - 权限代码
 * @returns {boolean} 是否具有该权限
 */
const hasEnhancedPermission = (team, permissionCode) => {
  if (!team || !permissionCode || !permissionStatus.value) return false
  
  const enhancedTeam = {
    ...team,
    权限状态: permissionStatus.value,
    当前用户id: userStore.userInfo?.id || '当前登录用户'
  }
  
  return hasPermission(enhancedTeam, permissionCode)
}

/**
 * 判断当前用户是否为团队创建者
 * 用于控制创建者专用功能的显示和操作权限
 * @param {Object} team - 团队信息对象
 * @returns {boolean} 是否为创建者
 */
const isFounder = (team) => {
  if (!team || !permissionStatus.value) return false
  
  // 优先使用权限状态接口中的创建者标识
  if (permissionStatus.value.是否团队创建者 === true) {
    return true
  }
  
  // 备用检查：使用特殊权限字段
  if (permissionStatus.value.特殊权限?.是否创始人 === true) {
    return true
  }
  
  return false
}

/**
 * 加载团队详情信息
 * 包括团队基本信息和用户权限状态
 */
const loadTeamDetail = async () => {
  // 验证团队id的有效性
  if (!teamId.value || teamId.value <= 0) {
    message.error('无效的团队id')
    router.push('/team/overview')
    return
  }

  try {
    loading.value = true
    
    // 记录调试信息
    console.log('开始加载团队详情:', {
      teamId: teamId.value,
      currentUser: userStore.userInfo?.id,
      timestamp: new Date().toISOString()
    })
    
    // 调用团队服务获取详情
    const teamResponse = await teamService.getTeamDetail(teamId.value, {
      包含成员统计: true,
      包含权限信息: true
    })
    
    // 处理特定错误情况
    if (teamResponse && teamResponse.error) {
      switch (teamResponse.error) {
        case 'TEAM_NOT_FOUND':
          message.error('团队不存在或已被删除')
          router.push('/team/overview')
          return
        case 'PERMISSION_DENIED':
          message.error('您没有权限访问该团队')
          router.push('/team/overview')
          return
        case 'TEAM_DISSOLVED':
          message.warning('该团队已被解散')
          router.push('/team/overview')
          return
        default:
          message.error(teamResponse.message || '获取团队详情失败')
          router.back()
          return
      }
    }
    
    // 处理成功响应
    if (teamResponse.status === 100 && teamResponse.data) {
      const data = teamResponse.data
      
      // 验证数据完整性
      if (!data || typeof data !== 'object') {
        throw new Error('团队详情数据格式错误')
      }
      
      if (!data.团队id || !data.团队名称) {
        throw new Error('团队详情数据不完整')
      }
      
      // 合并数据到teamInfo响应式对象
      Object.assign(teamInfo, {
        ...data,
        // 确保关键字段存在默认值
        团队名称: data.团队名称 || '未命名团队',
        用户角色: data.用户角色 || '访客',
        权限列表: Array.isArray(data.权限列表) ? data.权限列表 : [],
        当前成员数: data.当前成员数 || data.实际成员数 || 0,
        最大成员数: data.最大成员数 || 100,
        在团队中: Boolean(data.在团队中),
        是否团队成员: Boolean(data.是否团队成员),
        是否创建者: Boolean(data.是否创建者),
        可否管理: Boolean(data.可否管理)
      })
      
      // 记录成功加载的信息
      console.log('团队详情加载成功:', {
        teamId: teamId.value,
        teamName: teamInfo.团队名称,
        userRole: teamInfo.用户角色,
        memberCount: teamInfo.当前成员数,
        permissions: teamInfo.权限列表,
        inTeam: teamInfo.在团队中
      })
      
      // 根据URL参数设置默认标签页
      if (route.query.tab) {
        activeTab.value = route.query.tab
      }
    } else {
      throw new Error(teamResponse.message || '获取团队详情失败')
    }
    
    // 加载用户权限状态
    await loadUserPermissionStatus()
    
  } catch (error) {
    console.error('加载团队详情失败:', {
      teamId: teamId.value,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    })
    
    // 根据错误类型提供不同的处理
    if (error.message.includes('网络') || error.message.includes('超时')) {
      message.error('网络连接异常，请检查网络后重试')
    } else if (error.message.includes('团队不存在')) {
      message.error('团队不存在或已被删除')
      router.push('/team/overview')
      return
    } else {
      message.error('加载团队详情失败: ' + error.message)
    }

    // 导航回退处理
    if (history.length > 1) {
      router.back()
    } else {
      router.push('/team/overview')
    }
  } finally {
    loading.value = false
  }
}

/**
 * 加载用户在当前团队的权限状态
 * 单独抽取的方法，便于重用和维护
 */
const loadUserPermissionStatus = async () => {
  try {
    const permissionResponse = await teamService.getUserTeamPermissionStatus({
      团队id: teamId.value
    })
    
    if (permissionResponse.status === 100) {
      permissionStatus.value = permissionResponse.data
      // 将权限状态数据合并到teamInfo对象中
      teamInfo.权限状态 = permissionResponse.data
      
      console.log('权限状态加载成功:', permissionResponse.data)
    } else {
      // 权限状态加载失败时的默认值处理
      console.warn('权限状态加载失败，使用默认值:', permissionResponse.message)
      setDefaultPermissionStatus()
    }
  } catch (permissionError) {
    console.warn('权限状态加载失败:', permissionError)
    setDefaultPermissionStatus()
  }
}

/**
 * 设置默认权限状态
 * 当无法从服务器获取权限状态时使用
 */
const setDefaultPermissionStatus = () => {
  const defaultPermissionStatus = {
    用户角色: teamInfo.用户角色 || '访客',
    权限列表: teamInfo.权限列表 || [],
    是否团队创建者: teamInfo.是否创建者 || false,
    是否团队负责人: false,
    hasPermission: () => false
  }
  permissionStatus.value = defaultPermissionStatus
  teamInfo.权限状态 = defaultPermissionStatus
}

/**
 * 处理邀请成员操作
 * 检查权限后打开邀请弹窗
 */
const handleInviteMember = () => {
  if (!canInviteMembers(teamInfo)) {
    message.warning('您没有邀请成员的权限，请联系团队负责人')
    return
  }
  
  showInviteModal.value = true
}

/**
 * 处理快捷操作菜单
 * @param {{ key: string }} param0 - 菜单项的key值
 */
const handleQuickAction = ({ key }) => {
  switch (key) {
    case 'editTeam':
      activeTab.value = 'settings'
      break
    case 'managePermissions':
      showPermissionModal.value = true
      break
    case 'smartLeaveTeam':
      handleSmartLeaveTeam()
      break
    case 'transferOwnership':
      handleTransferOwnership()
      break
    case 'dissolveTeam':
      handleDissolveTeam()
      break
    case 'leaveTeam':
      handleLeaveTeam()
      break
  }
}

/**
 * 智能退出团队功能
 * 根据团队成员数量智能判断退出方案：
 * - 单人团队：提示确认解散
 * - 多人团队：引导转移所有权
 */
const handleSmartLeaveTeam = async () => {
  try {
    // 调用智能退出接口，获取适合的退出方案
    const response = await teamBasicService.smartLeaveTeam({
      团队id: teamId.value,
      确认解散: false // 第一次调用不确认，仅获取方案
    })

    if (response.status !== 100 || !response.data) {
      throw new Error(response.message || '获取退出方案失败')
    }

    const { action, team_name, available_members, message: actionMessage } = response.data

    // 根据后端返回的action类型执行不同处理
    switch (action) {
      case 'confirm_dissolve':
        showConfirmDissolveDialog(team_name)
        break
      case 'transfer_required':
        showTransferOwnershipDialog(available_members)
        break
      default:
        message.info(actionMessage || '请选择退出方式')
    }
  } catch (error) {
    console.error('智能退出失败:', error)
    message.error('智能退出失败: ' + error.message)
  }
}

/**
 * 显示确认解散团队的对话框
 * @param {string} teamName - 团队名称
 */
const showConfirmDissolveDialog = (teamName) => {
  Modal.confirm({
    title: '确认解散团队',
    content: `您是团队"${teamName}"的唯一成员。\n\n💡 由于您是唯一成员，退出团队将自动解散团队。\n\n⚠️ 解散后团队所有数据将被删除，且无法恢复。`,
    okText: '确认解散',
    cancelText: '取消',
    okType: 'danger',
    width: 480,
    onOk: () => executeTeamDissolve()
  })
}

/**
 * 执行团队解散操作
 */
const executeTeamDissolve = async () => {
  try {
    const dissolveResponse = await teamBasicService.smartLeaveTeam({
      团队id: teamId.value,
      确认解散: true
    })
    
    if (dissolveResponse.status === 100) {
      message.success('团队已解散')
      router.push('/team/overview')
    } else {
      throw new Error(dissolveResponse.message || '解散失败')
    }
  } catch (error) {
    console.error('解散团队失败:', error)
    message.error('解散团队失败: ' + error.message)
  }
}

/**
 * 显示转移所有权对话框
 * @param {Array} availableMembers - 可转移的成员列表
 */
const showTransferOwnershipDialog = (availableMembers) => {
  showTransferModal.value = true
  transferModalData.value = {
    teamInfo: { ...teamInfo, id: teamId.value },
    availableMembers: availableMembers || []
  }
}

/**
 * 处理转移所有权操作
 */
const handleTransferOwnership = async () => {
  try {
    // 获取可转移的成员列表
    const response = await teamBasicService.smartLeaveTeam({
      团队id: teamId.value,
      确认解散: false
    })

    const availableMembers = (response.status === 100 && response.data?.available_members) 
      ? response.data.available_members 
      : []

    showTransferOwnershipDialog(availableMembers)
  } catch (error) {
    console.error('获取成员列表失败:', error)
    showTransferOwnershipDialog([])
  }
}

/**
 * 处理解散团队操作
 * 创建者可以强制解散团队（无论成员数量）
 */
const handleDissolveTeam = () => {
  Modal.confirm({
    title: '解散团队',
    content: `您确定要解散"${teamInfo.团队名称}"团队吗？\n\n⚠️ 解散后团队所有数据将被删除，且无法恢复。`,
    okText: '确认解散',
    cancelText: '取消',
    okType: 'danger',
    width: 480,
    onOk: async () => {
      try {
        const response = await teamBasicService.dissolveTeam(teamId.value)
        
        if (response.status === 100) {
          message.success('团队已解散')
          router.push('/team/overview')
        } else {
          throw new Error(response.message || '解散失败')
        }
      } catch (error) {
        console.error('解散团队失败:', error)
        message.error('解散团队失败: ' + error.message)
      }
    }
  })
}

/**
 * 处理普通成员退出团队
 */
const handleLeaveTeam = () => {
  Modal.confirm({
    title: '退出团队',
    content: `您确定要退出"${teamInfo.团队名称}"团队吗？`,
    okText: '确认退出',
    cancelText: '取消',
    onOk: async () => {
      try {
        const response = await teamBasicService.leaveTeam({
          团队id: teamId.value
        })
        
        if (response.status === 100) {
          message.success('已退出团队')
          router.push('/team/overview')
        } else {
          throw new Error(response.message || '退出失败')
        }
      } catch (error) {
        console.error('退出团队失败:', error)
        message.error('退出团队失败: ' + error.message)
      }
    }
  })
}

/**
 * 邀请成功后的回调处理
 * @param {Object} data - 邀请结果数据
 */
const handleInviteSuccess = (data) => {
  console.log('邀请成功:', data)
  message.success('邀请发送成功')
  showInviteModal.value = false
  // 刷新团队详情以获取最新的成员统计
  loadTeamDetail()
}

/**
 * 权限管理成功后的回调处理
 */
const handlePermissionSuccess = () => {
  message.success('权限设置成功')
  showPermissionModal.value = false
  loadTeamDetail()
}

/**
 * 转移所有权成功后的回调处理
 */
const handleTransferSuccess = () => {
  message.success('所有权转移成功')
  showTransferModal.value = false
  router.push('/team/overview')
}

/**
 * 转移所有权取消后的回调处理
 */
const handleTransferCancel = () => {
  showTransferModal.value = false
  transferModalData.value = {
    teamInfo: {},
    availableMembers: []
  }
}

/**
 * 团队信息更新后的回调处理
 * @param {Object} updatedTeam - 更新后的团队信息
 */
const handleTeamUpdated = (updatedTeam) => {
  Object.assign(teamInfo, updatedTeam)
  message.success('团队信息更新成功')
}

// 工具函数区域

/**
 * 获取团队头像颜色
 * @param {string} teamName - 团队名称
 * @returns {string} 颜色值
 */
const getTeamAvatarColor = (teamName) => {
  if (!teamName) return '#1890ff'
  
  const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2']
  const index = teamName.charCodeAt(0) % colors.length
  return colors[index]
}

/**
 * 获取角色颜色
 * @param {string} role - 角色名称
 * @returns {string} 颜色值
 */
const getRoleColor = (role) => {
  const colorMap = {
    '创建者': 'gold',
    '管理员': 'blue',
    '成员': 'green',
    '访客': 'default'
  }
  return colorMap[role] || 'default'
}

/**
 * 获取状态颜色
 * @param {string} status - 状态名称
 * @returns {string} 颜色值
 */
const getStatusColor = (status) => {
  const colorMap = {
    '正常': 'green',
    '暂停': 'orange',
    '解散': 'red'
  }
  return colorMap[status] || 'default'
}

/**
 * 格式化日期显示
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '--'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch (error) {
    return '--'
  }
}

// 生命周期钩子
onMounted(() => {
  loadTeamDetail()
})

// 监听路由变化，重新加载团队详情
watch(() => route.params.teamId, (newTeamId) => {
  if (newTeamId) {
    loadTeamDetail()
  }
})
</script>

<style scoped>
.team-detail {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.breadcrumb {
  margin-bottom: 16px;
}

.team-header-card {
  margin-bottom: 24px;
}

.team-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.team-avatar-section {
  flex-shrink: 0;
}

.team-info-section {
  flex: 1;
  min-width: 0;
}

.team-title-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.team-name {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #262626;
}

.team-tags {
  display: flex;
  gap: 8px;
}

.team-description {
  color: #595959;
  font-size: 16px;
  margin-bottom: 16px;
  line-height: 1.6;
}

.team-meta-info {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #8c8c8c;
  font-size: 14px;
}

.team-actions-section {
  flex-shrink: 0;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.content-card {
  background: white;
}

.invite-btn {
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.invite-btn:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.invite-btn-disabled {
  opacity: 0.6;
}

.invite-tip {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .team-detail {
    padding: 16px;
  }
  
  .team-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .team-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .team-name {
    font-size: 24px;
  }
  
  .team-meta-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .team-actions-section {
    width: 100%;
    justify-content: flex-start;
  }
}
</style> 