from typing import Optional, List, Any, Dict
from datetime import datetime
from pydantic import BaseModel, Field


# ================== 抖音商品管理模型 ==================

class 抖音商品列表请求模型(BaseModel):
    """
    抖音商品列表请求模型
    
    用于获取抖音商品列表的分页查询请求
    
    Attributes:
        页码 (int): 页码，默认为1
        每页条数 (int): 每页显示条数，默认为20
        商品名称 (Optional[str]): 商品名称搜索关键词
        平台 (Optional[str]): 平台筛选条件
        状态 (Optional[str]): 商品状态筛选条件
        分类ID (Optional[str]): 商品分类ID
        最低价格 (Optional[float]): 最低价格筛选
        最高价格 (Optional[float]): 最高价格筛选
    """
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    商品名称: Optional[str] = Field(None, description="商品名称搜索")
    平台: Optional[str] = Field(None, description="平台筛选")
    状态: Optional[str] = Field(None, description="商品状态筛选")
    分类ID: Optional[str] = Field(None, description="商品分类ID")
    最低价格: Optional[float] = Field(None, description="最低价格筛选")
    最高价格: Optional[float] = Field(None, description="最高价格筛选")

class 抖音商品添加模型(BaseModel):
    """
    抖音商品添加模型
    
    用于添加新的抖音商品记录
    
    Attributes:
        商品id (str): 抖音商品ID
        平台 (str): 平台名称
        商品信息 (Dict[str, Any]): 商品详细信息JSON对象
        商品名称 (Optional[str]): 商品名称
        价格 (Optional[float]): 商品价格
        图片URL (Optional[str]): 商品图片URL
        商品描述 (Optional[str]): 商品描述
        分类 (Optional[str]): 商品分类
    """
    商品id: str = Field(..., description="抖音商品ID")
    平台: str = Field(..., description="平台名称")
    商品信息: Dict[str, Any] = Field(..., description="商品详细信息JSON对象")
    商品名称: Optional[str] = Field(None, description="商品名称")
    价格: Optional[float] = Field(None, description="商品价格")
    图片URL: Optional[str] = Field(None, description="商品图片")
    商品描述: Optional[str] = Field(None, description="商品描述")
    分类: Optional[str] = Field(None, description="商品分类")

class 抖音商品更新模型(BaseModel):
    """
    抖音商品更新模型
    
    用于更新现有抖音商品信息
    
    Attributes:
        商品信息 (Optional[Dict[str, Any]]): 商品详细信息JSON对象
        商品名称 (Optional[str]): 商品名称
        价格 (Optional[float]): 商品价格
        图片URL (Optional[str]): 商品图片URL
        商品描述 (Optional[str]): 商品描述
        分类 (Optional[str]): 商品分类
        状态 (Optional[str]): 商品状态
    """
    商品信息: Optional[Dict[str, Any]] = Field(None, description="商品详细信息JSON对象")
    商品名称: Optional[str] = Field(None, description="商品名称")
    价格: Optional[float] = Field(None, description="商品价格")
    图片URL: Optional[str] = Field(None, description="商品图片")
    商品描述: Optional[str] = Field(None, description="商品描述")
    分类: Optional[str] = Field(None, description="商品分类")
    状态: Optional[str] = Field(None, description="商品状态")

class 抖音商品批量导入模型(BaseModel):
    """
    抖音商品批量导入模型
    
    用于批量导入抖音商品数据
    
    Attributes:
        商品列表 (List[抖音商品添加模型]): 要导入的商品数据列表
    """
    商品列表: List[抖音商品添加模型] = Field(..., description="商品数据列表")

# ================== 商品搜索模型 ==================

class 商品搜索请求模型(BaseModel):
    """
    商品搜索请求模型
    
    用于商品搜索功能的参数模型
    
    Attributes:
        关键词 (Optional[str]): 搜索关键词
        平台 (Optional[str]): 平台筛选条件
        分类 (Optional[str]): 分类筛选条件
        价格区间 (Optional[Dict[str, float]]): 价格区间筛选
        排序方式 (Optional[str]): 排序方式，默认按时间倒序
        页码 (int): 页码，默认为1
        每页条数 (int): 每页显示条数，默认为20
    """
    关键词: Optional[str] = Field(None, description="搜索关键词")
    平台: Optional[str] = Field(None, description="平台筛选")
    分类: Optional[str] = Field(None, description="分类筛选")
    价格区间: Optional[Dict[str, float]] = Field(None, description="价格区间筛选")
    排序方式: Optional[str] = Field("time_desc", description="排序方式")
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")

# ================== 商品分类管理模型 ==================

class 商品分类添加模型(BaseModel):
    """
    商品分类添加模型
    
    用于添加新的商品分类
    
    Attributes:
        分类名称 (str): 分类名称
        父分类ID (Optional[int]): 父分类ID，顶级分类为None
        分类描述 (Optional[str]): 分类描述
        排序 (Optional[int]): 排序权重，默认为0
    """
    分类名称: str = Field(..., description="分类名称")
    父分类ID: Optional[int] = Field(None, description="父分类ID，顶级分类为null")
    分类描述: Optional[str] = Field(None, description="分类描述")
    排序: Optional[int] = Field(0, description="排序权重")

class 商品分类更新模型(BaseModel):
    """
    商品分类更新模型
    
    用于更新现有商品分类信息
    
    Attributes:
        分类名称 (Optional[str]): 分类名称
        分类描述 (Optional[str]): 分类描述
        排序 (Optional[int]): 排序权重
        状态 (Optional[str]): 分类状态
    """
    分类名称: Optional[str] = Field(None, description="分类名称")
    分类描述: Optional[str] = Field(None, description="分类描述")
    排序: Optional[int] = Field(None, description="排序权重")
    状态: Optional[str] = Field(None, description="分类状态")

# ================== 商品统计分析模型 ==================

class 商品统计请求模型(BaseModel):
    """
    商品统计请求模型
    
    用于商品统计分析的参数模型
    
    Attributes:
        时间范围 (str): 时间范围（7d, 30d, 90d, custom），默认30天
        开始日期 (Optional[str]): 开始日期（当时间范围为custom时必填）
        结束日期 (Optional[str]): 结束日期（当时间范围为custom时必填）
        平台 (Optional[str]): 平台筛选条件
        分类ID (Optional[str]): 分类筛选条件
    """
    时间范围: str = Field("30d", description="时间范围（7d, 30d, 90d, custom）")
    开始日期: Optional[str] = Field(None, description="开始日期（当时间范围为custom时必填）")
    结束日期: Optional[str] = Field(None, description="结束日期（当时间范围为custom时必填）")
    平台: Optional[str] = Field(None, description="平台筛选")
    分类ID: Optional[str] = Field(None, description="分类筛选")

class 商品排行请求模型(BaseModel):
    """
    商品排行请求模型
    
    用于商品排行榜查询的参数模型
    
    Attributes:
        排行类型 (str): 排行类型（浏览量、收藏量、销量等）
        时间范围 (str): 时间范围，默认30天
        平台 (Optional[str]): 平台筛选条件
        数量限制 (int): 返回商品数量，默认10个
    """
    排行类型: str = Field("浏览量", description="排行类型（浏览量、收藏量、销量等）")
    时间范围: str = Field("30d", description="时间范围")
    平台: Optional[str] = Field(None, description="平台筛选")
    数量限制: int = Field(10, description="返回商品数量")

# ================== 商品收藏管理模型 ==================

class 商品收藏添加模型(BaseModel):
    """
    商品收藏添加模型
    
    用于用户收藏商品的参数模型
    
    Attributes:
        商品ID (int): 商品ID
        收藏备注 (Optional[str]): 收藏备注信息
    """
    商品ID: int = Field(..., description="商品ID")
    收藏备注: Optional[str] = Field("", description="收藏备注")

class 商品收藏列表请求模型(BaseModel):
    """
    商品收藏列表请求模型
    
    用于获取用户收藏商品列表的参数模型
    
    Attributes:
        页码 (int): 页码，默认为1
        每页条数 (int): 每页显示条数，默认为20
        分类筛选 (Optional[str]): 分类筛选条件
    """
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    分类筛选: Optional[str] = Field(None, description="分类筛选")

# ================== 商品价格监控模型 ==================

class 商品价格监控添加模型(BaseModel):
    """
    商品价格监控添加模型
    
    用于添加商品价格监控的参数模型
    
    Attributes:
        商品ID (int): 商品ID
        目标价格 (float): 目标价格
        监控类型 (str): 监控类型（降价、涨价、等于）
        启用通知 (bool): 是否启用通知，默认为True
    """
    商品ID: int = Field(..., description="商品ID")
    目标价格: float = Field(..., description="目标价格")
    监控类型: str = Field(..., description="监控类型（降价、涨价、等于）")
    启用通知: bool = Field(True, description="是否启用通知")

class 商品价格监控列表请求模型(BaseModel):
    """
    商品价格监控列表请求模型
    
    用于获取商品价格监控列表的参数模型
    
    Attributes:
        页码 (int): 页码，默认为1
        每页条数 (int): 每页显示条数，默认为20
        状态 (Optional[str]): 监控状态筛选条件
        商品名称 (Optional[str]): 商品名称搜索关键词
    """
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    状态: Optional[str] = Field(None, description="监控状态筛选")
    商品名称: Optional[str] = Field(None, description="商品名称搜索")

# 商品管理相关数据模型

class 商品搜索模型(BaseModel):
    """
    商品搜索模型
    
    用于商品搜索查询的参数模型
    
    Attributes:
        关键词 (Optional[str]): 搜索关键词，支持商品名称、品牌、分类等
        分类ID (Optional[int]): 商品分类ID
        品牌ID (Optional[int]): 商品品牌ID
        价格范围 (Optional[str]): 价格范围，格式："最低价-最高价"
        状态 (Optional[str]): 商品状态筛选
        页码 (int): 页码，默认为1
        每页数量 (int): 每页显示数量，默认为20
    """
    关键词: Optional[str] = Field(None, description="搜索关键词")
    分类ID: Optional[int] = Field(None, description="商品分类ID")
    品牌ID: Optional[int] = Field(None, description="商品品牌ID")
    价格范围: Optional[str] = Field(None, description="价格范围")
    状态: Optional[str] = Field(None, description="商品状态")
    页码: int = Field(1, description="页码")
    每页数量: int = Field(20, description="每页数量")

class 商品详情查询模型(BaseModel):
    """
    商品详情查询模型
    
    用于查询单个商品详细信息的参数模型
    
    Attributes:
        商品ID (int): 商品ID
        包含库存信息 (bool): 是否包含库存信息，默认为True
        包含销售数据 (bool): 是否包含销售数据，默认为False
    """
    商品ID: int = Field(..., description="商品ID")
    包含库存信息: bool = Field(True, description="是否包含库存信息")
    包含销售数据: bool = Field(False, description="是否包含销售数据")

class 商品创建模型(BaseModel):
    """
    商品创建模型
    
    用于创建新商品的数据模型
    
    Attributes:
        商品名称 (str): 商品名称
        商品描述 (Optional[str]): 商品详细描述
        价格 (float): 商品价格
        分类ID (int): 商品分类ID
        品牌ID (Optional[int]): 商品品牌ID
        库存数量 (int): 初始库存数量
        商品图片 (Optional[List[str]]): 商品图片URL列表
        规格信息 (Optional[str]): 商品规格信息
        标签 (Optional[List[str]]): 商品标签列表
    """
    商品名称: str = Field(..., description="商品名称")
    商品描述: Optional[str] = Field(None, description="商品描述")
    价格: float = Field(..., description="商品价格")
    分类ID: int = Field(..., description="商品分类ID")
    品牌ID: Optional[int] = Field(None, description="商品品牌ID")
    库存数量: int = Field(0, description="初始库存数量")
    商品图片: Optional[List[str]] = Field(None, description="商品图片列表")
    规格信息: Optional[str] = Field(None, description="商品规格信息")
    标签: Optional[List[str]] = Field(None, description="商品标签")

class 商品更新模型(BaseModel):
    """
    商品更新模型
    
    用于更新现有商品信息的数据模型
    
    Attributes:
        商品ID (int): 商品ID
        商品名称 (Optional[str]): 商品名称
        商品描述 (Optional[str]): 商品详细描述
        价格 (Optional[float]): 商品价格
        分类ID (Optional[int]): 商品分类ID
        品牌ID (Optional[int]): 商品品牌ID
        商品图片 (Optional[List[str]]): 商品图片URL列表
        规格信息 (Optional[str]): 商品规格信息
        标签 (Optional[List[str]]): 商品标签列表
        状态 (Optional[str]): 商品状态
    """
    商品ID: int = Field(..., description="商品ID")
    商品名称: Optional[str] = Field(None, description="商品名称")
    商品描述: Optional[str] = Field(None, description="商品描述")
    价格: Optional[float] = Field(None, description="商品价格")
    分类ID: Optional[int] = Field(None, description="商品分类ID")
    品牌ID: Optional[int] = Field(None, description="商品品牌ID")
    商品图片: Optional[List[str]] = Field(None, description="商品图片列表")
    规格信息: Optional[str] = Field(None, description="商品规格信息")
    标签: Optional[List[str]] = Field(None, description="商品标签")
    状态: Optional[str] = Field(None, description="商品状态")

class 商品库存管理模型(BaseModel):
    """
    商品库存管理模型
    
    用于管理商品库存的数据模型
    
    Attributes:
        商品ID (int): 商品ID
        操作类型 (str): 操作类型（入库、出库、调整）
        数量变化 (int): 数量变化（正数为增加，负数为减少）
        操作原因 (Optional[str]): 操作原因说明
        操作人 (Optional[str]): 操作人员
    """
    商品ID: int = Field(..., description="商品ID")
    操作类型: str = Field(..., description="操作类型")
    数量变化: int = Field(..., description="数量变化")
    操作原因: Optional[str] = Field(None, description="操作原因")
    操作人: Optional[str] = Field(None, description="操作人员")

class 商品价格历史模型(BaseModel):
    """
    商品价格历史模型
    
    用于查询商品价格变化历史的参数模型
    
    Attributes:
        商品ID (int): 商品ID
        开始日期 (Optional[datetime]): 查询开始日期
        结束日期 (Optional[datetime]): 查询结束日期
        页码 (int): 页码，默认为1
        每页数量 (int): 每页显示数量，默认为50
    """
    商品ID: int = Field(..., description="商品ID")
    开始日期: Optional[datetime] = Field(None, description="开始日期")
    结束日期: Optional[datetime] = Field(None, description="结束日期")
    页码: int = Field(1, description="页码")
    每页数量: int = Field(50, description="每页数量")

class 商品分类模型(BaseModel):
    """
    商品分类模型
    
    用于商品分类管理的数据模型
    
    Attributes:
        分类ID (Optional[int]): 分类ID（创建时为空）
        分类名称 (str): 分类名称
        父分类ID (Optional[int]): 父分类ID（一级分类为空）
        分类描述 (Optional[str]): 分类描述
        排序值 (int): 排序值，默认为0
        状态 (str): 分类状态，默认为"启用"
    """
    分类ID: Optional[int] = Field(None, description="分类ID")
    分类名称: str = Field(..., description="分类名称")
    父分类ID: Optional[int] = Field(None, description="父分类ID")
    分类描述: Optional[str] = Field(None, description="分类描述")
    排序值: int = Field(0, description="排序值")
    状态: str = Field("启用", description="分类状态")

class 商品品牌模型(BaseModel):
    """
    商品品牌模型
    
    用于商品品牌管理的数据模型
    
    Attributes:
        品牌ID (Optional[int]): 品牌ID（创建时为空）
        品牌名称 (str): 品牌名称
        品牌描述 (Optional[str]): 品牌描述
        品牌LOGO (Optional[str]): 品牌LOGO图片URL
        官网地址 (Optional[str]): 品牌官网地址
        状态 (str): 品牌状态，默认为"启用"
    """
    品牌ID: Optional[int] = Field(None, description="品牌ID")
    品牌名称: str = Field(..., description="品牌名称")
    品牌描述: Optional[str] = Field(None, description="品牌描述")
    品牌LOGO: Optional[str] = Field(None, description="品牌LOGO")
    官网地址: Optional[str] = Field(None, description="官网地址")
    状态: str = Field("启用", description="品牌状态")

class 商品统计查询模型(BaseModel):
    """
    商品统计查询模型
    
    用于商品统计数据查询的参数模型
    
    Attributes:
        统计类型 (str): 统计类型（销量、库存、价格等）
        时间范围 (str): 时间范围（7d、30d、90d等）
        分类ID (Optional[int]): 分类ID筛选
        品牌ID (Optional[int]): 品牌ID筛选
        开始日期 (Optional[datetime]): 自定义开始日期
        结束日期 (Optional[datetime]): 自定义结束日期
    """
    统计类型: str = Field(..., description="统计类型")
    时间范围: str = Field("30d", description="时间范围")
    分类ID: Optional[int] = Field(None, description="分类ID筛选")
    品牌ID: Optional[int] = Field(None, description="品牌ID筛选")
    开始日期: Optional[datetime] = Field(None, description="开始日期")
    结束日期: Optional[datetime] = Field(None, description="结束日期")

class 商品导入模型(BaseModel):
    """
    商品导入模型
    
    用于批量导入商品的数据模型
    
    Attributes:
        文件路径 (str): 导入文件路径
        导入模式 (str): 导入模式（新增、覆盖、更新）
        跳过错误 (bool): 是否跳过错误行，默认为True
        验证规则 (Optional[str]): 验证规则配置
    """
    文件路径: str = Field(..., description="导入文件路径")
    导入模式: str = Field("新增", description="导入模式")
    跳过错误: bool = Field(True, description="是否跳过错误")
    验证规则: Optional[str] = Field(None, description="验证规则")

class 商品导出模型(BaseModel):
    """
    商品导出模型
    
    用于批量导出商品的参数模型
    
    Attributes:
        导出格式 (str): 导出格式（excel、csv、json）
        筛选条件 (Optional[dict]): 筛选条件
        导出字段 (Optional[List[str]]): 需要导出的字段列表
        包含图片 (bool): 是否包含图片信息，默认为False
    """
    导出格式: str = Field("excel", description="导出格式")
    筛选条件: Optional[dict] = Field(None, description="筛选条件")
    导出字段: Optional[List[str]] = Field(None, description="导出字段")
    包含图片: bool = Field(False, description="是否包含图片") 