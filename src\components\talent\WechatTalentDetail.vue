<template>
  <div class="wechat-talent-detail">
    <!-- 加载状态 -->
    <a-spin :spinning="loading" size="large">
      <div v-if="talent" class="detail-content">
        <!-- 达人基本信息卡片 -->
        <a-card class="basic-info-card" :bordered="false">
          <div class="talent-header">
            <!-- 头像和基本信息 -->
            <div class="avatar-section">
              <a-avatar 
                :size="80" 
                :src="talent.头像" 
                :alt="talent.昵称"
                class="talent-avatar"
              >
                <template #icon v-if="!talent.头像">
                  <WechatOutlined />
                </template>
              </a-avatar>
              
              <!-- 认领状态 -->
              <div class="claim-status">
                <a-tag 
                  v-if="talent.已认领 || talent.当前用户认领状态?.已认领" 
                  color="red" 
                  size="large"
                >
                  <CheckCircleOutlined />
                  已认领
                </a-tag>
                <a-tag v-else color="green" size="large">
                  <PlusCircleOutlined />
                  可认领
                </a-tag>
              </div>
            </div>
            
            <!-- 基本信息 -->
            <div class="basic-info">
              <h2 class="talent-name">{{ talent.昵称 || '未知昵称' }}</h2>
              <div class="talent-meta">
                <div class="meta-row" v-if="talent.微信号">
                  <WechatOutlined class="meta-icon" />
                  <span class="meta-label">微信号:</span>
                  <span class="meta-value">{{ talent.微信号 }}</span>
                </div>
                
                <div class="meta-row" v-if="talent.地区">
                  <EnvironmentOutlined class="meta-icon" />
                  <span class="meta-label">地区:</span>
                  <span class="meta-value">{{ talent.地区 }}</span>
                </div>
                
                <div class="meta-row" v-if="talent.性别">
                  <UserOutlined class="meta-icon" />
                  <span class="meta-label">性别:</span>
                  <span class="meta-value">{{ talent.性别 }}</span>
                </div>
                
                <div class="meta-row">
                  <ClockCircleOutlined class="meta-icon" />
                  <span class="meta-label">更新时间:</span>
                  <span class="meta-value">{{ formatTime(talent.更新时间) }}</span>
                </div>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
              <a-button 
                v-if="!talent.已认领 && !talent.当前用户认领状态?.已认领"
                type="primary" 
                size="large"
                @click="handleClaim"
                :loading="claiming"
                class="claim-button"
              >
                <PlusOutlined />
                认领达人
              </a-button>
              
              <a-button 
                v-else-if="talent.当前用户认领状态?.已认领"
                danger
                size="large"
                @click="handleUnclaim"
                :loading="unclaiming"
                class="unclaim-button"
              >
                <MinusOutlined />
                取消认领
              </a-button>
              
              <a-button 
                size="large"
                @click="handleEdit"
                class="edit-button"
              >
                <EditOutlined />
                编辑信息
              </a-button>
            </div>
          </div>
        </a-card>
        
        <!-- 个人简介 -->
        <a-card v-if="talent.个人简介" title="个人简介" class="description-card" :bordered="false">
          <p class="description-text">{{ talent.个人简介 }}</p>
        </a-card>
        
        <!-- 统计数据 -->
        <a-card title="数据统计" class="stats-card" :bordered="false">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="stat-item">
                <div class="stat-icon">
                  <TeamOutlined />
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatNumber(talent.好友数) }}</div>
                  <div class="stat-label">好友数</div>
                </div>
              </div>
            </a-col>
            
            <a-col :span="8">
              <div class="stat-item">
                <div class="stat-icon">
                  <MessageOutlined />
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatNumber(talent.朋友圈发布数) }}</div>
                  <div class="stat-label">朋友圈发布</div>
                </div>
              </div>
            </a-col>
            
            <a-col :span="8">
              <div class="stat-item">
                <div class="stat-icon">
                  <CalendarOutlined />
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ getDaysFromCreation(talent.创建时间) }}</div>
                  <div class="stat-label">入库天数</div>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-card>
        
        <!-- 联系方式 -->
        <a-card title="联系方式" class="contact-card" :bordered="false">
          <div v-if="talent.联系方式列表 && talent.联系方式列表.length > 0" class="contact-list">
            <div 
              v-for="contact in talent.联系方式列表" 
              :key="contact.id"
              class="contact-item"
            >
              <div class="contact-type">
                <component :is="getContactIcon(contact.联系类型)" class="contact-icon" />
                <span>{{ contact.联系类型 }}</span>
              </div>
              <div class="contact-content">{{ contact.联系内容 }}</div>
              <div class="contact-status">
                <a-tag :color="contact.是否验证 ? 'green' : 'orange'">
                  {{ contact.是否验证 ? '已验证' : '未验证' }}
                </a-tag>
              </div>
            </div>
          </div>
          <a-empty v-else description="暂无联系方式" :image="h(PhoneOutlined)" />
        </a-card>
        
        <!-- 认领信息（仅当前用户认领时显示） -->
        <a-card 
          v-if="talent.认领信息" 
          title="我的认领信息" 
          class="claim-info-card" 
          :bordered="false"
        >
          <div class="claim-info">
            <div class="info-row">
              <span class="info-label">认领时间:</span>
              <span class="info-value">{{ formatTime(talent.认领信息.认领时间) }}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">合作状态:</span>
              <a-tag :color="getCooperationStatusColor(talent.认领信息.合作状态)">
                {{ talent.认领信息.合作状态 }}
              </a-tag>
            </div>
            
            <div class="info-row" v-if="talent.认领信息.个人备注">
              <span class="info-label">个人备注:</span>
              <span class="info-value">{{ talent.认领信息.个人备注 }}</span>
            </div>
            
            <div class="info-row" v-if="talent.认领信息.个人标签 && talent.认领信息.个人标签.length > 0">
              <span class="info-label">个人标签:</span>
              <div class="tag-list">
                <a-tag 
                  v-for="tag in talent.认领信息.个人标签" 
                  :key="tag"
                  color="blue"
                >
                  {{ tag }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-card>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <a-empty 
          description="暂无达人详情数据"
          :image="h(WechatOutlined)"
        />
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  WechatOutlined, CheckCircleOutlined, PlusCircleOutlined, 
  EnvironmentOutlined, UserOutlined, ClockCircleOutlined,
  PlusOutlined, MinusOutlined, EditOutlined, TeamOutlined,
  MessageOutlined, CalendarOutlined, PhoneOutlined,
  MailOutlined, QqOutlined, MobileOutlined
} from '@ant-design/icons-vue'

// Props定义
const props = defineProps({
  // 微信达人详情数据
  talent: {
    type: Object,
    default: null
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits([
  'claim-talent',
  'unclaim-talent', 
  'edit-talent'
])

// 响应式数据
const claiming = ref(false)
const unclaiming = ref(false)

// 方法定义
/**
 * 格式化数字显示
 */
const formatNumber = (num) => {
  if (!num || num === 0) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

/**
 * 格式化时间显示
 */
const formatTime = (time) => {
  if (!time) return '未知'
  return new Date(time).toLocaleString()
}

/**
 * 计算入库天数
 */
const getDaysFromCreation = (createTime) => {
  if (!createTime) return '未知'
  const days = Math.floor((new Date() - new Date(createTime)) / (1000 * 60 * 60 * 24))
  return `${days}天`
}

/**
 * 获取联系方式图标
 */
const getContactIcon = (type) => {
  const iconMap = {
    '手机': MobileOutlined,
    '邮箱': MailOutlined,
    'QQ': QqOutlined,
    '微信': WechatOutlined,
    '其他': PhoneOutlined
  }
  return iconMap[type] || PhoneOutlined
}

/**
 * 获取合作状态颜色
 */
const getCooperationStatusColor = (status) => {
  const colorMap = {
    '未联系': 'default',
    '已联系': 'blue',
    '洽谈中': 'orange',
    '合作中': 'green',
    '已完成': 'purple',
    '已拒绝': 'red'
  }
  return colorMap[status] || 'default'
}

/**
 * 处理认领
 */
const handleClaim = async () => {
  claiming.value = true
  try {
    emit('claim-talent', props.talent)
  } finally {
    claiming.value = false
  }
}

/**
 * 处理取消认领
 */
const handleUnclaim = async () => {
  unclaiming.value = true
  try {
    emit('unclaim-talent', props.talent)
  } finally {
    unclaiming.value = false
  }
}

/**
 * 处理编辑
 */
const handleEdit = () => {
  emit('edit-talent', props.talent)
}

defineOptions({
  name: 'WechatTalentDetail'
})
</script>

<style scoped>
.wechat-talent-detail {
  max-width: 800px;
  margin: 0 auto;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 基本信息卡片 */
.basic-info-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.talent-header {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.talent-avatar {
  background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
  color: white;
  font-size: 32px;
}

.basic-info {
  flex: 1;
}

.talent-name {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
}

.talent-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.meta-icon {
  color: #07c160;
  font-size: 16px;
}

.meta-label {
  color: #666;
  min-width: 80px;
}

.meta-value {
  color: #262626;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.claim-button {
  background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
  border: none;
}

.unclaim-button:hover {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

/* 统计数据卡片 */
.stats-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  text-align: center;
}

.stat-icon {
  font-size: 24px;
  color: #07c160;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* 联系方式卡片 */
.contact-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.contact-type {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  font-weight: 500;
}

.contact-icon {
  color: #07c160;
}

.contact-content {
  flex: 1;
  font-family: monospace;
}

/* 认领信息卡片 */
.claim-info-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.claim-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-label {
  color: #666;
  min-width: 80px;
  font-weight: 500;
}

.info-value {
  color: #262626;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 其他卡片 */
.description-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.description-text {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin: 0;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .talent-header {
    flex-direction: column;
    text-align: center;
  }
  
  .action-buttons {
    width: 100%;
  }
  
  .stat-item {
    flex-direction: column;
    text-align: center;
  }
  
  .contact-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
