<template>
  <div class="forgot-password-container">
    <div class="forgot-password-wrapper">
      <div class="forgot-password-card">
        <div class="forgot-password-header">
          <h1 class="title">忘记密码</h1>
          <p class="subtitle">重置您的账户密码</p>
        </div>

        <!-- 步骤指示器 -->
        <a-steps 
          :current="currentStep" 
          size="small" 
          class="reset-steps"
        >
          <a-step title="验证手机号" />
          <a-step title="设置新密码" />
          <a-step title="重置完成" />
        </a-steps>

        <!-- 第一步：验证手机号 -->
        <div v-if="currentStep === 0" class="step-content">
          <a-form
            ref="phoneFormRef"
            :model="phoneForm"
            :rules="phoneRules"
            layout="vertical"
            @finish="handleSendCode"
          >
            <a-form-item name="phone" label="手机号">
              <a-input
                v-model:value="phoneForm.phone"
                size="large"
                placeholder="请输入注册时使用的手机号"
                :prefix="h(PhoneOutlined)"
              />
            </a-form-item>

            <a-form-item name="verification_code" label="验证码">
              <div class="code-input-group">
                <a-input
                  v-model:value="phoneForm.verification_code"
                  size="large"
                  placeholder="请输入验证码"
                  :prefix="h(SafetyOutlined)"
                  style="flex: 1; margin-right: 10px;"
                />
                <a-button
                  size="large"
                  :disabled="!canSendCode || countdown > 0"
                  :loading="sendingCode"
                  @click="sendVerificationCode"
                  class="code-button"
                >
                  {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
                </a-button>
              </div>
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                size="large"
                html-type="submit"
                :loading="verifying"
                block
                class="verify-button"
              >
                验证并继续
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <!-- 第二步：设置新密码 -->
        <div v-if="currentStep === 1" class="step-content">
          <a-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            layout="vertical"
            @finish="handleResetPassword"
          >
            <a-form-item name="new_password" label="新密码">
              <a-input-password
                v-model:value="passwordForm.new_password"
                size="large"
                placeholder="请输入新密码 (至少6位)"
                :prefix="h(LockOutlined)"
                @input="handlePasswordInput"
              />
              <div v-if="passwordStrengthMessage" class="password-strength">
                {{ passwordStrengthMessage }}
              </div>
            </a-form-item>

            <a-form-item name="confirm_password" label="确认新密码">
              <a-input-password
                v-model:value="passwordForm.confirm_password"
                size="large"
                placeholder="请再次输入新密码"
                :prefix="h(LockOutlined)"
              />
            </a-form-item>

            <a-form-item>
              <a-space size="middle" style="width: 100%">
                <a-button
                  size="large"
                  @click="currentStep = 0"
                  style="flex: 1"
                >
                  上一步
                </a-button>
                <a-button
                  type="primary"
                  size="large"
                  html-type="submit"
                  :loading="resetting"
                  style="flex: 2"
                  class="reset-button"
                >
                  重置密码
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>

        <!-- 第三步：重置完成 -->
        <div v-if="currentStep === 2" class="step-content success-content">
          <div class="success-icon">
            <CheckCircleOutlined />
          </div>
          <h3>密码重置成功！</h3>
          <p>您的密码已成功重置，请使用新密码登录</p>
          
          <a-button
            type="primary"
            size="large"
            @click="goToLogin"
            block
            class="login-button"
          >
            立即登录
          </a-button>
        </div>

        <!-- 返回登录链接 -->
        <div v-if="currentStep < 2" class="back-to-login">
          <span>想起密码了？</span>
          <router-link to="/login" class="link">
            返回登录
          </router-link>
        </div>

        <!-- 版权信息 -->
        <div class="footer-info">
          <p>&copy; 2024 灵邀AI达人管家. All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  PhoneOutlined,
  LockOutlined,
  SafetyOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { authAPI, validatePhone, validatePassword, validateVerificationCode } from '@/services'

const router = useRouter()

// 当前步骤
const currentStep = ref(0)

// 第一步表单数据
const phoneForm = ref({
  phone: '',
  verification_code: ''
})

// 第二步表单数据
const passwordForm = ref({
  new_password: '',
  confirm_password: ''
})

// 表单引用
const phoneFormRef = ref()
const passwordFormRef = ref()

// 状态管理
const sendingCode = ref(false)
const verifying = ref(false)
const resetting = ref(false)
const countdown = ref(0)

// 计算属性
const canSendCode = computed(() => {
  return validatePhone(phoneForm.value.phone)
})

const passwordStrengthMessage = ref('')

// 第一步验证规则
const phoneRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { 
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        if (!validatePhone(value)) {
          return Promise.reject(new Error('请输入正确的手机号格式'))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  verification_code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        if (!validateVerificationCode(value)) {
          return Promise.reject(new Error('验证码格式不正确'))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 第二步验证规则
const passwordRules = {
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        const validation = validatePassword(value)
        if (!validation.isValid) {
          return Promise.reject(new Error(validation.errors.join(', ')))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        if (value !== passwordForm.value.new_password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 处理密码输入
const handlePasswordInput = () => {
  const password = passwordForm.value.new_password
  if (!password) {
    passwordStrengthMessage.value = ''
    return
  }
  
  const validation = validatePassword(password)
  if (validation.isValid) {
    passwordStrengthMessage.value = '密码强度：良好'
  } else {
    passwordStrengthMessage.value = `密码要求：${validation.errors.join(', ')}`
  }
}

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) {
    message.error('请先输入正确的手机号')
    return
  }

  try {
    sendingCode.value = true
    
    await authAPI.sendSmsCode({
      phone: phoneForm.value.phone,
      type: '重置密码'
    })
    
    message.success('验证码已发送，请查收短信')
    
    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    message.error(error.message || '发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 处理第一步：发送验证码并验证
const handleSendCode = async () => {
  try {
    verifying.value = true
    
    // 这里可以添加验证码验证逻辑（如果后端提供独立验证接口）
    // 目前直接进入下一步，在重置密码时一起验证
    
    currentStep.value = 1
    message.success('验证码已确认，请设置新密码')
    
  } catch (error) {
    console.error('验证失败:', error)
    message.error(error.message || '验证失败，请重试')
  } finally {
    verifying.value = false
  }
}

// 处理密码重置
const handleResetPassword = async () => {
  try {
    resetting.value = true
    
    await authAPI.resetPassword({
      phone: phoneForm.value.phone,
      verification_code: phoneForm.value.verification_code,
      new_password: passwordForm.value.new_password
    })
    
    currentStep.value = 2
    message.success('密码重置成功！')
    
  } catch (error) {
    console.error('密码重置失败:', error)
    message.error(error.message || '密码重置失败，请重试')
  } finally {
    resetting.value = false
  }
}

// 跳转到登录页
const goToLogin = () => {
  router.push({
    path: '/login',
    query: { phone: phoneForm.value.phone }
  })
}
</script>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.forgot-password-wrapper {
  width: 100%;
  max-width: 450px;
}

.forgot-password-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.forgot-password-header {
  text-align: center;
  margin-bottom: 24px;
}

.title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.reset-steps {
  margin-bottom: 32px;
}

.step-content {
  min-height: 300px;
}

.code-input-group {
  display: flex;
  align-items: center;
}

.code-button {
  min-width: 120px;
  border-color: #667eea;
  color: #667eea;
}

.code-button:hover {
  border-color: #764ba2;
  color: #764ba2;
}

.password-strength {
  font-size: 12px;
  margin-top: 4px;
  color: #666;
}

.verify-button,
.reset-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.verify-button:hover,
.reset-button:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 64px;
  color: #52c41a;
  margin-bottom: 16px;
}

.success-content h3 {
  color: #1a1a1a;
  margin-bottom: 8px;
}

.success-content p {
  color: #666;
  margin-bottom: 24px;
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #52c41a, #389e0d);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #73d13d, #52c41a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
}

.back-to-login {
  text-align: center;
  color: #666;
  margin-top: 24px;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  margin-left: 4px;
}

.link:hover {
  color: #764ba2;
}

.footer-info {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.footer-info p {
  color: #999;
  font-size: 12px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .forgot-password-card {
    padding: 24px;
    margin: 10px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .code-button {
    min-width: 100px;
    font-size: 12px;
  }
  
  .step-content {
    min-height: 250px;
  }
}
</style> 