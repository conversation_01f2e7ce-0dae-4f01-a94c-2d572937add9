"""
店铺业务服务层
负责处理店铺相关的业务逻辑

特性：
1. 店铺详情查询业务逻辑
2. 店铺绑定业务逻辑
3. 店铺解绑业务逻辑
4. 店铺统计业务逻辑
5. 店铺信息更新业务逻辑
6. 遵循三层分离架构，只处理业务逻辑
"""

from typing import Any, Dict, Optional

from 数据.店铺数据访问层 import 店铺数据访问实例
from 服务.异步用户服务 import 异步收到店铺信息
from 日志 import 接口日志器, 错误日志器
from 状态 import 通用


class 店铺业务服务:
    """店铺业务服务类"""

    @staticmethod
    async def 获取店铺详情_业务处理(用户id: int, 店铺id: int) -> Dict[str, Any]:
        """
        获取店铺详情业务处理
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID
            
        Returns:
            处理结果
        """
        try:
            # 1. 验证用户权限
            有权限 = await 店铺数据访问实例.验证用户店铺权限(用户id, 店铺id)
            if not 有权限:
                return {
                    "status": 通用.无权限,
                    "message": "您没有权限访问该店铺信息",
                    "data": None
                }

            # 2. 获取店铺详情
            店铺详情 = await 店铺数据访问实例.获取店铺详情(用户id, 店铺id)
            if not 店铺详情:
                return {
                    "status": 通用.未找到,
                    "message": "店铺信息不存在",
                    "data": None
                }

            # 3. 格式化返回数据
            结果数据 = {
                "店铺id": 店铺详情["店铺id"],
                "店铺标识": 店铺详情["店铺标识"] or "",
                "店铺名称": 店铺详情["店铺名称"] or "",
                "店铺头像": 店铺详情["店铺头像"] or "",
                "创建时间": 店铺详情["创建时间"].strftime("%Y-%m-%d %H:%M:%S")
                if 店铺详情["创建时间"]
                else "",
                "更新时间": 店铺详情["更新时间"].strftime("%Y-%m-%d %H:%M:%S")
                if 店铺详情["更新时间"]
                else "",
                "产品数量": 店铺详情["产品数量"] or 0,
                "抖音商品数量": 店铺详情["抖音商品数量"] or 0,
            }

            接口日志器.info(f"获取店铺详情成功: 用户id={用户id}, 店铺id={店铺id}")
            return {
                "status": 通用.成功,
                "message": "获取店铺详情成功",
                "data": 结果数据
            }

        except Exception as e:
            错误日志器.error(f"获取店铺详情业务处理失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return {
                "status": 通用.服务器错误,
                "message": f"获取店铺详情失败: {str(e)}",
                "data": None
            }

    @staticmethod
    async def 绑定店铺_业务处理(
        用户id: int, shop_id: str, shop_name: str, avatar: str
    ) -> Dict[str, Any]:
        """
        绑定店铺业务处理
        
        Args:
            用户id: 用户id
            shop_id: 店铺标识
            shop_name: 店铺名称
            avatar: 店铺头像
            
        Returns:
            处理结果
        """
        try:
            # 调用现有的店铺绑定服务
            处理结果 = await 异步收到店铺信息(用户id, shop_id, shop_name, avatar)

            # 转换为统一格式
            if isinstance(处理结果, dict) and "status" in 处理结果:
                if "message" not in 处理结果 and "msg" in 处理结果:
                    处理结果["message"] = 处理结果["msg"]
                    del 处理结果["msg"]
                if "data" not in 处理结果:
                    处理结果["data"] = None
                return 处理结果

            接口日志器.info(f"绑定店铺成功: 用户id={用户id}, shop_id={shop_id}")
            return {
                "status": 通用.成功,
                "message": "店铺绑定成功",
                "data": 处理结果
            }

        except Exception as e:
            错误日志器.error(f"绑定店铺业务处理失败: 用户id={用户id}, shop_id={shop_id}, 错误={str(e)}")
            return {
                "status": 通用.服务器错误,
                "message": f"绑定店铺失败: {str(e)}",
                "data": None
            }

    @staticmethod
    async def 解绑店铺_业务处理(用户id: int, 店铺id: int) -> Dict[str, Any]:
        """
        解绑店铺业务处理
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID
            
        Returns:
            处理结果
        """
        try:
            # 1. 验证用户权限
            有权限 = await 店铺数据访问实例.验证用户店铺权限(用户id, 店铺id)
            if not 有权限:
                return {
                    "status": 通用.无权限,
                    "message": "您没有权限操作该店铺",
                    "data": None
                }

            # 2. 执行解绑操作
            解绑成功 = await 店铺数据访问实例.解绑用户店铺(用户id, 店铺id)
            if 解绑成功:
                接口日志器.info(f"解绑店铺成功: 用户id={用户id}, 店铺id={店铺id}")
                return {
                    "status": 通用.成功,
                    "message": "店铺解绑成功",
                    "data": None
                }
            else:
                return {
                    "status": 通用.服务器错误,
                    "message": "解绑操作失败",
                    "data": None
                }

        except Exception as e:
            错误日志器.error(f"解绑店铺业务处理失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return {
                "status": 通用.服务器错误,
                "message": f"解绑店铺失败: {str(e)}",
                "data": None
            }

    @staticmethod
    async def 获取店铺统计_业务处理(用户id: int, 店铺id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取店铺统计业务处理
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID（可选）
            
        Returns:
            处理结果
        """
        try:
            # 获取统计数据
            统计数据 = await 店铺数据访问实例.获取店铺统计(用户id, 店铺id)

            接口日志器.info(f"获取店铺统计成功: 用户id={用户id}, 店铺id={店铺id}")
            return {
                "status": 通用.成功,
                "message": "获取店铺统计成功",
                "data": 统计数据
            }

        except Exception as e:
            错误日志器.error(f"获取店铺统计业务处理失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return {
                "status": 通用.服务器错误,
                "message": f"获取店铺统计失败: {str(e)}",
                "data": None
            }

    @staticmethod
    async def 更新店铺信息_业务处理(
        用户id: int, 店铺id: int, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新店铺信息业务处理
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID
            更新数据: 要更新的数据
            
        Returns:
            处理结果
        """
        try:
            # 1. 验证用户权限
            有权限 = await 店铺数据访问实例.验证用户店铺权限(用户id, 店铺id)
            if not 有权限:
                return {
                    "status": 通用.无权限,
                    "message": "您没有权限操作该店铺",
                    "data": None
                }

            # 2. 执行更新操作
            更新成功 = await 店铺数据访问实例.更新店铺信息(店铺id, 更新数据)
            if 更新成功:
                接口日志器.info(f"更新店铺信息成功: 用户id={用户id}, 店铺id={店铺id}")
                return {
                    "status": 通用.成功,
                    "message": "店铺信息更新成功",
                    "data": None
                }
            else:
                return {
                    "status": 通用.服务器错误,
                    "message": "更新操作失败",
                    "data": None
                }

        except Exception as e:
            错误日志器.error(f"更新店铺信息业务处理失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return {
                "status": 通用.服务器错误,
                "message": f"更新店铺信息失败: {str(e)}",
                "data": None
            }


# 创建业务服务实例
店铺业务服务实例 = 店铺业务服务()
