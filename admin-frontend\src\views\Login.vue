<template>
  <div class="login-container">
    <ErrorBoundary>
      <div class="login-form">
        <div class="login-header">
          <h2>管理员登录</h2>
          <p>请输入您的登录凭据</p>
        </div>
        
        <a-form
          :model="formData"
          name="login"
          :label-col="{ span: 0 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @submit.prevent="handleSubmit"
        >
          <a-form-item
            name="username"
            :validate-status="getFieldStatus('username')"
            :help="getFieldError('username')"
          >
            <a-input
              v-model:value="formData.username"
              placeholder="用户名"
              size="large"
              @blur="handleFieldBlur('username')"
              @change="handleFieldChange('username', $event.target.value)"
            >
              <template #prefix>
                <UserOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            name="password"
            :validate-status="getFieldStatus('password')"
            :help="getFieldError('password')"
          >
            <a-input-password
              v-model:value="formData.password"
              placeholder="密码"
              size="large"
              @blur="handleFieldBlur('password')"
              @change="handleFieldChange('password', $event.target.value)"
            >
              <template #prefix>
                <LockOutlined class="site-form-item-icon" />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item>
            <a-button
              type="primary"
              class="login-form-button"
              size="large"
              :loading="loading || isSubmitting"
              @click="handleSubmit"
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>
        
        <!-- 优雅的错误提示 -->
        <div v-if="errorMsg" class="error-message">
          <a-alert
            :message="errorMsg"
            type="error"
            show-icon
            closable
            @close="clearError"
          >
            <template #description>
              <div class="error-description">
                <p v-if="isPasswordError">请检查您的用户名和密码是否正确</p>
                <p v-else-if="isNetworkError">网络连接异常，请稍后重试</p>
                <p v-else>如果问题持续存在，请联系系统管理员</p>

                <!-- 重试按钮 -->
                <div class="error-actions" v-if="showRetryButton">
                  <a-button
                    type="link"
                    size="small"
                    @click="retryLogin"
                    :loading="retrying"
                  >
                    <template #icon>
                      <ReloadOutlined />
                    </template>
                    重试登录
                  </a-button>
                </div>
              </div>
            </template>
          </a-alert>
        </div>
        
        <LoadingSpinner v-if="loading" type="fullscreen" tip="正在登录..." />
      </div>
    </ErrorBoundary>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store'
import { LockOutlined, UserOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useSuperAdminRequest } from '../composables/useApiRequest'
import superAdminService from '../services/superAdminService'
import ErrorBoundary from '../components/common/ErrorBoundary.vue'
import LoadingSpinner from '../components/common/LoadingSpinner.vue'

const router = useRouter()
const userStore = useUserStore()

// 表单数据 - 使用简单的响应式数据，不需要复杂的验证Hook
const formData = ref({
  username: '',
  password: ''
})

// 表单验证状态
const validationErrors = ref({})
const fieldStates = ref({})

// 使用SuperAdmin响应处理 Hook
const { 执行API请求, loading, data, error, isSuccess } = useSuperAdminRequest({
  showSuccessMessage: false, // 登录成功我们手动处理消息
  showErrorMessage: false,   // 登录失败消息我们手动处理，避免重复显示
  autoHandleLogin: false     // 登录页面不需要自动跳转登录
})

// 错误消息显示和重试逻辑
const errorMsg = ref('')
const retrying = ref(false)
const loginAttempts = ref(0)
const maxAttempts = 3
const isSubmitting = ref(false) // 全局提交锁

// 计算错误类型
const isPasswordError = computed(() => {
  return errorMsg.value && (
    errorMsg.value.includes('密码错误') ||
    errorMsg.value.includes('用户名或密码错误') ||
    errorMsg.value.includes('登录失败，请检查用户名和密码')
  )
})

const isNetworkError = computed(() => {
  return errorMsg.value && (
    errorMsg.value.includes('网络') ||
    errorMsg.value.includes('连接') ||
    errorMsg.value.includes('超时')
  )
})

const showRetryButton = computed(() => {
  return errorMsg.value && loginAttempts.value < maxAttempts && !loading.value
})

// 表单验证函数
const validateForm = () => {
  const errors = {}
  
  if (!formData.value.username.trim()) {
    errors.username = '请输入用户名'
  }
  
  if (!formData.value.password) {
    errors.password = '请输入密码'
  }
  
  validationErrors.value = errors
  return Object.keys(errors).length === 0
}

// 获取字段状态 - 用于显示验证结果的视觉反馈
const getFieldStatus = (fieldName) => {
  if (validationErrors.value[fieldName]) {
    return 'error'
  }
  if (fieldStates.value[fieldName]?.touched && !validationErrors.value[fieldName]) {
    return 'success'
  }
  return ''
}

// 获取字段错误信息
const getFieldError = (fieldName) => {
  return validationErrors.value[fieldName] || ''
}

// 处理字段失焦事件 - 进行单字段验证
const handleFieldBlur = (fieldName) => {
  fieldStates.value[fieldName] = { touched: true }
  
  // 单独验证该字段
  const errors = { ...validationErrors.value }
  if (fieldName === 'username' && !formData.value.username.trim()) {
    errors.username = '请输入用户名'
  } else if (fieldName === 'password' && !formData.value.password) {
    errors.password = '请输入密码'
  } else {
    delete errors[fieldName]
  }
  
  validationErrors.value = errors
}

// 处理字段值变化
const handleFieldChange = (fieldName, value) => {
  // 清除该字段的错误
  if (validationErrors.value[fieldName]) {
    const errors = { ...validationErrors.value }
    delete errors[fieldName]
    validationErrors.value = errors
  }
}

// 手动提交处理（防止重复提交）
const handleSubmit = async () => {
  // 全局提交锁保护
  if (isSubmitting.value) {
    console.log('🔒 提交已锁定，忽略重复点击')
    return
  }

  isSubmitting.value = true
  try {
    await onFinish()
  } finally {
    isSubmitting.value = false
  }
}

// 登录表单提交处理
const onFinish = async (values) => {
  // 防重复提交保护
  if (loading.value) {
    console.log('⏳ 登录请求正在进行中，忽略重复提交')
    return
  }

  const requestId = Date.now()
  console.log(`🚀 开始执行登录操作 [请求ID: ${requestId}]`)
  errorMsg.value = ''

  // 表单验证
  if (!validateForm()) {
    console.log('❌ 表单验证失败')
    return
  }
  
  // 调用SuperAdmin登录服务
  const loginData = await 执行API请求(
    () => superAdminService.login({
      用户名: formData.value.username,
      密码: formData.value.password
    }),
    {
      requiresAuth: false,  // 登录请求不需要预先认证
      onSuccess: async (data, response) => {
        console.log('✅ 登录成功，处理登录数据:', data)
        await handleLoginSuccess(data)
      },
      onError: (error) => {
        // 根据错误类型决定日志级别
        if (error.message && (error.message.includes('用户名或密码错误') || error.message.includes('密码错误'))) {
          console.log(`🔐 登录凭据验证失败 [请求ID: ${requestId}]:`, error.message)
        } else {
          console.warn(`⚠️ 登录请求异常 [请求ID: ${requestId}]:`, error)
        }
        handleLoginError(error)
      }
    }
  )
}

// 处理登录成功逻辑
const handleLoginSuccess = async (loginData) => {
  try {
    console.log('🔍 调试：完整的登录响应数据:', JSON.stringify(loginData, null, 2))

    // 统一响应模型结构：{status: 100, message: "消息", data: {...}}
    // 从data字段中获取实际的业务数据
    const responseData = loginData?.data || loginData
    console.log('🔍 调试：提取的响应数据:', JSON.stringify(responseData, null, 2))

    const token = responseData?.access_token
    const adminId = responseData?.管理员ID
    const permissions = responseData?.权限 || []

    console.log('🔍 调试：提取的令牌:', token)
    console.log('🔍 调试：提取的管理员ID:', adminId)

    if (!token) {
      throw new Error('未获取到访问令牌')
    }
    
    // 构建用户信息对象
    let userInfo = {
      username: formData.value.username,
      id: adminId || null,
      roles: ['admin'],
      permissions: permissions
    }
    
    // 尝试从JWT token中解析更多用户信息
    try {
      const tokenParts = token.split('.')
      if (tokenParts.length === 3) {
        const payload = JSON.parse(atob(tokenParts[1]))
        console.log('📋 JWT载荷解析结果:', payload)
        
        // 从token载荷中提取用户信息
        if (payload.用户信息 || payload.userInfo) {
          const tokenUserInfo = payload.用户信息 || payload.userInfo
          userInfo = {
            ...userInfo,
            id: tokenUserInfo.id || userInfo.id,
            username: tokenUserInfo.用户名 || tokenUserInfo.username || userInfo.username,
            role: tokenUserInfo.角色 || tokenUserInfo.role || 'admin'
          }
        }
      }
    } catch (tokenError) {
      console.warn('⚠️ JWT token解析失败，使用基础用户信息:', tokenError)
    }
    
    // 保存登录状态到store
    userStore.loginSuccess(userInfo, token, permissions)
    
    // 显示成功消息并跳转
    message.success('登录成功！欢迎回来')
    
    console.log('🎉 登录流程完成，即将跳转到仪表盘')
    
    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      router.push('/dashboard')
    }, 500)
    
  } catch (error) {
    console.error('❌ 处理登录成功时发生错误:', error)
    errorMsg.value = '登录处理失败：' + error.message
  }
}

// 表单提交失败处理
const onFinishFailed = (errorInfo) => {
  console.log('❌ 登录表单验证失败:', errorInfo)
  message.error('请正确填写表单信息！')
}

// 优雅的错误处理
const handleLoginError = (error) => {
  loginAttempts.value++
  console.log(`📊 登录尝试次数: ${loginAttempts.value}`)

  // 根据错误类型设置不同的错误消息
  let errorMessage = error.message || '登录失败'

  if (errorMessage.includes('用户名或密码错误') || errorMessage.includes('密码错误')) {
    errorMessage = '用户名或密码错误'

    // 如果尝试次数过多，给出更详细的提示
    if (loginAttempts.value >= 2) {
      errorMessage += `，已尝试 ${loginAttempts.value} 次`
    }

    if (loginAttempts.value >= maxAttempts) {
      errorMessage += '，请检查您的凭据或联系管理员'
    }
  } else if (errorMessage.includes('网络') || errorMessage.includes('连接')) {
    errorMessage = '网络连接异常，请检查网络后重试'
  } else if (errorMessage.includes('超时')) {
    errorMessage = '请求超时，请稍后重试'
  } else if (errorMessage.includes('服务器')) {
    errorMessage = '服务器暂时不可用，请稍后重试'
  }

  errorMsg.value = errorMessage

  // 如果是密码错误，清空密码字段让用户重新输入
  if (errorMessage.includes('密码错误') || errorMessage.includes('用户名或密码错误')) {
    formData.value.password = ''

    // 聚焦到密码输入框
    setTimeout(() => {
      const passwordInput = document.querySelector('input[type="password"]')
      if (passwordInput) {
        passwordInput.focus()
      }
    }, 100)
  }
}

// 清除错误信息
const clearError = () => {
  errorMsg.value = ''
  loginAttempts.value = 0
}

// 重试登录
const retryLogin = async () => {
  if (retrying.value || loading.value) return

  retrying.value = true
  errorMsg.value = ''

  try {
    await onFinish()
  } finally {
    retrying.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-form {
  width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  color: #1f2937;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.login-header p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.login-form-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.login-form-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.site-form-item-icon {
  color: #9ca3af;
}

.error-message {
  margin-top: 16px;
  animation: slideInDown 0.3s ease-out;
}

.error-description {
  margin-top: 8px;
}

.error-description p {
  margin: 4px 0;
  color: #666;
  font-size: 13px;
}

.error-actions {
  margin-top: 12px;
  text-align: right;
}

.error-actions .ant-btn {
  padding: 0;
  height: auto;
  font-size: 12px;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.ant-input) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

:deep(.ant-input:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

:deep(.ant-input-password) {
  border-radius: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-form-item-explain-error) {
  font-size: 12px;
  margin-top: 4px;
}

@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-form {
    width: 100%;
    max-width: 360px;
    padding: 24px;
  }
  
  .login-header h2 {
    font-size: 24px;
  }
}
</style>