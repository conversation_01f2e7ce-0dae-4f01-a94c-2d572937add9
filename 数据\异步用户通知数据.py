"""
用户通知数据层
处理用户通知相关的数据库操作
"""

import json
from typing import List, Dict, Any, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器


async def 异步获取用户通知列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    通知类型: Optional[str] = None,
    是否已读: Optional[bool] = None,
    排序字段: str = "创建时间",
    排序顺序: str = "DESC"
) -> Dict[str, Any]:
    """
    获取用户通知列表
    整合用户通知表和通告表的数据，通告默认为未读状态
    """
    try:
        # 计算偏移量
        偏移量 = (页码 - 1) * 每页数量

        # 构建排序子句
        if 排序字段 not in ["创建时间", "重要性"]:
            排序字段 = "创建时间"
        if 排序顺序.upper() not in ["ASC", "DESC"]:
            排序顺序 = "DESC"

        # 简化查询：分别获取通告和业务通知，然后合并
        通知列表 = []

        # 1. 获取系统通告（如果需要）
        if 通知类型 != "business":
            通告查询 = """
                SELECT
                    CONCAT('announcement_', a.id) as id,
                    $1 as 用户id,
                    'system_update' as 通知类型,
                    a.标题,
                    a.内容,
                    a.重要性,
                    COALESCE(n.是否已读, 0) as 是否已读,
                    n.阅读时间,
                    a.id as 来源通告id,
                    NULL as 业务关联id,
                    NULL as 业务类型,
                    a.创建时间,
                    a.更新时间
                FROM 通告 a
                LEFT JOIN 用户通知表 n ON (n.来源通告id = a.id AND n.用户id = $2)
                WHERE a.已发布 = 1
            """

            # 添加已读状态筛选
            if 是否已读 is not None:
                if 是否已读:
                    通告查询 += " AND n.是否已读 = 1"
                else:
                    通告查询 += " AND (n.是否已读 = 0 OR n.是否已读 IS NULL)"

            通告结果 = await 异步连接池实例.执行查询(通告查询, (用户id, 用户id))
            通知列表.extend(通告结果 or [])

        # 2. 获取业务通知（如果需要）
        if 通知类型 != "system_update":
            业务查询 = """
                SELECT
                    id,
                    用户id,
                    通知类型,
                    标题,
                    内容,
                    重要性,
                    是否已读,
                    阅读时间,
                    来源通告id,
                    业务关联id,
                    业务类型,
                    创建时间,
                    更新时间
                FROM 用户通知表
                WHERE 用户id = $1 AND 来源通告id IS NULL
            """

            # 添加已读状态筛选
            if 是否已读 is not None:
                业务查询 += f" AND 是否已读 = {1 if 是否已读 else 0}"

            业务结果 = await 异步连接池实例.执行查询(业务查询, (用户id,))
            通知列表.extend(业务结果 or [])

        # 3. 排序和分页
        if 通知列表:
            # Python排序
            reverse = (排序顺序 == "DESC")
            if 排序字段 == "创建时间":
                通知列表.sort(key=lambda x: x['创建时间'] or '', reverse=reverse)
            elif 排序字段 == "重要性":
                通知列表.sort(key=lambda x: x['重要性'] or 1, reverse=reverse)

        # 计算总数和分页
        总数 = len(通知列表)
        总页数 = (总数 + 每页数量 - 1) // 每页数量 if 总数 and 总数 > 0 else 0

        # 分页切片
        分页列表 = 通知列表[偏移量:偏移量 + 每页数量]

        # 处理内容字段
        for 通知 in 分页列表:
            if 通知.get('内容'):
                try:
                    if isinstance(通知['内容'], str):
                        通知['内容'] = json.loads(通知['内容'])
                except json.JSONDecodeError:
                    通知['内容'] = [{"类型": "文本", "内容": str(通知['内容'])}]
            else:
                通知['内容'] = []

        数据库日志器.info(f"用户 {用户id} 获取通知列表: 总数={总数}, 当前页={页码}, 返回={len(分页列表)}条")

        return {
            '列表': 分页列表,
            '总数': 总数,
            '总页数': 总页数,
            '当前页': 页码
        }

    except Exception as e:
        错误日志器.error(f"获取用户通知列表失败: {str(e)}", exc_info=True)
        return {'列表': [], '总数': 0, '总页数': 0, '当前页': 页码}


async def 异步获取用户未读通知数量(用户id: int) -> int:
    """
    获取用户未读通知数量
    包括通告表中未读的系统通知和用户通知表中的未读通知
    """
    try:
        未读数量 = 0

        # 1. 统计未读的系统通告
        通告查询 = """
            SELECT COUNT(*) as count
            FROM 通告 a
            LEFT JOIN 用户通知表 n ON (n.来源通告id = a.id AND n.用户id = $1)
            WHERE a.已发布 = 1 AND (n.是否已读 IS NULL OR n.是否已读 = 0)
        """
        通告结果 = await 异步连接池实例.执行查询(通告查询, (用户id,))
        未读数量 += 通告结果[0]['count'] if 通告结果 else 0

        # 2. 统计未读的业务通知
        业务查询 = """
            SELECT COUNT(*) as count
            FROM 用户通知表
            WHERE 用户id = $1 AND 来源通告id IS NULL AND 是否已读 = 0
        """
        业务结果 = await 异步连接池实例.执行查询(业务查询, (用户id,))
        未读数量 += 业务结果[0]['count'] if 业务结果 else 0

        数据库日志器.debug(f"用户 {用户id} 未读通知数量: {未读数量}")
        return 未读数量

    except Exception as e:
        错误日志器.error(f"获取用户未读通知数量失败: {str(e)}", exc_info=True)
        return 0


async def 异步标记通告已读(通告id: int, 用户id: int) -> Dict[str, Any]:
    """
    标记通告为已读

    参数:
        通告id: 通告id
        用户id: 用户id（用于权限验证）

    返回:
        操作结果字典
    """
    try:
        # 验证通告是否存在且已发布
        验证查询 = "SELECT id FROM 通告 WHERE id = $1 AND 已发布 = 1"
        验证结果 = await 异步连接池实例.执行查询(验证查询, (通告id,))

        if not 验证结果:
            return {"成功": False, "消息": "通告不存在或未发布"}

        # 检查是否已有用户通知记录
        检查查询 = "SELECT id FROM 用户通知表 WHERE 来源通告id = $1 AND 用户id = $2"
        检查结果 = await 异步连接池实例.执行查询(检查查询, (通告id, 用户id))

        if 检查结果:
            # 更新现有记录
            更新语句 = """
                UPDATE 用户通知表
                SET 是否已读 = 1, 阅读时间 = NOW()
                WHERE 来源通告id = $1 AND 用户id = $2
            """
            影响行数 = await 异步连接池实例.执行更新(更新语句, (通告id, 用户id))
            操作成功 = 影响行数 > 0
        else:
            # 创建新的已读记录
            获取通告查询 = "SELECT 标题, 内容, 重要性 FROM 通告 WHERE id = $1"
            通告结果 = await 异步连接池实例.执行查询(获取通告查询, (通告id,))
            通告 = 通告结果[0]

            插入语句 = """
                INSERT INTO 用户通知表
                (用户id, 通知类型, 标题, 内容, 重要性, 是否已读, 阅读时间, 来源通告id, 创建时间)
                VALUES ($1, $2, $3, $4, $5, $6, NOW(), $7, NOW())
            """
            await 异步连接池实例.执行插入(
                插入语句,
                (用户id, "system_update", 通告['标题'], 通告['内容'], 通告['重要性'], 1, 通告id)
            )
            # 插入成功（没有抛出异常）
            操作成功 = True

        if 操作成功:
            数据库日志器.info(f"用户 {用户id} 标记通告 {通告id} 为已读")
            return {"成功": True, "消息": "标记已读成功"}
        else:
            return {"成功": False, "消息": "标记已读失败"}

    except Exception as e:
        错误日志器.error(f"标记通告已读失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"标记已读失败: {str(e)}"}


async def 异步标记通知已读(通知id: int, 用户id: int) -> Dict[str, Any]:
    """
    标记普通通知为已读

    参数:
        通知id: 通知id
        用户id: 用户id（用于权限验证）

    返回:
        操作结果字典
    """
    try:
        # 验证通知是否属于该用户
        验证查询 = "SELECT id FROM 用户通知表 WHERE id = $1 AND 用户id = $2"
        验证结果 = await 异步连接池实例.执行查询(验证查询, (通知id, 用户id))

        if not 验证结果:
            return {"成功": False, "消息": "通知不存在或无权限"}

        # 更新已读状态
        更新语句 = """
            UPDATE 用户通知表
            SET 是否已读 = 1, 阅读时间 = NOW()
            WHERE id = $1 AND 用户id = $2 AND 是否已读 = 0
        """
        影响行数 = await 异步连接池实例.执行更新(更新语句, (通知id, 用户id))

        if 影响行数 > 0:
            数据库日志器.info(f"用户 {用户id} 标记通知 {通知id} 为已读")
            return {"成功": True, "消息": "标记已读成功"}
        else:
            return {"成功": False, "消息": "通知已经是已读状态"}

    except Exception as e:
        错误日志器.error(f"标记通知已读失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"标记已读失败: {str(e)}"}


async def 异步批量标记通知已读(
    通知id列表: List[int] ,
    通告id列表: List[int] ,
    用户id: int 
) -> Dict[str, Any]:
    """
    批量标记通知为已读
    支持分别传入通告id列表和普通通知id列表

    参数:
        通知id列表: 普通通知id列表
        通告id列表: 通告id列表
        用户id: 用户id
    """
    try:
        if not 通知id列表 and not 通告id列表:
            return {"成功": False, "消息": "通知id列表和通告id列表不能都为空"}

        总影响行数 = 0

        # 处理通告id
        if 通告id列表:
            for 通告id in 通告id列表:
                结果 = await 异步标记通告已读(通告id, 用户id)
                if 结果["成功"]:
                    总影响行数 += 1

        # 处理普通通知id
        if 通知id列表:
            占位符 = ','.join([f'${i+1}' for i in range(len(通知id列表))])
            更新语句 = f"""
                UPDATE 用户通知表
                SET 是否已读 = 1, 阅读时间 = NOW()
                WHERE id IN ({占位符}) AND 用户id = ${len(通知id列表)+1} AND 是否已读 = 0
            """
            更新参数 = tuple(通知id列表) + (用户id,)
            影响行数 = await 异步连接池实例.执行更新(更新语句, 更新参数)
            总影响行数 += 影响行数

        数据库日志器.info(f"用户 {用户id} 批量标记 {总影响行数} 个通知为已读")
        return {"成功": True, "消息": f"成功标记 {总影响行数} 个通知为已读", "影响行数": 总影响行数}

    except Exception as e:
        错误日志器.error(f"批量标记通知已读失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"批量标记已读失败: {str(e)}"}


async def 异步标记所有通知已读(用户id: int) -> Dict[str, Any]:
    """
    标记用户所有通知为已读
    包括通告和业务通知

    参数:
        用户id: 用户id

    返回:
        操作结果字典
    """
    try:
        总影响行数 = 0

        # 1. 更新现有的用户通知表中的未读记录
        更新现有语句 = """
            UPDATE 用户通知表
            SET 是否已读 = 1, 阅读时间 = NOW()
            WHERE 用户id = $1 AND 是否已读 = 0
        """
        现有影响行数 = await 异步连接池实例.执行更新(更新现有语句, (用户id,))
        总影响行数 += 现有影响行数

        # 2. 为所有已发布但用户未有记录的通告创建已读记录
        插入通告已读语句 = """
            INSERT INTO 用户通知表
            (用户id, 通知类型, 标题, 内容, 重要性, 是否已读, 阅读时间, 来源通告id, 创建时间)
            SELECT
                $1 as 用户id,
                'system_update' as 通知类型,
                a.标题,
                a.内容,
                a.重要性,
                1 as 是否已读,
                NOW() as 阅读时间,
                a.id as 来源通告id,
                NOW() as 创建时间
            FROM 通告 a
            LEFT JOIN 用户通知表 n ON (n.来源通告id = a.id AND n.用户id = $2)
            WHERE a.已发布 = 1 AND n.id IS NULL
        """
        通告影响行数 = await 异步连接池实例.执行插入(插入通告已读语句, (用户id, 用户id))
        if 通告影响行数:
            总影响行数 += 通告影响行数

        数据库日志器.info(f"用户 {用户id} 标记所有通知为已读，共 {总影响行数} 个")
        return {"成功": True, "消息": f"成功标记 {总影响行数} 个通知为已读", "影响行数": 总影响行数}

    except Exception as e:
        错误日志器.error(f"标记所有通知已读失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"标记所有已读失败: {str(e)}"}


async def 异步获取通知详情(通知id: int, 用户id: int) -> Optional[Dict[str, Any]]:
    """
    获取通知详情
    支持通告id和普通通知id
    """
    try:
        # 检查是否是通告id
        if str(通知id).startswith('announcement_'):
            通告id = int(str(通知id).replace('announcement_', ''))

            # 查询通告详情
            查询语句 = """
                SELECT
                    CONCAT('announcement_', a.id) as id,
                    $1 as 用户id,
                    'system_update' as 通知类型,
                    a.标题,
                    a.内容,
                    a.重要性,
                    COALESCE(n.是否已读, 0) as 是否已读,
                    n.阅读时间,
                    a.id as 来源通告id,
                    NULL as 业务关联id,
                    NULL as 业务类型,
                    a.创建时间,
                    a.更新时间
                FROM 通告 a
                LEFT JOIN 用户通知表 n ON (n.来源通告id = a.id AND n.用户id = $1)
                WHERE a.id = $2 AND a.已发布 = 1
            """
            结果 = await 异步连接池实例.执行查询(查询语句, (用户id, 用户id, 通告id))
        else:
            # 查询普通通知详情
            查询语句 = """
                SELECT
                    id, 用户id, 通知类型, 标题, 内容, 重要性,
                    是否已读, 阅读时间, 来源通告id, 业务关联id, 业务类型,
                    创建时间, 更新时间
                FROM 用户通知表
                WHERE id = $1 AND 用户id = $2
            """
            结果 = await 异步连接池实例.执行查询(查询语句, (通知id, 用户id))

        if not 结果:
            return None

        通知详情 = 结果[0]

        # 处理内容字段
        if 通知详情.get('内容'):
            try:
                if isinstance(通知详情['内容'], str):
                    通知详情['内容'] = json.loads(通知详情['内容'])
            except json.JSONDecodeError:
                通知详情['内容'] = [{"类型": "文本", "内容": str(通知详情['内容'])}]
        else:
            通知详情['内容'] = []

        return 通知详情

    except Exception as e:
        错误日志器.error(f"获取通知详情失败: {str(e)}", exc_info=True)
        return None
