<template>
  <div class="team-members-tab">
    <!-- 操作栏 -->
    <div class="members-header">
      <div class="header-info">
        <h3>团队成员 ({{ members.length }})</h3>
        <p>管理团队成员和权限</p>
      </div>
      
      <div class="header-actions">
        <!-- 其他操作 -->
        <a-dropdown v-if="canManage">
          <template #overlay>
            <a-menu @click="handleBatchAction">
              <a-menu-item key="batchSetRole">
                <TagOutlined />
                批量设置角色
              </a-menu-item>
              <a-menu-item key="batchSetPermissions">
                <SafetyCertificateOutlined />
                批量设置权限
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="exportMembers">
                <DownloadOutlined />
                导出成员列表
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="members-filter">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="8">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索成员姓名或手机号"
            @search="loadMembers"
            @change="debounceSearch"
          />
        </a-col>
        
        <a-col :xs="24" :sm="8" :md="6">
          <a-select
            v-model:value="roleFilter"
            placeholder="角色筛选"
            style="width: 100%"
            @change="loadMembers"
          >
            <a-select-option value="">全部角色</a-select-option>
            <a-select-option value="创始人">创始人</a-select-option>
            <a-select-option value="负责人">负责人</a-select-option>
            <a-select-option value="成员">成员</a-select-option>
          </a-select>
        </a-col>
        
        <a-col :xs="24" :sm="4" :md="4">
          <a-button @click="resetFilter">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 成员列表 -->
    <div class="members-list">
      <a-spin :spinning="loading">
        <a-list
          :data-source="filteredMembers"
          :pagination="pagination"
          item-layout="horizontal"
          size="large"
        >
          <template #renderItem="{ item }">
            <a-list-item class="member-item">
              <template #actions>
                <a-dropdown v-if="canManageMember(item)">
                  <template #overlay>
                    <a-menu @click="({ key }) => handleMemberAction(key, item)">
                      <!-- 设为负责人 -->
                      <a-menu-item key="setAsLeader" v-if="item.角色 === '成员'">
                        <CrownOutlined />
                        设为负责人
                      </a-menu-item>
                      <!-- 取消负责人 -->
                      <a-menu-item key="revokeLeader" v-if="item.角色 === '团队负责人'">
                        <UserOutlined />
                        取消负责人身份
                      </a-menu-item>

                      <a-menu-item key="setPermissions" v-if="canSetMemberPermissions(item)">
                        <SafetyCertificateOutlined />
                        设置权限
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="viewDetails">
                        <EyeOutlined />
                        查看详情
                      </a-menu-item>
                      <a-menu-divider v-if="canRemoveMember(item)" />
                      <a-menu-item 
                        key="removeMember" 
                        v-if="canRemoveMember(item)"
                        danger
                      >
                        <UserDeleteOutlined />
                        移除成员
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button size="small">
                    操作
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
              </template>

              <a-list-item-meta>
                <template #avatar>
                  <a-avatar
                    :size="48"
                    :style="{ backgroundColor: getUserAvatarColor(item.昵称 || item.手机号 || '用户') }"
                  >
                    {{ (item.昵称 || item.手机号 || '用户').charAt(0) }}
                  </a-avatar>
                </template>
                
                <template #title>
                  <div class="member-title">
                    <span class="member-name">{{ item.昵称 || item.手机号 }}</span>
                    <a-tag
                      v-if="item.角色 && item.角色.trim()"
                      :color="getRoleColor(item.角色)"
                      class="role-tag"
                    >
                      {{ item.角色 }}
                    </a-tag>
                    <a-tag
                      v-if="item.状态 && item.状态.trim()"
                      :color="getStatusColor(item.状态)"
                      size="small"
                    >
                      {{ item.状态 }}
                    </a-tag>
                  </div>
                </template>
                
                <template #description>
                  <div class="member-info">
                    <div class="info-row">
                      <span class="info-item">
                        <PhoneOutlined />
                        {{ item.手机号 }}
                      </span>
                      <span v-if="item.邮箱" class="info-item">
                        <MailOutlined />
                        {{ item.邮箱 }}
                      </span>
                    </div>
                    
                    <div class="info-row">
                      <span class="info-item">
                        <CalendarOutlined />
                        加入时间: {{ formatDate(item.加入时间) }}
                      </span>
                      <span v-if="item.权限数量" class="info-item">
                        <SafetyCertificateOutlined />
                        权限: {{ item.权限数量 }} 项
                      </span>
                    </div>
                    
                    <!-- 权限概览 -->
                    <div v-if="item.主要权限 && item.主要权限.length > 0" class="permissions-preview">
                      <span class="permissions-label">主要权限:</span>
                      <a-tag
                        v-for="permission in item.主要权限.slice(0, 3)"
                        :key="permission"
                        size="small"
                        color="blue"
                      >
                        {{ permission || '未知权限' }}
                      </a-tag>
                      <span v-if="item.主要权限.length > 3" class="more-permissions">
                        +{{ item.主要权限.length - 3 }} 更多
                      </span>
                    </div>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </a-spin>
    </div>

    <!-- 成员详情抽屉 -->
    <a-drawer
      v-model:open="showMemberDetail"
      title="成员详情"
      width="600"
      placement="right"
    >
      <div v-if="selectedMember">
        <!-- 成员详情内容 -->
        <a-descriptions title="基本信息" bordered>
          <a-descriptions-item label="姓名">{{ selectedMember.昵称 || selectedMember.手机号 }}</a-descriptions-item>
          <a-descriptions-item label="手机号">{{ selectedMember.手机号 }}</a-descriptions-item>
          <a-descriptions-item label="邮箱">{{ selectedMember.邮箱 || '未设置' }}</a-descriptions-item>
          <a-descriptions-item label="角色">
            <a-tag :color="getRoleColor(selectedMember.角色)">{{ selectedMember.角色 }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedMember.状态)">{{ selectedMember.状态 }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="加入时间">{{ formatDate(selectedMember.加入时间) }}</a-descriptions-item>
        </a-descriptions>
        
        <!-- 权限详情 -->
        <a-divider>权限详情</a-divider>
        <div v-if="selectedMember.权限列表 && selectedMember.权限列表.length > 0">
          <a-tag 
            v-for="permission in selectedMember.权限列表" 
            :key="permission"
            color="blue"
            style="margin-bottom: 8px;"
          >
            {{ permission }}
          </a-tag>
        </div>
        <a-empty v-else description="暂无特殊权限" />
      </div>
    </a-drawer>

    <!-- 批量权限管理弹窗 -->
    <PermissionModal
      v-model:open="showPermissionModal"
      :team="team"
      @permissions-updated="loadMembers"
    />
    
    <!-- 单个成员权限设置弹窗 -->
    <MemberPermissionModal
      v-if="selectedMember"
      v-model:open="showMemberPermissionModal"
      :member="selectedMember"
      :team="team"
      @permissions-updated="loadMembers"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, defineAsyncComponent, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  UserAddOutlined,
  TagOutlined,
  SafetyCertificateOutlined,
  DownloadOutlined,
  DownOutlined,
  ReloadOutlined,
  EyeOutlined,
  UserDeleteOutlined,
  PhoneOutlined,
  MailOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
  CrownOutlined,
  UserOutlined,
} from '@ant-design/icons-vue'
import teamService from '../../services/team'
import { getRoleColor, getRoleDisplayName } from '../../utils/roleUtils'
import { formatDate, getUserAvatarColor } from '../../utils/teamUtils'
import { teamMemberService } from '../../services/team/teamMember'
import { useUserStore } from '../../store/user'
import { usePermissions } from '../../composables/usePermissions'
import { usePermissionCheck } from '../../composables/usePermissionCheck'
import { useTeamPermissions } from '../../composables/useTeamPermissions'

const PermissionModal = defineAsyncComponent(() => import('../../components/team/PermissionModal.vue'))
const MemberPermissionModal = defineAsyncComponent(() => import('../../components/team/MemberPermissionModal.vue'))

defineOptions({
  name: 'TeamMembersTab'
})

const props = defineProps({
  team: {
    type: Object,
    required: true
  },
  canInvite: {
    type: Boolean,
    default: false
  },
  canManage: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['invite-member', 'refresh'])
const userStore = useUserStore()
const currentUser = computed(() => userStore.userInfo)

// 成员列表和分页
const loading = ref(false)
const members = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadMembers()
  }
})

// 搜索和筛选
const searchKeyword = ref('')
const roleFilter = ref('')

// 弹窗状态
const selectedMember = ref(null)
const showMemberDetail = ref(false)
const showPermissionModal = ref(false)
const showMemberPermissionModal = ref(false)

// 计算属性
const filteredMembers = computed(() => {
  let result = members.value

  if (searchKeyword.value) {
    result = result.filter(member => 
      (member.昵称 || '').includes(searchKeyword.value) ||
      member.手机号.includes(searchKeyword.value)
    )
  }

  if (roleFilter.value) {
    result = result.filter(member => member.角色 === roleFilter.value)
  }

  return result
})

// 权限检查
const permissionsUtils = usePermissions()
const { canManageMember: checkManageMember, canSetMemberPermissions: checkSetPermissions, canRemoveMember: checkRemoveMember } = usePermissionCheck()
const { isCreator, isLeader, canManageTeam, canInviteMembers, canRemoveMember: teamCanRemoveMember, canManagePermissions } = useTeamPermissions()

const canManage = computed(() => {
  const perms = props.team?.权限状态;
  if (!perms) return false;
  return perms.是否团队创建者 || perms.能否管理团队 || canManageTeam(props.team);
});

const canManageMember = (member) => {
  if (member.用户id === currentUser.value.id) return false // 不能管理自己
  if (isCreator(props.team)) return true // 创建者可以管理任何人
  if (member.角色 === '创始人') return false // 其他人不能管理创始人
  return canManage.value;
}

const canSetMemberPermissions = (member) => {
  return canManagePermissions(props.team, member)
}

const canRemoveMember = (member) => {
  return teamCanRemoveMember(props.team, member)
}

// 加载团队成员
const loadMembers = async () => {
  try {
    loading.value = true
    const response = await teamMemberService.getTeamMembers({
      团队id: props.team.团队id,
      当前页码: pagination.current,
      每页数量: pagination.pageSize,
      搜索关键词: searchKeyword.value,
      角色: roleFilter.value
    })

    console.log('团队成员API响应:', response) // 调试日志，发布前移除

    // 判断响应结构，检查数据可能在message或data字段中
    if (response.status === 100) {
      const data = response.message || response.data
      if (data) {
        members.value = data.成员列表 || []
        pagination.total = data.总数 || 0
      } else {
        throw new Error('响应数据格式不正确')
      }
    } else {
      throw new Error(response.message || '加载成员列表失败')
    }
  } catch (error) {
    message.error('加载成员列表失败: ' + error.message)
    console.error('加载成员列表错误:', error)
  } finally {
    loading.value = false
  }
}

// 处理批量操作
const handleBatchAction = ({ key }) => {
  if (key === 'batchSetPermissions') {
    message.info('该功能正在开发中...')
  }
}

// 处理成员操作
const handleMemberAction = (key, member) => {
  selectedMember.value = member
  switch (key) {
    case 'setAsLeader':
      updateRoleWithConfirmation(member, '团队负责人')
      break
    case 'revokeLeader':
      updateRoleWithConfirmation(member, '成员')
      break
    case 'setPermissions':
      showMemberPermissionModal.value = true
      break
    case 'viewDetails':
      showMemberDetail.value = true
      break
    case 'removeMember':
      showRemoveConfirm(member)
      break
  }
}

// 更新角色并弹出确认框
const updateRoleWithConfirmation = (member, newRole) => {
  const isPromoting = newRole === '团队负责人'
  Modal.confirm({
    title: `确认要将 ${member.昵称} ${isPromoting ? '设为' : '变更为'} ${newRole} 吗？`,
    icon: h(ExclamationCircleOutlined),
    content: isPromoting 
      ? '设为负责人后，该成员将拥有管理团队的权限。' 
      : '取消负责人身份后，该成员将失去管理权限，变为普通成员。',
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        await teamMemberService.updateMemberRole({
          团队id: props.team.团队id,
          目标用户id: member.用户id,
          新角色: newRole
        })
        message.success('角色更新成功')
        await loadMembers() // 重新加载成员列表
      } catch (error) {
        message.error(error.message || '更新角色失败')
        console.error('更新角色失败:', error)
      }
    },
    onCancel() {
      console.log('取消角色更新')
    },
  })
}

// 移除成员确认
const showRemoveConfirm = (member) => {
  Modal.confirm({
    title: '确认移除成员',
    content: `您确定要将成员 "${member.昵称 || member.手机号}" 从团队中移除吗？`,
    okText: '确认',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const response = await teamMemberService.removeMember({ 
          团队id: props.team.团队id, 
          用户id: member.用户id 
        });
        if (response.success) {
          message.success('成员移除成功');
          loadMembers(); // 重新加载成员列表
        } else {
          message.error(response.message || '移除成员失败');
        }
      } catch (error) {
        message.error('移除成员时出错: ' + error.message);
      }
    },
  });
}

// 重置筛选
const resetFilter = () => {
  searchKeyword.value = ''
  roleFilter.value = ''
  loadMembers()
}

// 搜索防抖
let searchTimer = null
const debounceSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    loadMembers()
  }, 500)
}

// 工具方法
const getStatusColor = (status) => {
  const colorMap = {
    '正常': 'success',
    '已禁用': 'error',
    '待激活': 'warning'
  }
  return colorMap[status] || 'default'
}

// 监听团队变化
watch(() => props.team.团队id, (newTeamId) => {
  if (newTeamId) {
    loadMembers()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.team.团队id) {
    loadMembers()
  }
})
</script>

<style scoped>
.team-members-tab {
  padding: 0;
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.members-filter {
  margin-bottom: 24px;
}

.members-list {
  background: white;
  border-radius: 8px;
}

.member-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.role-tag {
  font-weight: 500;
}

.member-info {
  margin-top: 8px;
}

.info-row {
  display: flex;
  gap: 16px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.permissions-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.permissions-label {
  color: #666;
  font-size: 14px;
}

.more-permissions {
  color: #1890ff;
  font-size: 12px;
}

.primary-invite-btn {
  font-weight: 600;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.primary-invite-btn:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .members-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .member-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-row {
    flex-direction: column;
    gap: 4px;
  }
}
</style> 