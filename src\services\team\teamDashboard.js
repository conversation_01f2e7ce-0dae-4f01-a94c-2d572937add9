import { debugApiResponse, getApiData, isApiSuccess } from '../../utils/apiUtils'
import api from '../api'

/**
 * 团队数据看板服务 - 全面重构优化版本
 * 参考工作台逻辑的主要优化：
 * 1. 并行数据加载机制，提升性能
 * 2. 统一命名规范，消除歧义
 * 3. 智能缓存机制，10分钟数据缓存+团队级缓存管理
 * 4. 优化数据处理逻辑，减少客户端计算
 * 5. 统一错误处理和降级机制
 * 6. 移除冗余代码和无效数据项
 * 7. 参考工作台架构，支持模块化业务指标展示
 * 8. 支持时间范围切换和团队成员排名功能
 */
export const teamDashboardService = {

  // ==================== 智能缓存管理系统 ====================

  // 缓存存储 - 使用Map优化查询性能
  _cache: new Map(),

  // 缓存配置
  _cacheConfig: {
    timeout: 10 * 60 * 1000, // 10分钟缓存有效期
    maxSize: 100, // 最大缓存条目数
    prefix: 'team_dashboard_' // 缓存键前缀
  },

  // 图标名称映射函数（移除database图标，因为不再需要入库好友数）
  mapIconName(iconName) {
    const iconMap = {
      'wechat': 'WechatOutlined',
      'team': 'TeamOutlined',
      'user-add': 'UserAddOutlined',
      'send': 'SendOutlined',
      'message': 'MessageOutlined',
      'interaction': 'InteractionOutlined',
      'gift': 'GiftOutlined',
      'check-circle': 'CheckCircleOutlined',
      'truck': 'TruckOutlined',
      'mail': 'MailOutlined'
    }
    return iconMap[iconName] || 'InfoCircleOutlined'
  },


  /**
   * 生成标准化缓存键
   * @param {string} method - 方法名
   * @param {*} params - 参数
   * @returns {string} 缓存键
   */
  _getCacheKey(method, params) {
    const paramsStr = typeof params === 'object' ? JSON.stringify(params) : String(params)
    return `${this._cacheConfig.prefix}${method}_${paramsStr}`
  },


  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {*} 缓存数据或null
   */
  _getCache(key) {
    if (!this._cache.has(key)) return null


    const cached = this._cache.get(key)
    const now = Date.now()


    // 检查缓存是否过期
    if (now - cached.timestamp > this._cacheConfig.timeout) {
      this._cache.delete(key)
      console.log(`teamDashboardService: 缓存已过期并清除: ${key}`)
      return null
    }


    console.log(`teamDashboardService: 命中缓存: ${key}`)
    return cached.data
  },


  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {*} data - 数据
   */
  _setCache(key, data) {
    // 检查缓存大小，超过限制时清理旧数据
    if (this._cache.size >= this._cacheConfig.maxSize) {
      this._clearOldCache()
    }


    this._cache.set(key, {
      data,
      timestamp: Date.now()
    })


    console.log(`teamDashboardService: 设置缓存: ${key}`)
  },


  /**
   * 清理过期缓存
   */
  _clearOldCache() {
    const now = Date.now()
    let clearedCount = 0


    for (const [key, value] of this._cache.entries()) {
      if (now - value.timestamp > this._cacheConfig.timeout) {
        this._cache.delete(key)
        clearedCount++
      }
    }


    console.log(`teamDashboardService: 清理了 ${clearedCount} 个过期缓存`)
  },


  /**
   * 清除指定团队的所有缓存
   * @param {number} teamId - 团队id
   */
  clearTeamCache(teamId) {
    let clearedCount = 0
    const teamIdStr = String(teamId)


    for (const key of this._cache.keys()) {
      // 匹配包含团队id的缓存键
      if (key.includes(`"团队id":${teamIdStr}`) ||
        key.includes(`"teamId":${teamIdStr}`) ||
        key.includes(`_${teamIdStr}_`)) {
        this._cache.delete(key)
        clearedCount++
      }
    }


    console.log(`teamDashboardService: 清除团队 ${teamId} 的 ${clearedCount} 个缓存`)
  },


  /**
   * 清除所有缓存
   */
  clearAllCache() {
    const size = this._cache.size
    this._cache.clear()
    console.log(`teamDashboardService: 清除了所有 ${size} 个缓存`)
  },


  // ==================== 核心聚合接口 ====================

  /**
   * 获取团队核心业务指标聚合数据 - 与工作台核心指标完全对标
   * @param {number} teamId - 团队id
   * @param {Object} options - 选项
   * @param {string} options.timeRange - 时间范围
   * @param {boolean} options.forceRefresh - 强制刷新
   * @returns {Promise<Object>} 核心业务指标聚合数据
   */
  async getTeamCoreMetricsAggregated(teamId, options = {}) {
    const startTime = performance.now()

    try {
      console.log('🚀 teamDashboardService: 开始获取团队核心业务指标聚合', {
        teamId,
        options,
        timestamp: new Date().toISOString()
      })

      // 参数验证和标准化
      if (!teamId || teamId <= 0) {
        const error = new Error('团队id无效')
        error.code = 'INVALID_TEAM_ID'
        throw error
      }

      const normalizedTeamId = Number(teamId)
      const requestOptions = {
        forceRefresh: false,
        timeRange: '今日',
        ...options
      }

      // 检查缓存（除非强制刷新）
      if (!requestOptions.forceRefresh) {
        const cacheKey = this._getCacheKey('coreMetricsAggregated', {
          teamId: normalizedTeamId,
          ...requestOptions
        })
        const cached = this._getCache(cacheKey)

        if (cached) {
          const endTime = performance.now()
          console.log(`teamDashboardService: 使用缓存的核心指标数据，耗时: ${(endTime - startTime).toFixed(2)}ms`)
          return cached
        }
      }

      console.log('🚀 teamDashboardService: 发起核心业务指标聚合API请求', {
        url: '/team/dashboard/core-metrics',
        payload: {
          团队id: normalizedTeamId,
          时间范围: requestOptions.timeRange
        }
      })

      // 发起API请求
      const requestData = {
        团队id: normalizedTeamId,
        时间范围: requestOptions.timeRange
      }

      const response = await api.post('/team/dashboard/core-metrics', requestData)

      console.log('teamDashboardService: 核心指标API响应接收', {
        status: response?.status,
        hasData: !!response?.data,
        responseTime: `${(performance.now() - startTime).toFixed(2)}ms`
      })

      debugApiResponse(response, '获取团队核心业务指标聚合')

      if (isApiSuccess(response)) {
        const apiData = getApiData(response)

        // 数据验证和处理
        if (!apiData || typeof apiData !== 'object') {
          throw new Error('API返回数据格式无效')
        }

        // 数据标准化处理
        const processedData = this._processCoreMetricsData(apiData, normalizedTeamId)

        const result = {
          status: 100,
          data: processedData,
          timestamp: Date.now(),
          cached: false
        }

        // 设置缓存
        const cacheKey = this._getCacheKey('coreMetricsAggregated', {
          teamId: normalizedTeamId,
          ...requestOptions
        })
        this._setCache(cacheKey, result)

        const endTime = performance.now()
        console.log(`teamDashboardService: 核心业务指标聚合获取成功，耗时: ${(endTime - startTime).toFixed(2)}ms`)

        return result
      } else {
        throw new Error(response?.message || '获取团队核心业务指标聚合失败')
      }
    } catch (error) {
      const endTime = performance.now()
      console.error('teamDashboardService: 获取团队核心业务指标聚合失败', {
        teamId,
        options,
        error: error.message,
        stack: error.stack,
        duration: `${(endTime - startTime).toFixed(2)}ms`
      })

      // 根据错误类型提供不同的处理策略
      if (error.code === 'INVALID_TEAM_ID') {
        throw error
      } else if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
        const timeoutError = new Error('请求超时，请检查网络连接')
        timeoutError.code = 'REQUEST_TIMEOUT'
        throw timeoutError
      } else {
        const wrappedError = new Error(`获取团队核心业务指标失败: ${error.message}`)
        wrappedError.originalError = error
        throw wrappedError
      }
    }
  },

  /**
   * 处理核心业务指标数据 - 直接使用后端中文属性名，无需转换
   * @param {Object} 原始数据 - 后端返回的原始数据
   * @param {number} 团队id - 团队id
   * @returns {Object} 处理后的数据
   */
  _processCoreMetricsData(原始数据, 团队id) {
    try {
      // 直接使用后端的中文属性名，确保数据结构完整性
      const 处理后数据 = {
        微信运营指标: 原始数据.微信运营指标 || {},
        达人管理指标: 原始数据.达人管理指标 || {},
        寄样管理指标: 原始数据.寄样管理指标 || {},
        团队汇总: 原始数据.团队汇总 || {},

        时间范围: 原始数据.时间范围 || '今日',
        更新时间: 原始数据.更新时间 || new Date().toISOString()
      }



      console.log('teamDashboardService: 核心业务指标数据处理完成', {
        团队id,
        微信指标数量: Object.keys(处理后数据.微信运营指标).length,
        达人指标数量: Object.keys(处理后数据.达人管理指标).length,
        寄样指标数量: Object.keys(处理后数据.寄样管理指标).length,

        团队成员数: 处理后数据.团队汇总.参与成员数
      })

      return 处理后数据
    } catch (error) {
      console.error('teamDashboardService: 核心业务指标数据处理失败', error)
      throw new Error(`核心业务指标数据处理失败: ${error.message}`)
    }
  },










  // ==================== 团队业务模块数据获取（参考工作台架构）====================


  /**
   * 获取团队业务模块数据 - 参考工作台并行加载机制
   * 支持模块化指标卡片展示和时间范围切换
   * @param {number} teamId - 团队id
   * @param {Object} params - 参数
   * @param {string} params.timeRange - 时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）
   * @param {boolean} params.forceRefresh - 强制刷新
   * @returns {Promise<Object>} 业务模块数据
   */
  async getTeamBusinessModules(teamId, params = {}) {
    const startTime = performance.now()


    try {
      console.log('🚀 teamDashboardService: 开始获取团队业务模块数据', {
        teamId,
        params,
        timestamp: new Date().toISOString()
      })


      // 参数验证和标准化
      if (!teamId || teamId <= 0) {
        throw new Error('团队id无效')
      }


      const normalizedTeamId = Number(teamId)
      const requestParams = {
        timeRange: '本周',
        forceRefresh: false,
        ...params
      }

      console.log('teamDashboardService: 业务模块请求参数', {
        团队id: normalizedTeamId,
        时间范围: requestParams.timeRange,
        强制刷新: requestParams.forceRefresh,
        原始参数: params
      })


      // 检查缓存
      if (!requestParams.forceRefresh) {
        const cacheKey = this._getCacheKey('businessModules', {
          teamId: normalizedTeamId,
          ...requestParams
        })
        const cached = this._getCache(cacheKey)

        if (cached) {
          const endTime = performance.now()
          console.log(`teamDashboardService: 使用缓存的业务模块数据，耗时: ${(endTime - startTime).toFixed(2)}ms`)
          return cached
        }
      }


      // 直接使用核心业务指标聚合接口，不再兼容旧接口
      console.log('🚀 teamDashboardService: 开始获取团队核心业务指标聚合数据，时间范围:', requestParams.timeRange)

      // 并行获取核心业务指标聚合数据和成员排名数据
      const [核心指标响应, 成员排名响应] = await Promise.allSettled([
        this.getTeamCoreMetricsAggregated(normalizedTeamId, {
          timeRange: requestParams.timeRange,
          forceRefresh: requestParams.forceRefresh
        }),
        this.getTeamMemberRanking(normalizedTeamId, requestParams)
      ])

      // 检查核心指标数据获取结果
      let 核心指标数据 = null
      if (核心指标响应.status === 'fulfilled' && 核心指标响应.value?.status === 100) {
        核心指标数据 = 核心指标响应.value.data
        console.log('✅ 核心业务指标聚合数据获取成功')
      } else {
        console.error('❌ 核心业务指标聚合数据获取失败:', 核心指标响应.reason || 核心指标响应.value)
        throw new Error('核心业务指标聚合数据获取失败')
      }


        console.log('📊 核心业务指标数据加载完成，响应状态:', {
          核心指标: !!核心指标数据,
          成员排名: 成员排名响应.status
        })

        // 使用核心业务指标聚合数据构建业务模块
        const 业务模块列表 = []

        console.log('🎯 使用核心业务指标聚合数据构建业务模块')

        // 微信运营核心指标模块
        if (核心指标数据.微信运营指标) {
          const 微信指标卡片 = []

          // 直接从原始数据构建指标卡片
          Object.entries(核心指标数据.微信运营指标).forEach(([指标名称, 指标数值]) => {
            微信指标卡片.push({
              标题: 指标名称,
              数值: 指标数值 || 0,
              格式化数值: String(指标数值 || 0),
              单位: 指标名称.includes('数量') ? '个' : '人',
              图标: this.mapIconName('wechat'),
              颜色: '#1890ff',
              趋势: `当前${指标数值 || 0}`,
              趋势类型: (指标数值 || 0) > 0 ? 'up' : 'stable'
            })
          })

          业务模块列表.push({
            key: 'wechat_core',
            title: '团队微信运营核心指标',
            metrics: 微信指标卡片,
            color: '#1890ff',
            汇总数据: 核心指标数据.微信运营指标
          })
        }

        // 达人管理核心指标模块
        if (核心指标数据.达人管理指标) {
          const 达人指标卡片 = []

          // 直接从原始数据构建指标卡片
          Object.entries(核心指标数据.达人管理指标).forEach(([指标名称, 指标数值]) => {
            达人指标卡片.push({
              标题: 指标名称,
              数值: 指标数值 || 0,
              格式化数值: String(指标数值 || 0),
              单位: 指标名称.includes('数量') || 指标名称.includes('总数') ? '个' : '人',
              图标: this.mapIconName('star'),
              颜色: '#fa8c16',
              趋势: `当前${指标数值 || 0}`,
              趋势类型: (指标数值 || 0) > 0 ? 'up' : 'stable'
            })
          })

          业务模块列表.push({
            key: 'talent_core',
            title: '团队达人管理核心指标',
            metrics: 达人指标卡片,
            color: '#fa8c16',
            汇总数据: 核心指标数据.达人管理指标
          })
        }

        // 寄样管理核心指标模块
        if (核心指标数据.寄样管理指标) {
          const 寄样指标卡片 = []

          // 直接从原始数据构建指标卡片
          Object.entries(核心指标数据.寄样管理指标).forEach(([指标名称, 指标数值]) => {
            寄样指标卡片.push({
              标题: 指标名称,
              数值: 指标数值 || 0,
              格式化数值: String(指标数值 || 0),
              单位: '件',
              图标: this.mapIconName('gift'),
              颜色: '#eb2f96',
              趋势: `当前${指标数值 || 0}`,
              趋势类型: (指标数值 || 0) > 0 ? 'up' : 'stable'
            })
          })

          业务模块列表.push({
            key: 'sample_core',
            title: '团队寄样管理核心指标',
            metrics: 寄样指标卡片,
            color: '#eb2f96',
            汇总数据: 核心指标数据.寄样管理指标
          })
        }

        // 处理成员排名数据
        let 成员排名数据 = null
        if (成员排名响应.status === 'fulfilled' && 成员排名响应.value) {
          成员排名数据 = 成员排名响应.value
        }

        // 构建最终结果
        const result = {
          status: 100,
          data: {
            businessModules: 业务模块列表,
            memberRanking: 成员排名数据,
            timeRange: requestParams.timeRange,
            teamId: normalizedTeamId,
            核心指标数据: 核心指标数据
          },
          timestamp: Date.now(),
          cached: false
        }

        // 设置缓存
        const cacheKey = this._getCacheKey('businessModules', {
          teamId: normalizedTeamId,
          ...requestParams
        })
        this._setCache(cacheKey, result)

        const endTime = performance.now()
        console.log(`teamDashboardService: 团队业务模块数据获取成功，耗时: ${(endTime - startTime).toFixed(2)}ms`)

        return result
    } catch (error) {
      const endTime = performance.now()
      console.error('teamDashboardService: 获取团队业务模块数据失败', {
        teamId,
        params,
        error: error.message,
        duration: `${(endTime - startTime).toFixed(2)}ms`
      })
      throw error
    }
  },


  /**
   * 获取团队微信运营指标数据
   * @param {number} teamId - 团队id
   * @param {Object} params - 参数
   * @returns {Promise<Object>} 微信运营指标数据
   */
  async getTeamWechatMetrics(teamId, params = {}) {
    try {
      const requestData = {
        团队id: teamId,
        时间范围: params.timeRange || '本周'
      }

      console.log('teamDashboardService: 微信指标请求', requestData)

      const response = await api.post('/team/metrics/wechat-metrics', requestData)

      debugApiResponse(response, '获取团队微信运营指标')

      if (isApiSuccess(response)) {
        return getApiData(response)
      } else {
        console.warn('teamDashboardService: 微信指标API返回失败状态', {
          status: response?.status,
          message: response?.message
        })
        return { 汇总数据: {} }
      }
    } catch (error) {
      console.error('teamDashboardService: 微信指标请求失败', error)
      return { 汇总数据: {} }
    }
  },
  /**
   * 获取团队达人管理指标数据
   * @param {number} teamId - 团队id
   * @param {Object} params - 参数
   * @returns {Promise<Object>} 达人管理指标数据
   */
  async getTeamTalentMetrics(teamId, params = {}) {
    try {
      const requestData = {
        团队id: teamId,
        时间范围: params.timeRange || '本周'
      }

      console.log('teamDashboardService: 达人指标请求', requestData)

      const response = await api.post('/team/metrics/talent-metrics', requestData)

      debugApiResponse(response, '获取团队达人管理指标')

      if (isApiSuccess(response)) {
        return getApiData(response)
      } else {
        console.warn('teamDashboardService: 达人指标API返回失败状态', {
          status: response?.status,
          message: response?.message
        })
        return { 汇总数据: {} }
      }
    } catch (error) {
      console.error('teamDashboardService: 达人指标请求失败', error)
      return { 汇总数据: {} }
    }
  },

  /**
   * 获取团队寄样管理指标数据
   * @param {number} teamId - 团队id
   * @param {Object} params - 参数
   * @returns {Promise<Object>} 寄样管理指标数据
   */
  async getTeamSampleMetrics(teamId, params = {}) {
    try {
      const requestData = {
        团队id: teamId,
        时间范围: params.timeRange || '本周'
      }

      console.log('teamDashboardService: 样品指标请求', requestData)

      const response = await api.post('/team/metrics/sample-metrics', requestData)

      debugApiResponse(response, '获取团队寄样管理指标')

      if (isApiSuccess(response)) {
        return getApiData(response)
      } else {
        console.warn('teamDashboardService: 寄样指标API返回失败状态', {
          status: response?.status,
          message: response?.message
        })
        return { 汇总数据: {} }
      }
    } catch (error) {
      console.error('teamDashboardService: 寄样指标请求失败', error)
      return { 汇总数据: {} }
    }
  },

  /**
   * 获取团队成员排名数据
   * @param {number} teamId - 团队id
   * @param {Object} params - 参数
   * @returns {Promise<Object>} 成员排名数据
   */
  async getTeamMemberRanking(teamId, params = {}) {
    try {
      const requestData = {
        团队id: teamId,
        时间范围: params.timeRange || '本周',
        排序方式: params.sortBy || '好友总数',
        限制数量: params.limit || 50
      }

      if (params.moduleType) {
        requestData.模块类型 = params.moduleType
      }

      const response = await api.post('/team/metrics/member-ranking', requestData)

      debugApiResponse(response, '获取团队成员排名')

      if (isApiSuccess(response)) {
        const data = getApiData(response)

        if (data && Array.isArray(data.成员排名)) {
          console.log('teamDashboardService: 成员排名数据处理，共', data.成员排名.length, '名成员')
          data.成员排名.forEach((member, index) => {
            member.排名 = member.排名 || (index + 1)
            if (!member.绩效等级) {
              member.绩效等级 = this._calculatePerformanceLevel(member, data.平均值 || {})
            }
          })
        }

        return data
      } else {
        console.warn('teamDashboardService: 成员排名API返回失败状态', {
          status: response?.status,
          message: response?.message
        })
        return { 成员列表: [], 平均值: {} }
      }
    } catch (error) {
      console.error('teamDashboardService: 获取团队成员排名失败', error)
      return { 成员列表: [], 平均值: {} }
    }
  },

  /**
   * 计算成员绩效等级
   * @param {Object} member - 成员数据
   * @param {Object} avgValues - 平均值数据
   * @returns {string} 绩效等级
   */
  _calculatePerformanceLevel(member, avgValues) {
    const friendCount = member.好友数量 || 0
    const avgFriendCount = avgValues.平均好友数量 || 0

    if (friendCount >= avgFriendCount * 1.5) {
      return '优秀'
    } else if (friendCount >= avgFriendCount * 1.2) {
      return '良好'
    } else if (friendCount >= avgFriendCount * 0.8) {
      return '一般'
    } else {
      return '待提升'
    }
  },

  /**
   * 刷新团队看板数据 - 强制刷新所有缓存
   * @param {number} teamId - 团队id
   * @returns {Promise<Object>} 刷新后的数据
   */
  async refreshTeamDashboard(teamId) {
    console.log(`teamDashboardService: 开始刷新团队 ${teamId} 的看板数据`)
    this.clearTeamCache(teamId)
    return await this.getTeamDashboardAggregated(teamId, { forceRefresh: true })
  },

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      size: this._cache.size,
      maxSize: this._cacheConfig.maxSize,
      timeout: this._cacheConfig.timeout,
      usageRate: Math.round((this._cache.size / this._cacheConfig.maxSize) * 100)
    }
  }
}

export default teamDashboardService