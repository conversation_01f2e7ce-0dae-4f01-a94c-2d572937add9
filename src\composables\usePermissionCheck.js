/**
 * 权限检查 Composable
 * 统一前端组件中的权限验证逻辑，避免重复代码
 */

import { computed } from 'vue'
import { useUserStore } from '../store/user'

export function usePermissionCheck() {
  const userStore = useUserStore()

  /**
   * 检查是否可以管理团队成员
   * @param {Object} member - 成员信息
   * @param {boolean} userCanManage - 当前用户是否有管理权限
   * @returns {boolean}
   */
  const canManageMember = (member, userCanManage) => {
    // 基础权限检查
    if (!userCanManage) return false
    
    // 不能管理创始人
    if (member?.角色 === '创始人' || member?.职位 === '创始人') return false
    
    // 不能管理自己
    if (member?.是否为当前用户) return false
    
    return true
  }

  /**
   * 检查是否可以设置成员权限
   * @param {Object} member - 成员信息
   * @param {boolean} userCanManage - 当前用户是否有管理权限
   * @returns {boolean}
   */
  const canSetMemberPermissions = (member, userCanManage) => {
    // 不能设置创始人的权限
    if (member?.角色 === '创始人' || member?.职位 === '创始人') return false
    
    // 不能设置自己的权限（避免用户误操作锁定自己）
    if (member?.是否为当前用户) return false
    
    // 需要有管理成员权限
    return userCanManage
  }

  /**
   * 检查是否可以移除成员
   * @param {Object} member - 成员信息
   * @param {boolean} userCanManage - 当前用户是否有管理权限
   * @returns {boolean}
   */
  const canRemoveMember = (member, userCanManage) => {
    // 不能移除创始人
    if (member?.角色 === '创始人' || member?.职位 === '创始人') return false
    
    // 不能移除自己
    if (member?.是否为当前用户) return false
    
    return userCanManage
  }

  /**
   * 检查是否可以邀请成员
   * @param {Object} team - 团队信息
   * @param {boolean} userCanManage - 当前用户是否有管理权限
   * @returns {boolean}
   */
  const canInviteMembers = (team, userCanManage) => {
    // 基础权限检查
    if (!userCanManage) return false
    
    // 检查团队是否还有可用席位
    if (team?.currentMembers >= team?.maxMembers) return false
    
    return true
  }

  /**
   * 检查是否可以管理团队设置
   * @param {Object} team - 团队信息
   * @param {string} userRole - 用户角色
   * @returns {boolean}
   */
  const canManageTeamSettings = (team, userRole) => {
    // 只有创始人和负责人可以管理团队设置
    return ['创始人', '团队负责人', 'founder', 'leader'].includes(userRole)
  }

  /**
   * 检查是否可以解散团队
   * @param {Object} team - 团队信息
   * @param {string} userRole - 用户角色
   * @returns {boolean}
   */
  const canDisbandTeam = (team, userRole) => {
    // 只有创始人可以解散团队
    return ['创始人', 'founder'].includes(userRole)
  }

  /**
   * 检查是否可以查看邀请链接
   * @param {Object} invitation - 邀请信息
   * @returns {boolean}
   */
  const canViewInviteLink = (invitation) => {
    // 检查是否有邀请令牌，不管状态如何都可以查看历史邀请链接
    return invitation?.邀请令牌 || (invitation?.备注 && invitation?.备注.includes('令牌:'))
  }

  /**
   * 检查是否可以重发邀请
   * @param {Object} invitation - 邀请信息
   * @returns {boolean}
   */
  const canResendInvitation = (invitation) => {
    return invitation?.状态 === '邀请待处理'
  }

  /**
   * 检查是否可以撤销邀请
   * @param {Object} invitation - 邀请信息
   * @returns {boolean}
   */
  const canRevokeInvitation = (invitation) => {
    return invitation?.状态 === '邀请待处理'
  }

  /**
   * 获取角色显示名称
   * @param {string} role - 角色代码
   * @returns {string}
   */
  const getRoleDisplayName = (role) => {
    const roleMap = {
      'founder': '创始人',
      'leader': '负责人', 
      'admin': '管理员',
      'member': '成员',
      '创始人': '创始人',
      '负责人': '负责人',
      '管理员': '管理员',
      '成员': '成员'
    }
    return roleMap[role] || role
  }

  /**
   * 检查用户是否有特定权限
   * @param {string} permission - 权限代码
   * @param {Array} userPermissions - 用户权限列表
   * @returns {boolean}
   */
  const hasPermission = (permission, userPermissions = []) => {
    return userPermissions.includes(permission)
  }

  /**
   * 批量检查权限
   * @param {Array} permissions - 权限代码数组
   * @param {Array} userPermissions - 用户权限列表
   * @param {string} mode - 检查模式：'all'(全部拥有) 或 'any'(拥有任一)
   * @returns {boolean}
   */
  const hasPermissions = (permissions, userPermissions = [], mode = 'all') => {
    if (mode === 'all') {
      return permissions.every(permission => userPermissions.includes(permission))
    } else {
      return permissions.some(permission => userPermissions.includes(permission))
    }
  }

  return {
    canManageMember,
    canSetMemberPermissions,
    canRemoveMember,
    canInviteMembers,
    canManageTeamSettings,
    canDisbandTeam,
    canViewInviteLink,
    canResendInvitation,
    canRevokeInvitation,
    getRoleDisplayName,
    hasPermission,
    hasPermissions
  }
} 