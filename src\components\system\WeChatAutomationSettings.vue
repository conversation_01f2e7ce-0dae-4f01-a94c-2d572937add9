<template>
  <div class="wechat-automation-settings">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>
            <WechatOutlined style="margin-right: 8px; color: #1890ff;" />
            微信自动化配置
          </h2>
          <p class="subtitle">配置微信自动添加好友的参数，实现智能化好友管理</p>
        </div>
        <div class="action-section">
          <a-button
            :loading="refreshing"
            @click="refreshConfigList"
            style="margin-right: 8px;"
          >
            <ReloadOutlined />
            刷新
          </a-button>
          <a-button
            type="primary"
            @click="showCreateConfigModal"
          >
            <PlusOutlined />
            新建配置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 配置概览卡片 -->
    <div class="overview-cards" v-if="!loading">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="配置总数"
              :value="configOverview.配置总数"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <SettingOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="启用配置"
              :value="configOverview.启用配置数"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="绑定账号"
              :value="configOverview.绑定微信账号数"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <WechatOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日添加"
              :value="configOverview.今日添加好友数"
              :value-style="{ color: '#f5222d' }"
            >
              <template #prefix>
                <UserAddOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 配置列表 -->
    <a-card class="config-list-card" :bordered="false">
      <template #title>
        <div class="list-header">
          <span>我的配置</span>
          <!-- 搜索框 -->
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索配置名称"
            style="width: 300px"
            @search="loadConfigList"
            allow-clear
          />
        </div>
      </template>

      <!-- 空状态 -->
      <div v-if="configList.length === 0 && !listLoading" class="empty-state">
        <a-empty description="暂无配置数据">
          <a-button type="primary" @click="showCreateConfigModal">
            立即创建配置
          </a-button>
        </a-empty>
      </div>

      <!-- 配置表格 -->
      <a-table
        v-else
        :columns="tableColumns"
        :data-source="configList"
        :loading="listLoading"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <!-- 配置信息列 -->
          <template v-if="column.key === 'config_info'">
            <div class="config-info-cell">
              <div class="config-header">
                <span class="config-name">{{ record.配置名称 }}</span>
              </div>
              <div class="config-details">
                <span class="wechat-account">
                  <WechatOutlined style="margin-right: 4px; color: #52c41a;" />
                  {{ record.微信账号名称 || '未绑定微信账号' }}
                </span>
              </div>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space size="small">
              <a-button size="small" type="link" @click="viewConfig(record)">
                详情
              </a-button>
              <a-button size="small" type="link" @click="editConfig(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个配置吗？"
                @confirm="deleteConfig(record.id)"
              >
                <a-button size="small" type="link" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑配置模态框 -->
    <a-modal
      v-model:open="configModalVisible"
      :title="modalTitle"
      width="min(1000px, 95vw)"
      :confirm-loading="modalLoading"
      @ok="handleConfigSubmit"
      @cancel="resetConfigModal"
      :body-style="{ padding: '16px 24px' }"
      :style="{ top: '20px' }"
    >
      <div class="config-form-container">
        <a-form
          ref="configFormRef"
          :model="configForm"
          :rules="configRules"
          layout="vertical"
        >
          <a-tabs v-model:activeKey="activeTabKey" type="card" class="config-tabs">
            <!-- 基础设置标签页 -->
            <a-tab-pane key="basic">
              <template #tab>
                <SettingOutlined />
                基础设置
              </template>
              <div class="tab-content">
            <a-row :gutter="16">
              <a-col :xs="24" :sm="24" :md="12">
                <a-form-item label="配置名称" name="配置名称">
                  <a-input
                    v-model:value="configForm.配置名称"
                    placeholder="请输入配置名称"
                    :maxlength="100"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="12">
                <a-form-item label="绑定微信账号" name="微信信息表id">
                  <a-select
                    v-model:value="configForm.微信信息表id"
                    placeholder="选择要绑定的微信账号"
                    :loading="wechatAccountsLoading"
                  >
                    <a-select-option
                      v-for="account in wechatAccounts"
                      :key="account.id"
                      :value="account.id"
                    >
                      {{ account.微信号 }} - {{ account.备注 || '无备注' }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

                <!-- 频率控制 -->
                <a-divider orientation="left">频率控制</a-divider>
                <a-row :gutter="16">
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="每日最大添加次数" name="每日最大添加次数">
                      <a-input-number
                        v-model:value="configForm.每日最大添加次数"
                        :min="1"
                        :max="50"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :xs="24" :sm="12" :md="8">
                    <a-form-item label="每小时最大添加次数" name="每小时最大添加次数">
                      <a-input-number
                        v-model:value="configForm.每小时最大添加次数"
                        :min="1"
                        :max="100"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :xs="24" :sm="24" :md="8">
                    <a-form-item label="连续添加次数上限" name="连续添加次数上限">
                      <a-input-number
                        v-model:value="configForm.连续添加次数上限"
                        :min="2"
                        :max="15"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row :gutter="16">
                  <a-col :xs="24" :sm="12" :md="12">
                    <a-form-item label="最小添加间隔(分钟)" name="最小添加间隔分钟">
                      <a-input-number
                        v-model:value="configForm.最小添加间隔分钟"
                        :min="1"
                        :max="60"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :xs="24" :sm="12" :md="12">
                    <a-form-item label="最大添加间隔(分钟)" name="最大添加间隔分钟">
                      <a-input-number
                        v-model:value="configForm.最大添加间隔分钟"
                        :min="10"
                        :max="120"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </a-tab-pane>

            <!-- 时间管理标签页 -->
            <a-tab-pane key="schedule">
              <template #tab>
                <CalendarOutlined />
                时间管理
              </template>
              <div class="tab-content">
            <a-row :gutter="16">
              <a-col :xs="24" :sm="12" :md="8">
                <a-form-item label="工作开始时间" name="工作开始时间">
                  <a-time-picker
                    v-model:value="configForm.工作开始时间"
                    format="HH:mm"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="8">
                <a-form-item label="工作结束时间" name="工作结束时间">
                  <a-time-picker
                    v-model:value="configForm.工作结束时间"
                    format="HH:mm"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="8">
                <a-form-item name="周末是否添加">
                  <template #label>
                    <span>周末工作</span>
                    <a-tooltip title="是否在周六日也进行自动添加好友">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
                    </a-tooltip>
                  </template>
                  <a-switch 
                    v-model:checked="configForm.周末是否添加"
                    checked-children="是" 
                    un-checked-children="否"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 周末配置 -->
            <div v-if="configForm.周末是否添加">
              <a-form-item label="周末每日最大添加次数" name="周末每日最大添加次数">
                <a-input-number
                  v-model:value="configForm.周末每日最大添加次数"
                  :min="1"
                  :max="30"
                  style="width: 200px"
                />
              </a-form-item>
            </div>

            <!-- 午休配置 -->
            <a-row :gutter="16">
              <a-col :xs="24" :sm="8" :md="6">
                <a-form-item name="是否启用午休">
                  <template #label>
                    <span>启用午休</span>
                    <a-tooltip title="是否在午休时间停止自动添加好友">
                      <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
                    </a-tooltip>
                  </template>
                  <a-switch
                    v-model:checked="configForm.是否启用午休"
                    checked-children="是"
                    un-checked-children="否"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8" :md="9" v-if="configForm.是否启用午休">
                <a-form-item label="午休开始时间" name="午休开始时间">
                  <a-time-picker
                    v-model:value="configForm.午休开始时间"
                    format="HH:mm"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8" :md="9" v-if="configForm.是否启用午休">
                <a-form-item label="午休结束时间" name="午休结束时间">
                  <a-time-picker
                    v-model:value="configForm.午休结束时间"
                    format="HH:mm"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
              </div>
            </a-tab-pane>

            <!-- 高级配置标签页 -->
            <a-tab-pane key="advanced">
              <template #tab>
                <SafetyOutlined />
                高级配置
              </template>
              <div class="tab-content">
            <a-row :gutter="16">
              <a-col :xs="24" :sm="12" :md="8">
                <a-form-item label="批次休息最小分钟" name="批次休息最小分钟">
                  <a-input-number
                    v-model:value="configForm.批次休息最小分钟"
                    :min="30"
                    :max="300"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="8">
                <a-form-item label="批次休息最大分钟" name="批次休息最大分钟">
                  <a-input-number
                    v-model:value="configForm.批次休息最大分钟"
                    :min="60"
                    :max="480"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="8">
                <a-form-item label="异常检测暂停分钟" name="异常检测暂停分钟">
                  <a-input-number
                    v-model:value="configForm.异常检测暂停分钟"
                    :min="10"
                    :max="120"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :xs="24" :sm="12" :md="8">
                <a-form-item label="随机延迟最小分钟" name="随机延迟最小分钟">
                  <a-input-number
                    v-model:value="configForm.随机延迟最小分钟"
                    :min="0"
                    :max="30"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="8">
                <a-form-item label="随机延迟最大分钟" name="随机延迟最大分钟">
                  <a-input-number
                    v-model:value="configForm.随机延迟最大分钟"
                    :min="0"
                    :max="60"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="8">
                <a-form-item label="成功率模拟概率" name="成功率模拟概率">
                  <a-input-number
                    v-model:value="configForm.成功率模拟概率"
                    :min="0.1"
                    :max="1"
                    :step="0.01"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

                <!-- 消息模板 -->
                <a-divider orientation="left">消息模板</a-divider>
                <a-row :gutter="16">
                  <a-col :span="24">
                    <a-form-item name="验证消息模板">
                      <template #label>
                        <span>验证消息模板</span>
                        <a-tooltip title="添加好友时发送的验证消息，支持变量：{达人昵称}、{联系方式类型}">
                          <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
                        </a-tooltip>
                      </template>
                      <a-textarea
                        v-model:value="configForm.验证消息模板"
                        placeholder="请输入验证消息模板，如：你好，我是{达人昵称}的朋友，想和你交流一下"
                        :rows="3"
                        :maxlength="200"
                        show-count
                      />
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row :gutter="16">
                  <a-col :span="24">
                    <a-form-item name="好友备注模板">
                      <template #label>
                        <span>好友备注模板</span>
                        <a-tooltip title="添加好友成功后的备注模板，支持变量：{达人昵称}、{联系方式类型}">
                          <QuestionCircleOutlined style="margin-left: 4px; color: #999;" />
                        </a-tooltip>
                      </template>
                      <a-input
                        v-model:value="configForm.好友备注模板"
                        placeholder="请输入好友备注模板，如：{达人昵称}-{联系方式类型}"
                        :maxlength="50"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </a-tab-pane>

          </a-tabs>
        </a-form>
      </div>
    </a-modal>

    <!-- 配置详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="配置详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedConfig" class="config-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <SettingOutlined style="margin-right: 8px; color: #1890ff;" />
            {{ selectedConfig.配置名称 }}
          </h3>
        </div>

        <!-- 配置详情 -->
        <div class="detail-section">
          <h4>配置详情</h4>
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="配置名称">
              {{ selectedConfig.配置名称 }}
            </a-descriptions-item>
            <a-descriptions-item label="绑定微信账号">
              {{ selectedConfig.微信账号名称 || '未绑定' }}
            </a-descriptions-item>
            <a-descriptions-item label="每日最大添加次数">
              {{ selectedConfig.每日最大添加次数 }}次
            </a-descriptions-item>
            <a-descriptions-item label="每小时最大添加次数">
              {{ selectedConfig.每小时最大添加次数 }}次
            </a-descriptions-item>
            <a-descriptions-item label="工作时间">
              {{ formatTime(selectedConfig.工作开始时间) }} - {{ formatTime(selectedConfig.工作结束时间) }}
            </a-descriptions-item>
            <a-descriptions-item label="周末工作">
              <a-tag :color="selectedConfig.周末是否添加 ? 'green' : 'default'" size="small">
                {{ selectedConfig.周末是否添加 ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="午休设置">
              <a-tag :color="selectedConfig.是否启用午休 ? 'green' : 'default'" size="small">
                {{ selectedConfig.是否启用午休 ? '启用' : '禁用' }}
              </a-tag>
              <span v-if="selectedConfig.是否启用午休" style="margin-left: 8px; color: #666;">
                {{ formatTime(selectedConfig.午休开始时间) }} - {{ formatTime(selectedConfig.午休结束时间) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(selectedConfig.创建时间) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 消息模板配置 -->
        <div class="detail-section">
          <h4>消息模板配置</h4>
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item label="验证消息模板">
              <div class="template-content">
                {{ selectedConfig.验证消息模板 || '你好，我是{达人昵称}的朋友，想和你交流一下' }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="好友备注模板">
              <div class="template-content">
                {{ selectedConfig.好友备注模板 || '{达人昵称}-{联系方式类型}' }}
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <a-button type="primary" size="large" @click="editConfigFromDetail">
            <EditOutlined />
            编辑配置
          </a-button>
          <a-button size="large" @click="detailModalVisible = false" style="margin-left: 12px;">
            关闭
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  WechatOutlined,
  ReloadOutlined,
  PlusOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  UserAddOutlined,
  QuestionCircleOutlined,
  EditOutlined,
  CalendarOutlined,
  SafetyOutlined
} from '@ant-design/icons-vue'
import { wechatAutomationService } from '@/services/wechatAutomationService'

defineOptions({ name: 'WeChatAutomationSettings' })

// 组件事件
const emit = defineEmits(['save'])

// 响应式数据
const loading = ref(true)
const refreshing = ref(false)
const listLoading = ref(false)
const configList = ref([])
const searchKeyword = ref('')

// 标签页状态
const activeTabKey = ref('basic')

// 配置概览数据
const configOverview = reactive({
  配置总数: 0,
  启用配置数: 0,
  绑定微信账号数: 0,
  今日添加好友数: 0
})

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const tableColumns = [
  {
    title: '配置信息',
    key: 'config_info',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center'
  }
]

// 模态框相关
const configModalVisible = ref(false)
const detailModalVisible = ref(false)
const modalLoading = ref(false)
const isEditMode = ref(false)
const selectedConfig = ref(null)
const configFormRef = ref()

// 微信账号列表
const wechatAccounts = ref([])
const wechatAccountsLoading = ref(false)

// 配置表单数据 - 动态从后端获取
const configForm = reactive({})

// 表单验证规则
const configRules = {
  配置名称: [
    { required: true, message: '请输入配置名称' },
    { max: 100, message: '配置名称不能超过100个字符' }
  ],
  微信信息表id: [
    { required: true, message: '请选择绑定的微信账号' }
  ],
  每日最大添加次数: [
    { required: true, message: '请输入每日最大添加次数' },
    { type: 'number', min: 1, max: 50, message: '请输入1-50之间的数字' }
  ],
  最小添加间隔分钟: [
    { required: true, message: '请输入最小添加间隔' },
    { type: 'number', min: 1, max: 60, message: '请输入1-60之间的数字' }
  ],
  最大添加间隔分钟: [
    { required: true, message: '请输入最大添加间隔' },
    { type: 'number', min: 10, max: 180, message: '请输入10-180之间的数字' }
  ],
  每小时最大添加次数: [
    { required: true, message: '请输入每小时最大添加次数' },
    { type: 'number', min: 1, max: 100, message: '请输入1-100之间的数字' }
  ],
  连续添加次数上限: [
    { required: true, message: '请输入连续添加次数上限' },
    { type: 'number', min: 2, max: 15, message: '请输入2-15之间的数字' }
  ],
  批次休息最小分钟: [
    { required: true, message: '请输入批次休息最小分钟' },
    { type: 'number', min: 30, max: 300, message: '请输入30-300之间的数字' }
  ],
  批次休息最大分钟: [
    { required: true, message: '请输入批次休息最大分钟' },
    { type: 'number', min: 60, max: 480, message: '请输入60-480之间的数字' }
  ],
  随机延迟最小分钟: [
    { required: true, message: '请输入随机延迟最小分钟' },
    { type: 'number', min: 0, max: 30, message: '请输入0-30之间的数字' }
  ],
  随机延迟最大分钟: [
    { required: true, message: '请输入随机延迟最大分钟' },
    { type: 'number', min: 0, max: 60, message: '请输入0-60之间的数字' }
  ],
  成功率模拟概率: [
    { required: true, message: '请输入成功率模拟概率' },
    { type: 'number', min: 0.1, max: 1, message: '请输入0.1-1之间的数字' }
  ],
  异常检测暂停分钟: [
    { required: true, message: '请输入异常检测暂停分钟' },
    { type: 'number', min: 10, max: 120, message: '请输入10-120之间的数字' }
  ],
  周末每日最大添加次数: [
    { type: 'number', min: 1, max: 30, message: '请输入1-30之间的数字' }
  ],
  工作开始时间: [
    { required: true, message: '请选择工作开始时间' }
  ],
  工作结束时间: [
    { required: true, message: '请选择工作结束时间' }
  ],
  验证消息模板: [
    { required: true, message: '请输入验证消息模板' },
    { max: 500, message: '验证消息模板不能超过500个字符' }
  ],
  好友备注模板: [
    { required: true, message: '请输入好友备注模板' },
    { max: 200, message: '好友备注模板不能超过200个字符' }
  ]
}

// 计算属性
const modalTitle = computed(() => {
  return isEditMode.value ? '编辑配置' : '新建配置'
})

// 页面初始化
onMounted(async () => {
  await applyDefaultConfig() // 先加载默认配置
  loadPageData()
})

/**
 * 加载页面数据
 */
const loadPageData = async () => {
  try {
    loading.value = true
    await Promise.all([
      loadConfigOverview(),
      loadConfigList(),
      loadWechatAccounts()
    ])
  } catch (error) {
    console.error('加载页面数据失败:', error)
    message.error('数据加载失败，请刷新页面重试')
  } finally {
    loading.value = false
  }
}

/**
 * 加载配置概览
 */
const loadConfigOverview = async () => {
  try {
    const response = await wechatAutomationService.getConfigOverview()
    if (response.status === 100) {
      Object.assign(configOverview, response.data)
    }
  } catch (error) {
    console.error('加载配置概览失败:', error)
  }
}

/**
 * 加载配置列表
 */
const loadConfigList = async () => {
  try {
    listLoading.value = true
    
    const params = {
      page: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
      search: searchKeyword.value
    }

    const response = await wechatAutomationService.getConfigList(params)
    
    if (response.status === 100) {
      configList.value = response.data.列表 || []
      paginationConfig.total = response.data.总数 || 0
      console.log(`✅ 成功加载 ${configList.value.length} 个配置`)
    } else {
      message.error(response.message || '加载配置列表失败')
    }
  } catch (error) {
    console.error('加载配置列表失败:', error)
    message.error('加载配置列表失败，请稍后重试')
  } finally {
    listLoading.value = false
  }
}

/**
 * 加载微信账号列表
 */
const loadWechatAccounts = async () => {
  try {
    wechatAccountsLoading.value = true
    
    const response = await wechatAutomationService.getWechatAccounts()
    
    if (response.status === 100) {
      wechatAccounts.value = response.data || []
    } else {
      console.error('加载微信账号列表失败:', response.message)
    }
  } catch (error) {
    console.error('加载微信账号列表失败:', error)
  } finally {
    wechatAccountsLoading.value = false
  }
}

/**
 * 刷新配置列表
 */
const refreshConfigList = async () => {
  refreshing.value = true
  try {
    await loadPageData()
    message.success('数据已刷新')
  } finally {
    refreshing.value = false
  }
}

/**
 * 表格变化处理
 */
const handleTableChange = (pagination, _filters, _sorter) => {
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
  loadConfigList()
}

/**
 * 显示创建配置模态框
 */
const showCreateConfigModal = () => {
  isEditMode.value = false
  resetConfigForm()
  configModalVisible.value = true
}

/**
 * 解析时间字符串为 dayjs 对象 - 直接处理数据库格式
 */
const parseTimeString = (timeStr) => {
  if (!timeStr) return null

  try {
    const timeString = String(timeStr).trim()

    // 处理 ISO Duration 格式 (PT9H)
    if (timeString.startsWith('PT') && timeString.endsWith('H')) {
      const hours = parseInt(timeString.slice(2, -1), 10)
      const today = dayjs().format('YYYY-MM-DD')
      return dayjs(`${today} ${hours.toString().padStart(2, '0')}:00`)
    }

    // 处理标准时间格式 (HH:mm:ss 或 H:mm:ss)
    if (timeString.includes(':')) {
      const parts = timeString.split(':')
      if (parts.length >= 2) {
        const hours = parseInt(parts[0], 10)
        const minutes = parseInt(parts[1], 10)
        const today = dayjs().format('YYYY-MM-DD')
        return dayjs(`${today} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`)
      }
    }

    return null
  } catch (error) {
    console.error('时间解析失败:', timeStr, error)
    return null
  }
}

/**
 * 编辑配置 - 调用接口获取完整配置信息
 */
const editConfig = async (config) => {
  try {
    modalLoading.value = true
    isEditMode.value = true

    // 调用接口获取完整的配置详情
    const response = await wechatAutomationService.getConfig({ 配置id: config.id })

    if (response.status === 100) {
      const fullConfig = response.data
      Object.assign(configForm, {
        ...fullConfig,
        // 智能解析时间字段，提供默认值避免验证失败
        工作开始时间: parseTimeString(fullConfig.工作开始时间) || dayjs('09:00', 'HH:mm'),
        工作结束时间: parseTimeString(fullConfig.工作结束时间) || dayjs('22:00', 'HH:mm'),
        午休开始时间: parseTimeString(fullConfig.午休开始时间) || dayjs('12:00', 'HH:mm'),
        午休结束时间: parseTimeString(fullConfig.午休结束时间) || dayjs('14:00', 'HH:mm')
      })
      configModalVisible.value = true
    } else {
      message.error(response.message || '获取配置详情失败')
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    message.error('获取配置详情失败，请稍后重试')
  } finally {
    modalLoading.value = false
  }
}

/**
 * 查看配置详情 - 调用接口获取完整配置信息
 */
const viewConfig = async (config) => {
  try {
    listLoading.value = true

    // 调用接口获取完整的配置详情
    const response = await wechatAutomationService.getConfig({ 配置id: config.id })

    if (response.status === 100) {
      selectedConfig.value = response.data
      detailModalVisible.value = true
    } else {
      message.error(response.message || '获取配置详情失败')
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    message.error('获取配置详情失败，请稍后重试')
  } finally {
    listLoading.value = false
  }
}

/**
 * 从详情页编辑配置
 */
const editConfigFromDetail = () => {
  detailModalVisible.value = false
  editConfig(selectedConfig.value)
}

/**
 * 删除配置
 */
const deleteConfig = async (configId) => {
  try {
    const response = await wechatAutomationService.deleteConfig(configId)
    
    if (response.status === 100) {
      message.success('配置删除成功')
      loadConfigList() // 重新加载列表
      loadConfigOverview() // 重新加载概览
    } else {
      message.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除配置失败:', error)
    message.error('删除失败，请稍后重试')
  }
}

/**
 * 提交配置
 */
const handleConfigSubmit = async () => {
  try {
    await configFormRef.value.validate()
    modalLoading.value = true

    // 处理时间格式 - 直接使用数据库兼容格式
    const submitData = {
      ...configForm,
      工作开始时间: configForm.工作开始时间 ? configForm.工作开始时间.format('HH:mm:ss') : null,
      工作结束时间: configForm.工作结束时间 ? configForm.工作结束时间.format('HH:mm:ss') : null,
      午休开始时间: configForm.午休开始时间 ? configForm.午休开始时间.format('HH:mm:ss') : null,
      午休结束时间: configForm.午休结束时间 ? configForm.午休结束时间.format('HH:mm:ss') : null,
      是否启用午休: configForm.是否启用午休 ? 1 : 0,
      周末是否添加: configForm.周末是否添加 ? 1 : 0
    }

    let response
    if (isEditMode.value) {
      response = await wechatAutomationService.updateConfig(configForm.id, submitData)
    } else {
      response = await wechatAutomationService.createConfig(submitData)
    }

    if (response.status === 100) {
      message.success(isEditMode.value ? '配置更新成功' : '配置创建成功')
      configModalVisible.value = false
      loadConfigList() // 重新加载列表
      loadConfigOverview() // 重新加载概览

      // 触发保存事件
      emit('save', 'wechat-automation', submitData)
    } else {
      // 特殊处理配置已存在的情况
      if (response.status === 3901) {
        message.error(response.message || '该微信号已存在配置，请选择其他微信号或使用更新功能')
      } else {
        message.error(response.message || '保存失败')
      }
    }
  } catch (error) {
    console.error('保存配置失败:', error)

    // 处理表单验证错误
    if (error && error.errorFields && error.errorFields.length > 0) {
      const firstError = error.errorFields[0]
      message.error(`表单验证失败: ${firstError.errors[0]}`)
      return
    }

    message.error('保存失败，请稍后重试')
  } finally {
    modalLoading.value = false
  }
}

/**
 * 从后端获取默认配置并应用到表单
 */
const applyDefaultConfig = async () => {
  try {
    const defaultConfig = await wechatAutomationService.getDefaultConfig()

    // 清空现有配置
    Object.keys(configForm).forEach(key => delete configForm[key])

    // 应用后端配置，智能解析时间格式
    Object.assign(configForm, {
      ...defaultConfig,
      id: null,
      微信信息表id: null,
      // 智能解析时间字段，提供默认值避免验证失败
      工作开始时间: parseTimeString(defaultConfig.工作开始时间) || dayjs('09:00', 'HH:mm'),
      工作结束时间: parseTimeString(defaultConfig.工作结束时间) || dayjs('22:00', 'HH:mm'),
      午休开始时间: parseTimeString(defaultConfig.午休开始时间) || dayjs('12:00', 'HH:mm'),
      午休结束时间: parseTimeString(defaultConfig.午休结束时间) || dayjs('14:00', 'HH:mm')
    })
  } catch (error) {
    console.error('获取默认配置失败:', error)
    message.error('获取默认配置失败，请稍后重试')
  }
}

/**
 * 重置配置表单
 */
const resetConfigForm = async () => {
  await applyDefaultConfig()
  configFormRef.value?.resetFields()
}

/**
 * 重置配置模态框
 */
const resetConfigModal = () => {
  resetConfigForm()
  activeTabKey.value = 'basic' // 重置到第一个标签页
  configModalVisible.value = false
}

/**
 * 格式化日期
 */
const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return '格式错误'
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.warn('日期格式化错误:', error)
    return '格式错误'
  }
}

/**
 * 格式化时间显示 - 直接处理数据库格式
 */
const formatTime = (timeStr) => {
  if (!timeStr) return '未设置'

  try {
    const timeString = String(timeStr).trim()

    // 处理 ISO Duration 格式 (PT9H)
    if (timeString.startsWith('PT') && timeString.endsWith('H')) {
      const hours = parseInt(timeString.slice(2, -1), 10)
      return `${hours.toString().padStart(2, '0')}:00`
    }

    // 处理标准时间格式
    if (timeString.includes(':')) {
      const parts = timeString.split(':')
      if (parts.length >= 2) {
        const hours = parseInt(parts[0], 10)
        const minutes = parseInt(parts[1], 10)
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
      }
    }

    return timeString
  } catch (error) {
    console.warn('时间格式化错误:', timeStr, error)
    return '格式错误'
  }
}


</script>

<style scoped>
.wechat-automation-settings {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.subtitle {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.action-section {
  display: flex;
  align-items: center;
}

/* 概览卡片 */
.overview-cards {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border-color: #d9d9d9;
}

/* 配置列表 */
.config-list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.config-info-cell {
  padding: 8px 0;
}

.config-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.config-name {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.config-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.wechat-account {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 13px;
}

/* 模态框样式 */
.config-form-container {
  height: 65vh;
  overflow: hidden;
}

.config-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-tabs .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.config-tabs .ant-tabs-content {
  height: 100%;
}

.config-tabs .ant-tabs-tabpane {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}

.config-tabs .ant-tabs-tab {
  padding: 8px 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.config-tabs .ant-tabs-tab .anticon {
  font-size: 14px;
}

.tab-content {
  padding: 16px 0;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 配置详情模态框 */
.config-detail-content {
  padding: 8px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.detail-tags {
  display: flex;
  gap: 4px;
}

.detail-section h4 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.detail-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.template-content {
  font-family: 'Courier New', monospace;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 13px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wechat-automation-settings {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .config-form-container {
    max-height: 50vh;
  }

  .config-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .wechat-account {
    font-size: 12px;
  }

  .config-tabs .ant-tabs-tab {
    padding: 6px 8px;
    font-size: 12px;
  }

  .config-tabs .ant-tabs-tab .anticon {
    font-size: 11px;
  }

  .tab-content {
    padding: 12px 0;
  }

  .config-form-container {
    height: 60vh;
  }
}

  .detail-actions {
    flex-direction: column;
    gap: 8px;
  }

  .detail-actions .ant-btn {
    width: 100%;
  }

</style> 