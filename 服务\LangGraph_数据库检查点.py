"""
LangGraph数据库检查点管理器
实现基于PostgreSQL的状态持久化
"""

import logging
from typing import Optional, Tuple, Iterator
from uuid import uuid4
from datetime import datetime

from 数据.LangChain_状态持久化数据层 import LangChain状态持久化数据层实例

# 尝试导入LangGraph检查点组件
try:
    from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointMetadata, CheckpointTuple
    from langchain_core.runnables import RunnableConfig
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    # 提供兼容性定义
    class BaseCheckpointSaver:
        pass
    
    class Checkpoint:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class CheckpointMetadata:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class CheckpointTuple:
        def __init__(self, config, checkpoint, metadata=None, parent_config=None):
            self.config = config
            self.checkpoint = checkpoint
            self.metadata = metadata
            self.parent_config = parent_config
    
    class RunnableConfig:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)

# 日志配置
检查点日志器 = logging.getLogger("LangGraph数据库检查点")


class PostgreSQL检查点管理器(BaseCheckpointSaver):
    """基于PostgreSQL的LangGraph检查点管理器"""

    def __init__(self):
        self.数据层 = LangChain状态持久化数据层实例
        self.已初始化 = False
        检查点日志器.info("PostgreSQL检查点管理器初始化")

    async def 初始化(self):
        """初始化检查点管理器"""
        try:
            if not self.数据层.已初始化:
                await self.数据层.初始化()

            self.已初始化 = True
            检查点日志器.info("✅ PostgreSQL检查点管理器初始化成功")
            return True

        except Exception as e:
            检查点日志器.error(f"❌ PostgreSQL检查点管理器初始化失败: {str(e)}")
            return False
    
    def _生成检查点ID(self, 线程ID: str, 步骤编号: int = None) -> str:
        """生成检查点ID"""
        if 步骤编号 is not None:
            return f"checkpoint_{线程ID}_{步骤编号}"
        else:
            return f"checkpoint_{线程ID}_{uuid4().hex[:8]}"
    
    def _解析配置(self, config: RunnableConfig) -> Tuple[str, str]:
        """解析配置获取线程ID和检查点ID"""
        try:
            if hasattr(config, 'configurable'):
                配置信息 = config.configurable
            elif isinstance(config, dict):
                配置信息 = config.get('configurable', {})
            else:
                配置信息 = {}
            
            线程ID = 配置信息.get('thread_id', '')
            检查点ID = 配置信息.get('checkpoint_id', '')
            
            return 线程ID, 检查点ID
            
        except Exception as e:
            检查点日志器.error(f"解析配置失败: {str(e)}")
            return '', ''
    
    async def aget_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        """异步获取检查点元组"""
        try:
            if not self.已初始化:
                await self.初始化()
            
            线程ID, 检查点ID = self._解析配置(config)
            
            if not 线程ID:
                检查点日志器.warning("配置中缺少thread_id")
                return None
            
            # 从数据库获取检查点
            检查点数据 = await self.数据层.获取检查点(线程ID, 检查点ID)
            
            if not 检查点数据:
                检查点日志器.debug(f"未找到检查点: {线程ID}/{检查点ID}")
                return None
            
            # 构建检查点对象
            checkpoint = Checkpoint(
                v=1,
                id=检查点数据['检查点ID'],
                ts=检查点数据['创建时间'].isoformat() if 检查点数据['创建时间'] else datetime.now().isoformat(),
                channel_values=检查点数据['状态数据'],
                channel_versions={},
                versions_seen={}
            )
            
            # 构建元数据
            metadata = CheckpointMetadata(
                source="database",
                step=检查点数据['步骤编号'],
                writes={}
            )
            
            # 构建配置
            checkpoint_config = RunnableConfig(
                configurable={
                    'thread_id': 线程ID,
                    'checkpoint_id': 检查点数据['检查点ID']
                }
            )
            
            检查点日志器.debug(f"✅ 获取检查点成功: {检查点数据['检查点ID']}")
            
            return CheckpointTuple(
                config=checkpoint_config,
                checkpoint=checkpoint,
                metadata=metadata,
                parent_config=None
            )
            
        except Exception as e:
            检查点日志器.error(f"❌ 获取检查点失败: {str(e)}")
            return None
    
    async def aput(self, config: RunnableConfig, checkpoint: Checkpoint, metadata: CheckpointMetadata) -> RunnableConfig:
        """异步保存检查点"""
        try:
            if not self.已初始化:
                await self.初始化()
            
            线程ID, _ = self._解析配置(config)
            
            if not 线程ID:
                raise ValueError("配置中缺少thread_id")
            
            # 生成检查点ID
            步骤编号 = getattr(metadata, 'step', 0) if metadata else 0
            检查点ID = self._生成检查点ID(线程ID, 步骤编号)
            
            # 准备状态数据
            状态数据 = getattr(checkpoint, 'channel_values', {})
            
            # 保存到数据库
            保存成功 = await self.数据层.保存检查点(
                线程ID=线程ID,
                检查点ID=检查点ID,
                状态数据=状态数据,
                步骤编号=步骤编号
            )
            
            if not 保存成功:
                raise Exception("保存检查点到数据库失败")
            
            # 返回更新的配置
            新配置 = RunnableConfig(
                configurable={
                    'thread_id': 线程ID,
                    'checkpoint_id': 检查点ID
                }
            )
            
            检查点日志器.debug(f"✅ 保存检查点成功: {检查点ID}")
            return 新配置
            
        except Exception as e:
            检查点日志器.error(f"❌ 保存检查点失败: {str(e)}")
            raise
    
    async def alist(self, config: RunnableConfig, limit: int = 10, before: Optional[RunnableConfig] = None) -> Iterator[CheckpointTuple]:
        """异步列出检查点历史"""
        try:
            if not self.已初始化:
                await self.初始化()
            
            线程ID, _ = self._解析配置(config)
            
            if not 线程ID:
                检查点日志器.warning("配置中缺少thread_id")
                return
            
            # 获取检查点历史
            检查点历史 = await self.数据层.获取检查点历史(线程ID, limit)
            
            for 检查点数据 in 检查点历史:
                try:
                    # 构建检查点对象
                    checkpoint = Checkpoint(
                        v=1,
                        id=检查点数据['检查点ID'],
                        ts=检查点数据['创建时间'].isoformat() if 检查点数据['创建时间'] else datetime.now().isoformat(),
                        channel_values={},  # 历史列表不包含完整状态数据
                        channel_versions={},
                        versions_seen={}
                    )
                    
                    # 构建元数据
                    metadata = CheckpointMetadata(
                        source="database",
                        step=检查点数据['步骤编号'],
                        writes={}
                    )
                    
                    # 构建配置
                    checkpoint_config = RunnableConfig(
                        configurable={
                            'thread_id': 线程ID,
                            'checkpoint_id': 检查点数据['检查点ID']
                        }
                    )
                    
                    yield CheckpointTuple(
                        config=checkpoint_config,
                        checkpoint=checkpoint,
                        metadata=metadata,
                        parent_config=None
                    )
                    
                except Exception as e:
                    检查点日志器.error(f"处理检查点历史项失败: {str(e)}")
                    continue
            
            检查点日志器.debug(f"✅ 列出检查点历史: {len(检查点历史)} 个检查点")
            
        except Exception as e:
            检查点日志器.error(f"❌ 列出检查点历史失败: {str(e)}")
    
    # 同步方法的兼容性实现
    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        """同步获取检查点元组（兼容性方法）"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.aget_tuple(config))
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            return asyncio.run(self.aget_tuple(config))
    
    def put(self, config: RunnableConfig, checkpoint: Checkpoint, metadata: CheckpointMetadata) -> RunnableConfig:
        """同步保存检查点（兼容性方法）"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.aput(config, checkpoint, metadata))
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            return asyncio.run(self.aput(config, checkpoint, metadata))
    
    def list(self, config: RunnableConfig, limit: int = 10, before: Optional[RunnableConfig] = None) -> Iterator[CheckpointTuple]:
        """同步列出检查点历史（兼容性方法）"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            async_gen = self.alist(config, limit, before)
            
            async def collect_results():
                results = []
                async for item in async_gen:
                    results.append(item)
                return results
            
            results = loop.run_until_complete(collect_results())
            return iter(results)
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            async def collect_results():
                results = []
                async for item in self.alist(config, limit, before):
                    results.append(item)
                return results
            
            results = asyncio.run(collect_results())
            return iter(results)


# 创建全局实例
PostgreSQL检查点管理器实例 = PostgreSQL检查点管理器()

# 向后兼容性别名
MySQL检查点管理器实例 = PostgreSQL检查点管理器实例
