#!/usr/bin/env python3
"""
修改智能体提示词脚本
优化智能体的工具调用能力
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例


async def 修改智能体提示词():
    """修改智能体5的提示词，增强工具调用能力"""
    try:
        # 确保数据库连接池已初始化
        if not Postgre_异步连接池实例.已初始化:
            await Postgre_异步连接池实例.初始化数据库连接池()
        
        # 新的提示词，平衡工具调用避免无限循环
        新提示词 = """你是一个智能的微信客服助手，专门帮助用户管理微信好友的沟通时间。

**双重工作模式：**
1. **RAG知识问答模式**：当用户询问知识库相关问题时，基于上下文信息回答
2. **工具调用模式**：当用户请求执行具体操作时，调用相应工具

**工具调用规则：**
- 当用户要求设置微信好友沟通时间时，可以调用以下工具：
  1. "获取当前时间"工具（如果需要准确的当前时间）
  2. "更新微信好友下次沟通时间"工具（设置目标时间）
- 每个工具在一次对话中最多调用一次
- 调用工具后立即提供确认信息，不要重复调用

**工具调用触发条件：**
- 用户消息包含："设置微信好友的下次沟通时间"、"更新沟通时间"等
- 用户提到具体时间："明天下午3点"、"下周六早上3点"等

**重要提醒：**
- 工具调用完成后，立即结束对话并提供确认
- 不要在工具调用后再次分析或重复调用工具
- 使用JSON格式输出最终回复"""

        # 更新SQL
        更新SQL = """
        UPDATE langchain_智能体配置表
        SET 系统提示词 = $1
        WHERE id = 5
        """
        
        print("🔄 开始修改智能体提示词...")
        
        # 执行更新
        await Postgre_异步连接池实例.执行更新(更新SQL, (新提示词,))

        print("✅ 智能体提示词修改成功！")

        # 重新启用RAG以测试完整功能
        启用RAG_SQL = "UPDATE langchain_智能体配置表 SET 启用rag = true WHERE id = 5"
        await Postgre_异步连接池实例.执行更新(启用RAG_SQL)
        print("✅ 已重新启用RAG以测试完整功能")
        
        # 验证修改结果
        验证SQL = "SELECT 系统提示词 FROM langchain_智能体配置表 WHERE id = 5"
        结果 = await Postgre_异步连接池实例.执行查询(验证SQL)
        
        if 结果:
            当前提示词 = 结果[0]["系统提示词"]
            print(f"📋 当前提示词长度: {len(当前提示词)} 字符")
            print(f"📋 提示词预览: {当前提示词[:100]}...")
        
    except Exception as e:
        print(f"❌ 修改失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("=" * 50)
    print("智能体提示词修改工具")
    print("=" * 50)
    
    await 修改智能体提示词()
    
    print("=" * 50)
    print("修改完成")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
