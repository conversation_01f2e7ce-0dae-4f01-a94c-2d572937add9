<template>
  <div class="wechat-talent-list">
    <!-- 微信达人卡片列表 -->
    <div class="talent-grid" v-if="talents.length > 0">
      <div 
        v-for="talent in talents" 
        :key="talent.id" 
        class="talent-card"
        @click="handleTalentClick(talent)"
      >
        <!-- 达人头像和基本信息 -->
        <div class="talent-header">
          <a-avatar 
            :size="60" 
            :src="talent.头像" 
            :alt="talent.昵称"
            class="talent-avatar"
          >
            <template #icon v-if="!talent.头像">
              <WechatOutlined />
            </template>
          </a-avatar>
          
          <!-- 认领状态标签 -->
          <div class="claim-status">
            <a-tag 
              v-if="talent.已认领 || talent.当前用户认领状态?.已认领" 
              color="red" 
              size="small"
            >
              <CheckCircleOutlined />
              已认领
            </a-tag>
            <a-tag v-else color="green" size="small">
              <PlusCircleOutlined />
              可认领
            </a-tag>
          </div>
        </div>
        
        <!-- 达人信息 -->
        <div class="talent-info">
          <h4 class="talent-name" :title="talent.昵称">
            {{ talent.昵称 || '未知昵称' }}
          </h4>
          
          <div class="talent-meta">
            <div class="meta-item" v-if="talent.微信号">
              <WechatOutlined />
              <span>{{ talent.微信号 }}</span>
            </div>
            
            <div class="meta-item" v-if="talent.地区">
              <EnvironmentOutlined />
              <span>{{ talent.地区 }}</span>
            </div>
            
            <div class="meta-item" v-if="talent.性别">
              <UserOutlined />
              <span>{{ talent.性别 }}</span>
            </div>
          </div>
          
          <!-- 统计数据 -->
          <div class="talent-stats">
            <div class="stat-item">
              <span class="stat-label">好友数</span>
              <span class="stat-value">{{ formatNumber(talent.好友数) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">朋友圈</span>
              <span class="stat-value">{{ formatNumber(talent.朋友圈发布数) }}</span>
            </div>
          </div>
          
          <!-- 联系方式状态 -->
          <div class="contact-status">
            <a-tag 
              v-if="talent.有联系方式" 
              color="blue" 
              size="small"
            >
              <PhoneOutlined />
              有联系方式
            </a-tag>
            <a-tag v-else color="default" size="small">
              <ExclamationCircleOutlined />
              无联系方式
            </a-tag>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="talent-actions">
          <a-button 
            v-if="!talent.已认领 && !talent.当前用户认领状态?.已认领"
            type="primary" 
            size="small" 
            @click.stop="handleClaim(talent)"
            :loading="claimingIds.includes(talent.id)"
            class="action-button claim-button"
          >
            <PlusOutlined />
            认领
          </a-button>
          
          <a-button 
            v-else-if="talent.当前用户认领状态?.已认领"
            type="default" 
            size="small" 
            @click.stop="handleUnclaim(talent)"
            :loading="unclaimingIds.includes(talent.id)"
            class="action-button unclaim-button"
          >
            <MinusOutlined />
            取消认领
          </a-button>
          
          <a-button 
            type="default" 
            size="small" 
            @click.stop="handleViewDetail(talent)"
            class="action-button detail-button"
          >
            <EyeOutlined />
            详情
          </a-button>
        </div>
        
        <!-- 更新时间 -->
        <div class="talent-footer">
          <span class="update-time">
            <ClockCircleOutlined />
            {{ formatTime(talent.更新时间) }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty 
        description="暂无微信达人数据"
        :image="h(WechatOutlined)"
      >
        <template #description>
          <span class="empty-description">
            {{ emptyDescription }}
          </span>
        </template>
      </a-empty>
    </div>
    
    <!-- 加载更多按钮 -->
    <div class="load-more-container" v-if="talents.length > 0 && hasMore">
      <a-button 
        type="default" 
        size="large" 
        @click="handleLoadMore"
        :loading="loading"
        block
        class="load-more-button"
      >
        <DownOutlined />
        加载更多微信达人
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  WechatOutlined, CheckCircleOutlined, PlusCircleOutlined, 
  EnvironmentOutlined, UserOutlined, PhoneOutlined, 
  ExclamationCircleOutlined, PlusOutlined, MinusOutlined, 
  EyeOutlined, ClockCircleOutlined, DownOutlined
} from '@ant-design/icons-vue'

// Props定义
const props = defineProps({
  // 微信达人列表数据
  talents: {
    type: Array,
    default: () => []
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  },
  // 是否还有更多数据
  hasMore: {
    type: Boolean,
    default: true
  },
  // 空状态描述
  emptyDescription: {
    type: String,
    default: '暂无微信达人数据，请尝试调整筛选条件或添加新的微信达人'
  }
})

// Emits定义
const emit = defineEmits([
  'talent-click',
  'claim-talent', 
  'unclaim-talent', 
  'view-detail',
  'load-more'
])

// 响应式数据
const claimingIds = ref([])  // 正在认领的达人id列表
const unclaimingIds = ref([])  // 正在取消认领的达人id列表

// 方法定义
/**
 * 格式化数字显示
 * @param {number} num - 要格式化的数字
 * @returns {string} 格式化后的字符串
 */
const formatNumber = (num) => {
  if (!num || num === 0) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

/**
 * 格式化时间显示
 * @param {string|Date} time - 时间
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (time) => {
  if (!time) return '未知'
  
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  // 小于1分钟
  if (diff < 60000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  }
  
  // 小于1天
  if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  }
  
  // 小于7天
  if (diff < 604800000) {
    return Math.floor(diff / 86400000) + '天前'
  }
  
  // 超过7天显示具体日期
  return date.toLocaleDateString()
}

/**
 * 处理达人卡片点击
 * @param {Object} talent - 达人信息
 */
const handleTalentClick = (talent) => {
  console.log('🎯 微信达人卡片点击:', talent)
  emit('talent-click', talent)
}

/**
 * 处理认领达人
 * @param {Object} talent - 达人信息
 */
const handleClaim = async (talent) => {
  console.log('➕ 认领微信达人:', talent)
  
  // 添加到认领中列表
  claimingIds.value.push(talent.id)
  
  try {
    emit('claim-talent', talent)
  } catch (error) {
    console.error('认领微信达人失败:', error)
    message.error('认领失败，请重试')
  } finally {
    // 从认领中列表移除
    const index = claimingIds.value.indexOf(talent.id)
    if (index > -1) {
      claimingIds.value.splice(index, 1)
    }
  }
}

/**
 * 处理取消认领达人
 * @param {Object} talent - 达人信息
 */
const handleUnclaim = async (talent) => {
  console.log('➖ 取消认领微信达人:', talent)
  
  // 添加到取消认领中列表
  unclaimingIds.value.push(talent.id)
  
  try {
    emit('unclaim-talent', talent)
  } catch (error) {
    console.error('取消认领微信达人失败:', error)
    message.error('取消认领失败，请重试')
  } finally {
    // 从取消认领中列表移除
    const index = unclaimingIds.value.indexOf(talent.id)
    if (index > -1) {
      unclaimingIds.value.splice(index, 1)
    }
  }
}

/**
 * 处理查看详情
 * @param {Object} talent - 达人信息
 */
const handleViewDetail = (talent) => {
  console.log('👁️ 查看微信达人详情:', talent)
  emit('view-detail', talent)
}

/**
 * 处理加载更多
 */
const handleLoadMore = () => {
  console.log('📄 加载更多微信达人')
  emit('load-more')
}

defineOptions({
  name: 'WechatTalentList'
})
</script>

<style scoped>
.wechat-talent-list {
  width: 100%;
}

.talent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.talent-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.talent-card:hover {
  border-color: #07c160;
  box-shadow: 0 4px 20px rgba(7, 193, 96, 0.1);
  transform: translateY(-2px);
}

.talent-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;
}

.talent-avatar {
  background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
  color: white;
  font-size: 24px;
}

.claim-status {
  flex-shrink: 0;
}

.talent-info {
  margin-bottom: 16px;
}

.talent-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.talent-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.meta-item .anticon {
  color: #07c160;
  font-size: 12px;
}

.talent-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #07c160;
}

.contact-status {
  margin-bottom: 16px;
}

.talent-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.action-button {
  flex: 1;
  border-radius: 6px;
  font-size: 12px;
  height: 32px;
}

.claim-button {
  background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
  border: none;
}

.claim-button:hover {
  background: linear-gradient(135deg, #2dd477 0%, #26e0b8 100%);
}

.unclaim-button {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.unclaim-button:hover {
  background: #ff4d4f;
  color: white;
}

.detail-button {
  border-color: #1890ff;
  color: #1890ff;
}

.detail-button:hover {
  background: #1890ff;
  color: white;
}

.talent-footer {
  display: flex;
  justify-content: center;
  padding-top: 12px;
  border-top: 1px solid #f5f5f5;
}

.update-time {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.empty-description {
  color: #666;
  font-size: 14px;
}

.load-more-container {
  margin-top: 24px;
}

.load-more-button {
  border-radius: 8px;
  height: 48px;
  font-size: 14px;
  border-color: #07c160;
  color: #07c160;
}

.load-more-button:hover {
  background: #07c160;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .talent-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .talent-card {
    padding: 16px;
  }
  
  .talent-actions {
    flex-direction: column;
  }
  
  .action-button {
    flex: none;
  }
}
</style>
