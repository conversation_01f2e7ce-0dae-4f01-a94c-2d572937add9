import {defineStore} from 'pinia';
import {fetchOpenApiSchema, parseOpenApiSchema, sendRequest as sendApiRequest} from '@/services/apiTestingService';
import {message} from 'ant-design-vue';

// 辅助函数：生成唯一ID
const generateUniqueId = () => `_${Math.random().toString(36).substr(2, 9)}`;

export const useApiTestingModule = defineStore('apiTesting', {
  state: () => ({
    // 平台自身的认证状态 (如果需要独立认证)
    isTestPlatformAuthenticated: false,
    testPlatformToken: localStorage.getItem('testPlatformToken') || null,
    testPlatformUser: null, // 可以存储一些测试平台用户信息

    // admin-frontend 主应用的认证token (应从主认证模块获取)
    mainAppToken: localStorage.getItem('apiTestingToken') || null, // 这个token需要从主应用的auth store获取或同步
    
    // API服务器配置
    apiBaseUrl: localStorage.getItem('apiTestingBaseUrl') || (import.meta.env.MODE === 'development' ? 'http://127.0.0.1:8000' : ''),
    autoLoadOnStart: localStorage.getItem('apiTestingAutoLoad') !== 'false', // 默认为true
    
    // 认证相关配置
    authPreference: localStorage.getItem('apiTestingAuthPreference') || 'header', // 'header', 'cookie', 'both'
    saveTokenToCookie: localStorage.getItem('apiTestingSaveTokenToCookie') === 'true' || false,
    forceAuth: localStorage.getItem('apiTestingForceAuth') === 'true' || false, // 新增：强制所有接口使用认证

    // 接口定义
    apiCategories: [], // 初始化为空数组
    apiEndpoints: [],   // 初始化为空数组
    filteredEndpoints: [], // 根据分类和搜索筛选后的接口列表

    // 当前操作状态
    selectedApiId: null,
    currentParams: {},
    currentHeaders: {},
    isLoading: false,

    // 响应结果
    responseStatus: null, // HTTP 状态码，如 200, 422, 500
    responseFastApiStatus: null, // FastAPI 业务状态码，如 100
    responseMessage: null, // FastAPI 的 message 字段
    responseData: null, // 响应体 (FastAPI message中的内容)
    responseHeaders: null, // 响应头
    responseTime: null, // 请求耗时 (ms)
    responseSize: null, // 响应大小 (bytes)

    // 请求历史
    requestHistory: JSON.parse(localStorage.getItem('apiRequestHistory') || '[]'),
    maxHistoryItems: 50, // 最大历史记录条数

    // 操作日志 (简化版，可以只存文本)
    logs: [],
    maxLogItems: 100,

    // UI状态
    activeApiCategoryKey: null,
    searchTerm: '',
  }),

  getters: {
    isAuthenticated: (state) => state.isTestPlatformAuthenticated,
    getSelectedApiDetails: (state) => {
      if (!state.selectedApiId) return null;
      return state.apiEndpoints.find(api => api.id === state.selectedApiId);
    },
    getFilteredEndpoints: (state) => {
      let endpoints = state.apiEndpoints;
      if (state.activeApiCategoryKey) {
        endpoints = endpoints.filter(api => api.category === state.activeApiCategoryKey);
      }
      if (state.searchTerm) {
        const term = state.searchTerm.toLowerCase();
        endpoints = endpoints.filter(api => 
          api.name.toLowerCase().includes(term) || 
          api.path.toLowerCase().includes(term)
        );
      }
      return endpoints;
    },
  },

  actions: {
    // --- 认证相关 (测试平台自身) ---
    async loginTestPlatform(credentials) {
      // try {
      //   const response = await loginToTestPlatform(credentials); // 实际调用service
      //   this.testPlatformToken = response.token;
      //   this.testPlatformUser = response.user;
      //   this.isTestPlatformAuthenticated = true;
      //   localStorage.setItem('testPlatformToken', response.token);
      //   this.addLog('测试平台登录成功');
      //   return true;
      // } catch (error) {
      //   this.addLog(`测试平台登录失败: ${error.message}`);
      //   this.isTestPlatformAuthenticated = false;
      //   return false;
      // }
      // 模拟
      if (credentials.username === 'admin' && credentials.password === 'password') {
        this.testPlatformToken = 'fake-test-platform-token';
        this.testPlatformUser = { name: '测试管理员' };
        this.isTestPlatformAuthenticated = true;
        localStorage.setItem('testPlatformToken', this.testPlatformToken);
        this.addLog('测试平台登录成功 (模拟)');
        return true;
      }
      this.addLog('测试平台登录失败 (模拟)');
      return false;
    },

    logoutTestPlatform() {
      this.testPlatformToken = null;
      this.testPlatformUser = null;
      this.isTestPlatformAuthenticated = false;
      localStorage.removeItem('testPlatformToken');
      this.addLog('测试平台已登出');
    },

    // --- 主应用Token管理 ---
    // 这个方法应该由主应用的认证逻辑调用，或者在主应用token变化时同步
    setMainAppToken(token) {
      this.mainAppToken = token;
      localStorage.setItem('apiTestingToken', token);
      this.addLog(`主应用Token已${token ? '设置' : '清除'}`);
      
      // 如果设置了保存到Cookie，则同时更新Cookie
      if (token && (this.authPreference === 'cookie' || this.authPreference === 'both')) {
        this.saveTokenToCookie(token);
      }
    },
    
    // 保存登录响应中的Token
    saveTokenFromResponse(response) {
      const responseData = response?.data;
      if (responseData?.access_token) {
        const token = responseData.access_token;
        const tokenType = responseData.token_type || 'bearer';
        
        // 保存到store中
        this.mainAppToken = token;
        
        // 保存到localStorage
        localStorage.setItem('apiTestingToken', token);
        
        // 如果配置了保存到Cookie，则也保存到Cookie
        if (this.authPreference === 'cookie' || this.authPreference === 'both') {
          this.saveTokenToCookie(token);
        }
        
        this.addLog(`已从响应中保存${tokenType}类型的认证Token`);
        return true;
      }
      return false;
    },
    
    // 保存Token到Cookie
    saveTokenToCookie(token) {
      if (!token) return;
      
      // 设置Cookie，过期时间为30天或从Token中解析
      try {
        const expiryDate = new Date();
        
        // 尝试从JWT Token中解析过期时间
        try {
          const base64Url = token.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const payload = JSON.parse(jsonPayload);
          
          if (payload.exp) {
            // JWT的exp是Unix时间戳（秒）
            expiryDate.setTime(payload.exp * 1000);
          } else {
            // 默认30天
            expiryDate.setDate(expiryDate.getDate() + 30);
          }
        } catch (e) {
          // 解析失败，使用默认30天
          expiryDate.setDate(expiryDate.getDate() + 30);
        }
        
        // 设置Cookie
        document.cookie = `token=${token}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Strict`;
        this.addLog('已将Token保存到Cookie中');
      } catch (error) {
        console.error('保存Token到Cookie失败:', error);
        this.addLog(`保存Token到Cookie失败: ${error.message}`);
      }
    },
    
    // 从Cookie中移除Token
    removeTokenFromCookie() {
      document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      this.addLog('已从Cookie中移除Token');
    },
    
    // 设置认证方式偏好
    setAuthPreference(preference) {
      if (['header', 'cookie', 'both'].includes(preference)) {
        this.authPreference = preference;
        localStorage.setItem('apiTestingAuthPreference', preference);
        this.addLog(`已将认证方式偏好设置为: ${preference}`);
        
        // 如果选择了Cookie或both，且有Token，则保存到Cookie
        if ((preference === 'cookie' || preference === 'both') && this.mainAppToken) {
          this.saveTokenToCookie(this.mainAppToken);
        } else if (preference === 'header') {
          // 如果只使用header，则移除Cookie中的token
          this.removeTokenFromCookie();
        }
      }
    },
    
    // 新增：设置是否强制所有接口使用认证
    setForceAuth(force) {
      this.forceAuth = force;
      localStorage.setItem('apiTestingForceAuth', force);
      this.addLog(`已${force ? '开启' : '关闭'}强制所有接口使用认证`);
    },
    
    // 手动导入Token
    importToken(token, saveToPreference = true) {
      if (!token) {
        this.addLog('无效的Token，导入失败');
        return false;
      }
      
      this.mainAppToken = token;
      localStorage.setItem('apiTestingToken', token);
      this.addLog('已手动导入Token');
      
      if (saveToPreference && (this.authPreference === 'cookie' || this.authPreference === 'both')) {
        this.saveTokenToCookie(token);
      }
      
      return true;
    },
    
    // 清除Token
    clearToken() {
      this.mainAppToken = null;
      localStorage.removeItem('apiTestingToken');
      this.removeTokenFromCookie();
      this.addLog('已清除所有Token');
    },

    // --- API服务器配置管理 ---
    setApiBaseUrl(url) {
      // 验证URL格式
      try {
        const urlObj = new URL(url);
        // 确保是http或https协议
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
          throw new Error('URL必须使用http或https协议');
        }
        
        // 移除末尾的斜杠
        const cleanUrl = url.replace(/\/$/, '');
        
        this.apiBaseUrl = cleanUrl;
        localStorage.setItem('apiTestingBaseUrl', cleanUrl);
        this.addLog(`API基础URL已设置为: ${cleanUrl}`);
        return true;
      } catch (error) {
        this.addLog(`设置API基础URL失败: ${error.message}`);
        return false;
      }
    },

    resetApiBaseUrl() {
      const defaultUrl = 'http://127.0.0.1:8000';
      this.apiBaseUrl = defaultUrl;
      localStorage.setItem('apiTestingBaseUrl', defaultUrl);
      this.addLog(`API基础URL已重置为默认值: ${defaultUrl}`);
    },

    // --- 接口数据管理 ---
    async loadApiDefinitions() {
      this.isLoading = true;
      this.addLog('开始加载接口定义...');
      try {
        // 使用用户配置的API基础URL
        const schemaUrl = `${this.apiBaseUrl}/850483315-openapi.json`;
        this.addLog(`正在从 ${schemaUrl} 加载接口定义...`);
        
        const openApiSchema = await fetchOpenApiSchema(schemaUrl);
        
        if (!openApiSchema) {
          throw new Error('未能获取OpenAPI schema，返回为空');
        }
        
        const parseResult = parseOpenApiSchema(openApiSchema);
        const { categories, endpoints, stats } = parseResult;
        
        this.apiCategories = categories;
        this.apiEndpoints = endpoints;

        if (this.apiCategories.length > 0) {
          // 确保如果当前 activeApiCategoryKey 无效或未设置，则设置一个默认的
          const isValidCategoryKey = this.apiCategories.some(cat => cat.key === this.activeApiCategoryKey);
          if (!this.activeApiCategoryKey || !isValidCategoryKey) {
            this.activeApiCategoryKey = this.apiCategories[0].key;
          }
        } else {
          this.activeApiCategoryKey = null; // 没有分类，则清空
        }
        
        // 增强的成功日志，包含统计信息
        const successMsg = `接口定义加载成功！统计信息：
        • 总接口数: ${stats.totalEndpoints}
        • 分类数: ${stats.totalCategories}
        • 需要认证的接口: ${stats.authRequiredCount}
        • 方法分布: ${Object.entries(stats.methodStats).map(([method, count]) => `${method}(${count})`).join(', ')}`;
        
        this.addLog(successMsg);
        
        // 显示友好的成功消息
        message.success(`API定义加载成功！共${stats.totalEndpoints}个接口，${stats.totalCategories}个分类`);

      } catch (error) {
        console.error('[API测试平台] 加载接口定义失败:', error);
        
        // 根据错误类型提供更有针对性的日志和用户提示
        let errorMessage = '加载API定义失败';
        let userTip = '';
        
        if (error.message.includes('404')) {
          errorMessage = 'OpenAPI文档未找到';
          userTip = '请检查后端服务是否正常运行，且OpenAPI文档路径配置正确';
        } else if (error.message.includes('超时')) {
          errorMessage = '请求超时';
          userTip = '请检查网络连接和服务器响应时间';
        } else if (error.message.includes('网络连接')) {
          errorMessage = '网络连接失败';
          userTip = '请检查后端服务是否在正确端口运行';
        } else if (error.message.includes('CORS')) {
          errorMessage = 'CORS跨域问题';
          userTip = '请检查后端CORS配置';
        } else {
          errorMessage = error.message || '未知错误';
        }
        
        this.addLog(`错误：${errorMessage} - ${error.message}`);
        if (userTip) {
          this.addLog(`建议：${userTip}`);
        }
        
        message.error(`${errorMessage}${userTip ? ` - ${userTip}` : ''}`);
      } finally {
        this.isLoading = false;
      }
    },

    // 测试连接功能
    async testConnection(url = null) {
      const testUrl = url || this.apiBaseUrl;
      try {
        this.addLog(`正在测试连接: ${testUrl}`);
        const response = await fetch(`${testUrl}/850483315-openapi.json`, {
          method: 'GET',
          timeout: 10000
        });
        
        if (response.ok) {
          this.addLog(`连接测试成功: ${testUrl}`);
          return {
            success: true,
            message: `连接成功！服务器响应正常 (${response.status})`
          };
        } else {
          this.addLog(`连接测试失败: ${testUrl} - ${response.status}`);
          return {
            success: false,
            message: `连接失败：服务器返回 ${response.status} ${response.statusText}`
          };
        }
      } catch (error) {
        this.addLog(`连接测试失败: ${testUrl} - ${error.message}`);
        return {
          success: false,
          message: `连接失败：${error.message}`
        };
      }
    },

    setActiveCategory(categoryKey) {
      this.activeApiCategoryKey = categoryKey;
      this.selectedApiId = null; // 清空已选接口
      this.searchTerm = ''; // 清空搜索
      this.addLog(`切换接口分类: ${categoryKey}`);
    },

    setSearchTerm(term) {
      this.searchTerm = term;
    },

    selectApi(apiId) {
      this.selectedApiId = apiId;
      const api = this.getSelectedApiDetails;
      if (api) {
        this.currentParams = api.params.reduce((acc, param) => {
          acc[param.key] = param.defaultValue !== undefined ? param.defaultValue : '';
          return acc;
        }, {});
        this.currentHeaders = api.headers.reduce((acc, header) => {
          if (header.editable !== false) { // 只添加可编辑的或未指定可编辑性的
            acc[header.key] = header.value !== undefined ? header.value : '';
          }
          return acc;
        }, {});
        // 自动添加 Content-Type if not present and params exist
        if (api.params && api.params.length > 0 && !this.currentHeaders['Content-Type']) {
            this.currentHeaders['Content-Type'] = 'application/json';
        }
        this.addLog(`选择接口: ${api.name}`);
        // 清空上次响应
        this.clearResponse();
      } else {
        this.currentParams = {};
        this.currentHeaders = {};
      }
    },

    updateParam(key, value) {
      if (this.currentParams.hasOwnProperty(key)) {
        this.currentParams[key] = value;
      }
    },

    updateHeader(key, value) {
      this.currentHeaders[key] = value;
    },
    addHeader(key, value) {
        if (key && !this.currentHeaders.hasOwnProperty(key)) {
            this.currentHeaders[key] = value || '';
        }
    },
    removeHeader(key) {
        delete this.currentHeaders[key];
    },

    clearResponse() {
        this.responseStatus = null;
        this.responseFastApiStatus = null;
        this.responseMessage = null;
        this.responseData = null;
        this.responseHeaders = null;
        this.responseTime = null;
        this.responseSize = null;
    },

    // --- 请求发送 ---
    async executeRequest() {
      const api = this.getSelectedApiDetails;
      if (!api) {
        this.addLog('错误：未选择任何接口进行请求');
        return;
      }

      this.isLoading = true;
      this.clearResponse();
      this.addLog(`准备发送请求: ${api.method} ${api.path}`);
      const startTime = Date.now();

      try {
        // 准备请求参数，优化参数处理逻辑
        const processedParams = this.buildRequestParams(api);
        
        // 准备请求头，移除空值并添加认证
        const requestHeaders = this.buildRequestHeaders();
        
        // 构建请求配置
        const requestConfig = {
          headers: requestHeaders,
          withCredentials: this.authPreference === 'cookie' || this.authPreference === 'both',
          timeout: 30000
        };

        this.addLog(`发送请求: ${api.method} ${api.path}`);
        this.addLog(`请求参数: ${JSON.stringify(processedParams, null, 2)}`);
        this.addLog(`请求头: ${JSON.stringify(requestHeaders, null, 2)}`);

        // 发送请求 - 使用优化后的service
        const result = await sendApiRequest(
          api, 
          processedParams, 
          requestConfig, 
          this.mainAppToken,
          this.apiBaseUrl
        );

        // 统一处理响应结果
        this.handleResponse(result, api, startTime);

      } catch (error) {
        // 统一处理错误
        this.handleError(error, api, startTime);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * 构建请求参数
     * 根据API定义处理和验证参数
     */
    buildRequestParams(api) {
      const processedParams = {};
      
      if (api.params && Array.isArray(api.params)) {
        // 根据OpenAPI定义处理参数
        api.params.forEach(paramDef => {
          const paramKey = paramDef.key || paramDef.name;
          const currentValue = this.currentParams[paramKey];

          if (currentValue !== undefined && currentValue !== '') {
            // 根据参数类型进行转换
            processedParams[paramKey] = this.convertParamValue(currentValue, paramDef);
          } else if (currentValue === undefined && paramDef.defaultValue !== undefined) {
            // 使用默认值
            processedParams[paramKey] = paramDef.defaultValue;
            this.addLog(`参数 "${paramKey}" 使用默认值: ${paramDef.defaultValue}`);
          }
        });
      } else {
        // 简单处理：移除空字符串
        for (const key in this.currentParams) {
          if (this.currentParams[key] !== '') {
            processedParams[key] = this.currentParams[key];
          }
        }
      }
      
      return processedParams;
    },

    /**
     * 转换参数值类型
     */
    convertParamValue(value, paramDef) {
      const type = paramDef.type;
      
      try {
        switch (type) {
          case 'object':
          case 'array':
            return typeof value === 'string' ? JSON.parse(value) : value;
          case 'number':
            const numValue = Number(value);
            return !isNaN(numValue) ? numValue : value;
          case 'boolean':
            return value === 'true' || value === true;
          default:
            return value;
        }
      } catch (e) {
        this.addLog(`警告: 参数 "${paramDef.key}" 类型转换失败，将作为字符串发送`);
        return value;
      }
    },

    /**
     * 构建请求头
     * 处理认证和自定义头部
     */
    buildRequestHeaders() {
      // 移除空值的请求头
      const headers = Object.entries(this.currentHeaders).reduce((acc, [key, value]) => {
        if (value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {});

      // 检查是否手动设置了Authorization头
      const hasManualAuth = Object.keys(headers).some(key => key.toLowerCase() === 'authorization');

      // 添加认证头（如果需要且未手动设置）
      const needsAuth = this.getSelectedApiDetails?.requiresAuth || this.forceAuth;
      if (needsAuth && this.mainAppToken && !hasManualAuth) {
        if (this.authPreference === 'header' || this.authPreference === 'both') {
          headers['Authorization'] = `Bearer ${this.mainAppToken}`;
          this.addLog(`已自动添加认证头${!this.getSelectedApiDetails?.requiresAuth ? ' (强制认证模式)' : ''}`);
        }
      }

      return headers;
    },

    /**
     * 统一处理响应结果
     */
    handleResponse(result, api, startTime) {
      const endTime = Date.now();
      this.responseTime = endTime - startTime;

      if (result.success) {
        // 成功响应
        this.responseStatus = result.status;
        this.responseHeaders = result.headers;
        this.responseSize = result.metadata?.responseSize || 0;
        
        // 解析后端标准响应格式
        const responseData = result.data;
        if (responseData && typeof responseData === 'object' && 
            responseData.hasOwnProperty('status') && responseData.hasOwnProperty('message')) {
          // 标准后端响应格式 {status: 100, message: "...", data: {...}}
          this.responseFastApiStatus = responseData.status;
          
          // 安全地处理 message 字段
          if (typeof responseData.message === 'string') {
            this.responseMessage = responseData.message;
          } else {
            this.responseMessage = 'OK';
          }
          
          // 将完整的响应对象赋值给 responseData 以在响应体中显示
          this.responseData = responseData;
        } else {
          // 非标准响应格式
          this.responseFastApiStatus = result.status;
          this.responseMessage = null;
          this.responseData = responseData;
        }

        // 自动保存登录Token
        this.checkAndSaveLoginToken(responseData, api);
        
        this.addLog(`请求成功 - 用时: ${this.responseTime}ms, 大小: ${this.responseSize} bytes`);
        this.addRequestToHistory(api, 'success', this.responseStatus, this.responseFastApiStatus);
        
      } else {
        // 处理错误响应（API测试服务已标准化处理）
        this.handleApiError(result, api);
      }
    },

    /**
     * 统一处理错误
     */
    handleError(error, api, startTime) {
      const endTime = Date.now();
      this.responseTime = endTime - startTime;
      
      this.addLog(`请求失败: ${api.path} - ${error.message}`);
      
      // 网络错误或其他异常
      this.responseStatus = 'NETWORK_ERROR';
      this.responseFastApiStatus = null;
      this.responseMessage = error.message || '网络连接失败';
      this.responseData = { 错误类型: '网络错误', 详细信息: error.message };
      
      this.addRequestToHistory(api, 'error', 'NETWORK_ERROR');
      message.error(`请求处理失败: ${this.responseMessage}`);
    },

    /**
     * 处理API错误响应
     */
    handleApiError(result, api) {
      this.responseStatus = result.status;
      this.responseHeaders = result.headers;
      this.responseSize = result.metadata?.responseSize || 0;
      
      const errorData = result.data;
      if (errorData && typeof errorData === 'object' && 
          errorData.hasOwnProperty('status') && errorData.hasOwnProperty('message')) {
        // 标准错误响应格式
        this.responseFastApiStatus = errorData.status;
        this.responseMessage = typeof errorData.message === 'string' 
          ? errorData.message 
          : JSON.stringify(errorData.message);
        this.responseData = errorData;
      } else {
        // 非标准错误响应
        this.responseFastApiStatus = result.status;
        this.responseMessage = errorData?.detail || errorData?.error || result.message || '服务器返回错误';
        this.responseData = errorData;
      }
      
      this.addLog(`API错误 - HTTP: ${this.responseStatus}, 业务状态: ${this.responseFastApiStatus || 'N/A'}`);
      this.addRequestToHistory(api, 'error', this.responseStatus, this.responseFastApiStatus);
      message.error(`API调用失败: ${this.responseMessage}`);
    },

    /**
     * 检查并保存登录Token
     */
    checkAndSaveLoginToken(responseData, api) {
      const isLoginApi = api.path.includes('登录') || api.path.includes('login');
      const hasToken = responseData && (
        responseData.access_token || responseData.访问令牌 ||
        responseData.token || responseData.令牌
      );
      
      if (isLoginApi && hasToken) {
        const tokenData = {
          access_token: responseData.access_token || responseData.访问令牌 || 
                       responseData.token || responseData.令牌,
          token_type: responseData.token_type || responseData.令牌类型 || 'bearer'
        };
        this.saveTokenFromResponse({ data: tokenData });
        this.addLog('检测到登录成功响应，已自动保存Token');
      }
    },

    // --- 历史记录管理 ---
    addRequestToHistory(api, statusType, httpStatus, fastApiStatus = null) {
      const historyItem = {
        id: generateUniqueId(),
        apiId: api.id,
        name: api.name,
        path: api.path,
        method: api.method,
        timestamp: new Date().toISOString(),
        statusType, // 'success' or 'error'
        httpStatus,
        fastApiStatus,
        params: JSON.parse(JSON.stringify(this.currentParams)), // 深拷贝
        headers: JSON.parse(JSON.stringify(this.currentHeaders)), // 深拷贝
      };
      this.requestHistory.unshift(historyItem);
      if (this.requestHistory.length > this.maxHistoryItems) {
        this.requestHistory.pop();
      }
      localStorage.setItem('apiRequestHistory', JSON.stringify(this.requestHistory));
    },

    loadRequestFromHistory(historyId) {
      const historyItem = this.requestHistory.find(item => item.id === historyId);
      if (historyItem) {
        this.selectApi(historyItem.apiId); // 会自动加载参数和头部模板
        // 然后覆盖为历史记录中的实际参数和头部
        this.currentParams = JSON.parse(JSON.stringify(historyItem.params));
        this.currentHeaders = JSON.parse(JSON.stringify(historyItem.headers));
        this.addLog(`从历史记录加载: ${historyItem.name}`);
        this.clearResponse(); // 清空当前响应，准备重新发送或查看
      }
    },

    clearRequestHistory() {
      this.requestHistory = [];
      localStorage.removeItem('apiRequestHistory');
      this.addLog('请求历史已清空');
    },

    // --- 从历史加载参数 ---
    loadParamsFromHistory(paramsObject) {
      if (paramsObject && typeof paramsObject === 'object') {
        // 深拷贝以确保响应性和避免直接修改历史记录中的对象
        this.currentParams = JSON.parse(JSON.stringify(paramsObject));
        this.addLog(`已从历史记录加载参数集。`);
        // 确保与 selectApi 中的逻辑一致，如果参数表单中没有定义的字段，
        // 它们虽然存在于 currentParams 中，但不会显示。这是可接受的行为。
      } else {
        this.addLog('警告：尝试从历史记录加载无效的参数集。');
      }
    },

    // --- 日志管理 ---
    addLog(message) {
      const logEntry = `[${new Date().toLocaleTimeString()}] ${message}`;
      this.logs.unshift(logEntry);
      if (this.logs.length > this.maxLogItems) {
        this.logs.pop();
      }
      // console.log(logEntry); // 可以在开发时输出到控制台
    },

    clearLogs() {
      this.logs = [];
    },
  },
});