"""
用户联系人管理路由
"""

from fastapi import APIRouter, Depends

from 数据模型.用户联系人模型 import (
    关联用户联系人请求模型,
    创建联系人并关联请求模型,
    查询用户联系人请求模型
)
from 数据模型.响应模型 import 统一响应模型
from 服务.用户联系人服务 import 用户联系人服务实例
from 依赖项.认证 import 获取当前用户
from 日志 import 错误日志器

# 创建路由器
用户联系人路由 = APIRouter(prefix="/user-contact", tags=["用户联系人管理"])



@用户联系人路由.post("/associate", summary="关联联系人到达人补充信息")
async def 关联联系人接口(
    请求数据: 关联用户联系人请求模型,
    当前用户: dict = Depends(获取当前用户)
):
    """
    将用户联系人关联到达人补充信息
    
    功能说明：
    - 更新用户达人补充信息表的用户联系人表id字段
    - 建立联系人与达人补充信息的关联关系
    """
    try:
        # 调用服务层关联联系人
        结果 = await 用户联系人服务实例.关联联系人到达人补充信息(
            请求数据.补充信息id,
            请求数据.用户联系人id
        )
        
        if 结果["status"] == "success":
            return 统一响应模型.成功(None, 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])
            
    except Exception as e:
        错误日志器.error(f"关联联系人接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"关联联系人失败: {str(e)}")


@用户联系人路由.post("/list", summary="获取用户联系人列表")
async def 获取用户联系人列表接口(
    请求数据: 查询用户联系人请求模型,
    当前用户: dict = Depends(获取当前用户)
):
    """
    获取当前用户的联系人列表

    功能说明：
    - 查询当前用户的所有联系人及关联的联系方式
    - 支持按姓名和联系方式搜索
    """
    try:
        用户id = 当前用户["id"]

        # 调用服务层查询联系人列表
        结果 = await 用户联系人服务实例.查询用户联系人列表(用户id, 请求数据.关键词)

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果["data"], "获取联系人列表成功")
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取用户联系人列表接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"获取联系人列表失败: {str(e)}")


@用户联系人路由.post("/create-and-associate", summary="创建联系人并关联到达人补充信息")
async def 创建联系人并关联接口(
    请求数据: 创建联系人并关联请求模型,
    当前用户: dict = Depends(获取当前用户)
):
    """
    创建用户联系人并关联到达人补充信息（事务操作）

    功能说明：
    - 在事务中创建用户联系人并关联到达人补充信息
    - 确保事务一致性：如果关联失败，则不创建联系人
    - 避免创建孤立的联系人记录
    """
    try:
        用户id = 当前用户["id"]

        # 调用服务层创建联系人并关联
        结果 = await 用户联系人服务实例.创建联系人并关联到补充信息(
            用户id,
            请求数据.姓名,
            请求数据.补充信息id
        )

        if 结果["status"] == "success":
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(400, 结果["message"])

    except Exception as e:
        错误日志器.error(f"创建联系人并关联接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"创建联系人并关联失败: {str(e)}")
