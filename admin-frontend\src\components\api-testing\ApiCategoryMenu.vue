<template>
  <div class="api-category-menu">
    <!-- API分类菜单：展示所有接口分类，支持点击选择 -->
    <a-menu
      mode="inline"
      theme="light" 
      :selected-keys="[activeCategoryKey]"
      @click="handleCategoryClick"
    >
      <!-- 遍历所有分类，为每个分类创建菜单项 -->
      <a-menu-item v-for="category in categories" :key="category.key">
        <template #icon>
          <!-- 根据分类配置的图标名称动态渲染对应的Ant Design图标 -->
          <component :is="getAntIcon(category.icon)" v-if="category.icon" />
          <!-- 如果没有配置图标，则使用默认的文件夹图标 -->
          <folder-outlined v-else />
        </template>
        {{ category.name }}
      </a-menu-item>
    </a-menu>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue';
import { Menu as AMenu, MenuItem as AMenuItem } from 'ant-design-vue';
import { useApiTestingModule } from '@/store/apiTestingModule';
import {
  AppstoreOutlined,
  BookOutlined,
  EllipsisOutlined,
  FolderOutlined,
  RobotOutlined,
  SettingOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  WechatOutlined,
} from '@ant-design/icons-vue';

const store = useApiTestingModule();

// 计算属性：从store中获取分类数据和当前激活的分类
const categories = computed(() => store.apiCategories);
const activeCategoryKey = computed(() => store.activeApiCategoryKey);

// 图标映射表：将图标名称字符串映射到实际的Ant Design Vue图标组件
const antIcons = {
  user: UserOutlined,
  appstore: AppstoreOutlined,
  'shopping-cart': ShoppingCartOutlined,
  robot: RobotOutlined,
  setting: SettingOutlined,
  wechat: WechatOutlined,
  book: BookOutlined,
  ellipsis: EllipsisOutlined,
  folder: FolderOutlined,
};

/**
 * 根据图标名称获取对应的Ant Design Vue图标组件
 * @param {string} iconName - 图标名称
 * @returns {Component} - 对应的Vue组件，找不到时返回默认图标
 */
const getAntIcon = (iconName) => {
  return antIcons[iconName] || FolderOutlined;
};

/**
 * 处理分类菜单项点击事件
 * @param {Object} item - 点击的菜单项信息
 * @param {string} item.key - 分类的唯一标识
 */
const handleCategoryClick = ({ key }) => {
  console.log('[分类菜单] 选择分类:', key);
  store.setActiveCategory(key);
};

// 监听分类数据变化，自动选择第一个分类（如果当前没有选中的分类）
watch([categories, activeCategoryKey], ([newCategories, newActiveKey]) => {
  // 当满足以下条件时，自动选择第一个分类：
  // 1. 有可用的分类数据
  // 2. 当前没有激活的分类
  if (newCategories.length > 0 && !newActiveKey) {
    console.log('[分类菜单] 自动选择第一个分类:', newCategories[0].key);
    store.setActiveCategory(newCategories[0].key);
  }
}, { immediate: true });
</script>

<style scoped>
.api-category-menu {
  /* 分类菜单容器样式 */
}

.ant-menu-inline {
  /* 移除内联菜单的默认右边框，与浅色主题保持一致 */
  border-right: none;
}
</style> 