<template>
  <div class="talent-management">
    <a-card class="custom-card" :bordered="false">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <!-- 达人公海 -->
        <a-tab-pane key="talent-pool" class="tab-pane">
          <template #tab>
            <span class="tab-header">
              <user-outlined />
              达人公海
            </span>
          </template>
          <div class="content-placeholder">
            <a-empty
              description="达人公海功能正在开发中..."
              :image="h(UserOutlined)"
            />
          </div>
        </a-tab-pane>

        <!-- 我的达人 -->
        <a-tab-pane key="my-talents" class="tab-pane">
          <template #tab>
            <span class="tab-header">
              <team-outlined />
              我的达人
            </span>
          </template>
          <div class="content-placeholder">
            <a-empty
              description="我的达人功能正在开发中..."
              :image="h(TeamOutlined)"
            />
          </div>
        </a-tab-pane>


      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  UserOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'

defineOptions({
  name: 'TalentManagement'
})

// 路由相关
const route = useRoute()
const router = useRouter()

// 当前激活的标签页
const activeTab = ref(route.query.tab || 'talent-pool')

/**
 * 处理标签页切换
 */
const handleTabChange = (key) => {
  activeTab.value = key

  // 更新URL参数
  router.replace({
    query: {
      ...route.query,
      tab: key
    }
  })
}
</script>

<style scoped>
.talent-management {
  padding: 0;
}

.custom-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-pane {
  padding: 0;
}

.tab-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.content-placeholder {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>