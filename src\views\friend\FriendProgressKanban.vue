<template>
  <div class="kanban-board">
    <div v-if="loading" class="loading-spinner">
      <a-spin size="large" />
      <p>正在努力加载看板数据...</p>
    </div>
    <div v-else-if="!wechatId" class="empty-state">
      <a-empty description="请先在页面顶部选择一个微信账号来查看看板" />
    </div>
    <div v-else class="board-columns">
      <draggable
        class="lanes-container"
        v-model="localColumns"
        group="lanes"
        item-key="id"
        handle=".lane-header"
        :animation="200"
      >
        <template #item="{ element: column }">
          <div class="kanban-lane">
            <div class="lane-header">
              <span class="lane-title">{{ column.title }}</span>
              <span class="lane-count">{{ column.cards.length }}</span>
            </div>
            <draggable
              class="task-list"
              v-model="column.cards"
              group="tasks"
              item-key="id"
              :animation="150"
              @change="(event) => handleTaskMove(event, column.id)"
            >
              <template #item="{ element: card }">
                <a-card class="task-card" hoverable @click="showTaskDetail(card)">
                  <p class="task-title">{{ card.title }}</p>
                  <div class="task-meta">
                    <span>负责人: {{ card.owner }}</span>
                    <span>更新于: {{ card.lastUpdate }}</span>
                  </div>
                </a-card>
              </template>
            </draggable>
          </div>
        </template>
      </draggable>
    </div>
    <a-modal v-model:open="isDetailModalVisible" title="任务详情" :footer="null">
      <pre>{{ JSON.stringify(currentTask, null, 2) }}</pre>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from 'vue';
import draggable from 'vuedraggable';
import { message } from 'ant-design-vue';

const props = defineProps({
  wechatId: Number,
  kanbanData: Array,
  loading: Boolean,
});

const emit = defineEmits(['refresh']);

const localColumns = ref([]);
const isDetailModalVisible = ref(false);
const currentTask = ref(null);

watch(() => props.kanbanData, (newData) => {
  if (newData && Array.isArray(newData)) {
    // 确保每个泳道都有cards属性，便于拖拽操作
    localColumns.value = newData.map(lane => ({
      ...lane,
      cards: lane.cards || []
    }));
    console.log('看板数据已处理:', localColumns.value);
  } else {
    console.warn('看板数据为空或格式不正确:', newData);
    localColumns.value = [];
  }
}, { immediate: true, deep: true });


const handleTaskMove = async (event, newColumnId) => {
  if (event.added) {
    const task = event.added.element;
    console.log(`任务 ${task.id} 已移动到列 ${newColumnId}`);
    // 这里可以调用API更新任务状态
    // await kanbanService.updateTaskStatus(task.id, newColumnId);
    message.success(`任务 "${task.title}" 已更新状态！`);
    // 可以选择性地通知父组件刷新
    // emit('refresh');
  }
};

const showTaskDetail = (task) => {
  currentTask.value = task.details || task;
  isDetailModalVisible.value = true;
};
</script>

<style lang="less" scoped>
/* 保持样式不变 */
.kanban-board {
  min-height: 60vh;
}
.loading-spinner, .empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  p {
    margin-top: 16px;
    color: #888;
  }
}
.board-columns {
  overflow-x: auto;
  padding-bottom: 16px;
}
.lanes-container {
  display: flex;
  gap: 16px;
}
.kanban-lane {
  flex: 0 0 300px;
  background-color: #f0f2f5;
  border-radius: 8px;
  padding: 8px;
}
.lane-header {
  padding: 8px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  cursor: move;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 8px;
}
.lane-title {
  font-size: 16px;
}
.lane-count {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}
.task-list {
  min-height: 400px;
  max-height: 70vh;
  overflow-y: auto;
}
.task-card {
  margin-bottom: 8px;
  cursor: pointer;
  .task-title {
    font-weight: 500;
    margin-bottom: 8px;
  }
  .task-meta {
    font-size: 12px;
    color: #888;
    display: flex;
    justify-content: space-between;
  }
}
</style> 