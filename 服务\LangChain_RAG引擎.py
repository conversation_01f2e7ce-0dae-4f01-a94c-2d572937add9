"""
LangChain RAG引擎 - PostgreSQL向量存储版本

功能：
1. 文档加载和处理
2. PostgreSQL向量存储管理
3. 检索增强生成
4. 知识库集成

重构说明：
- 使用PostgreSQL pgvector作为唯一向量存储
- 统一向量数据和元数据管理
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# PostgreSQL向量化功能已集成到主类中，无需外部导入


# LangChain核心组件
try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter  # type: ignore
    from langchain_community.document_loaders import (  # type: ignore
        CSVLoader,  # type: ignore
        TextLoader,  # type: ignore
        UnstructuredExcelLoader,  # type: ignore
        UnstructuredWordDocumentLoader,  # type: ignore
    )

    try:
        from langchain_community.document_loaders import (
            PyPDFLoader as PDFLoader,  # type: ignore
        )
    except ImportError:
        from langchain_community.document_loaders import (
            UnstructuredPDFLoader as PDFLoader,  # type: ignore
        )

    from langchain_core.documents import Document  # type: ignore

    # 嵌入模型导入
    try:
        from langchain_openai import OpenAIEmbeddings  # type: ignore
    except ImportError:
        from langchain_community.embeddings import OpenAIEmbeddings  # type: ignore

    try:
        from langchain_huggingface import HuggingFaceEmbeddings  # type: ignore
    except ImportError:
        from langchain_community.embeddings import HuggingFaceEmbeddings  # type: ignore

    LANGCHAIN_AVAILABLE = True
except ImportError as e:
    print(f"LangChain导入失败: {e}")
    LANGCHAIN_AVAILABLE = False

    # 创建占位符类
    class Document:
        pass

    class RecursiveCharacterTextSplitter:
        pass

    class TextLoader:
        pass

    class PDFLoader:
        pass

    class UnstructuredWordDocumentLoader:
        pass

    class UnstructuredExcelLoader:
        pass

    class CSVLoader:
        pass

    class OpenAIEmbeddings:
        pass

    class HuggingFaceEmbeddings:
        pass


# PostgreSQL向量存储配置
POSTGRESQL_VECTOR_AVAILABLE = True


# 配置日志
RAG日志器 = logging.getLogger("LangChain.RAG引擎")


class LangChainRAG引擎:
    """LangChain RAG引擎 - PostgreSQL向量存储版本"""

    def __init__(self):
        self.嵌入模型 = None
        self.文档分割器 = None
        self.已初始化 = False
        self.知识库路径 = "知识库"
        self.当前智能体id = None  # 记录当前初始化的智能体id
        self.当前知识id = None  # 记录当前知识id

        # PostgreSQL向量存储配置
        self.向量存储类型 = "postgresql"  # 统一使用PostgreSQL
        self.向量维度 = 1536  # 默认OpenAI嵌入维度，将根据实际模型动态调整

        # 批处理配置
        self.默认批处理大小 = 10
        self.最大批处理大小 = 50

        # 查询优化信息
        self._最近查询优化信息 = None

    async def 初始化(
        self,
        智能体id: Optional[int] = None,
        知识id: Optional[int] = None,
        嵌入模型类型: str = "auto",
    ):
        """初始化RAG引擎 - 使用PostgreSQL向量存储"""
        try:
            # 记录当前智能体id和知识id
            self.当前智能体id = 智能体id
            self.当前知识id = 知识id

            if not LANGCHAIN_AVAILABLE:
                RAG日志器.warning("LangChain不可用，使用简化模式")
                self.已初始化 = True
                return True

            # 初始化嵌入模型 - 优先使用智能体关联知识库的嵌入模型
            await self._初始化智能嵌入模型(嵌入模型类型, 智能体id)

            # 初始化文档分割器
            await self._初始化文档分割器()

            # 验证PostgreSQL向量扩展
            await self._验证PostgreSQL向量扩展()

            # 优先从数据库加载智能体关联的知识库
            if 智能体id:
                await self._加载智能体关联知识库(智能体id)

            self.已初始化 = True
            RAG日志器.info(f"RAG引擎初始化成功 - 向量存储类型: {self.向量存储类型}")
            return True

        except Exception as e:
            RAG日志器.error(f"RAG引擎初始化失败: {str(e)}")
            return False

    async def 根据知识id初始化(self, 知识id: int) -> bool:
        """根据知识id直接初始化RAG引擎"""
        try:
            self.当前知识id = 知识id

            # 获取知识库详情
            知识库详情 = await self._获取知识库详情(知识id)
            if not 知识库详情:
                RAG日志器.error(f"知识库 {知识id} 不存在")
                return False

            # 设置当前知识库配置，供嵌入模型初始化时使用
            self.当前知识库配置 = 知识库详情
            RAG日志器.info(f"设置知识库配置: {知识库详情}")

            # 初始化嵌入模型
            嵌入模型id = 知识库详情.get("嵌入模型")
            if 嵌入模型id:
                await self._初始化指定嵌入模型(嵌入模型id)
            else:
                await self._初始化智能嵌入模型()

            # 初始化文档分割器
            await self._初始化文档分割器()

            # 验证PostgreSQL向量扩展
            await self._验证PostgreSQL向量扩展()

            self.已初始化 = True
            RAG日志器.info(f"根据知识id {知识id} 初始化RAG引擎成功")
            return True

        except Exception as e:
            RAG日志器.error(f"根据知识id {知识id} 初始化RAG引擎失败: {str(e)}")
            return False

    async def _初始化智能嵌入模型(
        self, 嵌入模型类型: str = "auto", 智能体id: Optional[int] = None
    ):
        """智能初始化嵌入模型 - 优先使用智能体关联知识库的嵌入模型"""
        try:
            RAG日志器.info(f"🔧 开始初始化嵌入模型 - 智能体id: {智能体id}")

            # 如果有智能体id，优先获取其关联知识库的嵌入模型（复用缓存）
            if 智能体id:
                try:
                    # 🔧 关键修复：每次都重新获取最新的知识库信息，确保嵌入模型一致性
                    self._知识库信息_缓存 = await self._获取智能体关联知识库信息(
                        智能体id
                    )

                    智能体嵌入模型 = self._知识库信息_缓存.get("嵌入模型")
                    if 智能体嵌入模型:
                        嵌入模型实例 = await self._创建数据库嵌入模型(智能体嵌入模型)
                        if 嵌入模型实例:
                            self.嵌入模型 = 嵌入模型实例
                            RAG日志器.info(
                                f"✅ 使用智能体关联知识库嵌入模型: {智能体嵌入模型['显示名称']} (ID: {智能体嵌入模型['id']})"
                            )
                            return
                        else:
                            RAG日志器.warning(
                                f"⚠️ 嵌入模型实例创建失败: {智能体嵌入模型['显示名称']}"
                            )
                    else:
                        RAG日志器.info(f"ℹ️ 智能体 {智能体id} 关联知识库未指定嵌入模型")
                except Exception as e:
                    RAG日志器.warning(f"⚠️ 获取智能体关联知识库嵌入模型失败: {str(e)}")

            # 备用方案：从数据库获取启用的嵌入模型配置
            RAG日志器.info("📝 使用备用方案：从数据库获取嵌入模型配置...")

            try:
                # 获取启用的嵌入模型配置
                嵌入模型配置 = await self._获取数据库嵌入模型配置()

                if 嵌入模型配置:
                    # 使用数据库配置创建嵌入模型
                    嵌入模型实例 = await self._创建数据库嵌入模型(嵌入模型配置)
                    if 嵌入模型实例:
                        self.嵌入模型 = 嵌入模型实例
                        RAG日志器.info(
                            f"✅ 使用数据库嵌入模型成功: {嵌入模型配置['模型名称']}"
                        )
                        return
                    else:
                        RAG日志器.warning("数据库嵌入模型创建失败")
                else:
                    RAG日志器.info("数据库中没有启用的嵌入模型")

            except Exception as db_error:
                RAG日志器.warning(f"数据库嵌入模型初始化失败: {str(db_error)}")

            # 备用方案：使用模型管理器
            try:
                from 服务.LangChain_模型管理器 import LangChain模型管理器实例

                RAG日志器.info("尝试使用模型管理器中的向量模型...")
                向量模型 = await LangChain模型管理器实例.获取向量模型()

                if 向量模型:
                    self.嵌入模型 = 向量模型
                    RAG日志器.info("✅ 使用模型管理器中的向量模型成功")
                    return
                else:
                    RAG日志器.info("模型管理器中没有可用向量模型")

            except Exception as vm_error:
                RAG日志器.warning(f"模型管理器向量模型初始化失败: {str(vm_error)}")

            # 仅尝试OpenAI嵌入模型（如果有API Key）
            try:
                import os

                if os.getenv("OPENAI_API_KEY"):
                    RAG日志器.info("检测到OpenAI API Key，尝试初始化OpenAI嵌入模型...")
                    self.嵌入模型 = OpenAIEmbeddings()
                    # 测试模型是否可用
                    test_text = "测试文本"
                    _ = self.嵌入模型.embed_query(test_text)
                    RAG日志器.info("✅ OpenAI嵌入模型初始化成功")
                    return
                else:
                    RAG日志器.info("未检测到OPENAI_API_KEY环境变量，跳过OpenAI嵌入模型")
            except Exception as openai_error:
                RAG日志器.warning(f"OpenAI嵌入模型初始化失败: {str(openai_error)}")

            # 所有嵌入模型初始化都失败
            self.嵌入模型 = None
            RAG日志器.error("所有嵌入模型初始化都失败，向量化功能不可用")

        except Exception as e:
            RAG日志器.error(f"嵌入模型初始化失败: {str(e)}")
            self.嵌入模型 = None

    def 获取嵌入模型批处理大小(self) -> int:
        """获取当前嵌入模型的批处理大小限制"""
        if not self.嵌入模型:
            return 1

        # 根据嵌入模型类型返回相应的批处理大小限制
        模型类名 = self.嵌入模型.__class__.__name__

        if "DashScope" in 模型类名:
            # 阿里云DashScope嵌入模型批处理大小限制为10
            return 10
        elif "OpenAI" in 模型类名:
            # OpenAI嵌入模型批处理大小限制较大
            return 100
        elif "HuggingFace" in 模型类名:
            # HuggingFace本地模型通常没有严格限制
            return 50
        else:
            # 默认保守的批处理大小
            return 10

    async def _获取数据库嵌入模型配置(
        self, 知识id: Optional[int] = None
    ) -> Optional[Dict[str, Any]]:
        """从数据库获取嵌入模型配置，优先使用知识库指定的模型"""
        try:
            # 如果指定了知识id，先尝试获取知识库配置的嵌入模型
            if 知识id:
                # 直接从知识库表的嵌入模型字段获取模型id
                知识库查询SQL = """
                SELECT 嵌入模型 FROM langchain_知识库表
                WHERE id = $1 AND 知识库状态 = '活跃'
                """
                知识库结果 = await 异步连接池实例.执行查询(知识库查询SQL, (知识id,))

                if 知识库结果:
                    知识库数据 = 知识库结果[0]
                    嵌入模型id = 知识库数据.get("嵌入模型")

                    # 使用知识库表的嵌入模型字段
                    if 嵌入模型id:
                        # 获取指定的嵌入模型配置
                        模型查询SQL = """
                        SELECT id, 模型名称, 模型类型, 显示名称, 提供商, api密钥, api基础url
                        FROM langchain_模型配置表
                        WHERE id = $1 AND 模型类型 LIKE '%embedding%'
                        """
                        模型结果 = await 异步连接池实例.执行查询(
                            模型查询SQL, (嵌入模型id,)
                        )

                        if 模型结果:
                            配置 = 模型结果[0]
                            RAG日志器.info(
                                f"使用知识库指定的嵌入模型: {配置['显示名称']} (ID: {嵌入模型id})"
                            )
                            return 配置
                        else:
                            RAG日志器.warning(
                                f"知识库指定的嵌入模型不存在或已禁用 (ID: {嵌入模型id})"
                            )
                    else:
                        RAG日志器.info("知识库未指定嵌入模型，将使用默认模型")

            # 如果没有指定模型或指定的模型不可用，使用默认模型
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, api密钥, api基础url
            FROM langchain_模型配置表
            WHERE 模型类型 LIKE '%embedding%'
            ORDER BY id DESC
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL)

            if 结果:
                配置 = 结果[0]
                RAG日志器.info(f"使用默认嵌入模型: {配置['模型名称']}")
                return 配置
            else:
                RAG日志器.warning("数据库中没有启用的嵌入模型")
                return None

        except Exception as e:
            RAG日志器.error(f"获取数据库嵌入模型配置失败: {str(e)}")
            return None

    async def _创建数据库嵌入模型(self, 配置: Dict[str, Any]) -> Optional[Any]:
        """根据数据库配置创建嵌入模型实例"""
        try:
            模型名称 = 配置["模型名称"]
            # 模型类型 = 配置["模型类型"]  # 暂未使用
            提供商 = 配置["提供商"].lower()
            API密钥 = 配置["api密钥"]
            API基础URL = 配置["api基础url"]
            # 获取模型参数，如果数据库中没有该字段则使用默认值
            模型参数 = 配置.get("模型参数", {})

            # 处理模型参数的不同格式
            if isinstance(模型参数, str):
                try:
                    模型参数 = json.loads(模型参数) if 模型参数.strip() else {}
                except json.JSONDecodeError as e:
                    RAG日志器.warning(f"模型参数JSON解析失败: {e}, 使用默认参数")
                    模型参数 = {}
            elif 模型参数 is None:
                模型参数 = {}

            # 为不同提供商设置默认参数
            if not 模型参数:
                if "huggingface" in 提供商 or "local" in 提供商:
                    模型参数 = {
                        "model_kwargs": {"device": "cpu"},
                        "encode_kwargs": {"normalize_embeddings": True},
                    }
                else:
                    模型参数 = {}

            RAG日志器.info(f"创建嵌入模型实例: {模型名称}, 提供商: {提供商}")

            # 根据提供商创建对应的嵌入模型
            if "openai" in 提供商:
                if not API密钥:
                    RAG日志器.error("OpenAI嵌入模型需要API密钥")
                    return None

                openai_params = {"openai_api_key": API密钥, "model": 模型名称}
                if API基础URL:
                    openai_params["openai_api_base"] = API基础URL

                return OpenAIEmbeddings(**openai_params)

            elif "alibaba" in 提供商 or "阿里" in 提供商:
                if not API密钥:
                    RAG日志器.error("阿里云嵌入模型需要API密钥")
                    return None

                try:
                    from langchain_community.embeddings import DashScopeEmbeddings

                    return DashScopeEmbeddings(
                        dashscope_api_key=API密钥, model=模型名称
                    )
                except ImportError:
                    RAG日志器.error("DashScope库未安装")
                    return None

            elif "google" in 提供商:
                if not API密钥:
                    RAG日志器.error("Google嵌入模型需要API密钥")
                    return None

                try:
                    from langchain_google_genai import GoogleGenerativeAIEmbeddings

                    return GoogleGenerativeAIEmbeddings(
                        google_api_key=API密钥, model=模型名称
                    )
                except ImportError:
                    RAG日志器.error("Google AI库未安装")
                    return None

            elif "huggingface" in 提供商 or "local" in 提供商:
                try:
                    return HuggingFaceEmbeddings(model_name=模型名称, **模型参数)
                except Exception as e:
                    RAG日志器.error(f"HuggingFace嵌入模型创建失败: {str(e)}")
                    return None
            else:
                RAG日志器.error(f"不支持的嵌入模型提供商: {提供商}")
                return None

        except Exception as e:
            RAG日志器.error(f"创建数据库嵌入模型失败: {str(e)}")
            return None

    async def _初始化文档分割器(self):
        """初始化文档分割器"""
        try:
            self.文档分割器 = RecursiveCharacterTextSplitter(
                chunk_size=1000,  # type: ignore
                chunk_overlap=200,  # type: ignore
                length_function=len,  # type: ignore
                separators=["\n\n", "\n", " ", ""],  # type: ignore
            )
            RAG日志器.info("✅ 文档分割器初始化成功")
        except Exception:
            # 如果参数不支持，使用简化版本
            self.文档分割器 = RecursiveCharacterTextSplitter()
            RAG日志器.info("✅ 简化文档分割器初始化成功")

    async def _验证PostgreSQL向量扩展(self):
        """验证PostgreSQL向量扩展是否可用"""
        try:
            RAG日志器.info("🔧 验证PostgreSQL向量扩展...")

            # 检查pgvector扩展是否安装
            检查SQL = (
                "SELECT extname, extversion FROM pg_extension WHERE extname = 'vector'"
            )
            async with 异步连接池实例.获取连接() as 连接:
                结果 = await 连接.fetchrow(检查SQL)

            if 结果:
                RAG日志器.info(
                    f"✅ PostgreSQL向量扩展已安装: {结果['extname']} v{结果['extversion']}"
                )
                return True
            else:
                RAG日志器.error("❌ PostgreSQL向量扩展未安装，请先安装pgvector扩展")
                return False

        except Exception as e:
            RAG日志器.error(f"❌ 验证PostgreSQL向量扩展失败: {str(e)}")
            return False

    async def _生成查询向量(self, 查询文本: str) -> List[float]:
        """生成查询向量 - 统一的向量生成方法"""
        try:
            if not self.嵌入模型:
                raise ValueError("嵌入模型未初始化")

            # 根据模型类型选择同步或异步方法
            if hasattr(self.嵌入模型, "embed_query"):
                return self.嵌入模型.embed_query(查询文本)
            else:
                return await self.嵌入模型.aembed_query(查询文本)

        except Exception as e:
            RAG日志器.error(f"生成查询向量失败: {str(e)}")
            raise

    async def _批量生成向量(self, 文本列表: List[str]) -> List[List[float]]:
        """批量生成向量 - 优化的批处理方法"""
        try:
            if not self.嵌入模型:
                raise ValueError("嵌入模型未初始化")

            向量列表 = []
            for 文本 in 文本列表:
                if hasattr(self.嵌入模型, "embed_query"):
                    向量 = self.嵌入模型.embed_query(文本)
                else:
                    向量 = await self.嵌入模型.aembed_query(文本)
                向量列表.append(向量)

            return 向量列表

        except Exception as e:
            RAG日志器.error(f"批量生成向量失败: {str(e)}")
            raise

    # ==================== PostgreSQL向量化辅助方法 ====================

    async def _创建向量化任务(
        self, 文档记录ID: int, 嵌入模型类型: str = "openai"
    ) -> Optional[int]:
        """创建向量化任务记录"""
        try:
            插入SQL = """
            INSERT INTO langchain_向量化任务表
            (知识库文档表id, 任务状态, 开始时间, 嵌入模型类型)
            VALUES ($1, '待处理', CURRENT_TIMESTAMP, $2)
            RETURNING id
            """
            async with 异步连接池实例.获取连接() as 连接:
                结果 = await 连接.fetchrow(插入SQL, int(文档记录ID), str(嵌入模型类型))

            if 结果:
                任务ID = 结果["id"]
                RAG日志器.debug(
                    f"创建向量化任务成功: 任务ID={任务ID}, 文档id={文档记录ID}"
                )
                return 任务ID
            else:
                RAG日志器.error("创建向量化任务失败: 未返回任务ID")
                return None

        except Exception as e:
            RAG日志器.error(f"创建向量化任务失败: {str(e)}")
            return None

    async def _更新向量化任务状态(
        self,
        任务ID: int,
        状态: str,
        错误信息: Optional[str] = None,
        总分块数: Optional[int] = None,
    ) -> bool:
        """更新向量化任务状态"""
        try:
            if 状态 == "已完成":
                更新SQL = """
                UPDATE langchain_向量化任务表
                SET 任务状态 = $1, 完成时间 = CURRENT_TIMESTAMP, 更新时间 = CURRENT_TIMESTAMP
                WHERE id = $2
                """
                参数 = (str(状态), int(任务ID))
            elif 状态 == "处理中" and 总分块数:
                更新SQL = """
                UPDATE langchain_向量化任务表
                SET 任务状态 = $1, 总分块数 = $2, 更新时间 = CURRENT_TIMESTAMP
                WHERE id = $3
                """
                参数 = (str(状态), int(总分块数), int(任务ID))
            else:
                更新SQL = """
                UPDATE langchain_向量化任务表
                SET 任务状态 = $1, 错误信息 = $2, 更新时间 = CURRENT_TIMESTAMP
                WHERE id = $3
                """
                参数 = (str(状态), str(错误信息) if 错误信息 else None, int(任务ID))

            async with 异步连接池实例.获取连接() as 连接:
                await 连接.execute(更新SQL, *参数)

            RAG日志器.debug(f"更新向量化任务状态成功: 任务ID={任务ID}, 状态={状态}")
            return True

        except Exception as e:
            RAG日志器.error(f"更新向量化任务状态失败: {str(e)}")
            return False

    async def _更新向量化任务进度(
        self, 任务ID: int, 成功数量: int, 失败数量: int
    ) -> bool:
        """更新向量化任务进度"""
        try:
            更新SQL = """
            UPDATE langchain_向量化任务表
            SET 已完成分块数 = $1, 失败分块数 = $2, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $3
            """
            async with 异步连接池实例.获取连接() as 连接:
                await 连接.execute(更新SQL, int(成功数量), int(失败数量), int(任务ID))

            RAG日志器.debug(
                f"更新向量化任务进度成功: 任务ID={任务ID}, 成功={成功数量}, 失败={失败数量}"
            )
            return True

        except Exception as e:
            RAG日志器.error(f"更新向量化任务进度失败: {str(e)}")
            return False

    async def _处理单个分块向量化(
        self,
        文档记录ID: int,
        分块序号: int,
        分块内容: str,
    ) -> Dict[str, Any]:
        """处理单个分块的向量化"""
        try:
            # 生成向量
            向量 = await self._批量生成向量([分块内容])
            if not 向量 or len(向量) == 0:
                return {"success": False, "error": "向量生成失败"}

            向量数据 = 向量[0]
            向量字符串 = "[" + ",".join(map(str, 向量数据)) + "]"

            # 构建元数据
            元数据 = {
                "chunk_index": 分块序号,
                "chunk_size": len(分块内容),
                "document_record_id": 文档记录ID,
            }

            # 更新向量记录
            更新SQL = """
            UPDATE langchain_文档向量表
            SET 分块内容 = $1, 向量数据 = $2::vector, 向量维度 = $3, 元数据 = $4
            WHERE langchain_知识库文档表id = $5 AND 分块序号 = $6
            """

            async with 异步连接池实例.获取连接() as 连接:
                await 连接.execute(
                    更新SQL,
                    str(分块内容),
                    str(向量字符串),
                    int(len(向量数据)),
                    str(json.dumps(元数据, ensure_ascii=False)),
                    int(文档记录ID),
                    int(分块序号),
                )

            return {"success": True}

        except Exception as e:
            RAG日志器.error(f"❌ 单个分块向量化失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _获取文档详情(self, 文档记录ID: int) -> Optional[Dict[str, Any]]:
        """获取文档详情"""
        try:
            查询SQL = """
            SELECT id, 文档名称, 文档内容, 元数据, 文档类型, 文档大小
            FROM langchain_知识库文档表
            WHERE id = $1
            """
            async with 异步连接池实例.获取连接() as 连接:
                结果 = await 连接.fetchrow(查询SQL, int(文档记录ID))

            if 结果:
                文档详情 = dict(结果)
                # 解析元数据
                if 文档详情.get("元数据"):
                    try:
                        文档详情["元数据"] = json.loads(文档详情["元数据"])
                    except (json.JSONDecodeError, TypeError):
                        文档详情["元数据"] = {}

                RAG日志器.debug(f"获取文档详情成功: 文档id={文档记录ID}")
                return 文档详情
            else:
                RAG日志器.warning(f"文档不存在: 文档id={文档记录ID}")
                return None

        except Exception as e:
            RAG日志器.error(f"获取文档详情失败: {str(e)}")
            return None

    async def _获取知识库详情(self, 知识id: int) -> Optional[Dict[str, Any]]:
        """获取知识库详情"""
        try:
            查询SQL = """
            SELECT id, 知识库名称, 知识库描述, 嵌入模型, 存储类型, 向量维度
            FROM langchain_知识库表
            WHERE id = $1 AND 存储类型 = 'postgresql'
            """
            async with 异步连接池实例.获取连接() as 连接:
                结果 = await 连接.fetchrow(查询SQL, int(知识id))

            if 结果:
                return dict(结果)
            else:
                return None

        except Exception as e:
            RAG日志器.error(f"获取知识库详情失败: {str(e)}")
            return None

    async def _初始化指定嵌入模型(self, 嵌入模型id: int):
        """根据模型id初始化指定的嵌入模型"""
        try:
            # 获取嵌入模型配置
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, api密钥, api基础url
            FROM langchain_模型配置表
            WHERE id = $1
            """
            async with 异步连接池实例.获取连接() as 连接:
                模型配置 = await 连接.fetchrow(查询SQL, int(嵌入模型id))

            if 模型配置:
                嵌入模型实例 = await self._创建数据库嵌入模型(dict(模型配置))
                if 嵌入模型实例:
                    self.嵌入模型 = 嵌入模型实例
                    # 从知识库配置中获取向量维度
                    if hasattr(self, "当前知识库配置") and self.当前知识库配置.get(
                        "向量维度"
                    ):
                        self.向量维度 = self.当前知识库配置["向量维度"]
                        RAG日志器.info(f"使用知识库配置的向量维度: {self.向量维度}")
                    else:
                        # 使用系统默认维度
                        self.向量维度 = 1024
                        RAG日志器.warning(
                            f"未找到知识库向量维度配置，使用默认维度: {self.向量维度}"
                        )
                    RAG日志器.info(
                        f"✅ 指定嵌入模型初始化成功: {模型配置['显示名称']}, 向量维度: {self.向量维度}"
                    )
                else:
                    RAG日志器.error(f"指定嵌入模型创建失败: {模型配置['显示名称']}")
            else:
                RAG日志器.error(f"未找到ID为 {嵌入模型id} 的嵌入模型配置")

        except Exception as e:
            RAG日志器.error(f"初始化指定嵌入模型失败: {str(e)}")

    async def _加载知识库(self, 知识库列表: List[str]):
        """加载知识库文档"""
        try:
            所有文档 = []

            for 知识库名称 in 知识库列表:
                知识库路径 = Path(self.知识库路径) / 知识库名称
                if 知识库路径.exists():
                    文档列表 = await self._加载目录文档(知识库路径)
                    所有文档.extend(文档列表)

            if 所有文档:
                # 分割文档
                try:
                    文档片段 = self.文档分割器.split_documents(所有文档)  # type: ignore
                except Exception:
                    # 如果方法不存在，直接使用原文档
                    文档片段 = 所有文档

                # 添加到向量存储
                await self._添加文档到向量存储(文档片段)

                RAG日志器.info(
                    f"成功加载 {len(所有文档)} 个文档，分割为 {len(文档片段)} 个片段"
                )

        except Exception as e:
            RAG日志器.error(f"加载知识库失败: {str(e)}")

    async def _加载目录文档(self, 目录路径: Path) -> List[Document]:
        """加载目录中的所有文档"""
        文档列表 = []

        try:
            for 文件路径 in 目录路径.rglob("*"):
                if 文件路径.is_file():
                    文档 = await self._加载单个文档(文件路径)
                    if 文档:
                        文档列表.extend(文档)
        except Exception as e:
            RAG日志器.error(f"加载目录文档失败: {str(e)}")

        return 文档列表

    async def _加载单个文档(self, 文件路径: Path) -> Optional[List[Document]]:
        """加载单个文档"""
        try:
            文件扩展名 = 文件路径.suffix.lower()

            if 文件扩展名 == ".txt":
                try:
                    加载器 = TextLoader(str(文件路径), encoding="utf-8")  # type: ignore
                except Exception:
                    加载器 = TextLoader()  # type: ignore
            elif 文件扩展名 == ".pdf":
                try:
                    加载器 = PDFLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = PDFLoader()  # type: ignore
            elif 文件扩展名 in [".doc", ".docx"]:
                try:
                    加载器 = UnstructuredWordDocumentLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = UnstructuredWordDocumentLoader()  # type: ignore
            elif 文件扩展名 in [".xls", ".xlsx"]:
                try:
                    加载器 = UnstructuredExcelLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = UnstructuredExcelLoader()  # type: ignore
            elif 文件扩展名 == ".csv":
                try:
                    加载器 = CSVLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = CSVLoader()  # type: ignore
            else:
                RAG日志器.warning(f"不支持的文件类型: {文件扩展名}")
                return None

            try:
                文档列表 = 加载器.load()  # type: ignore
            except Exception:
                # 如果load方法不存在，返回空列表
                文档列表 = []

            # 添加元数据
            for 文档 in 文档列表:
                文档.metadata.update(
                    {
                        "source": str(文件路径),
                        "filename": 文件路径.name,
                        "file_type": 文件扩展名,
                    }
                )

            return 文档列表

        except Exception as e:
            RAG日志器.error(f"加载文档失败 {文件路径}: {str(e)}")
            return None

    async def _添加文档到向量存储(self, 文档列表: List[Document]):
        """添加文档到向量存储 - 增强版本"""
        try:
            RAG日志器.info(f"🔧 开始添加 {len(文档列表)} 个文档到向量存储")

            if not self.向量存储:
                RAG日志器.error("❌ 向量存储未初始化")
                return

            if not 文档列表:
                RAG日志器.warning("⚠️ 文档列表为空，无需添加")
                return

            # 🔧 检查向量存储类型和方法
            向量存储类型 = type(self.向量存储).__name__
            RAG日志器.info(f"📊 向量存储类型: {向量存储类型}")

            # 🔧 检查可用方法
            可用方法 = [
                method for method in dir(self.向量存储) if not method.startswith("_")
            ]
            RAG日志器.debug(f"🔍 向量存储可用方法: {可用方法}")

            try:
                if hasattr(self.向量存储, "add_documents"):
                    RAG日志器.info("✅ 使用 add_documents 方法添加文档")
                    结果 = self.向量存储.add_documents(文档列表)  # type: ignore
                    RAG日志器.info(f"✅ add_documents 执行完成，返回结果: {type(结果)}")
                elif hasattr(self.向量存储, "add_texts"):
                    RAG日志器.info("✅ 使用 add_texts 方法添加文档")
                    texts = [doc.page_content for doc in 文档列表]
                    metadatas = [doc.metadata for doc in 文档列表]
                    结果 = self.向量存储.add_texts(texts=texts, metadatas=metadatas)  # type: ignore
                    RAG日志器.info(f"✅ add_texts 执行完成，返回结果: {type(结果)}")
                else:
                    RAG日志器.error("❌ 向量存储不支持 add_documents 或 add_texts 方法")
                    return

                RAG日志器.info(f"✅ 成功添加 {len(文档列表)} 个文档片段到向量存储")

            except Exception as add_error:
                RAG日志器.error(f"❌ 添加文档到向量存储时出错: {str(add_error)}")
                import traceback

                RAG日志器.error(f"❌ 添加文档错误堆栈: {traceback.format_exc()}")

        except Exception as e:
            RAG日志器.error(f"❌ 添加文档到向量存储失败: {str(e)}")
            import traceback

            RAG日志器.error(f"❌ 完整错误堆栈: {traceback.format_exc()}")

    async def 检索(
        self,
        查询: str,
        知识id: Optional[int] = None,
        k: int = 5,
        查询优化配置: Optional[Dict[str, Any]] = None,
        相似度阈值: float = 0.7,
    ) -> List[Document]:
        """检索相关文档 - PostgreSQL版本"""
        try:
            if not self.已初始化:
                RAG日志器.warning("RAG引擎未初始化")
                return []

            # 确定知识id
            使用的知识id = 知识id or self.当前知识id
            if not 使用的知识id:
                RAG日志器.error("未指定知识id且当前未设置知识库")
                return []

            # 检查是否启用查询优化
            if 查询优化配置 and 查询优化配置.get("启用", False):
                # 使用公共的三次优化检索方法
                最终检索结果, 优化信息 = await self._执行三次优化检索(
                    查询, 使用的知识id, k, 相似度阈值, 查询优化配置
                )

                # 保存查询优化信息
                self._最近查询优化信息 = 优化信息

                return 最终检索结果

            else:
                # 单阶段检索（传统模式）
                RAG日志器.info("📖 单阶段RAG检索")
                # 清空查询优化信息
                self._最近查询优化信息 = None
                检索结果 = await self.向量检索(查询, 使用的知识id, k, 相似度阈值)
                return 检索结果

        except Exception as e:
            RAG日志器.error(f"检索失败: {str(e)}")
            return []

    async def 检索带分数(
        self,
        查询: str,
        知识id: Optional[int] = None,
        k: int = 5,
        查询优化配置: Optional[Dict[str, Any]] = None,
        相似度阈值: float = 0.7,
    ) -> List[tuple]:
        """检索相关文档并返回相似度分数 - PostgreSQL版本"""
        try:
            RAG日志器.info(
                f"🔍 PostgreSQL检索查询: '{查询[:50]}{'...' if len(查询) > 50 else ''}', k={k}"
            )
            RAG日志器.debug(
                f"智能体id: {getattr(self, '当前智能体id', 'None')}, 知识id: {知识id or self.当前知识id}"
            )

            # 确定知识id
            使用的知识id = 知识id or self.当前知识id
            if not 使用的知识id:
                RAG日志器.error("未指定知识id且当前未设置知识库")
                return []

            # 检查是否启用查询优化
            if 查询优化配置 and 查询优化配置.get("启用", False):
                # 使用公共的三次优化检索方法
                最终检索结果, 优化信息 = await self._执行三次优化检索(
                    查询, 使用的知识id, k, 相似度阈值, 查询优化配置
                )

                # 保存查询优化信息
                self._最近查询优化信息 = 优化信息

                # 转换为带分数的格式
                带分数结果 = []
                for 文档 in 最终检索结果:
                    分数 = 文档.metadata.get("相似度分数", 0.0)
                    带分数结果.append((文档, 分数))

                return 带分数结果
            else:
                # 单阶段检索（传统模式）
                RAG日志器.info("📖 单阶段RAG检索（带分数）")
                # 清空查询优化信息
                self._最近查询优化信息 = None

            if not self.已初始化:
                RAG日志器.error("❌ RAG引擎未初始化")
                return []

            if not self.嵌入模型:
                RAG日志器.error("❌ 嵌入模型未初始化")
                return []

            if not LANGCHAIN_AVAILABLE:
                RAG日志器.error("❌ LangChain不可用，无法执行检索")
                return []

            # 检查PostgreSQL中的向量数据
            try:
                数据库向量查询SQL = """
                SELECT COUNT(*) as 向量数量
                FROM langchain_文档向量表 v
                JOIN langchain_知识库文档表 d ON v.langchain_知识库文档表id = d.id
                WHERE d.langchain_知识库表id = $1 AND d.向量状态 = '已完成'
                """

                async with 异步连接池实例.获取连接() as 连接:
                    数据库结果 = await 连接.fetchrow(
                        数据库向量查询SQL, int(使用的知识id)
                    )

                数据库向量数量 = 数据库结果["向量数量"] if 数据库结果 else 0
                RAG日志器.info(f"📊 知识库 {使用的知识id} 包含 {数据库向量数量} 个向量")

                if 数据库向量数量 == 0:
                    RAG日志器.warning("⚠️ 知识库中没有向量数据，请先进行向量化")
                    return []

            except Exception as e:
                RAG日志器.error(f"❌ 检查向量数据失败: {str(e)}")
                return []

            # 执行PostgreSQL向量相似性搜索
            try:
                # 生成查询向量
                查询向量 = await self._生成查询向量(查询)

                # 使用PostgreSQL进行向量检索
                检索结果列表 = await self._PostgreSQL向量相似度检索(
                    使用的知识id,
                    查询向量,
                    k,
                    相似度阈值,  # 使用传入的相似度阈值
                )

                # 转换为(Document, score)元组格式
                检索结果 = []
                for 结果 in 检索结果列表:
                    文档 = Document(
                        page_content=结果["分块内容"],
                        metadata={
                            "文档名称": 结果["文档名称"],
                            "文档uuid": 结果["文档uuid"],
                            "分块序号": 结果["分块序号"],
                            **结果["元数据"],
                        },
                    )
                    # 注意：PostgreSQL返回的是相似度分数，LangChain期望的是距离分数
                    距离分数 = 1 - 结果["相似度分数"]
                    检索结果.append((文档, 距离分数))

                RAG日志器.info(f"✅ PostgreSQL检索完成，返回 {len(检索结果)} 个结果")

                # 简化的结果日志
                if 检索结果:
                    分数列表 = [分数 for _, 分数 in 检索结果]
                    RAG日志器.debug(
                        f"📊 距离分数范围: {min(分数列表):.3f} - {max(分数列表):.3f}"
                    )
                else:
                    RAG日志器.warning("⚠️ 检索返回空结果")

            except Exception as e:
                RAG日志器.error(f"❌ PostgreSQL检索执行失败: {str(e)}")
                检索结果 = []

            return 检索结果

        except Exception as e:
            RAG日志器.error(f"❌ 检索带分数失败: {str(e)}")
            return []

    async def 添加文档(
        self, 文档内容: str, 知识id: int, 元数据: Optional[Dict[str, Any]] = None
    ) -> bool:
        """添加单个文档到PostgreSQL"""
        try:
            if not self.已初始化:
                RAG日志器.error("RAG引擎未初始化")
                return False

            if not self.嵌入模型:
                RAG日志器.error("嵌入模型未初始化")
                return False

            # 分割文档
            if self.文档分割器:
                文档分块列表 = self.文档分割器.split_text(文档内容)
            else:
                # 简单分割
                文档分块列表 = [
                    文档内容[i : i + 1000] for i in range(0, len(文档内容), 1000)
                ]

            if not 文档分块列表:
                RAG日志器.error("文档分块失败")
                return False

            # 先创建文档记录
            文档记录SQL = """
            INSERT INTO langchain_知识库文档表
            (langchain_知识库表id, 文档名称, 文档内容, 元数据, 文档类型, 向量状态)
            VALUES ($1, $2, $3, $4, 'text', '处理中')
            RETURNING id
            """

            async with 异步连接池实例.获取连接() as 连接:
                文档记录 = await 连接.fetchrow(
                    文档记录SQL,
                    知识id,
                    元数据.get("filename", "直接添加文档")
                    if 元数据
                    else "直接添加文档",
                    文档内容,
                    json.dumps(元数据 or {}, ensure_ascii=False),
                )

            if not 文档记录:
                RAG日志器.error("创建文档记录失败")
                return False

            文档记录ID = 文档记录["id"]

            # 向量化文档
            向量化结果 = await self._向量化单个文档到PostgreSQL(文档记录ID, 知识id)

            if 向量化结果.get("success"):
                RAG日志器.info(f"成功添加文档，分割为 {len(文档分块列表)} 个片段")
                return True
            else:
                RAG日志器.error(f"文档向量化失败: {向量化结果.get('error')}")
                return False

        except Exception as e:
            RAG日志器.error(f"添加文档失败: {str(e)}")
            return False

    async def 删除文档(self, 文档记录ID: int) -> bool:
        """删除文档 - PostgreSQL版本"""
        try:
            # 删除PostgreSQL中的向量数据
            删除向量SQL = """
            DELETE FROM langchain_文档向量表
            WHERE langchain_知识库文档表id = $1
            """

            # 删除文档记录
            删除文档SQL = """
            DELETE FROM langchain_知识库文档表
            WHERE id = $1
            """

            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.transaction():
                    # 先删除向量化任务记录
                    删除任务SQL = """
                    DELETE FROM langchain_向量化任务表
                    WHERE 知识库文档表id = $1
                    """
                    任务删除结果 = await 连接.execute(删除任务SQL, 文档记录ID)

                    # 再删除向量数据
                    向量删除结果 = await 连接.execute(删除向量SQL, 文档记录ID)
                    # 最后删除文档记录
                    文档删除结果 = await 连接.execute(删除文档SQL, 文档记录ID)

            RAG日志器.info(f"✅ 成功从PostgreSQL删除文档: {文档记录ID}")
            return True

        except Exception as e:
            RAG日志器.error(f"删除文档失败: {str(e)}")
            return False

    # ==================== PostgreSQL向量化方法 ====================

    async def 向量化知识库(self, 知识id: int) -> Dict[str, Any]:
        """批量向量化知识库中的所有文档 - PostgreSQL向量存储架构"""
        try:
            if not self.已初始化:
                return {"success": False, "error": "RAG引擎未初始化"}

            if not self.嵌入模型:
                return {"success": False, "error": "嵌入模型未初始化"}

            RAG日志器.info(
                f"🚀 开始批量向量化知识库 {知识id} 中的所有文档 - PostgreSQL向量存储模式"
            )

            # 获取知识库文档
            文档列表 = await self._获取知识库文档(知识id)
            if not 文档列表:
                return {"success": False, "error": "知识库中没有文档"}

            总分块数 = 0
            成功文档数 = 0
            失败文档数 = 0

            for 文档 in 文档列表:
                try:
                    # 获取文档记录ID
                    文档记录ID = 文档.get("id")
                    if not 文档记录ID:
                        RAG日志器.warning(
                            f"文档缺少记录ID，跳过: {文档.get('文档名称', 'unknown')}"
                        )
                        continue

                    # 向量化单个文档
                    向量化结果 = await self._向量化单个文档到PostgreSQL(
                        文档记录ID, 知识id
                    )

                    if 向量化结果.get("success"):
                        总分块数 += 向量化结果.get("分块数量", 0)
                        成功文档数 += 1
                        RAG日志器.debug(
                            f"✅ 文档 {文档记录ID} 向量化成功，分块数: {向量化结果.get('分块数量', 0)}"
                        )
                    else:
                        失败文档数 += 1
                        RAG日志器.error(
                            f"❌ 文档 {文档记录ID} 向量化失败: {向量化结果.get('error')}"
                        )

                except Exception as e:
                    失败文档数 += 1
                    RAG日志器.error(f"❌ 处理文档时出错: {str(e)}")

            RAG日志器.info(
                f"✅ 知识库 {知识id} 向量化完成: 成功 {成功文档数} 个文档，失败 {失败文档数} 个，总分块 {总分块数}"
            )

            return {
                "success": True,
                "知识id": 知识id,
                "总文档数": len(文档列表),
                "成功文档数": 成功文档数,
                "失败文档数": 失败文档数,
                "总分块数": 总分块数,
                "存储模式": "PostgreSQL向量存储",
            }

        except Exception as e:
            RAG日志器.error(f"❌ 知识库向量化失败: {str(e)}")
            return {"success": False, "error": f"知识库向量化失败: {str(e)}"}

    async def _向量化单个文档到PostgreSQL(
        self, 文档记录ID: int, 知识id: int
    ) -> Dict[str, Any]:
        """向量化单个文档并存储到PostgreSQL - 增强事务一致性和状态同步"""
        任务ID = None
        try:
            # 1. 创建向量化任务记录
            任务ID = await self._创建向量化任务(文档记录ID)
            if not 任务ID:
                return {"success": False, "error": "创建向量化任务失败"}

            # 2. 获取文档内容
            文档详情 = await self._获取文档详情(文档记录ID)
            if not 文档详情:
                await self._同步更新向量化状态(
                    任务ID, 文档记录ID, "失败", "失败", 0, 0, {"失败批次": 1}
                )
                return {"success": False, "error": "文档不存在"}

            文档内容 = 文档详情.get("文档内容", "")
            if not 文档内容.strip():
                await self._同步更新向量化状态(
                    任务ID, 文档记录ID, "失败", "失败", 0, 0, {"失败批次": 1}
                )
                return {"success": False, "error": "文档内容为空"}

            # 3. 分割文档
            if not self.文档分割器:
                await self._同步更新向量化状态(
                    任务ID, 文档记录ID, "失败", "失败", 0, 0, {"失败批次": 1}
                )
                return {"success": False, "error": "文档分割器未初始化"}

            try:
                if hasattr(self.文档分割器, "split_text"):
                    文档分块列表 = self.文档分割器.split_text(文档内容)
                else:
                    # 简单分割作为后备方案
                    文档分块列表 = [
                        文档内容[i : i + 1000] for i in range(0, len(文档内容), 1000)
                    ]
                    RAG日志器.warning("使用简单分割作为后备方案")
            except Exception as e:
                await self._更新向量化任务状态(
                    任务ID, "失败", f"文档分割失败: {str(e)}"
                )
                return {"success": False, "error": f"文档分割失败: {str(e)}"}

            if not 文档分块列表:
                await self._更新向量化任务状态(任务ID, "失败", "文档分割后无有效内容")
                return {"success": False, "error": "文档分割后无有效内容"}

            # 4. 更新任务状态为处理中
            await self._更新向量化任务状态(任务ID, "处理中", None, len(文档分块列表))

            # 5. 批量向量化并存储 - 增强事务处理
            成功数量 = 0
            失败数量 = 0
            批次统计 = {"成功批次": 0, "失败批次": 0, "部分成功批次": 0}

            # 使用自适应批处理大小
            批处理大小 = min(self.默认批处理大小, len(文档分块列表))

            for batch_start in range(0, len(文档分块列表), 批处理大小):
                batch_end = min(batch_start + 批处理大小, len(文档分块列表))
                batch_chunks = 文档分块列表[batch_start:batch_end]

                try:
                    # 使用事务处理单个批次
                    batch_result = await self._处理向量化批次(
                        文档记录ID, batch_chunks, batch_start, 任务ID
                    )

                    if batch_result["success"]:
                        batch_success = batch_result["成功数量"]
                        batch_failed = batch_result["失败数量"]

                        成功数量 += batch_success
                        失败数量 += batch_failed

                        # 统计批次结果
                        if batch_failed == 0:
                            批次统计["成功批次"] += 1
                        elif batch_success == 0:
                            批次统计["失败批次"] += 1
                        else:
                            批次统计["部分成功批次"] += 1
                    else:
                        # 整个批次失败
                        失败数量 += len(batch_chunks)
                        批次统计["失败批次"] += 1
                        RAG日志器.error(
                            f"批次向量化完全失败: {batch_result.get('error', '未知错误')}"
                        )

                    # 更新进度
                    await self._更新向量化任务进度(任务ID, 成功数量, 失败数量)

                except Exception as e:
                    RAG日志器.error(f"批次向量化异常: {str(e)}")
                    失败数量 += len(batch_chunks)
                    批次统计["失败批次"] += 1
                    await self._更新向量化任务进度(任务ID, 成功数量, 失败数量)

            # 6. 完成任务 - 增强状态判断逻辑
            总分块数 = len(文档分块列表)
            成功率 = 成功数量 / 总分块数 if 总分块数 > 0 else 0

            if 失败数量 == 0:
                # 完全成功
                最终状态 = "已完成"
                文档状态 = "已完成"
                RAG日志器.info(
                    f"✅ 文档 {文档记录ID} 向量化完全成功: {成功数量}/{总分块数}"
                )
            elif 成功数量 == 0:
                # 完全失败
                最终状态 = "失败"
                文档状态 = "失败"
                RAG日志器.error(f"❌ 文档 {文档记录ID} 向量化完全失败: 0/{总分块数}")
            elif 成功率 >= 0.8:
                # 大部分成功（80%以上）
                最终状态 = "基本完成"
                文档状态 = "基本完成"
                RAG日志器.warning(
                    f"⚠️ 文档 {文档记录ID} 向量化基本完成: {成功数量}/{总分块数} ({成功率:.1%})"
                )
            else:
                # 部分成功（80%以下）
                最终状态 = "部分失败"
                文档状态 = "部分失败"
                RAG日志器.warning(
                    f"⚠️ 文档 {文档记录ID} 向量化部分失败: {成功数量}/{总分块数} ({成功率:.1%})"
                )

            # 同步更新任务和文档状态
            await self._同步更新向量化状态(
                任务ID, 文档记录ID, 最终状态, 文档状态, 成功数量, 失败数量, 批次统计
            )

            return {
                "success": True,
                "分块数量": len(文档分块列表),
                "成功数量": 成功数量,
                "失败数量": 失败数量,
                "任务ID": 任务ID,
            }

        except Exception as e:
            RAG日志器.error(f"文档向量化失败: {str(e)}")
            if 任务ID:
                await self._同步更新向量化状态(
                    任务ID, 文档记录ID, "失败", "失败", 0, 0, {"失败批次": 1}
                )
            return {"success": False, "error": f"文档向量化失败: {str(e)}"}

    async def _处理向量化批次(
        self, 文档记录ID: int, batch_chunks: List[str], batch_start: int, 任务ID: int
    ) -> Dict[str, Any]:
        """处理单个向量化批次 - 使用事务确保批次内一致性"""
        try:
            # 批量生成向量
            batch_vectors = await self._批量生成向量(batch_chunks)

            # 使用事务批量保存向量
            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.transaction():
                    成功数量 = 0
                    失败数量 = 0

                    for i, (分块内容, 向量) in enumerate(
                        zip(batch_chunks, batch_vectors)
                    ):
                        try:
                            # 将向量转换为PostgreSQL vector格式
                            向量字符串 = "[" + ",".join(map(str, 向量)) + "]"

                            # 准备元数据
                            元数据 = {
                                "chunk_index": batch_start + i,
                                "chunk_size": len(分块内容),
                                "document_record_id": 文档记录ID,
                                "batch_id": f"{任务ID}_{batch_start}",
                            }

                            # 更新现有的向量记录（替换占位符）
                            更新SQL = """
                            UPDATE langchain_文档向量表
                            SET 分块内容 = $1, 向量数据 = $2::vector, 向量维度 = $3,
                                元数据 = $4
                            WHERE langchain_知识库文档表id = $5 AND 分块序号 = $6
                            """

                            await 连接.execute(
                                更新SQL,
                                str(分块内容),  # 确保是字符串类型
                                str(向量字符串),  # 确保是字符串类型
                                int(len(向量)),  # 确保是整数类型
                                str(
                                    json.dumps(元数据, ensure_ascii=False)
                                ),  # 确保是字符串类型
                                int(文档记录ID),  # 确保是整数类型
                                int(batch_start + i),  # 确保是整数类型
                            )

                            成功数量 += 1

                        except Exception as item_error:
                            RAG日志器.error(f"单个向量保存失败: {str(item_error)}")
                            失败数量 += 1
                            # 继续处理其他项，不中断事务

                    # 如果批次中有成功的项，提交事务
                    if 成功数量 > 0:
                        RAG日志器.debug(
                            f"批次事务提交: 成功 {成功数量}, 失败 {失败数量}"
                        )

            return {
                "success": True,
                "成功数量": 成功数量,
                "失败数量": 失败数量,
            }

        except Exception as e:
            RAG日志器.error(f"批次向量化处理失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "成功数量": 0,
                "失败数量": len(batch_chunks),
            }

    async def _同步分块元数据到PostgreSQL(self, 文档记录ID: int, 文档对象列表):
        """同步分块元数据到PostgreSQL - 精简版本"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 更新文档的向量化状态和分块数量
            更新SQL = """
            UPDATE langchain_知识库文档表
            SET 向量状态 = '已完成',
                向量分块数量 = $1,
                最后向量化时间 = CURRENT_TIMESTAMP,
                更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """
            async with 异步连接池实例.获取连接() as 连接:
                await 连接.execute(更新SQL, len(文档对象列表), 文档记录ID)

            RAG日志器.debug(
                f"✅ PostgreSQL元数据同步完成: 文档 {文档记录ID}, 分块数 {len(文档对象列表)}"
            )

        except Exception as e:
            RAG日志器.error(f"❌ 同步分块元数据到PostgreSQL失败: {str(e)}")
            raise  # 重新抛出异常以便上层处理

    async def _获取知识库文档(self, 知识id: int) -> List:
        """从数据库获取知识库文档"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 查询知识库文档 - 使用中文状态字段
            查询SQL = """
            SELECT id, 文档名称, 文档内容, 元数据, 文档类型
            FROM langchain_知识库文档表
            WHERE langchain_知识库表id = $1 AND 文档状态 = '已处理'
            ORDER BY 创建时间 DESC
            """

            async with 异步连接池实例.获取连接() as 连接:
                文档记录列表 = await 连接.fetch(查询SQL, 知识id)

            if not 文档记录列表:
                RAG日志器.warning(f"知识库 {知识id} 中没有找到文档")
                return []

            # 转换为Document对象
            from langchain_core.documents import Document

            文档列表 = []

            for 记录 in 文档记录列表:
                文档id, 文档名称, 文档内容, 元数据字符串, 文档类型 = 记录

                # 解析元数据
                try:
                    元数据 = json.loads(元数据字符串) if 元数据字符串 else {}
                except (json.JSONDecodeError, TypeError):
                    元数据 = {}

                # 添加必要的元数据
                元数据.update(
                    {
                        "document_record_id": 文档id,
                        "filename": 文档名称,
                        "file_type": 文档类型,
                        "knowledge_base_id": 知识id,
                    }
                )

                文档对象 = Document(page_content=文档内容 or "", metadata=元数据)
                文档列表.append(文档对象)

            RAG日志器.info(f"✅ 从知识库 {知识id} 获取到 {len(文档列表)} 个文档")
            return 文档列表

        except Exception as e:
            RAG日志器.error(f"❌ 获取知识库文档失败: {str(e)}")
            return []

    async def 清理(self):
        """清理资源"""
        try:
            self.嵌入模型 = None
            self.文档分割器 = None
            self.已初始化 = False
            self.当前智能体id = None
            self.当前知识id = None
            RAG日志器.info("RAG引擎资源清理完成")
        except Exception as e:
            RAG日志器.error(f"RAG引擎清理失败: {str(e)}")

    def 获取状态(self) -> Dict[str, Any]:
        """获取RAG引擎状态"""
        状态信息 = {
            "已初始化": self.已初始化,
            "LangChain可用": LANGCHAIN_AVAILABLE,
            "PostgreSQL向量存储": True,
            "嵌入模型": str(type(self.嵌入模型).__name__) if self.嵌入模型 else None,
            "向量存储类型": self.向量存储类型,
            "知识库路径": self.知识库路径,
            "当前知识id": self.当前知识id,
        }

        # 添加详细信息
        if self.向量存储:
            状态信息["向量存储类型"] = type(self.向量存储).__name__
            # 尝试获取文档数量
            try:
                if hasattr(self.向量存储, "_collection") and hasattr(
                    self.向量存储._collection, "count"
                ):
                    状态信息["向量总数"] = self.向量存储._collection.count()
                elif hasattr(self.向量存储, "index") and hasattr(
                    self.向量存储.index, "ntotal"
                ):
                    状态信息["向量总数"] = self.向量存储.index.ntotal
                else:
                    状态信息["向量总数"] = 0
            except Exception:
                状态信息["向量总数"] = 0

        # 检测OpenAI是否可用
        try:
            import os

            状态信息["OpenAI_可用"] = bool(os.getenv("OPENAI_API_KEY"))
        except Exception:
            状态信息["OpenAI_可用"] = False

        return 状态信息

    async def _获取智能体关联知识库信息(self, 智能体id: int) -> Dict[str, Any]:
        """获取智能体关联知识库的配置信息（嵌入模型）- 使用关联表"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 分步查询以避免复杂JOIN导致的参数问题
            # 第一步：获取智能体基本信息和关联的知识id
            智能体查询SQL = """
            SELECT
                ac.启用rag,
                akr.langchain_知识库表id as 知识id
            FROM langchain_智能体配置表 ac
            LEFT JOIN langchain_智能体知识库关联表 akr ON ac.id = akr.langchain_智能体配置表id
                AND akr.状态 = $1
            WHERE ac.id = $2 AND ac.启用rag = true
            LIMIT 1
            """

            RAG日志器.info(f"🔍 执行智能体查询SQL: {智能体查询SQL}")
            RAG日志器.info(
                f"🔍 智能体查询参数: ('active', {智能体id}), 智能体id类型: {type(智能体id)}"
            )

            智能体结果 = await 异步连接池实例.执行查询(
                智能体查询SQL, ("active", 智能体id)
            )

            RAG日志器.info(f"🔍 智能体查询结果: {智能体结果}")

            if not 智能体结果:
                RAG日志器.info(f"ℹ️ 智能体 {智能体id} 未启用rag或不存在")
                return {"嵌入模型": None}

            数据 = 智能体结果[0]

            # 检查是否有关联的知识库
            知识id = 数据.get("知识id")
            if not 知识id:
                RAG日志器.info(f"ℹ️ 智能体 {智能体id} 未关联任何知识库")
                return {"嵌入模型": None}

            # 第二步：查询知识库详情和嵌入模型信息
            知识库查询SQL = """
            SELECT
                kb.id, kb.知识库名称, kb.存储类型,
                m.id as 模型id, m.模型名称, m.模型类型, m.显示名称, m.提供商,
                m.api密钥, m.api基础url
            FROM langchain_知识库表 kb
            LEFT JOIN langchain_模型配置表 m ON kb.嵌入模型 = m.id
            WHERE kb.id = $1 AND kb.知识库状态 = $2 AND kb.存储类型 = 'postgresql'
            """

            RAG日志器.info(f"🔍 执行知识库查询SQL: {知识库查询SQL}")
            RAG日志器.info(f"🔍 知识库查询参数: ({知识id}, '正常')")

            知识库结果 = await 异步连接池实例.执行查询(知识库查询SQL, (知识id, "正常"))

            RAG日志器.info(f"🔍 知识库查询结果: {知识库结果}")

            if not 知识库结果:
                RAG日志器.warning(f"⚠️ 知识库 {知识id} 不存在或未激活")
                return {"嵌入模型": None}

            知识库数据 = 知识库结果[0]

            返回信息 = {
                "知识id": 知识库数据.get("id"),
                "知识库名称": 知识库数据.get("知识库名称"),
                "存储类型": 知识库数据.get("存储类型"),
                "嵌入模型": None,
            }

            # 如果有嵌入模型配置
            if 知识库数据.get("模型id"):
                返回信息["嵌入模型"] = {
                    "id": 知识库数据["模型id"],
                    "模型名称": 知识库数据["模型名称"],
                    "模型类型": 知识库数据["模型类型"],
                    "显示名称": 知识库数据["显示名称"],
                    "提供商": 知识库数据["提供商"],
                    "api密钥": 知识库数据["api密钥"],
                    "api基础url": 知识库数据["api基础url"],
                    "模型参数": {},  # 使用空字典作为默认参数
                    # 注意：langchain_模型配置表中没有启用状态字段，所有模型默认启用
                }
                RAG日志器.info(
                    f"✅ 找到智能体 {智能体id} 嵌入模型: {知识库数据['显示名称']}"
                )

            return 返回信息

        except Exception as e:
            RAG日志器.error(f"❌ 获取智能体关联知识库信息失败: {str(e)}")
            RAG日志器.error(f"❌ 错误详情: 智能体id={智能体id}, 类型={type(智能体id)}")
            import traceback

            RAG日志器.error(f"❌ 完整错误堆栈: {traceback.format_exc()}")
            return {"嵌入模型": None}

    async def _加载智能体关联知识库(self, 智能体id: int):
        """从数据库加载智能体关联的知识库 - PostgreSQL版本"""
        try:
            RAG日志器.info(f"🔧 开始加载智能体 {智能体id} 关联的知识库")

            # 获取智能体关联的知识库信息
            智能体知识库信息 = await self._获取智能体关联知识库信息(智能体id)

            if not 智能体知识库信息 or not 智能体知识库信息.get("知识id"):
                RAG日志器.warning(f"智能体 {智能体id} 未关联任何知识库")
                return

            知识id = 智能体知识库信息["知识id"]

            # 检查PostgreSQL中的向量数据
            数据库向量查询SQL = """
            SELECT COUNT(*) as 向量数量
            FROM langchain_文档向量表 v
            JOIN langchain_知识库文档表 d ON v.langchain_知识库文档表id = d.id
            WHERE d.langchain_知识库表id = $1 AND d.向量状态 = '已完成'
            """

            async with 异步连接池实例.获取连接() as 连接:
                数据库结果 = await 连接.fetchrow(数据库向量查询SQL, 知识id)

            数据库向量数量 = 数据库结果["向量数量"] if 数据库结果 else 0
            RAG日志器.info(f"📊 知识库 {知识id} 包含 {数据库向量数量} 个向量")

            if 数据库向量数量 > 0:
                RAG日志器.info(f"✅ 知识库 {知识id} 已包含向量数据，可以进行检索")
                self.当前知识id = 知识id
            else:
                RAG日志器.warning(f"⚠️ 知识库 {知识id} 暂无向量数据，需要先进行向量化")

        except Exception as e:
            RAG日志器.error(f"❌ 加载智能体关联知识库失败: {str(e)}")
            import traceback

            RAG日志器.error(f"❌ 完整错误堆栈: {traceback.format_exc()}")

    # 重复的方法已删除 - 使用上面优化的_获取知识库文档方法

    async def 向量检索(
        self, 查询: str, 知识id: int, k: int = 5, 相似度阈值: float = 0.7
    ) -> List[Document]:
        """PostgreSQL向量检索"""
        try:
            RAG日志器.info(f"PostgreSQL向量检索: {查询}")

            if not self.嵌入模型:
                RAG日志器.error("嵌入模型未初始化")
                return []

            # 生成查询向量
            查询向量 = await self._生成查询向量(查询)

            # 使用PostgreSQL进行向量检索
            检索结果列表 = await self._PostgreSQL向量相似度检索(
                知识id, 查询向量, k, 相似度阈值
            )

            # 转换为Document对象
            文档列表 = []
            for 结果 in 检索结果列表:
                文档 = Document(
                    page_content=结果["分块内容"],
                    metadata={
                        "相似度分数": 结果["相似度分数"],
                        "文档名称": 结果["文档名称"],
                        "文档uuid": 结果["文档uuid"],
                        "分块序号": 结果["分块序号"],
                        **结果["元数据"],
                    },
                )
                文档列表.append(文档)

            RAG日志器.info(f"PostgreSQL向量检索完成，返回 {len(文档列表)} 个结果")
            return 文档列表

        except Exception as e:
            RAG日志器.error(f"PostgreSQL向量检索失败: {str(e)}")
            return []

    async def _批量保存向量到PostgreSQL(
        self,
        文档记录ID: int,
        分块内容列表: List[str],
        向量列表: List[List[float]],
        起始索引: int = 0,
    ) -> int:
        """批量保存向量到PostgreSQL"""
        成功数量 = 0

        try:
            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.transaction():
                    for i, (分块内容, 向量) in enumerate(zip(分块内容列表, 向量列表)):
                        try:
                            # 将向量转换为PostgreSQL vector格式
                            向量字符串 = "[" + ",".join(map(str, 向量)) + "]"

                            # 准备元数据
                            元数据 = {
                                "chunk_index": 起始索引 + i,
                                "chunk_size": len(分块内容),
                                "document_record_id": 文档记录ID,
                            }

                            # 先检查是否已存在记录
                            检查SQL = """
                            SELECT id FROM langchain_文档向量表
                            WHERE langchain_知识库文档表id = $1 AND 分块序号 = $2
                            """

                            现有记录 = await 连接.fetchrow(
                                检查SQL, 文档记录ID, 起始索引 + i
                            )

                            if 现有记录:
                                # 更新现有记录
                                更新SQL = """
                                UPDATE langchain_文档向量表
                                SET 向量数据 = $1::vector, 元数据 = $2
                                WHERE langchain_知识库文档表id = $3 AND 分块序号 = $4
                                """
                                await 连接.execute(
                                    更新SQL,
                                    向量字符串,
                                    json.dumps(元数据, ensure_ascii=False),
                                    文档记录ID,
                                    起始索引 + i,
                                )
                            else:
                                # 插入新记录
                                插入SQL = """
                                INSERT INTO langchain_文档向量表
                                (langchain_知识库文档表id, 分块序号, 分块内容, 向量维度, 向量数据, 元数据, 创建时间)
                                VALUES ($1, $2, $3, $4, $5::vector, $6, CURRENT_TIMESTAMP)
                                """
                                await 连接.execute(
                                    插入SQL,
                                    文档记录ID,
                                    起始索引 + i,
                                    分块内容,
                                    len(向量),
                                    向量字符串,
                                    json.dumps(元数据, ensure_ascii=False),
                                )

                            成功数量 += 1

                        except Exception as e:
                            RAG日志器.error(f"保存单个向量失败: {str(e)}")
                            continue

            RAG日志器.debug(
                f"批量保存向量成功: 文档id={文档记录ID}, 成功数量={成功数量}/{len(分块内容列表)}"
            )
            return 成功数量

        except Exception as e:
            RAG日志器.error(f"批量保存向量失败: {str(e)}")
            return 成功数量

    async def _更新文档向量化状态(
        self, 文档记录ID: int, 状态: str, 分块数量: int
    ) -> bool:
        """更新文档的向量化状态 - 优化后的统一状态管理"""
        try:
            更新SQL = """
            UPDATE langchain_知识库文档表
            SET 向量状态 = $1, 向量分块数量 = $2, 最后向量化时间 = CURRENT_TIMESTAMP, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $3
            """
            async with 异步连接池实例.获取连接() as 连接:
                await 连接.execute(更新SQL, 状态, 分块数量, 文档记录ID)

            RAG日志器.debug(f"更新文档向量化状态成功: 文档id={文档记录ID}, 状态={状态}")
            return True

        except Exception as e:
            RAG日志器.error(f"更新文档向量化状态失败: {str(e)}")
            return False

    async def _同步更新向量化状态(
        self,
        任务ID: int,
        文档记录ID: int,
        任务状态: str,
        文档状态: str,
        成功数量: int,
        失败数量: int,
        批次统计: Dict[str, int],
    ) -> bool:
        """同步更新向量化任务和文档状态 - 确保状态一致性"""
        try:
            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.transaction():
                    # 1. 更新向量化任务状态
                    任务更新SQL = """
                    UPDATE langchain_向量化任务表
                    SET 任务状态 = $1, 已完成分块数 = $2, 失败分块数 = $3,
                        完成时间 = CASE WHEN $1 IN ('已完成', '失败', '基本完成') THEN CURRENT_TIMESTAMP ELSE 完成时间 END,
                        批次统计 = $4, 更新时间 = CURRENT_TIMESTAMP
                    WHERE id = $5
                    """

                    await 连接.execute(
                        任务更新SQL,
                        str(任务状态),
                        int(成功数量),
                        int(失败数量),
                        str(json.dumps(批次统计, ensure_ascii=False)),
                        int(任务ID),
                    )

                    # 2. 在同一事务中更新文档状态（避免嵌套连接）
                    # 获取实际向量数量进行验证
                    验证SQL = """
                    SELECT COUNT(*) as 实际数量
                    FROM langchain_文档向量表
                    WHERE langchain_知识库文档表id = $1 AND 向量数据 IS NOT NULL
                    """
                    结果 = await 连接.fetchrow(验证SQL, int(文档记录ID))
                    实际向量数量 = 结果["实际数量"] if 结果 else 0

                    # 更新文档状态
                    文档更新SQL = """
                    UPDATE langchain_知识库文档表
                    SET 向量状态 = $1, 向量分块数量 = $2, 最后向量化时间 = CURRENT_TIMESTAMP, 更新时间 = CURRENT_TIMESTAMP
                    WHERE id = $3
                    """
                    await 连接.execute(
                        文档更新SQL, str(文档状态), int(实际向量数量), int(文档记录ID)
                    )

                    RAG日志器.info(
                        f"✅ 状态同步完成: 任务ID={任务ID}, 文档id={文档记录ID}, "
                        f"任务状态={任务状态}, 文档状态={文档状态}, 成功={成功数量}, 实际向量={实际向量数量}"
                    )

                    return True

        except Exception as e:
            RAG日志器.error(f"同步更新向量化状态失败: {str(e)}")
            return False

    async def _PostgreSQL向量相似度检索(
        self,
        知识id: int,
        查询向量: List[float],
        最大数量: int = 10,
        相似度阈值: float = 0.7,
    ) -> List[Dict[str, Any]]:
        """使用PostgreSQL进行向量相似度检索"""
        try:
            # 将查询向量转换为PostgreSQL vector格式
            查询向量字符串 = "[" + ",".join(map(str, 查询向量)) + "]"

            # 移除SQL中的距离阈值过滤，统一在后处理中进行相似度阈值过滤
            # 这样确保过滤逻辑与优化相似度计算公式保持一致
            检索SQL = """
            SELECT
                dv.分块内容,
                dv.元数据,
                dv.向量数据 <=> $1::vector as 余弦距离,
                doc.文档名称,
                doc.文档uuid,
                dv.分块序号
            FROM langchain_文档向量表 dv
            JOIN langchain_知识库文档表 doc ON dv.langchain_知识库文档表id = doc.id
            WHERE doc.langchain_知识库表id = $2
            AND doc.向量状态 IN ('已完成', '基本完成')
            AND dv.向量数据 IS NOT NULL
            ORDER BY dv.向量数据 <=> $1::vector
            LIMIT $3
            """

            async with 异步连接池实例.获取连接() as 连接:
                结果列表 = await 连接.fetch(
                    检索SQL,
                    str(查询向量字符串),
                    int(知识id),
                    int(最大数量 * 2),  # 获取更多结果用于后处理过滤
                )

            # 转换结果格式并应用相似度阈值过滤
            检索结果 = []
            过滤掉的数量 = 0

            for 记录 in 结果列表:
                # 优化的相似度转换公式
                余弦距离 = 记录["余弦距离"]
                相似度分数 = self._计算优化相似度(余弦距离)

                # 应用相似度阈值过滤
                if 相似度分数 < 相似度阈值:
                    过滤掉的数量 += 1
                    continue

                结果项 = {
                    "分块内容": 记录["分块内容"],
                    "相似度分数": 相似度分数,
                    "文档名称": 记录["文档名称"],
                    "文档uuid": 记录["文档uuid"],
                    "分块序号": 记录["分块序号"],
                    "元数据": {},
                }

                # 解析元数据
                if 记录["元数据"]:
                    try:
                        结果项["元数据"] = json.loads(记录["元数据"])
                    except (json.JSONDecodeError, TypeError):
                        结果项["元数据"] = {}

                检索结果.append(结果项)

                # 达到最大数量限制时停止
                if len(检索结果) >= 最大数量:
                    break

            RAG日志器.info(
                f"PostgreSQL向量检索完成: 知识id={知识id}, "
                f"原始结果={len(结果列表)}, 过滤掉={过滤掉的数量}, "
                f"最终返回={len(检索结果)}个结果, 相似度阈值={相似度阈值}"
            )
            return 检索结果

        except Exception as e:
            RAG日志器.error(f"PostgreSQL向量检索失败: {str(e)}")
            return []

    def _计算优化相似度(self, 余弦距离: float) -> float:
        """
        优化的相似度转换公式

        设计原理：
        1. 余弦距离范围：[0, 2]，0表示完全相同，1表示正交，2表示完全相反
        2. 传统转换：相似度 = 1 - 距离，但会产生负值且对语义搜索过于严格
        3. 优化转换：使用分段函数，提高实用性

        参数:
            余弦距离: PostgreSQL <=> 操作符返回的余弦距离

        返回:
            float: 优化后的相似度分数 [0, 1]
        """
        try:
            # 确保距离在有效范围内
            距离 = max(0.0, min(2.0, float(余弦距离)))

            # 分段优化转换公式
            if 距离 <= 0.1:
                # 极高相似度区间：距离0-0.1 -> 相似度0.95-1.0
                # 线性映射，保持高精度
                相似度 = 1.0 - (距离 * 0.5)
            elif 距离 <= 0.5:
                # 高相似度区间：距离0.1-0.5 -> 相似度0.7-0.95
                # 使用平滑曲线，提升中等相似度的分数
                相似度 = 0.95 - ((距离 - 0.1) / 0.4) * 0.25
            elif 距离 <= 1.0:
                # 中等相似度区间：距离0.5-1.0 -> 相似度0.2-0.7
                # 保持合理的梯度变化
                相似度 = 0.7 - ((距离 - 0.5) / 0.5) * 0.5
            elif 距离 <= 1.5:
                # 低相似度区间：距离1.0-1.5 -> 相似度0.05-0.2
                # 快速衰减，但保留一定分数
                相似度 = 0.2 - ((距离 - 1.0) / 0.5) * 0.15
            else:
                # 极低相似度区间：距离1.5-2.0 -> 相似度0-0.05
                # 接近零但不完全为零
                相似度 = max(0.0, 0.05 - ((距离 - 1.5) / 0.5) * 0.05)

            # 确保结果在[0, 1]范围内
            return max(0.0, min(1.0, 相似度))

        except Exception as e:
            RAG日志器.error(f"计算优化相似度失败: {str(e)}")
            # 降级到传统公式
            return max(0.0, 1.0 - float(余弦距离))

    # ==================== PostgreSQL向量管理方法 ====================

    async def 获取知识库向量统计(self, 知识id: int) -> Dict[str, Any]:
        """获取知识库向量统计信息"""
        try:
            统计SQL = """
            SELECT
                COUNT(DISTINCT d.id) as 文档数量,
                COUNT(v.id) as 向量数量,
                COUNT(CASE WHEN v.向量数据 IS NOT NULL THEN 1 END) as 已完成向量数,
                COUNT(CASE WHEN v.向量数据 IS NULL THEN 1 END) as 待处理向量数,
                COUNT(CASE WHEN d.向量状态 = '失败' THEN 1 END) as 失败向量数
            FROM langchain_知识库文档表 d
            LEFT JOIN langchain_文档向量表 v ON d.id = v.langchain_知识库文档表id
            WHERE d.langchain_知识库表id = $1
            """

            async with 异步连接池实例.获取连接() as 连接:
                结果 = await 连接.fetchrow(统计SQL, 知识id)

            统计信息 = {
                "success": True,
                "知识id": 知识id,
                "文档数量": 结果["文档数量"] if 结果 else 0,
                "向量数量": 结果["向量数量"] if 结果 else 0,
                "已完成向量数": 结果["已完成向量数"] if 结果 else 0,
                "待处理向量数": 结果["待处理向量数"] if 结果 else 0,
                "失败向量数": 结果["失败向量数"] if 结果 else 0,
                "存储类型": "PostgreSQL",
            }

            return 统计信息

        except Exception as e:
            RAG日志器.error(f"获取知识库向量统计失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def 清空知识库向量(self, 知识id: int) -> Dict[str, Any]:
        """清空知识库中的所有向量数据"""
        try:
            # 删除向量数据
            删除向量SQL = """
            DELETE FROM langchain_文档向量表
            WHERE langchain_知识库文档表id IN (
                SELECT id FROM langchain_知识库文档表
                WHERE langchain_知识库表id = $1
            )
            """

            # 重置文档向量化状态
            重置状态SQL = """
            UPDATE langchain_知识库文档表
            SET 向量状态 = '待处理', 向量分块数量 = 0, 最后向量化时间 = NULL
            WHERE langchain_知识库表id = $1
            """

            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.transaction():
                    删除结果 = await 连接.execute(删除向量SQL, int(知识id))
                    更新结果 = await 连接.execute(重置状态SQL, int(知识id))

            RAG日志器.info(f"✅ 成功清空知识库 {知识id} 的向量数据")
            return {"success": True, "message": "向量数据已清空"}

        except Exception as e:
            RAG日志器.error(f"清空知识库向量失败: {str(e)}")
            return {"success": False, "error": str(e)}

    # ==================== 查询优化功能 ====================

    async def _优化查询(
        self, 原始查询: str, 查询优化配置: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        查询优化核心方法

        Args:
            原始查询: 用户输入的原始查询
            查询优化配置: 查询优化配置参数

        Returns:
            优化后的查询文本
        """
        try:
            # 如果没有配置或未启用优化，直接返回原始查询
            if not 查询优化配置 or not 查询优化配置.get("启用", False):
                RAG日志器.debug("查询优化未启用，使用原始查询")
                return 原始查询

            RAG日志器.info(
                f"🔧 开始查询优化: '{原始查询[:50]}{'...' if len(原始查询) > 50 else ''}'"
            )

            # 获取优化配置
            优化策略 = 查询优化配置.get("优化策略", "rewrite")
            优化模型id = 查询优化配置.get("优化模型id")
            提示词模板 = 查询优化配置.get(
                "提示词模板", "请将以下查询重写为更适合向量检索的形式：{query}"
            )

            # 根据优化策略选择处理方法
            if 优化策略 == "rewrite":
                优化后查询 = await self._查询重写(原始查询, 优化模型id, 提示词模板)
            elif 优化策略 == "expand":
                优化后查询 = await self._查询扩展(原始查询, 优化模型id, 提示词模板)
            elif 优化策略 == "multi_query":
                优化后查询 = await self._多查询生成(原始查询, 优化模型id, 提示词模板)
            else:
                RAG日志器.warning(f"未知的优化策略: {优化策略}，使用原始查询")
                优化后查询 = 原始查询

            RAG日志器.info(
                f"✅ 查询优化完成: '{优化后查询[:50]}{'...' if len(优化后查询) > 50 else ''}'"
            )
            return 优化后查询

        except Exception as e:
            RAG日志器.error(f"❌ 查询优化失败: {str(e)}，使用原始查询")
            return 原始查询

    async def _获取知识库默认查询优化配置(
        self, 知识id: int
    ) -> Optional[Dict[str, Any]]:
        """
        从知识库表获取默认查询优化配置
        """
        try:
            from 数据.异步连接池 import 异步连接池实例

            查询SQL = """
            SELECT
                默认启用查询优化,
                默认查询优化策略,
                默认查询优化模型id,
                默认查询优化提示词
            FROM langchain_知识库表
            WHERE id = $1 AND 知识库状态 = '正常'
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (知识id,))

            if not 结果:
                RAG日志器.warning(f"未找到知识库 {知识id} 或知识库未激活")
                return None

            配置记录 = 结果[0]

            # 构建默认查询优化配置
            默认配置 = {
                "启用": bool(配置记录.get("默认启用查询优化", False)),
                "优化策略": 配置记录.get("默认查询优化策略", "rewrite"),
                "优化模型id": 配置记录.get("默认查询优化模型id"),
                "提示词模板": 配置记录.get("默认查询优化提示词", ""),
            }

            RAG日志器.debug(
                f"知识库 {知识id} 默认查询优化配置: 启用={默认配置['启用']}, "
                f"策略={默认配置['优化策略']}, 模型id={默认配置['优化模型id']}"
            )

            return 默认配置

        except Exception as e:
            RAG日志器.error(f"获取知识库默认查询优化配置失败: {str(e)}")
            return None

    async def _查询优化(self, 原始查询: str, 查询优化配置: Dict) -> str:
        """
        统一的查询优化方法，支持三种优化策略
        """
        try:
            if not 查询优化配置 or not 查询优化配置.get("启用", False):
                return 原始查询

            优化策略 = 查询优化配置.get("优化策略", "rewrite")
            模型id = 查询优化配置.get("优化模型id")
            提示词模板 = 查询优化配置.get("提示词模板", "")

            if 优化策略 == "rewrite":
                return await self._查询重写(原始查询, 模型id, 提示词模板)
            elif 优化策略 == "expand":
                return await self._查询扩展(原始查询, 模型id, 提示词模板)
            elif 优化策略 == "multi_query":
                return await self._多查询生成(原始查询, 模型id, 提示词模板)
            else:
                RAG日志器.warning(f"未知的优化策略: {优化策略}")
                return 原始查询

        except Exception as e:
            RAG日志器.error(f"查询优化失败: {str(e)}")
            return 原始查询

    async def _生成多个优化查询(
        self, 原始查询: str, 查询优化配置: Dict, 数量: int = 2
    ) -> List[str]:
        """
        生成多个不同的优化查询
        """
        try:
            if not 查询优化配置 or not 查询优化配置.get("启用", False):
                return [原始查询] * 数量

            优化策略 = 查询优化配置.get("优化策略", "rewrite")
            模型id = 查询优化配置.get("优化模型id")
            提示词模板 = 查询优化配置.get("提示词模板", "")

            优化查询列表 = []

            for i in range(数量):
                if 优化策略 == "rewrite":
                    优化查询 = await self._查询重写_多样化(
                        原始查询, 模型id, 提示词模板, i + 1
                    )
                elif 优化策略 == "expand":
                    优化查询 = await self._查询扩展_多样化(
                        原始查询, 模型id, 提示词模板, i + 1
                    )
                elif 优化策略 == "multi_query":
                    优化查询 = await self._多查询生成_多样化(
                        原始查询, 模型id, 提示词模板, i + 1
                    )
                else:
                    优化查询 = 原始查询

                优化查询列表.append(优化查询)

            return 优化查询列表

        except Exception as e:
            RAG日志器.error(f"生成多个优化查询失败: {str(e)}")
            return [原始查询] * 数量

    async def _执行三次优化检索(
        self,
        查询: str,
        使用的知识id: int,
        k: int,
        相似度阈值: float,
        查询优化配置: Dict,
    ) -> tuple:
        """
        执行三次优化检索的公共逻辑
        返回: (最终检索结果, 优化信息字典)
        """
        import time

        优化策略 = 查询优化配置.get("优化策略", "rewrite")
        RAG日志器.info(f"🔄 启用三次查询优化: 策略={优化策略}")

        优化开始时间 = time.time()

        # 第一次优化：使用原始查询
        RAG日志器.info("🔍 第一次优化：原始查询")
        第一次检索结果 = await self.向量检索(查询, 使用的知识id, k, 相似度阈值)
        第一次最高相似度 = self._计算最高相似度(第一次检索结果)

        # 生成两个不同的优化查询
        优化查询列表 = await self._生成多个优化查询(查询, 查询优化配置, 2)

        # 确保有足够的优化查询（错误处理）
        if len(优化查询列表) < 2:
            优化查询列表.extend([查询] * (2 - len(优化查询列表)))

        # 第二次优化：使用第一个优化查询
        RAG日志器.info(f"🔍 第二次优化：{优化策略}策略 - '{优化查询列表[0][:50]}...'")
        第二次检索结果 = await self.向量检索(
            优化查询列表[0], 使用的知识id, k, 相似度阈值
        )
        第二次最高相似度 = self._计算最高相似度(第二次检索结果)

        # 第三次优化：使用第二个优化查询
        RAG日志器.info(f"🔍 第三次优化：{优化策略}策略 - '{优化查询列表[1][:50]}...'")
        第三次检索结果 = await self.向量检索(
            优化查询列表[1], 使用的知识id, k, 相似度阈值
        )
        第三次最高相似度 = self._计算最高相似度(第三次检索结果)

        查询优化耗时 = int((time.time() - 优化开始时间) * 1000)

        # 策略名称映射
        策略名称映射 = {
            "rewrite": "查询重写",
            "expand": "查询扩展",
            "multi_query": "多查询生成",
        }
        策略中文名 = 策略名称映射.get(优化策略, 优化策略)

        # 比较三次结果，选择最高相似度的
        检索结果列表 = [
            (第一次检索结果, 第一次最高相似度, "原始查询", 查询),
            (
                第二次检索结果,
                第二次最高相似度,
                f"第一次优化（{策略中文名}）",
                优化查询列表[0],
            ),
            (
                第三次检索结果,
                第三次最高相似度,
                f"第二次优化（{策略中文名}）",
                优化查询列表[1],
            ),
        ]

        # 选择相似度最高的结果
        最佳结果 = max(检索结果列表, key=lambda x: x[1])
        最终检索结果, 最高相似度, 最终选择, 最终查询 = 最佳结果

        # 生成优化效果描述
        if 最终选择 == "原始查询":
            优化效果 = "warning"
            优化描述 = f"优化无效：原始查询相似度最高({最高相似度:.4f})，使用原始结果"
        else:
            优化效果 = "success"
            优化描述 = f"优化有效：{最终选择}相似度最高({最高相似度:.4f})"

        # 构建优化信息
        优化信息 = {
            "原始查询": 查询,
            "优化策略": 优化策略,
            "第一次优化": {
                "查询": 查询,
                "结果数量": len(第一次检索结果),
                "最高相似度": 第一次最高相似度,
            },
            "第二次优化": {
                "查询": 优化查询列表[0],
                "结果数量": len(第二次检索结果),
                "最高相似度": 第二次最高相似度,
            },
            "第三次优化": {
                "查询": 优化查询列表[1],
                "结果数量": len(第三次检索结果),
                "最高相似度": 第三次最高相似度,
            },
            "最终选择": 最终选择,
            "最终查询": 最终查询,
            "优化效果类型": 优化效果,
            "优化效果描述": 优化描述,
            "性能统计": {
                "查询优化耗时ms": 查询优化耗时,
                "检索耗时ms": 0,  # 已包含在优化耗时中
            },
        }

        RAG日志器.info(
            f"✅ 三次查询优化完成: {最终选择}, 最终返回 {len(最终检索结果)} 个结果"
        )

        return 最终检索结果, 优化信息

    async def _调用优化模型(self, 模型, 提示词: str) -> str:
        """
        调用优化模型的公共方法，处理不同模型的特殊配置
        """
        if not LANGCHAIN_AVAILABLE:
            return ""

        from langchain_core.messages import HumanMessage

        try:
            # 特殊处理qwen模型
            if hasattr(模型, "model_name") and "qwen" in str(模型.model_name).lower():
                响应 = await 模型.ainvoke(
                    [HumanMessage(content=提示词)],
                    config={"model_kwargs": {"enable_thinking": False}},
                )
            else:
                响应 = await 模型.ainvoke([HumanMessage(content=提示词)])

            return 响应.content.strip()
        except Exception:
            # 降级处理
            响应 = await 模型.ainvoke([HumanMessage(content=提示词)])
            return 响应.content.strip()

    async def _查询重写(
        self, 原始查询: str, 模型id: Optional[int], 提示词模板: str
    ) -> str:
        """
        查询重写：将用户查询重写为更适合向量检索的形式
        """
        try:
            # 获取优化模型
            RAG日志器.info(f"🔧 开始获取优化模型: 模型id={模型id}")
            模型 = await self._获取优化模型(模型id)
            if not 模型:
                RAG日志器.warning(f"⚠️ 优化模型获取失败，使用原始查询: 模型id={模型id}")
                return 原始查询

            # 构建提示词
            # 🔧 修复：如果提示词模板为空，使用默认模板
            if not 提示词模板 or not 提示词模板.strip():
                提示词模板 = """请将以下查询重写为更适合向量检索的形式，只返回重写后的查询文本，不要添加任何解释或标题：

原始查询：{query}

重写后的查询："""
                RAG日志器.info("🔧 使用默认查询重写提示词模板")

            提示词 = 提示词模板.format(query=原始查询)
            RAG日志器.debug(
                f"🔧 构建的提示词: {提示词[:100]}{'...' if len(提示词) > 100 else ''}"
            )

            # 调用模型进行查询重写
            RAG日志器.info(f"🤖 调用模型进行查询重写: 提示词长度={len(提示词)}")
            响应内容 = await self._调用优化模型(模型, 提示词)
            if 响应内容:
                优化后查询 = self._提取优化后查询(响应内容)
                RAG日志器.info(f"✅ 查询重写成功: '{原始查询}' → '{优化后查询}'")
                return 优化后查询
            else:
                RAG日志器.warning("⚠️ 模型调用失败，返回原始查询")
                return 原始查询

        except Exception as e:
            RAG日志器.error(f"查询重写失败: {str(e)}")
            return 原始查询

    async def _查询重写_多样化(
        self, 原始查询: str, 模型id: Optional[int], 提示词模板: str, 变体编号: int
    ) -> str:
        """
        查询重写多样化版本：生成不同的重写变体
        """
        try:
            # 获取优化模型
            模型 = await self._获取优化模型(模型id)
            if not 模型:
                return 原始查询

            # 构建多样化提示词
            if not 提示词模板 or not 提示词模板.strip():
                # 根据变体编号使用不同的重写策略
                if 变体编号 == 1:
                    多样化提示词 = f"""请将以下查询重写为更适合向量检索的形式。使用更正式的表达方式，只返回重写后的查询文本：

原始查询：{原始查询}

重写策略：使用正式、标准的表达方式
重写后的查询："""
                elif 变体编号 == 2:
                    多样化提示词 = f"""请将以下查询重写为更适合向量检索的形式。使用更详细的描述方式，只返回重写后的查询文本：

原始查询：{原始查询}

重写策略：使用更详细、具体的描述
重写后的查询："""
                else:
                    多样化提示词 = f"""请将以下查询重写为更适合向量检索的形式。使用同义词替换的方式，只返回重写后的查询文本：

原始查询：{原始查询}

重写策略：使用同义词和近义词替换
重写后的查询："""
            else:
                多样化提示词 = (
                    提示词模板.format(query=原始查询)
                    + f"\n\n注意：这是第{变体编号}个变体，请提供完全不同的重写方式，确保与之前的变体有明显差异。"
                )

            # 调用模型
            响应内容 = await self._调用优化模型(模型, 多样化提示词)
            if 响应内容:
                优化后查询 = self._提取优化后查询(响应内容)
                RAG日志器.info(
                    f"✅ 查询重写变体{变体编号}成功: '{原始查询}' → '{优化后查询}'"
                )
                return 优化后查询
            else:
                return 原始查询

        except Exception as e:
            RAG日志器.error(f"查询重写变体{变体编号}失败: {str(e)}")
            return 原始查询

    async def _查询扩展(
        self, 原始查询: str, 模型id: Optional[int], 提示词模板: str
    ) -> str:
        """
        查询扩展：为原始查询添加相关的同义词和概念
        """
        try:
            # 获取优化模型
            模型 = await self._获取优化模型(模型id)
            if not 模型:
                return 原始查询

            # 构建扩展提示词
            # 🔧 修复：如果提示词模板为空，使用默认模板
            if not 提示词模板 or not 提示词模板.strip():
                扩展提示词 = f"""
请为以下查询添加相关的同义词、相关概念和扩展词汇，以提高向量检索的召回率：

原始查询：{原始查询}

请返回扩展后的查询，包含原始查询和相关词汇：
"""
            elif "{query}" in 提示词模板:
                扩展提示词 = 提示词模板.format(query=原始查询)
            else:
                扩展提示词 = f"""
请为以下查询添加相关的同义词、相关概念和扩展词汇，以提高向量检索的召回率：

原始查询：{原始查询}

请返回扩展后的查询，包含原始查询和相关词汇：
"""

            # 调用模型进行查询扩展
            扩展后查询 = await self._调用优化模型(模型, 扩展提示词)
            if 扩展后查询:
                RAG日志器.debug(f"查询扩展: '{原始查询}' → '{扩展后查询}'")
                return 扩展后查询
            else:
                return 原始查询

        except Exception as e:
            RAG日志器.error(f"查询扩展失败: {str(e)}")
            return 原始查询

    async def _查询扩展_多样化(
        self, 原始查询: str, 模型id: Optional[int], 提示词模板: str, 变体编号: int
    ) -> str:
        """
        查询扩展多样化版本：生成不同的扩展变体
        """
        try:
            # 获取优化模型
            模型 = await self._获取优化模型(模型id)
            if not 模型:
                return 原始查询

            # 构建多样化扩展提示词
            if not 提示词模板 or not 提示词模板.strip():
                # 根据变体编号使用不同的扩展策略
                if 变体编号 == 1:
                    多样化扩展提示词 = f"""请为以下查询添加同义词和近义词，扩展查询的表达范围：

原始查询：{原始查询}

扩展策略：添加同义词和近义词
扩展后的查询："""
                elif 变体编号 == 2:
                    多样化扩展提示词 = f"""请为以下查询添加相关概念和上下文词汇，丰富查询的语义内容：

原始查询：{原始查询}

扩展策略：添加相关概念和上下文词汇
扩展后的查询："""
                else:
                    多样化扩展提示词 = f"""请为以下查询添加行业术语和专业词汇，提高查询的专业性：

原始查询：{原始查询}

扩展策略：添加行业术语和专业词汇
扩展后的查询："""
            else:
                多样化扩展提示词 = (
                    提示词模板.format(query=原始查询)
                    + f"\n\n注意：这是第{变体编号}个变体，请使用完全不同的扩展策略，确保结果有明显差异。"
                )

            # 调用模型
            扩展后查询 = await self._调用优化模型(模型, 多样化扩展提示词)
            if 扩展后查询:
                RAG日志器.info(
                    f"✅ 查询扩展变体{变体编号}成功: '{原始查询}' → '{扩展后查询}'"
                )
                return 扩展后查询
            else:
                return 原始查询

        except Exception as e:
            RAG日志器.error(f"查询扩展变体{变体编号}失败: {str(e)}")
            return 原始查询

    async def _多查询生成(
        self, 原始查询: str, 模型id: Optional[int], 提示词模板: str
    ) -> str:
        """
        多查询生成：生成多个相关查询，然后合并为一个综合查询
        """
        try:
            # 获取优化模型
            模型 = await self._获取优化模型(模型id)
            if not 模型:
                return 原始查询

            # 构建多查询提示词
            # 🔧 修复：如果提示词模板为空，使用默认模板
            if not 提示词模板 or not 提示词模板.strip():
                多查询提示词 = f"""
基于以下原始查询，生成3个不同角度但相关的查询变体，以提高检索覆盖率：

原始查询：{原始查询}

请生成3个查询变体，每个一行：
1.
2.
3.
"""
            elif "{query}" in 提示词模板:
                多查询提示词 = 提示词模板.format(query=原始查询)
            else:
                多查询提示词 = f"""
基于以下原始查询，生成3个不同角度但相关的查询变体，以提高检索覆盖率：

原始查询：{原始查询}

请生成3个查询变体，每个一行：
1.
2.
3.
"""

            # 调用模型生成多查询
            多查询结果 = await self._调用优化模型(模型, 多查询提示词)
            if 多查询结果:
                # 解析多个查询并合并
                查询列表 = []
                for line in 多查询结果.split("\n"):
                    line = line.strip()
                    if line and not line.startswith(("1.", "2.", "3.", "-", "*")):
                        查询列表.append(line)
                    elif line.startswith(("1.", "2.", "3.")):
                        # 移除编号前缀
                        clean_query = line.split(".", 1)[1].strip()
                        if clean_query:
                            查询列表.append(clean_query)

                # 合并查询（包含原始查询）
                if 查询列表:
                    合并查询 = f"{原始查询} {' '.join(查询列表)}"
                else:
                    合并查询 = 原始查询

                RAG日志器.debug(f"多查询生成: '{原始查询}' → '{合并查询[:100]}...'")
                return 合并查询
            else:
                return 原始查询

        except Exception as e:
            RAG日志器.error(f"多查询生成失败: {str(e)}")
            return 原始查询

    async def _多查询生成_多样化(
        self, 原始查询: str, 模型id: Optional[int], 提示词模板: str, 变体编号: int
    ) -> str:
        """
        多查询生成多样化版本：生成不同的查询分解变体
        """
        try:
            # 获取优化模型
            模型 = await self._获取优化模型(模型id)
            if not 模型:
                return 原始查询

            # 构建多样化多查询提示词
            if not 提示词模板 or not 提示词模板.strip():
                # 根据变体编号使用不同的分解策略
                if 变体编号 == 1:
                    多样化多查询提示词 = f"""基于以下原始查询，从功能性角度生成3个不同的查询变体：

原始查询：{原始查询}

分解策略：功能性分解 - 从功能、用途、目的等角度
请生成3个查询变体，每个一行：
1.
2.
3."""
                elif 变体编号 == 2:
                    多样化多查询提示词 = f"""基于以下原始查询，从属性特征角度生成3个不同的查询变体：

原始查询：{原始查询}

分解策略：属性特征分解 - 从特征、属性、特点等角度
请生成3个查询变体，每个一行：
1.
2.
3."""
                else:
                    多样化多查询提示词 = f"""基于以下原始查询，从应用场景角度生成3个不同的查询变体：

原始查询：{原始查询}

分解策略：应用场景分解 - 从使用场景、应用领域等角度
请生成3个查询变体，每个一行：
1.
2.
3."""
            else:
                多样化多查询提示词 = (
                    提示词模板.format(query=原始查询)
                    + f"\n\n注意：这是第{变体编号}个变体，请使用完全不同的分解策略，确保生成的查询有明显差异。"
                )

            # 调用模型
            多查询结果 = await self._调用优化模型(模型, 多样化多查询提示词)
            if 多查询结果:
                # 解析多个查询并合并
                查询列表 = []
                for line in 多查询结果.split("\n"):
                    line = line.strip()
                    if line and not line.startswith(("1.", "2.", "3.", "-", "*")):
                        查询列表.append(line)
                    elif line.startswith(("1.", "2.", "3.")):
                        clean_query = line.split(".", 1)[1].strip()
                        if clean_query:
                            查询列表.append(clean_query)

                # 合并查询
                if 查询列表:
                    合并查询 = f"{原始查询} {' '.join(查询列表)}"
                else:
                    合并查询 = 原始查询

                RAG日志器.info(
                    f"✅ 多查询生成变体{变体编号}成功: '{原始查询}' → '{合并查询[:50]}...'"
                )
                return 合并查询
            else:
                return 原始查询

        except Exception as e:
            RAG日志器.error(f"多查询生成变体{变体编号}失败: {str(e)}")
            return 原始查询

    async def _获取优化模型(self, 模型id: Optional[int]):
        """
        获取用于查询优化的LLM模型
        """
        try:
            if not 模型id:
                RAG日志器.warning("未指定优化模型id，跳过查询优化")
                return None

            # 从数据库获取模型配置
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, api密钥, api基础url, 最大令牌数
            FROM langchain_模型配置表
            WHERE id = $1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (模型id,))

            if not 结果:
                RAG日志器.warning(f"未找到模型id {模型id}")
                return None

            模型配置 = 结果[0]

            # 根据提供商创建模型实例
            if LANGCHAIN_AVAILABLE:
                return await self._创建模型实例(模型配置)
            else:
                RAG日志器.warning("LangChain不可用，无法创建优化模型")
                return None

        except Exception as e:
            RAG日志器.error(f"获取优化模型失败: {str(e)}")
            return None

    async def _创建模型实例(self, 模型配置: Dict[str, Any]):
        """
        根据配置创建模型实例
        """
        try:
            提供商 = 模型配置.get("提供商", "").lower()
            模型名称 = 模型配置.get("模型名称", "")
            API密钥 = 模型配置.get("api密钥", "")
            API基础URL = 模型配置.get("api基础url", "")

            # 获取模型参数（使用数据库字段和默认值）
            最大令牌数 = 模型配置.get("最大令牌数", 1000)
            模型参数 = {
                "temperature": 0.1,  # 查询优化使用较低温度确保一致性
                "max_tokens": 最大令牌数,
                "top_p": 0.9,
            }

            # 根据提供商创建相应的模型
            if 提供商 == "openai":
                from langchain_openai import ChatOpenAI

                return ChatOpenAI(
                    model=模型名称,
                    api_key=API密钥,
                    base_url=API基础URL if API基础URL else None,
                    temperature=模型参数.get("temperature", 0.1),
                    max_tokens=模型参数.get("max_tokens", 1000),
                )
            elif 提供商 == "anthropic":
                from langchain_anthropic import ChatAnthropic

                return ChatAnthropic(
                    model=模型名称,
                    api_key=API密钥,
                    temperature=模型参数.get("temperature", 0.1),
                    max_tokens=模型参数.get("max_tokens", 1000),
                )
            elif 提供商 == "阿里云":
                # 🔧 关键修复：添加阿里云模型支持
                from langchain_openai import ChatOpenAI

                return ChatOpenAI(
                    model=模型名称,
                    api_key=API密钥,
                    base_url=API基础URL
                    or "https://dashscope.aliyuncs.com/compatible-mode/v1",
                    temperature=模型参数.get("temperature", 0.1),
                    max_tokens=模型参数.get("max_tokens", 1000),
                    top_p=模型参数.get("top_p", 0.9),
                    # 🔧 修复：阿里云模型必须设置enable_thinking=False用于非流式调用
                    extra_body={"enable_thinking": False},
                )
            else:
                RAG日志器.warning(f"不支持的模型提供商: {提供商}")
                return None

        except ImportError as e:
            RAG日志器.error(f"导入模型库失败: {str(e)}")
            return None
        except Exception as e:
            RAG日志器.error(f"创建模型实例失败: {str(e)}")
            return None

    def 获取查询优化信息(self) -> Optional[Dict[str, Any]]:
        """获取最近一次查询优化的详细信息"""
        return self._最近查询优化信息

    def 清空查询优化信息(self):
        """清空查询优化信息"""
        self._最近查询优化信息 = None

    def _计算最高相似度(self, 检索结果):
        """计算检索结果中的最高相似度分数"""
        if not 检索结果:
            return 0.0
        return max(
            (doc.metadata.get("相似度分数", 0.0) for doc in 检索结果), default=0.0
        )

    def _提取优化后查询(self, 模型响应):
        """从模型响应中提取核心查询内容"""
        响应内容 = 模型响应.strip()

        # 如果响应内容太短或明显不完整，直接返回原始内容
        if len(响应内容) < 10 or 响应内容.endswith(":") or 响应内容.endswith("："):
            RAG日志器.warning(f"模型响应可能不完整: '{响应内容}'")
            return 响应内容

        import re

        # 尝试提取 ** ** 之间的内容
        星号模式 = re.search(r"\*\*(.*?)\*\*", 响应内容, re.DOTALL)
        if 星号模式:
            提取的查询 = 星号模式.group(1).strip()
            if 提取的查询 and len(提取的查询) > 3:
                return 提取的查询

        # 尝试提取"重写后的查询："、"扩展后的查询："、"综合查询："后的内容
        查询标记模式 = re.search(
            r"(?:重写后的查询|扩展后的查询|综合查询)[:：]\s*(.*?)(?:\n\n|说明|$)",
            响应内容,
            re.DOTALL | re.IGNORECASE,
        )
        if 查询标记模式:
            提取的查询 = 查询标记模式.group(1).strip()
            # 移除可能的星号标记
            提取的查询 = re.sub(r"^\*\*|\*\*$", "", 提取的查询).strip()
            if 提取的查询 and len(提取的查询) > 3:
                return 提取的查询

        # 尝试提取冒号后的内容（适用于简单格式）
        冒号模式 = re.search(r"[:：]\s*(.+)", 响应内容, re.DOTALL)
        if 冒号模式:
            提取的查询 = 冒号模式.group(1).strip()
            # 取第一行作为查询
            第一行 = 提取的查询.split("\n")[0].strip()
            if 第一行 and len(第一行) > 3:
                return 第一行

        # 如果都没有匹配到，返回第一行非空内容
        lines = [line.strip() for line in 响应内容.split("\n") if line.strip()]
        if lines:
            第一行 = lines[0]
            # 如果第一行是标题或提示，尝试第二行
            if (
                any(keyword in 第一行 for keyword in ["改写", "重写", "优化", "查询"])
                and len(lines) > 1
            ):
                return lines[1] if len(lines[1]) > 3 else 第一行
            return 第一行

        return 响应内容

    def _选择最优结果并保存对比信息(
        self,
        原始查询,
        优化后查询,
        优化耗时,
        查询优化配置,
        第一阶段检索结果,
        第一阶段最高相似度,
        第二阶段检索结果,
        第二阶段最高相似度,
    ):
        """选择相似度更高的结果并保存完整的对比信息"""
        # 选择相似度更高的结果作为最终输出
        if 第二阶段最高相似度 > 第一阶段最高相似度:
            最终检索结果 = 第二阶段检索结果
            最终选择 = "第二阶段"
            优化效果类型 = "success"
            优化效果描述 = f"优化有效：相似度从 {第一阶段最高相似度:.4f} 提升到 {第二阶段最高相似度:.4f}"
        else:
            最终检索结果 = 第一阶段检索结果
            最终选择 = "第一阶段"
            优化效果类型 = "warning"
            优化效果描述 = f"优化无效：相似度从 {第一阶段最高相似度:.4f} 降低到 {第二阶段最高相似度:.4f}，使用原始结果"

        # 保存完整的两阶段检索对比信息
        self._最近查询优化信息 = {
            "第一阶段检索": {
                "查询": 原始查询,
                "结果数量": len(第一阶段检索结果),
                "最高相似度": 第一阶段最高相似度,
                "检索结果": 第一阶段检索结果,
            },
            "查询优化": {
                "原始查询": 原始查询,
                "优化后查询": 优化后查询,
                "优化策略": "基于检索结果优化",
                "优化模型": 查询优化配置.get("优化模型id", "默认模型"),
                "优化耗时": 优化耗时,
            },
            "第二阶段检索": {
                "查询": 优化后查询,
                "结果数量": len(第二阶段检索结果),
                "最高相似度": 第二阶段最高相似度,
                "检索结果": 第二阶段检索结果,
            },
            "对比分析": {
                "第一阶段最高相似度": 第一阶段最高相似度,
                "第二阶段最高相似度": 第二阶段最高相似度,
                "相似度提升": 第二阶段最高相似度 - 第一阶段最高相似度,
                "最终选择": 最终选择,
                "优化效果": {"类型": 优化效果类型, "描述": 优化效果描述},
            },
        }

        return 最终检索结果


# 创建全局RAG引擎实例
RAG引擎实例 = LangChainRAG引擎()
