"""
店铺订单服务层
负责店铺订单相关的业务逻辑处理
"""

import asyncio
import hashlib
import io
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

import pandas as pd

from 数据.店铺订单数据 import 店铺订单数据层实例
from 日志 import 接口日志器, 错误日志器
from 状态 import 通用


# 全局任务管理器 - 控制并发导入任务数量
class 导入任务管理器:
    """智能导入任务管理器，支持去重、并发控制和资源管理"""

    def __init__(self, 最大并发数: int = 3):
        self.最大并发数 = 最大并发数
        self.当前任务数 = 0
        self.任务锁 = asyncio.Lock()
        self.活跃任务 = {}  # 任务ID -> 任务信息
        self.用户文件映射 = {}  # 用户id -> {文件hash: 任务ID} (统一的用户文件映射)

        # 清理相关配置
        self.清理间隔 = 300  # 5分钟清理一次
        self.任务超时时间 = 1800  # 30分钟超时
        self.清理任务 = None
        self.正在关闭 = False

        # 启动定期清理任务
        self._启动清理任务()

    def _计算文件hash(self, file_content: bytes) -> str:
        """计算文件内容的MD5哈希值"""
        return hashlib.md5(file_content).hexdigest()

    async def 检查文件重复(
        self, 用户id: int, 文件hash: str
    ) -> Optional[Dict[str, Any]]:
        """
        检查文件是否重复，返回现有任务信息或None

        参数:
            用户id: 用户id
            文件hash: 文件MD5哈希值

        返回:
            Dict: 包含现有任务信息，如果没有重复则返回None
        """
        async with self.任务锁:
            # 检查用户是否已上传相同文件
            if 用户id in self.用户文件映射:
                用户任务 = self.用户文件映射[用户id]
                if 文件hash in 用户任务:
                    现有任务ID = 用户任务[文件hash]
                    if 现有任务ID in self.活跃任务:
                        接口日志器.info(
                            f"内存中发现重复文件: 用户{用户id}, 文件hash={文件hash[:8]}, 现有任务={现有任务ID}"
                        )
                        return {
                            "任务ID": 现有任务ID,
                            "文件hash": 文件hash,
                            "是否重复": True,
                        }

            return None

    def _启动清理任务(self):
        """启动定期清理任务"""
        # 防止重复启动
        if self.清理任务 and not self.清理任务.done():
            return

        async def 清理循环():
            try:
                while not self.正在关闭:
                    try:
                        await self._清理超时任务()
                        await self._清理孤儿映射()
                    except Exception as e:
                        错误日志器.error(f"定期清理任务异常: {str(e)}")

                    # 使用可中断的睡眠
                    try:
                        await asyncio.sleep(self.清理间隔)
                    except asyncio.CancelledError:
                        break
            except asyncio.CancelledError:
                接口日志器.info("清理任务被取消")
            except Exception as e:
                错误日志器.error(f"清理循环异常: {str(e)}")

        self.清理任务 = asyncio.create_task(清理循环())

    async def _清理超时任务(self):
        """清理超时的任务"""
        async with self.任务锁:
            当前时间 = datetime.now()
            超时任务列表 = []

            for 任务ID, 任务信息 in self.活跃任务.items():
                超时时间 = 任务信息.get("超时时间")
                if 超时时间 and 当前时间 > 超时时间:
                    超时任务列表.append(任务ID)

            # 清理超时任务
            for 任务ID in 超时任务列表:
                await self._强制清理任务(任务ID, "超时清理")

    async def _清理孤儿映射(self):
        """清理孤儿映射（映射指向不存在的任务）"""
        async with self.任务锁:
            需要清理的用户 = []

            for 用户id, 用户任务 in self.用户文件映射.items():
                需要删除的hash = []
                for 文件hash, 任务ID in 用户任务.items():
                    if 任务ID not in self.活跃任务:
                        需要删除的hash.append(文件hash)

                # 清理孤儿映射
                for 文件hash in 需要删除的hash:
                    del 用户任务[文件hash]

                # 如果用户没有任何任务，标记删除
                if not 用户任务:
                    需要清理的用户.append(用户id)

            # 删除空的用户映射
            for 用户id in 需要清理的用户:
                del self.用户文件映射[用户id]

            if 需要清理的用户:
                接口日志器.info(f"清理了 {len(需要清理的用户)} 个空用户映射")

    def _清理任务映射(self, 任务ID: str, 用户id: int = None):
        """清理指定任务的映射关系（内部方法，调用时必须已持有锁）"""
        if 用户id and 用户id in self.用户文件映射:
            用户任务 = self.用户文件映射[用户id]
            需要删除的hash = []
            for 文件hash, 映射任务ID in 用户任务.items():
                if 映射任务ID == 任务ID:
                    需要删除的hash.append(文件hash)

            for 文件hash in 需要删除的hash:
                del 用户任务[文件hash]

            # 如果用户没有其他任务，删除用户映射
            if not 用户任务:
                del self.用户文件映射[用户id]

    def _移除活跃任务(self, 任务ID: str):
        """移除活跃任务并更新计数（内部方法，调用时必须已持有锁）"""
        if 任务ID in self.活跃任务:
            del self.活跃任务[任务ID]
            self.当前任务数 = max(0, self.当前任务数 - 1)

    async def _强制清理任务(self, 任务ID: str, 原因: str = "强制清理"):
        """强制清理指定任务（不需要锁，调用时已在锁内）"""
        try:
            if 任务ID in self.活跃任务:
                任务信息 = self.活跃任务[任务ID]
                用户id = 任务信息.get("用户id")

                # 使用统一的清理方法
                self._清理任务映射(任务ID, 用户id)
                self._移除活跃任务(任务ID)

                接口日志器.info(f"强制清理任务: {任务ID}, 原因: {原因}")
        except Exception as e:
            错误日志器.error(f"强制清理任务失败 {任务ID}: {str(e)}")

    async def 优雅关闭(self, 超时时间: float = 5.0):
        """优雅关闭任务管理器"""
        接口日志器.info("开始优雅关闭任务管理器...")
        self.正在关闭 = True

        try:
            # 取消清理任务
            if self.清理任务 and not self.清理任务.done():
                self.清理任务.cancel()
                try:
                    await asyncio.wait_for(self.清理任务, timeout=1.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    接口日志器.warning("清理任务取消超时")

            # 清理所有活跃任务（带超时保护）
            async def 清理所有任务():
                async with self.任务锁:
                    活跃任务列表 = list(self.活跃任务.keys())
                    for 任务ID in 活跃任务列表:
                        await self._强制清理任务(任务ID, "优雅关闭")

                    # 清理所有映射
                    self.用户文件映射.clear()
                    self.活跃任务.clear()
                    self.当前任务数 = 0

            try:
                await asyncio.wait_for(清理所有任务(), timeout=超时时间)
            except asyncio.TimeoutError:
                错误日志器.warning(f"优雅关闭超时({超时时间}秒)，强制清理")
                # 强制清理
                async with self.任务锁:
                    self.用户文件映射.clear()
                    self.活跃任务.clear()
                    self.当前任务数 = 0

            接口日志器.info("任务管理器优雅关闭完成")
        except Exception as e:
            错误日志器.error(f"优雅关闭异常: {str(e)}")
            # 确保状态被重置
            self.正在关闭 = True

    async def 获取活跃任务状态(self) -> Dict[str, Any]:
        """获取活跃任务状态信息"""
        async with self.任务锁:
            return {
                "当前任务数": self.当前任务数,
                "最大并发数": self.最大并发数,
                "活跃任务": len(self.活跃任务),
                "用户映射数": len(self.用户文件映射),
                "总文件映射数": sum(
                    len(用户任务) for 用户任务 in self.用户文件映射.values()
                ),
                "正在关闭": self.正在关闭,
            }

    async def 异常清理任务(self, 任务ID: str, 异常信息: str = None):
        """异常情况下的任务清理（外部调用接口，自动加锁）"""
        try:
            async with self.任务锁:
                if 任务ID in self.活跃任务:
                    清理原因 = f"异常清理: {异常信息 or '未知异常'}"
                    await self._强制清理任务(任务ID, 清理原因)
        except Exception as e:
            错误日志器.error(f"异常清理任务失败 {任务ID}: {str(e)}")

    async def 获取任务许可(
        self,
        任务ID: str,
        用户id: int = None,
        文件hash: str = None,
        文件名: str = None,
    ) -> bool:
        """获取任务执行许可，支持去重检查"""
        async with self.任务锁:
            if self.当前任务数 >= self.最大并发数:
                return False

            # 如果提供了文件信息，记录用户文件映射用于去重
            if 用户id is not None and 文件hash is not None:
                # 记录用户文件映射
                if 用户id not in self.用户文件映射:
                    self.用户文件映射[用户id] = {}
                self.用户文件映射[用户id][文件hash] = 任务ID

            self.当前任务数 += 1
            self.活跃任务[任务ID] = {
                "开始时间": datetime.now(),
                "状态": "运行中",
                "超时时间": datetime.now() + timedelta(seconds=1800),  # 30分钟超时
                "用户id": 用户id,
                "文件名": 文件名,
            }
            接口日志器.info(
                f"任务{任务ID}获得执行许可，当前活跃任务数: {self.当前任务数}，预计超时时间: {self.活跃任务[任务ID]['超时时间'].strftime('%H:%M:%S')}"
            )
            return True

    async def 释放任务许可(self, 任务ID: str):
        """释放任务执行许可，并清理相关映射（外部调用接口，自动加锁）"""
        async with self.任务锁:
            if 任务ID in self.活跃任务:
                任务信息 = self.活跃任务[任务ID]
                运行时长 = datetime.now() - 任务信息["开始时间"]
                用户id = 任务信息.get("用户id")

                # 使用统一的清理方法
                self._清理任务映射(任务ID, 用户id)
                self._移除活跃任务(任务ID)

                接口日志器.info(
                    f"任务{任务ID}释放许可，运行时长: {运行时长}，当前活跃任务数: {self.当前任务数}"
                )


# 全局任务管理器实例
任务管理器 = 导入任务管理器(最大并发数=3)


class 店铺订单服务:
    """店铺订单业务服务层"""

    def __init__(self):
        self.数据层 = 店铺订单数据层实例

    async def 导入Excel订单(self, file_content: bytes, 用户id: int) -> Dict[str, Any]:
        """
        导入Excel订单文件

        参数:
            file_content: Excel文件内容
            用户id: 当前用户id

        返回:
            Dict: 导入结果
        """
        try:
            接口日志器.info(f"开始导入Excel订单: 用户id={用户id}")

            # 解析Excel文件
            try:
                df = pd.read_excel(io.BytesIO(file_content))
            except Exception as e:
                return {
                    "status": 通用.参数错误,
                    "message": f"Excel文件解析失败: {str(e)}",
                }

            if df.empty:
                return {"status": 通用.参数错误, "message": "Excel文件内容为空"}

            # 处理订单数据
            处理结果 = await self._处理订单数据(df, 用户id)

            接口日志器.info(f"Excel订单导入完成: 用户id={用户id}, 结果={处理结果}")
            return {"status": 通用.成功, "message": "订单导入完成", "data": 处理结果}

        except Exception as e:
            错误日志器.error(f"导入Excel订单失败: {str(e)}")
            return {"status": 通用.服务器错误, "message": f"导入失败: {str(e)}"}

    async def 异步导入Excel订单(
        self, file_content: bytes, 文件名: str, 用户id: int
    ) -> Dict[str, Any]:
        """
        异步导入Excel订单文件 - 支持去重和智能任务合并

        参数:
            file_content: Excel文件内容
            文件名: 原始文件名
            用户id: 当前用户id

        返回:
            Dict: 包含任务ID的响应
        """
        # 计算文件hash用于统一的去重检查
        文件hash = hashlib.md5(file_content).hexdigest()

        # 1. 首先检查内存中的活跃任务是否重复
        内存重复检查 = await 任务管理器.检查文件重复(用户id, 文件hash)
        if 内存重复检查:
            现有任务ID = 内存重复检查["任务ID"]
            # 查询现有任务的数据库记录
            现有记录 = await self.数据层.查询导入记录(现有任务ID, 用户id)
            if 现有记录:
                接口日志器.info(
                    f"内存中发现重复文件: hash={文件hash[:8]}, 任务ID={现有任务ID}, 状态={现有记录['任务状态']}"
                )
                return {
                    "status": 通用.成功,
                    "message": "该文件已在处理中，无需重复上传",
                    "data": {
                        "任务ID": 现有任务ID,
                        "导入记录ID": 现有记录["id"],
                        "是否重复任务": True,
                        "任务状态": 现有记录["任务状态"],
                        "进度百分比": float(现有记录["进度百分比"]),
                    },
                }

        # 2. 检查数据库中是否有重复的文件导入记录（基于hash）
        数据库重复检查 = await self.数据层.检查用户文件重复导入(用户id, 文件hash)
        if 数据库重复检查:
            现有任务ID = 数据库重复检查["任务ID"]
            接口日志器.info(
                f"数据库中发现重复文件: hash={文件hash[:8]}, 任务ID={现有任务ID}, 状态={数据库重复检查['任务状态']}"
            )
            return {
                "status": 通用.成功,
                "message": "该文件已存在，无需重复上传",
                "data": {
                    "任务ID": 现有任务ID,
                    "导入记录ID": 数据库重复检查["id"],
                    "是否重复任务": True,
                    "任务状态": 数据库重复检查["任务状态"],
                    "进度百分比": float(数据库重复检查["进度百分比"]),
                },
            }

        任务ID = str(uuid.uuid4())
        导入记录ID = None
        任务许可已获取 = False
        文件路径 = None

        try:
            # 检查是否可以获取任务许可（包含文件信息用于去重）
            if not await 任务管理器.获取任务许可(任务ID, 用户id, 文件hash, 文件名):
                return {
                    "status": 通用.操作失败,
                    "message": "当前导入任务过多，请稍后再试",
                }
            任务许可已获取 = True

            # 保存Excel文件到临时目录
            文件路径 = await self._保存Excel文件(任务ID, 文件名, file_content)
            if not 文件路径:
                raise Exception("保存Excel文件失败")

            # 创建导入记录（包含文件路径和hash）
            导入记录ID = await self.数据层.创建导入记录(
                任务ID, 用户id, 文件名, len(file_content), 文件路径, 文件hash
            )

            if not 导入记录ID:
                raise Exception("创建导入记录失败")

            # 启动后台任务（使用文件路径而不是内存内容）
            task = asyncio.create_task(
                self._执行分批导入任务(任务ID, 文件路径, 用户id, 导入记录ID)
            )

            # 确保任务已成功创建
            if task.done() and task.exception():
                raise task.exception()

            接口日志器.info(f"异步导入任务已启动: 任务ID={任务ID}, 用户id={用户id}")
            return {
                "status": 通用.成功,
                "message": "导入任务已启动",
                "data": {"任务ID": 任务ID, "导入记录ID": 导入记录ID},
            }

        except Exception as e:
            错误日志器.error(f"启动异步导入失败: {str(e)}")

            # 异常清理任务
            if 任务许可已获取:
                await 任务管理器.异常清理任务(任务ID, str(e))

            if 导入记录ID:
                await self.数据层.更新导入记录状态(
                    导入记录ID, "失败", 0.0, f"任务启动失败: {str(e)}"
                )

            return {"status": 通用.服务器错误, "message": f"启动导入失败: {str(e)}"}

    async def _保存Excel文件(
        self, 任务ID: str, 文件名: str, file_content: bytes
    ) -> Optional[str]:
        """
        保存Excel文件到临时目录

        参数:
            任务ID: 任务ID
            文件名: 原始文件名
            file_content: 文件内容

        返回:
            Optional[str]: 文件路径，失败返回None
        """
        try:
            import os
            from datetime import datetime

            # 创建临时文件目录
            临时目录 = "数据/临时文件"
            os.makedirs(临时目录, exist_ok=True)

            # 生成安全的文件名
            时间戳 = datetime.now().strftime("%Y%m%d_%H%M%S")
            文件扩展名 = os.path.splitext(文件名)[1]
            安全文件名 = f"import_{任务ID}_{时间戳}{文件扩展名}"
            文件路径 = os.path.join(临时目录, 安全文件名)

            # 保存文件
            with open(文件路径, "wb") as f:
                f.write(file_content)

            接口日志器.info(f"Excel文件保存成功: {文件路径}")
            return 文件路径

        except Exception as e:
            错误日志器.error(f"保存Excel文件失败: {str(e)}")
            return None

    async def _执行分批导入任务(
        self,
        任务ID: str,
        文件路径: str,
        用户id: int,
        导入记录ID: int,
        起始批次: int = 0,
    ):
        """
        执行分批导入任务 - 1000行分批处理

        参数:
            任务ID: 任务ID
            文件路径: Excel文件路径
            用户id: 用户id
            导入记录ID: 导入记录ID
            起始批次: 起始批次号（用于断点续传）
        """
        try:
            接口日志器.info(f"开始执行分批导入任务: {任务ID}, 文件: {文件路径}")

            # 更新任务状态为进行中
            if 起始批次 > 0:
                # 续传任务：保留原有进度
                当前记录 = await self.数据层.查询导入记录_通过ID(导入记录ID, 用户id)
                当前进度 = 当前记录["进度百分比"] if 当前记录 else 0.0
                await self.数据层.更新导入记录状态(导入记录ID, "进行中", 当前进度)
            else:
                # 新任务：从0开始
                await self.数据层.更新导入记录状态(导入记录ID, "进行中", 0.0)

            # 设置任务超时时间为30分钟
            任务超时时间 = 1800  # 30分钟 = 1800秒

            try:
                async with asyncio.timeout(任务超时时间):
                    # 分批处理Excel文件
                    await self._分批处理Excel文件(
                        任务ID, 文件路径, 用户id, 导入记录ID, 起始批次
                    )

            except asyncio.TimeoutError:
                # 任务超时处理 - 保留文件和进度信息用于续传
                错误日志器.error(f"导入任务{任务ID}超时，文件已保留用于续传")
                try:
                    # 先查询当前进度信息，避免丢失已处理的进度
                    当前记录 = await self.数据层.查询导入记录_通过ID(导入记录ID, 用户id)
                    当前进度 = 当前记录["进度百分比"] if 当前记录 else 0.0

                    await self.数据层.更新导入记录状态(
                        导入记录ID,
                        "超时",
                        当前进度,  # 保留当前进度而不是重置为0.0
                        f"导入任务超时（{任务超时时间 // 60}分钟），可点击续传继续处理",
                    )
                except Exception as timeout_update_error:
                    错误日志器.error(
                        f"更新超时状态失败: {任务ID}, 错误: {str(timeout_update_error)}"
                    )
                return

        except Exception as e:
            错误日志器.error(f"分批导入任务执行失败: {任务ID}, 错误: {str(e)}")
            try:
                await self.数据层.更新导入记录状态(
                    导入记录ID, "失败", 0.0, f"导入执行失败: {str(e)}"
                )
                # 删除临时文件
                await self._清理临时文件(文件路径)
            except Exception as update_error:
                错误日志器.error(
                    f"更新任务状态失败: {任务ID}, 错误: {str(update_error)}"
                )
        finally:
            # 确保释放任务许可
            try:
                await 任务管理器.释放任务许可(任务ID)
            except Exception as release_error:
                错误日志器.error(
                    f"释放任务许可失败: {任务ID}, 错误: {str(release_error)}"
                )

    async def _分批处理Excel文件(
        self,
        任务ID: str,
        文件路径: str,
        用户id: int,
        导入记录ID: int,
        起始批次: int = 0,
    ):
        """
        分批处理Excel文件 - 1000行一批
        """
        try:
            import pandas as pd

            # 分批大小
            批处理大小 = 1000
            当前批次 = 起始批次
            总行数 = 0

            # 获取续传时的已有统计数据
            if 起始批次 > 0:
                # 续传模式：从数据库获取已有统计
                导入记录 = await self.数据层.查询导入记录_通过ID(导入记录ID, 用户id)
                总成功数量 = 导入记录.get("成功数量", 0)
                总失败数量 = 导入记录.get("失败数量", 0)
                总跳过数量 = 导入记录.get("跳过数量", 0)
                接口日志器.info(
                    f"[任务{任务ID}] 续传模式，已有统计: 成功{总成功数量}, 失败{总失败数量}, 跳过{总跳过数量}"
                )
            else:
                # 新任务模式：从0开始统计
                总成功数量 = 0
                总失败数量 = 0
                总跳过数量 = 0
                接口日志器.info(f"[任务{任务ID}] 新任务模式，从0开始统计")

            接口日志器.info(f"[任务{任务ID}] 开始分批处理，起始批次: {起始批次}")

            # 首先获取总行数（用于进度计算）
            try:
                with pd.ExcelFile(文件路径) as xls:
                    总行数 = len(pd.read_excel(xls, sheet_name=0))
                await self.数据层.更新导入记录总行数(导入记录ID, 总行数)
                接口日志器.info(f"[任务{任务ID}] Excel总行数: {总行数}")
            except Exception as e:
                错误日志器.error(f"获取Excel总行数失败: {str(e)}")
                总行数 = 0

            # 分批读取和处理
            try:
                # 一次性读取Excel文件（Excel不支持chunksize）
                df = pd.read_excel(文件路径)

                # 修正Excel列名映射
                df = self._修正Excel列名映射(df)

                # 手动分批处理
                批次计数器 = 0
                总批次数 = (len(df) + 批处理大小 - 1) // 批处理大小  # 向上取整

                for 批次开始 in range(0, len(df), 批处理大小):
                    # 跳过已处理的批次（用于断点续传）
                    if 批次计数器 < 起始批次:
                        批次计数器 += 1
                        continue

                    # 获取当前批次数据
                    批次结束 = min(批次开始 + 批处理大小, len(df))
                    批次数据 = df.iloc[批次开始:批次结束]

                    接口日志器.info(
                        f"[任务{任务ID}] 处理第{批次计数器}批次，数据行数: {len(批次数据)} ({批次开始 + 1}-{批次结束})"
                    )

                    # 处理当前批次
                    批次结果 = await self._处理单批次数据(批次数据, 批次计数器, 任务ID)

                    # 累计统计
                    总成功数量 += 批次结果["成功数量"]
                    总失败数量 += 批次结果["失败数量"]
                    总跳过数量 += 批次结果["跳过数量"]

                    # 计算进度
                    已处理行数 = (批次计数器 + 1) * 批处理大小
                    if 总行数 > 0:
                        进度百分比 = min((已处理行数 / 总行数) * 100, 100.0)
                    else:
                        进度百分比 = 0.0

                    # 更新批次进度
                    await self.数据层.更新批次进度(
                        导入记录ID,
                        批次计数器,
                        已处理行数,
                        总成功数量,
                        总失败数量,
                        总跳过数量,
                        进度百分比,
                    )

                    接口日志器.info(
                        f"[任务{任务ID}] 第{批次计数器}批次完成，成功: {批次结果['成功数量']}, "
                        f"失败: {批次结果['失败数量']}, 跳过: {批次结果['跳过数量']}, 进度: {进度百分比:.1f}%"
                    )

                    批次计数器 += 1

                    # 批次间休眠，释放资源
                    await asyncio.sleep(0.01)

                    # 定期垃圾回收
                    if 批次计数器 % 5 == 0:
                        import gc

                        gc.collect()

            except Exception as e:
                错误日志器.error(f"[任务{任务ID}] 分批处理失败: {str(e)}")
                raise
            finally:
                # 释放DataFrame内存
                if "df" in locals():
                    del df
                    import gc

                    gc.collect()

            # 处理完成，更新最终状态
            最终状态 = "已完成" if 总失败数量 == 0 else "部分失败"
            await self.数据层.完成导入记录(
                导入记录ID, 最终状态, 总成功数量, 总失败数量, 总跳过数量, 100.0
            )

            # 删除临时文件
            await self._清理临时文件(文件路径)

            接口日志器.info(
                f"[任务{任务ID}] 分批导入完成，总计: 成功{总成功数量}, 失败{总失败数量}, 跳过{总跳过数量}"
            )

        except Exception as e:
            错误日志器.error(f"[任务{任务ID}] 分批处理Excel文件失败: {str(e)}")
            raise

    async def _处理单批次数据(
        self, 批次数据: "pd.DataFrame", 批次号: int, 任务ID: str
    ) -> Dict[str, Any]:
        """
        处理单个批次的数据

        参数:
            批次数据: 当前批次的DataFrame
            批次号: 批次编号
            任务ID: 任务ID

        返回:
            Dict: 处理结果统计
        """
        成功数量 = 0
        失败数量 = 0
        跳过数量 = 0
        错误详情 = []

        try:
            # 提取订单ID列表进行批量去重检查
            订单ids = []
            有效行数据 = []

            for index, row in 批次数据.iterrows():
                行号 = index + 2  # Excel行号（从第2行开始，第1行是表头）

                # 验证订单ID
                if pd.isna(row.get("订单id")) or str(row.get("订单id")).strip() == "":
                    错误详情.append(f"第{行号}行：订单id为空")
                    失败数量 += 1
                    continue

                try:
                    订单id = int(float(str(row["订单id"]).strip()))
                    订单ids.append(订单id)
                    有效行数据.append((index, row, 行号, 订单id))
                except ValueError:
                    错误详情.append(f"第{行号}行：订单id格式错误")
                    失败数量 += 1
                    continue

            # 批量检查订单是否已存在
            if 订单ids:
                已存在订单集合 = await self.数据层.批量检查订单存在(订单ids)

                # 准备批量插入的数据
                待插入数据 = []

                for index, row, 行号, 订单id in 有效行数据:
                    if 订单id in 已存在订单集合:
                        跳过数量 += 1
                        continue

                    try:
                        # 处理达人关联逻辑
                        抖音火山号 = row.get("抖音火山号")
                        用户订单认领表id = None
                        if not pd.isna(抖音火山号) and str(抖音火山号).strip():
                            抖音火山号 = str(抖音火山号).strip()
                            用户订单认领表id = await self._处理达人关联逻辑(
                                订单id, 抖音火山号, 行号
                            )

                        # 处理订单数据
                        处理后数据 = self._处理单行订单数据(row, 订单id)
                        处理后数据["用户订单认领表id"] = 用户订单认领表id
                        待插入数据.append(处理后数据)

                    except Exception as e:
                        错误详情.append(f"第{行号}行：数据处理失败 - {str(e)}")
                        失败数量 += 1
                        continue

                # 批量插入数据
                if 待插入数据:
                    try:
                        插入成功数量 = await self.数据层.批量插入订单数据(待插入数据)
                        成功数量 += 插入成功数量

                        # 如果插入数量不匹配，记录失败
                        if 插入成功数量 < len(待插入数据):
                            失败数量 += len(待插入数据) - 插入成功数量
                            错误详情.append(f"批次{批次号}：部分数据插入失败")

                    except Exception as e:
                        失败数量 += len(待插入数据)
                        错误详情.append(f"批次{批次号}：批量插入失败 - {str(e)}")

            return {
                "成功数量": 成功数量,
                "失败数量": 失败数量,
                "跳过数量": 跳过数量,
                "错误详情": 错误详情[:5],  # 只保留前5个错误
            }

        except Exception as e:
            错误日志器.error(f"[任务{任务ID}] 处理批次{批次号}失败: {str(e)}")
            return {
                "成功数量": 0,
                "失败数量": len(批次数据),
                "跳过数量": 0,
                "错误详情": [f"批次{批次号}处理异常: {str(e)}"],
            }

    async def _清理临时文件(self, 文件路径: str):
        """
        清理临时文件
        """
        try:
            import os

            if os.path.exists(文件路径):
                os.remove(文件路径)
                接口日志器.info(f"临时文件已删除: {文件路径}")
        except Exception as e:
            错误日志器.error(f"删除临时文件失败: {文件路径}, 错误: {str(e)}")

    def _处理单行订单数据(self, row: "pd.Series", 订单id: int) -> Dict[str, Any]:
        """
        处理单行订单数据

        参数:
            row: pandas行数据
            订单id: 订单ID

        返回:
            Dict: 处理后的订单数据
        """
        处理后数据 = {}

        # 处理订单id（BIGINT）
        处理后数据["订单id"] = 订单id

        # 处理字符串字段
        字符串字段 = [
            "商品id",
            "商品名称",
            "作者账号",
            "抖音火山号",
            "订单状态",
            "超时未结算原因",
            "商品来源",
            "店铺id",
            "店铺名称",
            "佣金发票",
            "是否阶梯佣金",
            "分销来源",
            "计划类型",
            "订单来源",
            "流量细分来源",
            "流量来源",
            "订单类型",
        ]

        for 字段 in 字符串字段:
            值 = row.get(字段)
            if pd.isna(值) or str(值).strip() == "":
                处理后数据[字段] = None
            else:
                处理后数据[字段] = str(值).strip()

        # 处理金额字段（DECIMAL(10,2)）
        金额字段 = [
            "支付金额",
            "预估佣金支出",
            "结算金额",
            "实际佣金支出",
            "定金金额",
            "预估奖励佣金支出",
            "结算奖励佣金支出",
            "支付补贴",
            "平台补贴",
            "达人补贴",
            "运费",
            "税费",
            "运费补贴",
            "推广技术服务费",
            "预估推广费支出",
            "结算推广费支出",
        ]

        for 字段 in 金额字段:
            值 = row.get(字段)
            if pd.isna(值) or str(值).strip() == "":
                处理后数据[字段] = 0.00
            else:
                try:
                    处理后数据[字段] = float(str(值).strip())
                except ValueError:
                    处理后数据[字段] = 0.00

        # 处理百分比字段（DECIMAL(5,4)）
        百分比字段 = ["佣金率", "冻结比例", "基础佣金率", "升佣佣金率", "推广费率"]

        for 字段 in 百分比字段:
            值 = row.get(字段)
            if pd.isna(值) or str(值).strip() == "":
                处理后数据[字段] = 0.0000
            else:
                值_str = str(值).strip()
                try:
                    if "%" in 值_str:
                        # 处理百分号格式 "10.00%" -> 0.1000
                        数值 = float(值_str.replace("%", "")) / 100
                    else:
                        数值 = float(值_str)
                    处理后数据[字段] = round(数值, 4)
                except ValueError:
                    处理后数据[字段] = 0.0000

        # 处理整数字段（INT）
        整数字段 = ["商品数量", "门槛销量", "阶梯计划ID", "营销活动id"]

        for 字段 in 整数字段:
            值 = row.get(字段)
            if pd.isna(值) or str(值).strip() == "":
                处理后数据[字段] = 0 if 字段 in ["商品数量", "门槛销量"] else None
            else:
                try:
                    处理后数据[字段] = int(float(str(值).strip()))
                except ValueError:
                    处理后数据[字段] = 0 if 字段 in ["商品数量", "门槛销量"] else None

        # 处理日期字段（DATETIME）
        日期字段 = ["付款时间", "收货时间", "订单结算时间", "尾款支付时间"]

        for 字段 in 日期字段:
            值 = row.get(字段)
            if pd.isna(值) or str(值).strip() == "" or str(值).strip() == "-":
                处理后数据[字段] = None
            else:
                try:
                    # 尝试解析日期
                    if isinstance(值, str):
                        处理后数据[字段] = pd.to_datetime(值).strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )
                    else:
                        处理后数据[字段] = pd.to_datetime(值).strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )
                except Exception:
                    处理后数据[字段] = None

        return 处理后数据

    def _修正Excel列名映射(self, df: "pd.DataFrame") -> "pd.DataFrame":
        """
        修正Excel列名映射

        参数:
            df: 原始DataFrame

        返回:
            修正后的DataFrame
        """
        # 字段映射字典：Excel列名 -> 标准列名
        列名映射 = {"抖音/火山号": "抖音火山号"}

        # 重命名列
        df = df.rename(columns=列名映射)

        接口日志器.debug(f"Excel列名映射修正完成，映射规则: {列名映射}")
        return df

    async def _处理达人关联逻辑(
        self, 订单id: int, 抖音火山号: str, 行号: int
    ) -> Optional[int]:
        """
        处理达人关联逻辑 - 优化版本

        新逻辑：
        1. 首先确保用户订单认领表中有对应的抖音号记录
        2. 然后尝试关联达人信息（如果找到的话）

        参数:
            订单id: 订单ID
            抖音火山号: 抖音火山号
            行号: Excel行号（用于日志）

        返回:
            Optional[int]: 用户订单认领表id，失败返回None
        """
        try:
            # 步骤1：检查用户订单认领表 - 查询是否已存在相同抖音号的认领记录（不考虑达人表id）
            接口日志器.debug(f"第{行号}行：检查用户订单认领表，抖音号={抖音火山号}")
            现有认领记录id = await self.数据层.检查用户订单认领记录存在_仅抖音号(
                抖音火山号
            )

            if 现有认领记录id:
                接口日志器.debug(f"第{行号}行：使用现有认领记录，id={现有认领记录id}")
            else:
                # 步骤2：创建新的认领记录（仅抖音号，达人表id为NULL）
                接口日志器.debug(f"第{行号}行：创建新的用户订单认领记录（仅抖音号）")
                现有认领记录id = await self.数据层.创建用户订单认领记录_仅抖音号(
                    抖音火山号
                )
                if not 现有认领记录id:
                    错误日志器.error(f"第{行号}行：创建用户订单认领记录失败")
                    return None
                接口日志器.debug(f"第{行号}行：创建新认领记录成功，id={现有认领记录id}")

            # 步骤3：查询达人表 - 根据抖音火山号在达人表中查找对应记录
            接口日志器.debug(f"第{行号}行：开始查询达人表，抖音火山号={抖音火山号}")
            达人id = await self.数据层.查找达人信息(抖音火山号)

            if 达人id:
                # 步骤4：更新认领记录的达人表id
                接口日志器.debug(
                    f"第{行号}行：找到达人记录，开始关联达人，达人id={达人id}"
                )
                更新结果 = await self.数据层.更新用户订单认领记录_达人表id(
                    现有认领记录id, 达人id
                )
                if 更新结果:
                    接口日志器.debug(f"第{行号}行：成功关联达人，达人id={达人id}")
                else:
                    错误日志器.error(f"第{行号}行：更新达人关联失败")
            else:
                接口日志器.debug(
                    f"第{行号}行：未找到匹配的达人记录，达人表id保持为NULL"
                )

            return 现有认领记录id

        except Exception as e:
            错误日志器.error(f"第{行号}行：处理达人关联逻辑失败: {str(e)}")
            return None

    async def _执行订单同步(self, 订单id: int, 抖音火山号: str) -> bool:
        """
        执行订单同步逻辑 - 已弃用，保留用于兼容性

        注意：此方法已被新的 _处理达人关联逻辑 方法替代，
        新的导入流程不再使用此方法。

        参数:
            订单id: 订单ID
            抖音火山号: 抖音火山号

        返回:
            bool: 同步是否成功
        """
        try:
            if not 抖音火山号 or str(抖音火山号).strip() == "":
                接口日志器.debug(f"订单{订单id}：抖音火山号为空，跳过同步")
                return False

            抖音火山号 = str(抖音火山号).strip()

            # 步骤A：查找达人信息
            达人id = await self.数据层.查找达人信息(抖音火山号)
            if not 达人id:
                接口日志器.debug(
                    f"订单{订单id}：未找到匹配的达人信息，抖音火山号={抖音火山号}"
                )
                return False

            # 步骤B：插入用户订单认领表（带重复性检查）
            接口日志器.debug(
                f"订单{订单id}：开始插入用户订单认领记录，抖音号={抖音火山号}, 达人id={达人id}"
            )
            认领记录id = await self.数据层.插入用户订单认领记录(抖音火山号, 达人id)
            if not 认领记录id:
                错误日志器.error(f"订单{订单id}：插入用户订单认领记录失败")
                return False

            接口日志器.debug(f"订单{订单id}：获得认领记录id={认领记录id}")

            # 步骤C：更新店铺订单表
            接口日志器.debug(
                f"订单{订单id}：开始更新订单认领关联，认领记录id={认领记录id}"
            )
            更新结果 = await self.数据层.更新订单认领关联(订单id, 认领记录id)
            if not 更新结果:
                错误日志器.error(f"订单{订单id}：更新订单认领关联失败")
                return False

            # 验证关联是否正确（调试用）
            验证结果 = await self.数据层.验证订单认领关联(订单id)
            接口日志器.debug(f"订单{订单id}：关联验证结果={验证结果}")

            接口日志器.info(
                f"订单同步成功：订单id={订单id}, 抖音火山号={抖音火山号}, 达人id={达人id}, 认领记录id={认领记录id}"
            )
            return True

        except Exception as e:
            错误日志器.error(f"订单同步失败：订单id={订单id}, 错误={str(e)}")
            return False

    async def 获取订单列表(
        self,
        用户id: int,
        页码: int = 1,
        每页数量: int = 20,
        店铺id: Optional[int] = None,
        订单状态: Optional[str] = None,
        商品名称: Optional[str] = None,
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取订单列表

        参数:
            用户id: 用户id
            页码: 页码
            每页数量: 每页数量
            店铺id: 店铺id筛选
            订单状态: 订单状态筛选
            开始时间: 开始时间筛选
            结束时间: 结束时间筛选

        返回:
            Dict: 订单列表数据
        """
        try:
            接口日志器.info(f"获取订单列表: 用户id={用户id}")

            订单列表, 总数 = await self.数据层.查询订单列表(
                用户id, 页码, 每页数量, 店铺id, 订单状态, 商品名称, 开始时间, 结束时间
            )

            # 格式化返回数据
            格式化订单列表 = []
            for 订单 in 订单列表:
                格式化订单 = self._格式化订单数据(订单)
                格式化订单列表.append(格式化订单)

            结果数据 = {
                "列表": 格式化订单列表,
                "总数": 总数,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": (总数 + 每页数量 - 1) // 每页数量,
            }

            return {
                "status": 通用.成功,
                "message": "获取订单列表成功",
                "data": 结果数据,
            }

        except Exception as e:
            错误日志器.error(f"获取订单列表失败: {str(e)}")
            return {"status": 通用.服务器错误, "message": f"获取订单列表失败: {str(e)}"}

    def _格式化订单数据(self, 订单: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化订单数据

        参数:
            订单: 原始订单数据

        返回:
            Dict: 格式化后的订单数据
        """
        return {
            "订单id": str(订单["订单id"]),
            "商品id": 订单["商品id"],
            "商品名称": 订单["商品名称"],
            "作者账号": 订单["作者账号"],
            "支付金额": float(订单["支付金额"]) if 订单["支付金额"] else 0.00,
            "佣金率": float(订单["佣金率"]) if 订单["佣金率"] else 0.0000,
            "预估佣金支出": float(订单["预估佣金支出"])
            if 订单["预估佣金支出"]
            else 0.00,
            "实际佣金支出": float(订单["实际佣金支出"])
            if 订单["实际佣金支出"]
            else 0.00,
            "订单状态": 订单["订单状态"],
            "付款时间": 订单["付款时间"].strftime("%Y-%m-%d %H:%M:%S")
            if 订单["付款时间"]
            else None,
            "收货时间": 订单["收货时间"].strftime("%Y-%m-%d %H:%M:%S")
            if 订单["收货时间"]
            else None,
            "店铺id": 订单["店铺id"],
            "店铺名称": 订单["店铺名称"],
            "商品数量": 订单["商品数量"],
            "创建时间": 订单["创建时间"].strftime("%Y-%m-%d %H:%M:%S")
            if 订单["创建时间"]
            else None,
        }

    async def 获取订单状态选项(self, 用户id: int) -> Dict[str, Any]:
        """
        获取订单状态选项

        参数:
            用户id: 用户id

        返回:
            Dict: 订单状态选项列表
        """
        try:
            接口日志器.info(f"获取订单状态选项: 用户id={用户id}")

            状态选项 = await self.数据层.获取订单状态选项(用户id)

            return {
                "status": 通用.成功,
                "message": "获取订单状态选项成功",
                "data": 状态选项,
            }

        except Exception as e:
            错误日志器.error(f"获取订单状态选项失败: {str(e)}")
            return {
                "status": 通用.服务器错误,
                "message": f"获取订单状态选项失败: {str(e)}",
            }

    async def 查询订单详情(self, 订单id: str, 用户id: int) -> Dict[str, Any]:
        """
        查询订单详情

        参数:
            订单id: 订单ID（字符串格式）
            用户id: 用户id

        返回:
            Dict: 订单详情信息
        """
        try:
            接口日志器.info(f"查询订单详情: 订单ID={订单id}, 用户id={用户id}")

            # 调用数据层查询订单详情
            订单详情 = await self.数据层.查询订单详情(订单id, 用户id)

            if not 订单详情:
                return {
                    "status": 通用.数据不存在,
                    "message": "订单不存在或无权限访问",
                }

            # 格式化返回数据
            格式化详情 = {
                # 基本信息
                "订单id": 订单详情.get("订单id"),
                "商品名称": 订单详情.get("商品名称"),
                "商品规格": 订单详情.get("商品规格"),
                "商品数量": 订单详情.get("商品数量"),
                "商品单价": float(订单详情.get("商品单价", 0)),
                "支付金额": float(订单详情.get("支付金额", 0)),
                "订单状态": 订单详情.get("订单状态"),
                # 时间信息
                "下单时间": 订单详情.get("下单时间").strftime("%Y-%m-%d %H:%M:%S")
                if 订单详情.get("下单时间")
                else None,
                "付款时间": 订单详情.get("付款时间").strftime("%Y-%m-%d %H:%M:%S")
                if 订单详情.get("付款时间")
                else None,
                # 佣金信息
                "佣金率": float(订单详情.get("佣金率", 0)),
                "预估佣金支出": float(订单详情.get("预估佣金支出", 0)),
                "实际佣金支出": float(订单详情.get("实际佣金支出", 0)),
                # 店铺信息
                "店铺id": 订单详情.get("店铺id"),
                "店铺名称": 订单详情.get("店铺名称"),
                # 达人信息
                "抖音火山号": 订单详情.get("抖音火山号"),
                "关联抖音号": 订单详情.get("关联抖音号"),
                "关联达人表id": 订单详情.get("关联达人表id"),
                # 其他信息
                "商品id": 订单详情.get("商品id"),
                "作者账号": 订单详情.get("作者账号"),
                "推广位": 订单详情.get("推广位"),
                "推广位名称": 订单详情.get("推广位名称"),
            }

            return {
                "status": 通用.成功,
                "message": "查询订单详情成功",
                "data": 格式化详情,
            }

        except Exception as e:
            错误日志器.error(
                f"查询订单详情失败: 订单ID={订单id}, 用户id={用户id}, 错误={str(e)}"
            )
            return {
                "status": 通用.服务器错误,
                "message": f"查询订单详情失败: {str(e)}",
            }

    async def 查询导入进度(self, 任务ID: str, 用户id: int) -> Dict[str, Any]:
        """
        查询导入任务进度

        参数:
            任务ID: 导入任务ID
            用户id: 用户id（用于权限验证）

        返回:
            Dict: 导入进度信息
        """
        try:
            接口日志器.info(f"查询导入进度: 任务ID={任务ID}, 用户id={用户id}")

            # 查询导入记录
            导入记录 = await self.数据层.查询导入记录(任务ID, 用户id)

            if not 导入记录:
                return {
                    "status": 通用.记录不存在,
                    "message": "导入任务不存在或无权限访问",
                }

            # 格式化返回数据
            进度信息 = {
                "任务ID": 导入记录["任务ID"],
                "任务状态": 导入记录["任务状态"],
                "文件名": 导入记录["文件名"],
                "总行数": 导入记录["总行数"],
                "成功数量": 导入记录["成功数量"],
                "失败数量": 导入记录["失败数量"],
                "跳过数量": 导入记录["跳过数量"],
                "进度百分比": float(导入记录["进度百分比"]),
                "开始时间": 导入记录["开始时间"].strftime("%Y-%m-%d %H:%M:%S")
                if 导入记录["开始时间"]
                else None,
                "完成时间": 导入记录["完成时间"].strftime("%Y-%m-%d %H:%M:%S")
                if 导入记录["完成时间"]
                else None,
                "错误信息": 导入记录["错误信息"],
            }

            return {
                "status": 通用.成功,
                "message": "查询导入进度成功",
                "data": 进度信息,
            }

        except Exception as e:
            错误日志器.error(f"查询导入进度失败: {str(e)}")
            return {"status": 通用.服务器错误, "message": f"查询导入进度失败: {str(e)}"}

    async def 获取任务管理器状态(self) -> Dict[str, Any]:
        """
        获取任务管理器状态信息

        返回:
            Dict: 任务管理器状态信息
        """
        try:
            任务状态 = await 任务管理器.获取活跃任务状态()

            return {
                "status": 通用.成功,
                "message": "获取任务状态成功",
                "data": 任务状态,
            }

        except Exception as e:
            错误日志器.error(f"获取任务管理器状态失败: {str(e)}")
            return {"status": 通用.服务器错误, "message": f"获取任务状态失败: {str(e)}"}

    async def 续传导入任务(self, 导入记录ID: int, 用户id: int) -> Dict[str, Any]:
        """
        续传超时的导入任务

        参数:
            导入记录ID: 导入记录ID
            用户id: 用户id

        返回:
            Dict: 续传结果
        """
        try:
            # 查询导入记录
            导入记录 = await self.数据层.查询导入记录_通过ID(导入记录ID, 用户id)
            if not 导入记录:
                return {"status": 通用.参数错误, "message": "导入记录不存在"}

            # 检查是否可以续传
            if 导入记录["任务状态"] != "超时":
                return {"status": 通用.参数错误, "message": "只有超时的任务才能续传"}

            if not 导入记录.get("可续传") or not 导入记录.get("文件路径"):
                return {"status": 通用.参数错误, "message": "该任务不支持续传"}

            # 检查文件是否存在
            import os

            if not os.path.exists(导入记录["文件路径"]):
                return {"status": 通用.参数错误, "message": "原始文件已丢失，无法续传"}

            # 生成新的任务ID
            新任务ID = str(uuid.uuid4())

            # 检查是否可以获取任务许可
            if not await 任务管理器.获取任务许可(新任务ID):
                return {
                    "status": 通用.操作失败,
                    "message": "当前导入任务过多，请稍后再试",
                }

            try:
                # 更新任务状态为进行中
                await self.数据层.更新导入记录状态(
                    导入记录ID, "进行中", 导入记录["进度百分比"]
                )

                # 获取续传起始批次
                起始批次 = 导入记录.get("当前批次", 0)

                # 启动续传任务
                asyncio.create_task(
                    self._执行分批导入任务(
                        新任务ID, 导入记录["文件路径"], 用户id, 导入记录ID, 起始批次
                    )
                )

                接口日志器.info(
                    f"续传任务启动成功: 原记录ID={导入记录ID}, 新任务ID={新任务ID}, 起始批次={起始批次}"
                )

                return {
                    "status": 通用.成功,
                    "message": "续传任务启动成功",
                    "data": {
                        "任务ID": 新任务ID,
                        "导入记录ID": 导入记录ID,
                        "起始批次": 起始批次,
                        "已处理行数": 导入记录.get("已处理行数", 0),
                    },
                }

            except Exception as task_error:
                # 释放任务许可
                await 任务管理器.释放任务许可(新任务ID)
                raise task_error

        except Exception as e:
            错误日志器.error(
                f"续传导入任务失败: 导入记录ID={导入记录ID}, 错误: {str(e)}"
            )
            return {"status": 通用.服务器错误, "message": f"续传失败: {str(e)}"}

    async def 获取导入记录列表(
        self, 用户id: int, 页码: int = 1, 每页数量: int = 20, 状态筛选: str = None
    ) -> Dict[str, Any]:
        """
        获取用户的导入记录列表
        """
        try:
            接口日志器.info(f"获取导入记录列表: 用户id={用户id}")

            记录列表, 总数 = await self.数据层.查询导入记录列表(
                用户id, 页码, 每页数量, 状态筛选
            )

            # 格式化返回数据
            格式化记录列表 = []
            for 记录 in 记录列表:
                格式化记录 = {
                    "id": 记录["id"],  # 续传接口需要的记录ID
                    "任务ID": 记录["任务ID"],
                    "文件名": 记录["文件名"],
                    "任务状态": 记录["任务状态"],
                    "总行数": 记录["总行数"],
                    "成功数量": 记录["成功数量"],
                    "失败数量": 记录["失败数量"],
                    "跳过数量": 记录["跳过数量"],
                    "进度百分比": float(记录["进度百分比"]),
                    "开始时间": 记录["开始时间"].strftime("%Y-%m-%d %H:%M:%S")
                    if 记录["开始时间"]
                    else None,
                    "完成时间": 记录["完成时间"].strftime("%Y-%m-%d %H:%M:%S")
                    if 记录["完成时间"]
                    else None,
                    "错误信息": 记录["错误信息"],
                    "可续传": bool(记录["可续传"]),  # 前端判断续传按钮显示的关键字段
                    "已处理行数": 记录["已处理行数"] or 0,  # 续传确认弹窗显示用
                    "当前批次": 记录["当前批次"] or 0,  # 续传逻辑需要的起始批次
                }
                格式化记录列表.append(格式化记录)

            结果数据 = {
                "列表": 格式化记录列表,
                "总数": 总数,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": (总数 + 每页数量 - 1) // 每页数量,
            }

            return {
                "status": 通用.成功,
                "message": "获取导入记录列表成功",
                "data": 结果数据,
            }

        except Exception as e:
            错误日志器.error(f"获取导入记录列表失败: {str(e)}")
            return {
                "status": 通用.服务器错误,
                "message": f"获取导入记录列表失败: {str(e)}",
            }


# 创建全局实例
店铺订单服务实例 = 店铺订单服务()
