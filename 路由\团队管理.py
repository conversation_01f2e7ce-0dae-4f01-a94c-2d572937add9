from typing import List, Literal, Optional

from fastapi import APIRouter, Body, Depends, Request
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户
from 数据.公司数据 import 创建公司 as 数据层创建公司
from 数据.公司数据 import 数据层更新公司
from 数据.公司数据 import 获取公司列表 as 数据层获取公司列表
from 数据.团队成员数据 import 异步更新成员角色和权限
from 数据.团队数据看板 import 创建团队 as 数据层创建团队
from 数据.团队数据看板 import (
    加入团队,
    获取团队操作日志,
    踢出团队成员,
)
from 数据.团队数据看板 import 获取团队成员列表 as 数据层获取团队成员列表
from 数据.团队数据看板 import 获取团队详情 as 数据层获取团队详情
from 数据.团队数据看板 import 获取用户团队列表 as 数据层获取用户团队列表
from 数据.团队数据看板 import 获取用户团队统计 as 数据层获取用户团队统计
from 数据.团队数据看板 import 解散团队 as 数据层解散团队
from 数据.团队权限数据 import 获取用户团队权限状态
from 数据.用户 import 异步用户是否存在_id
from 数据.异步邀请管理 import 异步通过手机号邀请成员
from 数据模型.响应模型 import 统一响应模型
from 日志 import 接口日志器, 错误日志器
from 服务.团队达人服务 import (
    获取团队微信达人列表服务,
    获取团队微信达人统计服务,
    获取团队达人列表服务,
    获取团队达人统计服务,
    获取团队达人详情服务,
    获取团队达人详细分析服务,
)
from 状态 import 状态


# 统一权限检查函数，减少冗余代码
async def 检查团队访问权限(
    团队id: int, 用户id: int, 错误消息: str = "无权限访问该团队"
):
    """
    统一的团队访问权限检查函数

    Args:
        团队id: 团队标识
        用户id: 用户标识
        错误消息: 自定义错误消息

    Returns:
        tuple: (是否有权限, 响应模型或None)
    """
    try:
        团队详情 = await 数据层获取团队详情(团队id, 用户id)
        if not 团队详情["success"]:
            return False, 统一响应模型.失败(状态码=403, 消息=错误消息)
        return True, None
    except Exception as e:
        错误日志器.error(f"权限检查异常: 团队id={团队id}, 用户id={用户id}, 错误={e}")
        return False, 统一响应模型.失败(状态码=500, 消息="权限检查失败")


# 创建路由
团队管理路由 = APIRouter(tags=["团队管理"])

# =============== 请求模型定义 ===============


# 公司相关模型
class 公司创建请求(BaseModel):
    公司名称: str = Field(..., min_length=1, max_length=255, description="公司名称")
    公司简称: Optional[str] = Field(None, max_length=100, description="公司简称")
    公司代码: Optional[str] = Field(None, max_length=50, description="公司代码")
    公司地址: Optional[str] = Field(None, description="公司地址")
    联系电话: Optional[str] = Field(None, max_length=50, description="联系电话")
    邮箱: Optional[str] = Field(None, max_length=255, description="公司邮箱")
    法人代表: Optional[str] = Field(None, max_length=100, description="法人代表")
    营业执照号: Optional[str] = Field(None, max_length=100, description="营业执照号")
    备注: Optional[str] = Field(None, description="备注信息")


class 公司更新请求(BaseModel):
    公司ID: int = Field(..., gt=0, description="公司ID")
    公司名称: str = Field(..., min_length=1, max_length=255, description="公司名称")
    公司简称: Optional[str] = Field(None, max_length=100, description="公司简称")
    公司代码: Optional[str] = Field(None, max_length=50, description="公司代码")
    公司地址: Optional[str] = Field(None, description="公司地址")
    联系电话: Optional[str] = Field(None, max_length=50, description="联系电话")
    邮箱: Optional[str] = Field(None, max_length=255, description="公司邮箱")
    法人代表: Optional[str] = Field(None, max_length=100, description="法人代表")
    营业执照号: Optional[str] = Field(None, max_length=100, description="营业执照号")
    备注: Optional[str] = Field(None, description="备注信息")


class 公司列表请求(BaseModel):
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    公司状态: Optional[str] = Field(None, description="公司状态")
    审核状态: Optional[str] = Field(
        None, description="审核状态：待审核、已批准、已拒绝"
    )


# 团队相关模型
class 团队创建请求(BaseModel):
    团队名称: str = Field(..., min_length=1, max_length=255, description="团队名称")
    公司ID: int = Field(..., gt=0, description="所属公司ID")
    团队代码: Optional[str] = Field(None, max_length=50, description="团队代码")
    团队描述: Optional[str] = Field(None, description="团队描述")
    团队负责人id: Optional[int] = Field(None, gt=0, description="团队负责人id")
    最大成员数: int = Field(100, ge=1, le=1000, description="最大成员数")
    备注: Optional[str] = Field(None, description="备注信息")


class 团队成员加入请求(BaseModel):
    用户id: int = Field(..., gt=0, description="用户id")
    团队id: int = Field(..., gt=0, description="团队id")
    职位: Optional[str] = Field(None, max_length=100, description="职位")


class 团队成员列表请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    成员状态: Optional[str] = Field(None, description="成员状态")
    角色筛选: Optional[str] = Field(
        None, description="角色筛选：创始人、负责人、成员等"
    )


class 用户团队列表请求(BaseModel):
    用户id: Optional[int] = Field(
        None, gt=0, description="用户id，不传则获取当前用户的团队"
    )
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(10, ge=1, le=100, description="每页数量")
    团队关系类型: Optional[str] = Field(
        None,
        description="团队关系类型：all-全部，created-我创建的，managed-我管理的，joined-我所在的",
    )
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    公司ID: Optional[int] = Field(None, gt=0, description="公司ID筛选")


class 用户团队统计请求(BaseModel):
    用户id: Optional[int] = Field(
        None, gt=0, description="用户id，不传则获取当前用户的统计"
    )


class 团队解散请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")


class 踢出团队成员请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    用户id: int = Field(..., gt=0, description="被踢出的用户id")
    移除原因: Optional[str] = Field(None, description="移除原因")


class 退出团队请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")


class 团队详情请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    包含成员统计: bool = Field(True, description="是否包含成员统计信息")
    包含权限信息: bool = Field(True, description="是否包含用户权限信息")


class 团队邀请请求(BaseModel):
    手机号: str = Field(
        ..., min_length=11, max_length=11, description="被邀请用户手机号"
    )
    团队id: int = Field(..., gt=0, description="团队id")
    角色类型: str = Field("member", description="角色类型：leader-负责人，member-成员")
    权限列表: Optional[List[str]] = Field([], description="权限代码列表")


class 成员角色更新请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    目标用户id: int = Field(..., gt=0, description="要更新角色的用户id")
    新角色: Literal["团队负责人", "成员"] = Field(..., description="要设置的新角色")


class 成员权限详情请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    用户id: int = Field(..., gt=0, description="要查询权限的用户id")


class 团队数据看板聚合请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: Optional[str] = Field("本周", description="时间范围（本周、本月等）")
    包含模块: Optional[List[str]] = Field(None, description="包含的模块列表")


class 团队业务模块请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: Optional[str] = Field(
        "本周",
        description="时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）",
    )


class 团队成员排名请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: Optional[str] = Field(
        "本周",
        description="时间范围（昨日、今日、本周、上周、本月、上月、本季度、上季度）",
    )
    排序方式: Optional[str] = Field(
        "好友数量", description="排序方式：好友数量、今日添加、达人沟通等"
    )
    限制数量: Optional[int] = Field(
        10, ge=1, le=50, description="返回成员数量，默认10个"
    )


class 团队成员详细绩效请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: Optional[str] = Field("本周", description="时间范围（本周、本月等）")
    包含排行榜: Optional[bool] = Field(True, description="是否包含排行榜数据")
    包含个人详情: Optional[bool] = Field(True, description="是否包含成员个人详情数据")


# 添加智能退出团队请求模型
class 智能退出团队请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    确认解散: bool = Field(False, description="如果是单人团队，是否确认解散")


# 添加转移所有权请求模型
class 转移所有权请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    新所有者用户id: int = Field(..., gt=0, description="新所有者的用户id")
    转移后退出: bool = Field(False, description="转移后是否立即退出团队")


# 团队达人相关请求模型
class 团队达人统计请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field("30d", description="时间范围：7d、30d、90d、all")
    包含非活跃: bool = Field(False, description="是否包含非活跃达人")


class 团队达人列表请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    成员id: Optional[int] = Field(None, gt=0, description="按成员筛选")
    关键词: Optional[str] = Field(None, description="搜索关键词")
    排序字段: str = Field("认领时间", description="排序字段")
    排序方式: str = Field("desc", description="排序方式：asc、desc")
    平台: str = Field("douyin", description="平台类型：douyin（抖音）、wechat（微信）")





class 团队达人详细分析请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field("30d", description="分析时间范围：7d、30d、90d、1y")


class 团队达人详情请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    达人id: int = Field(..., gt=0, description="达人id")


# 团队微信达人相关请求模型
class 团队微信达人统计请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    时间范围: str = Field("30d", description="时间范围：7d、30d、90d、all")
    包含非活跃: bool = Field(False, description="是否包含非活跃达人")


class 团队微信达人列表请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    成员id: Optional[int] = Field(None, gt=0, description="按成员筛选")
    关键词: Optional[str] = Field(None, description="搜索关键词")
    状态筛选: Optional[str] = Field(None, description="达人状态筛选")
    排序字段: str = Field("认领时间", description="排序字段")
    排序方式: str = Field("desc", description="排序方式：asc/desc")


# =============== 公司审核管理接口 ===============

# =============== 公司管理接口 ===============


@团队管理路由.post(
    "/company/create",
    response_model=统一响应模型,
    summary="创建公司",
    description="创建新的公司信息",
)
async def 创建公司(
    请求数据: 公司创建请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """创建公司接口"""
    try:
        创建人id = 当前用户["id"]

        结果 = await 数据层创建公司(
            公司名称=请求数据.公司名称,
            创建人id=创建人id,
            公司简称=请求数据.公司简称,
            公司代码=请求数据.公司代码,
            公司地址=请求数据.公司地址,
            联系电话=请求数据.联系电话,
            邮箱=请求数据.邮箱,
            法人代表=请求数据.法人代表,
            营业执照号=请求数据.营业执照号,
            备注=请求数据.备注,
        )

        if 结果["success"]:
            接口日志器.info(f"用户 {创建人id} 创建公司成功: {请求数据.公司名称}")
            return 统一响应模型.成功(
                数据={"company_id": 结果["company_id"]}, 消息=结果["message"]
            )
        else:
            # 使用业务状态码而不是HTTP状态码
            业务状态码 = 结果.get("status", 状态.公司管理.创建失败)
            return 统一响应模型.失败(状态码=业务状态码, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(f"创建公司接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.公司管理.创建失败, 消息="创建公司失败")


@团队管理路由.post(
    "/company/list",
    response_model=统一响应模型,
    summary="获取公司列表",
    description="分页获取用户创建的公司列表，支持搜索和筛选",
)
async def 获取公司列表接口(
    请求数据: 公司列表请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    try:
        # 只获取当前用户创建的公司
        当前用户id = 当前用户["id"]
        接口日志器.info(
            f"用户 {当前用户id} 请求获取公司列表，参数: {请求数据.model_dump()}"
        )

        # 转换审核状态为整数类型
        审核状态_int = None
        if 请求数据.审核状态:
            try:
                审核状态_int = int(请求数据.审核状态)
            except (ValueError, TypeError):
                # 如果转换失败，保持为None
                审核状态_int = None

        结果 = await 数据层获取公司列表(
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            搜索关键词=请求数据.搜索关键词,
            公司状态=请求数据.公司状态,
            审核状态=审核状态_int,
            创建人id=当前用户id,  # 关键修改：只返回当前用户创建的公司
        )

        接口日志器.info(
            f"用户 {当前用户id} 获取公司列表成功，返回 {len(结果.get('list', []))} 个公司"
        )
        return 统一响应模型.成功(数据=结果)
    except Exception as e:
        import traceback

        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"获取公司列表接口异常 - 请求参数: {请求数据.model_dump()}, 当前用户: {当前用户.get('id')}, 错误: {e}\n详细错误信息:\n{错误详情}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态码=500, 消息=f"获取公司列表失败: {str(e)}")


@团队管理路由.put(
    "/company/update",
    response_model=统一响应模型,
    summary="更新公司信息",
    description="更新公司基本信息，支持重新提交审核",
)
async def 更新公司接口(
    request: Request,
    请求数据: 公司更新请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    try:
        当前用户id = 当前用户["id"]
        公司ID = 请求数据.公司ID

        接口日志器.info(f"用户 {当前用户id} 请求更新公司 {公司ID}: {请求数据.公司名称}")

        # 调用数据层更新公司
        结果 = await 数据层更新公司(
            公司ID=公司ID,
            公司名称=请求数据.公司名称,
            公司简称=请求数据.公司简称,
            公司代码=请求数据.公司代码,
            公司地址=请求数据.公司地址,
            联系电话=请求数据.联系电话,
            邮箱=请求数据.邮箱,
            法人代表=请求数据.法人代表,
            营业执照号=请求数据.营业执照号,
            备注=请求数据.备注,
            更新人ID=当前用户id,
        )

        if 结果["success"]:
            接口日志器.info(f"用户 {当前用户id} 更新公司成功: {请求数据.公司名称}")
            return 统一响应模型.成功(数据={"company_id": 公司ID}, 消息=结果["message"])
        else:
            # 使用业务状态码而不是HTTP状态码
            业务状态码 = 结果.get("status", 状态.公司管理.创建失败)
            return 统一响应模型.失败(状态码=业务状态码, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(f"更新公司接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.公司管理.创建失败, 消息="更新公司失败")


# =============== 团队管理接口 ===============


@团队管理路由.post(
    "/create",
    response_model=统一响应模型,
    summary="创建团队",
    description="创建新的团队",
)
async def 创建团队(
    request: Request,
    请求数据: 团队创建请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """创建团队接口"""
    try:
        创建人id = 当前用户["id"]

        # 验证团队负责人id是否存在
        if 请求数据.团队负责人id:
            if not await 异步用户是否存在_id(请求数据.团队负责人id):
                return 统一响应模型.失败(
                    状态码=404, 消息=f"团队负责人用户id {请求数据.团队负责人id} 不存在"
                )

        结果 = await 数据层创建团队(
            团队名称=请求数据.团队名称,
            公司ID=请求数据.公司ID,
            创建人id=创建人id,
            团队代码=请求数据.团队代码,
            团队描述=请求数据.团队描述,
            团队负责人id=请求数据.团队负责人id,
            备注=请求数据.备注,
        )

        if 结果["success"]:
            接口日志器.info(f"用户 {创建人id} 创建团队成功: {请求数据.团队名称}")
            return 统一响应模型.成功(
                数据={"team_id": 结果["team_id"]}, 消息=结果["message"]
            )
        else:
            # 使用业务状态码而不是HTTP状态码
            业务状态码 = 结果.get("status", 状态.团队管理.创建失败)
            return 统一响应模型.失败(状态码=业务状态码, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(f"创建团队接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.团队管理.创建失败, 消息="创建团队失败")


@团队管理路由.post(
    "/detail",
    response_model=统一响应模型,
    summary="获取团队详情",
    description="获取团队详细信息，包括基本信息、成员统计、用户权限等",
)
async def 获取团队详情接口(
    request: Request,
    请求数据: 团队详情请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """获取团队详情接口（优化版）"""
    try:
        用户id = 当前用户["id"]
        团队id = 请求数据.团队id

        # 调用优化后的数据层函数
        结果 = await 数据层获取团队详情(
            团队id=团队id,
            用户id=用户id,
            包含成员统计=请求数据.包含成员统计,
            包含权限信息=请求数据.包含权限信息,
        )

        # 统一处理响应
        if 结果["success"]:
            接口日志器.info(f"用户 {用户id} 获取团队详情成功: 团队id={团队id}")
            return 统一响应模型.成功(数据=结果["data"])

        # 错误处理映射
        错误代码映射 = {
            "INVALID_TEAM_ID": (400, "团队id格式无效"),
            "TEAM_NOT_FOUND": (404, "团队不存在"),
            "TEAM_DISSOLVED": (410, "团队已解散"),
            "USER_NOT_IN_TEAM": (403, "无权限访问该团队"),
            "SYSTEM_ERROR": (500, "系统异常，请稍后重试"),
        }

        错误代码 = 结果.get("code", "UNKNOWN_ERROR")
        状态码, 默认消息 = 错误代码映射.get(错误代码, (400, "请求失败"))
        错误消息 = 结果.get("message", 默认消息)

        接口日志器.warning(
            f"用户 {用户id} 获取团队详情失败: 团队id={团队id}, 错误={错误代码}"
        )
        return 统一响应模型.失败(状态码=状态码, 消息=错误消息)

    except Exception as e:
        团队id = getattr(请求数据, "团队id", "unknown")
        用户id = 当前用户.get("id", "unknown")
        错误日志器.error(
            f"获取团队详情接口异常: 团队id={团队id}, 用户id={用户id}, 错误={e}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队详情失败，系统异常")


@团队管理路由.post(
    "/member/join",
    response_model=统一响应模型,
    summary="邀请成员加入团队",
    description="邀请用户加入指定团队",
)
async def 邀请成员加入团队(
    request: Request,
    请求数据: 团队成员加入请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """邀请成员加入团队接口"""
    try:
        # 权限检查等逻辑
        结果 = await 加入团队(
            用户id=请求数据.用户id,
            团队id=请求数据.团队id,
            职位=请求数据.职位,
            邀请人id=当前用户["id"],
            审批人ID=当前用户["id"],
        )

        if 结果["success"]:
            return 统一响应模型.成功(消息=结果["message"])
        else:
            return 统一响应模型.失败(状态码=400, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(f"邀请成员加入团队接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="邀请成员失败")


@团队管理路由.post(
    "/members",
    response_model=统一响应模型,
    summary="获取团队成员列表",
    description="分页获取团队成员列表，支持搜索和筛选",
)
async def 获取团队成员列表(
    request: Request,
    请求数据: 团队成员列表请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """获取团队成员列表接口"""
    try:
        # 添加详细的调试日志
        接口日志器.info("=== 获取团队成员列表接口 开始 ===")
        接口日志器.info(f"当前用户id: {当前用户.get('id')}")
        接口日志器.info(
            f"请求参数: 团队id={请求数据.团队id}, 页码={请求数据.页码}, 每页数量={请求数据.每页数量}, 搜索关键词={请求数据.搜索关键词}, 成员状态={请求数据.成员状态}, 角色筛选={请求数据.角色筛选}"
        )

        结果 = await 数据层获取团队成员列表(
            团队id=请求数据.团队id,
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            搜索关键词=请求数据.搜索关键词,
            成员状态=请求数据.成员状态,
            角色筛选=请求数据.角色筛选,
            当前用户id=当前用户["id"],
        )

        接口日志器.info(
            f"数据层返回结果: 成员数量={len(结果.get('成员列表', []))}, 总数={结果.get('总数', 0)}"
        )
        接口日志器.info("=== 获取团队成员列表接口 结束 ===")

        return 统一响应模型.成功(数据=结果)

    except Exception as e:
        import traceback

        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"获取团队成员列表接口异常: 团队id={请求数据.团队id}, 用户id={当前用户.get('id')}, 错误={e}\n详细错误信息:\n{错误详情}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态码=500, 消息="获取团队成员失败")


@团队管理路由.post(
    "/user/teams",
    response_model=统一响应模型,
    summary="获取用户团队列表",
    description="获取用户参与的团队列表，支持团队关系类型筛选",
)
async def 获取用户团队列表(
    request: Request,
    请求数据: 用户团队列表请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """获取用户团队列表接口"""
    try:
        # 增加详细的调试日志
        接口日志器.info("=== 获取用户团队列表接口 开始 ===")
        接口日志器.info(
            f"当前用户信息: ID={当前用户.get('id')}, phone={当前用户.get('phone')}"
        )
        接口日志器.info(f"请求数据: {请求数据.model_dump()}")

        用户id = 请求数据.用户id or 当前用户["id"]
        接口日志器.info(
            f"确定的用户id: {用户id} (来源: {'请求参数' if 请求数据.用户id else '当前用户'})"
        )

        # 验证用户id的有效性
        if not 用户id or 用户id <= 0:
            接口日志器.error(f"无效的用户id: {用户id}")
            return 统一响应模型.失败(状态码=400, 消息="无效的用户id")

        接口日志器.info(
            f"调用数据层函数 - 参数: 用户id={用户id}, 页码={请求数据.页码}, 每页数量={请求数据.每页数量}, 团队关系类型={请求数据.团队关系类型}, 搜索关键词={请求数据.搜索关键词}, 公司ID={请求数据.公司ID}"
        )

        结果 = await 数据层获取用户团队列表(
            用户id=用户id,
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            团队关系类型=请求数据.团队关系类型,
            搜索关键词=请求数据.搜索关键词,
            公司ID=请求数据.公司ID,
        )

        接口日志器.info(
            f"数据层返回结果: 团队数量={len(结果.get('团队列表', []))}, 总数={结果.get('总数', 0)}"
        )
        接口日志器.info("=== 获取用户团队列表接口 结束 ===")

        # 数据层已返回标准格式，直接使用
        return 统一响应模型.成功(数据=结果)

    except Exception as e:
        import traceback

        错误详情 = traceback.format_exc()
        错误日志器.error(
            f"获取用户团队列表接口异常 - 用户id: {请求数据.用户id}, 当前用户: {当前用户.get('id')}, 错误: {e}\n详细错误信息:\n{错误详情}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态码=500, 消息=f"获取用户团队列表失败: {str(e)}")


@团队管理路由.post(
    "/user/stats",
    response_model=统一响应模型,
    summary="获取用户团队统计",
    description="获取用户团队关系统计数据",
)
async def 获取用户团队统计(
    request: Request,
    请求数据: 用户团队统计请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """获取用户团队统计接口"""
    try:
        用户id = 请求数据.用户id or 当前用户["id"]
        结果 = await 数据层获取用户团队统计(用户id)
        return 统一响应模型.成功(数据=结果)
    except Exception as e:
        错误日志器.error(f"获取用户团队统计接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户团队统计失败")


@团队管理路由.post(
    "/leave",
    response_model=统一响应模型,
    summary="退出团队",
    description="用户退出指定的团队，创建者无法退出，需要先转移所有权",
)
async def 退出团队(
    request: Request,
    请求数据: 退出团队请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """用户退出团队接口"""
    try:
        操作人ID = 当前用户["id"]
        团队id = 请求数据.团队id

        # 权限验证
        权限状态 = await 获取用户团队权限状态(团队id, 操作人ID)
        if not 权限状态 or not 权限状态.get("在团队中"):
            return 统一响应模型.失败(状态码=403, 消息="您不在该团队中，无法退出")

        # 创建者不能退出，必须先转移所有权或解散团队
        if 权限状态.get("是否团队创建者"):
            return 统一响应模型.失败(
                状态码=400, 消息="团队创建者不能退出，请先转移所有权或解散团队"
            )

        # 使用踢出成员的逻辑来处理退出
        结果 = await 踢出团队成员(
            团队id=团队id,
            被踢出用户id=操作人ID,
            操作人ID=操作人ID,  # 自己操作自己
            踢出原因="用户主动退出",
        )

        if 结果["success"]:
            接口日志器.info(f"用户 {操作人ID} 主动退出团队 {团队id}")
            return 统一响应模型.成功(消息="您已成功退出团队")
        else:
            return 统一响应模型.失败(状态码=400, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(f"退出团队接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="退出团队失败")


@团队管理路由.post(
    "/dissolve",
    response_model=统一响应模型,
    summary="解散团队",
    description="解散指定的团队，只有拥有删除团队权限的成员可以执行此操作",
)
async def 解散团队(
    request: Request,
    请求数据: 团队解散请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """解散团队接口"""
    try:
        操作人ID = 当前用户["id"]
        团队id = 请求数据.团队id

        # 权限验证
        权限状态 = await 获取用户团队权限状态(团队id, 操作人ID)
        if not 权限状态 or not 权限状态.get("能否删除团队"):
            return 统一响应模型.失败(状态码=403, 消息="您没有权限解散该团队")

        结果 = await 数据层解散团队(团队id, 操作人ID)

        if 结果["success"]:
            接口日志器.info(f"用户 {操作人ID} 成功解散团队 {团队id}")
            return 统一响应模型.成功(消息=结果["message"])
        else:
            return 统一响应模型.失败(状态码=400, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(f"解散团队接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="解散团队失败")


@团队管理路由.post(
    "/invite",
    response_model=统一响应模型,
    summary="统一邀请接口",
    description="智能邀请接口，自动判断用户注册状态并生成相应的邀请链接",
)
async def 统一邀请接口(
    request: Request,
    请求数据: 团队邀请请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    统一邀请接口 - 智能处理已注册和未注册用户
    已注册用户：生成确认链接，登录后确认加入团队
    未注册用户：生成注册邀请链接，注册后直接登录并确认加入团队
    """
    try:
        # 简化的角色类型映射
        角色映射 = {"leader": "负责人", "member": "成员"}

        # 安全检查：邀请时不允许设置为创建者
        if 请求数据.角色类型 in ["founder", "creator", "创建者"]:
            return 统一响应模型.失败(状态码=400, 消息="邀请成员时不能设置为创建者角色")

        角色 = 角色映射.get(请求数据.角色类型, "成员")

        结果 = await 异步通过手机号邀请成员(
            手机号=请求数据.手机号,
            团队id=请求数据.团队id,
            邀请人id=当前用户["id"],
            角色=角色,
            权限列表=请求数据.权限列表,
        )

        if 结果["success"]:
            接口日志器.info(
                f"用户 {当前用户['id']} 邀请 {请求数据.手机号} 加入团队 {请求数据.团队id}"
            )
            return 统一响应模型.成功(数据=结果.get("data"), 消息=结果["message"])
        else:
            return 统一响应模型.失败(状态码=400, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(
            f"统一邀请接口异常 - 手机号: {请求数据.手机号}, 团队id: {请求数据.团队id}, 错误: {e}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态码=500, 消息="邀请发送失败")


@团队管理路由.post(
    "/members/update-role",
    response_model=统一响应模型,
    summary="更新成员角色和权限",
    description="更新指定团队成员的角色，并自动重新初始化其权限。仅限团队创建者操作。",
)
async def 更新成员角色接口(
    request: Request,
    请求数据: 成员角色更新请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """更新成员角色和权限的接口"""
    try:
        操作人ID = 当前用户["id"]

        结果 = await 异步更新成员角色和权限(
            团队id=请求数据.团队id,
            目标用户id=请求数据.目标用户id,
            新角色=请求数据.新角色,
            操作人ID=操作人ID,
        )

        if 结果.get("success"):
            接口日志器.info(
                f"用户 {操作人ID} 成功更新了用户 {请求数据.目标用户id} 的角色为 {请求数据.新角色}"
            )
            return 统一响应模型.成功(消息=结果.get("message", "更新成员角色成功"))
        else:
            错误日志器.warning(
                f"用户 {操作人ID} 更新角色失败: {结果.get('message')}, "
                f"请求: {请求数据.model_dump()}"
            )
            return 统一响应模型.失败(
                状态码=400, 消息=结果.get("message", "更新成员角色失败")
            )

    except Exception as e:
        错误日志器.error(f"更新成员角色接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="更新成员角色时发生内部错误")


@团队管理路由.post(
    "/member/permissions/detail",
    response_model=统一响应模型,
    summary="获取成员权限详情",
    description="获取指定团队成员的详细权限信息",
)
async def 获取成员权限详情接口(
    request: Request,
    请求数据: 成员权限详情请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取成员权限详情接口
    - 验证操作者是否有查看权限
    - 返回成员的详细权限列表
    """
    try:
        操作者ID = 当前用户["id"]

        # 检查操作者是否有权限查看该团队成员信息
        权限状态 = await 获取用户团队权限状态(请求数据.团队id, 操作者ID)

        if not 权限状态:
            return 统一响应模型.失败(
                状态码=403, 消息="您不是该团队成员，无权查看此信息"
            )

        # 检查是否有查看权限（管理员或查看自己的权限）
        是否可查看 = (
            权限状态.get("可以管理成员", False)  # 有管理权限
            or 操作者ID == 请求数据.用户id  # 查看自己的权限
        )

        if not 是否可查看:
            return 统一响应模型.失败(状态码=403, 消息="您没有查看该成员权限的权限")

        # 获取成员权限详情
        成员权限状态 = await 获取用户团队权限状态(请求数据.团队id, 请求数据.用户id)

        if 成员权限状态:
            return 统一响应模型.成功(数据=成员权限状态)
        else:
            return 统一响应模型.失败(状态码=404, 消息="成员权限信息不存在")

    except Exception as e:
        错误日志器.error(
            f"获取成员权限详情接口异常 - 团队id: {请求数据.团队id}, 用户id: {请求数据.用户id}, 错误: {e}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态码=500, 消息="获取成员权限详情失败")


@团队管理路由.post(
    "/member/remove",
    response_model=统一响应模型,
    summary="踢出团队成员",
    description="从团队中移除指定成员，只有有权限的成员可以执行此操作",
)
async def 踢出团队成员接口(
    request: Request,
    请求数据: 踢出团队成员请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """踢出团队成员接口"""
    try:
        操作人ID = 当前用户["id"]
        团队id = 请求数据.团队id
        被踢出用户id = 请求数据.用户id

        # 不能踢出自己
        if 操作人ID == 被踢出用户id:
            return 统一响应模型.失败(
                状态码=400, 消息="不能将自己踢出团队，请使用'退出团队'功能"
            )

        # 权限验证
        权限状态 = await 获取用户团队权限状态(团队id, 操作人ID)
        if not 权限状态 or not 权限状态.get("能否移除成员"):
            return 统一响应模型.失败(状态码=403, 消息="您没有权限移除该成员")

        # 检查被踢出用户是否为创建者
        被踢出用户权限 = await 获取用户团队权限状态(团队id, 被踢出用户id)
        if 被踢出用户权限 and 被踢出用户权限.get("是否团队创建者"):
            return 统一响应模型.失败(状态码=403, 消息="不能移除团队创建者")

        结果 = await 踢出团队成员(
            团队id=团队id,
            被踢出用户id=被踢出用户id,
            操作人ID=操作人ID,
            踢出原因=请求数据.移除原因,
        )

        if 结果["success"]:
            接口日志器.info(
                f"用户 {操作人ID} 从团队 {团队id} 移除了用户 {被踢出用户id}"
            )
            return 统一响应模型.成功(消息=结果["message"])
        else:
            return 统一响应模型.失败(状态码=400, 消息=结果["message"])

    except Exception as e:
        错误日志器.error(f"踢出团队成员接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="踢出成员失败")


class 团队活动日志请求(BaseModel):
    团队id: int = Field(..., gt=0, description="团队id")
    页码: int = Field(1, ge=1, description="当前页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    天数: int = Field(30, ge=1, le=365, description="查询天数，默认30天")
    操作类型: Optional[str] = Field(None, description="操作类型筛选")


class 用户最近活动请求(BaseModel):
    限制数量: int = Field(10, ge=1, le=50, description="限制返回的活动数量")
    天数: int = Field(7, ge=1, le=30, description="查询天数，默认7天")


@团队管理路由.post("/activity/logs")
async def 获取团队活动日志(
    request: Request,
    请求数据: 团队活动日志请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """获取团队操作日志，用于前端显示最近活动"""
    try:
        当前用户id = 当前用户["id"]

        # 验证用户是否有权限查看团队日志
        权限状态 = await 获取用户团队权限状态(团队id=请求数据.团队id, 用户id=当前用户id)

        if not 权限状态 or not 权限状态.get("是否团队成员"):
            错误日志器.warning(
                f"团队活动日志权限检查失败 - 团队id: {请求数据.团队id}, 用户id: {当前用户id}, 权限状态: {权限状态}"
            )
            return 统一响应模型.失败(状态码=403, 消息="您没有权限查看该团队的活动日志")

        # 获取团队操作日志
        日志结果 = await 获取团队操作日志(
            团队id=请求数据.团队id,
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            天数=请求数据.天数,
            操作类型=请求数据.操作类型,
        )

        接口日志器.info(
            f"获取团队活动日志成功: 团队id={请求数据.团队id}, 操作者={当前用户id}, 日志数量={len(日志结果.get('活动列表', []))}"
        )
        return 统一响应模型.成功(数据=日志结果)

    except Exception as e:
        错误日志器.error(f"获取团队活动日志失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取团队活动日志失败")


@团队管理路由.post(
    "/user/recent-activities",
    response_model=统一响应模型,
    summary="获取用户最近活动",
    description="获取当前用户在所有团队中的最近活动记录",
)
async def 获取用户最近活动接口(
    request: Request,
    请求数据: 用户最近活动请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """获取用户最近活动记录"""
    try:
        当前用户id = 当前用户["id"]

        # 调用数据层获取用户最近活动
        from 数据.团队基础数据 import 获取用户最近活动

        活动列表 = await 获取用户最近活动(
            用户id=当前用户id,
            天数=请求数据.天数,
            限制数量=请求数据.限制数量
        )

        接口日志器.info(
            f"用户 {当前用户id} 获取最近活动成功，共 {len(活动列表)} 条记录"
        )
        return 统一响应模型.成功(数据={"活动列表": 活动列表})

    except Exception as e:
        错误日志器.error(f"获取用户最近活动失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取用户最近活动失败")


@团队管理路由.post(
    "/smart-leave",
    response_model=统一响应模型,
    summary="智能退出团队",
    description="创建者智能退出：单人团队直接解散，多人团队需要转移所有权",
)
async def 智能退出团队(
    request: Request,
    请求数据: 智能退出团队请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    智能退出团队接口 - 优化创建者退出流程

    业务逻辑：
    1. 如果团队只有创建者1人 且 确认解散=True → 直接解散团队
    2. 如果团队有其他成员 → 返回需要转移所有权的提示和成员列表
    3. 非创建者 → 直接退出
    """
    try:
        操作人ID = 当前用户["id"]
        团队id = 请求数据.团队id

        # 获取团队基本信息和权限状态
        from 数据.团队基础数据 import 获取团队基本信息

        团队信息 = await 获取团队基本信息(团队id)

        if not 团队信息:
            return 统一响应模型.失败(状态码=404, 消息="团队不存在")

        权限状态 = await 获取用户团队权限状态(团队id, 操作人ID)
        if not 权限状态 or not 权限状态.get("在团队中"):
            return 统一响应模型.失败(状态码=403, 消息="您不在该团队中，无法退出")

        # 如果不是创建者，直接执行普通退出逻辑
        if not 权限状态.get("是否团队创建者"):
            结果 = await 踢出团队成员(
                团队id=团队id,
                被踢出用户id=操作人ID,
                操作人ID=操作人ID,
                踢出原因="用户主动退出",
            )

            if 结果["success"]:
                接口日志器.info(f"用户 {操作人ID} 主动退出团队 {团队id}")
                return 统一响应模型.成功(消息="您已成功退出团队")
            else:
                return 统一响应模型.失败(状态码=400, 消息=结果["message"])

        # 创建者智能退出逻辑
        当前成员数 = 团队信息.get("当前成员数", 0)

        # 情况1：单人团队，创建者可以直接解散
        if 当前成员数 <= 1:
            if 请求数据.确认解散:
                # 直接解散团队
                结果 = await 数据层解散团队(团队id, 操作人ID)

                if 结果["success"]:
                    接口日志器.info(f"创建者 {操作人ID} 解散单人团队 {团队id}")
                    return 统一响应模型.成功(
                        数据={
                            "action": "dissolved",
                            "team_name": 团队信息.get("团队名称"),
                        },
                        消息="团队已解散",
                    )
                else:
                    return 统一响应模型.失败(状态码=400, 消息=结果["message"])
            else:
                # 返回确认解散的提示
                return 统一响应模型.成功(
                    数据={
                        "action": "confirm_dissolve",
                        "team_name": 团队信息.get("团队名称"),
                        "member_count": 当前成员数,
                        "message": "这是一个单人团队，退出将自动解散团队",
                    },
                    消息="需要确认解散团队",
                )

        # 情况2：多人团队，需要转移所有权
        else:
            # 获取可以转移的成员列表（除了创建者自己）
            from 数据.团队成员数据 import 获取团队成员列表

            成员列表结果 = await 获取团队成员列表(
                团队id=团队id, 页码=1, 每页数量=100, 成员状态="正常"
            )

            可转移成员 = []
            if 成员列表结果.get("success") and 成员列表结果.get("data"):
                for 成员 in 成员列表结果["data"]["list"]:
                    if 成员.get("用户id") != 操作人ID:  # 排除创建者自己
                        可转移成员.append(
                            {
                                "用户id": 成员.get("用户id"),
                                "用户名": 成员.get("用户名"),
                                "职位": 成员.get("职位"),
                                "加入时间": 成员.get("加入时间"),
                            }
                        )

            return 统一响应模型.成功(
                数据={
                    "action": "transfer_required",
                    "team_name": 团队信息.get("团队名称"),
                    "member_count": 当前成员数,
                    "available_members": 可转移成员,
                    "message": f"团队有 {当前成员数} 名成员，需要先转移所有权给其他成员，或解散团队",
                },
                消息="需要转移所有权",
            )

    except Exception as e:
        错误日志器.error(f"智能退出团队接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="智能退出团队失败")


@团队管理路由.post(
    "/transfer-ownership",
    response_model=统一响应模型,
    summary="转移团队所有权",
    description="团队创建者将所有权转移给其他成员",
)
async def 转移团队所有权(
    request: Request,
    请求数据: 转移所有权请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    转移团队所有权接口

    业务逻辑：
    1. 验证当前用户是团队创建者
    2. 验证目标用户是团队正常成员
    3. 更新团队的创建人id
    4. 更新目标用户的职位为"创建者"
    5. 原创建者职位变为"成员"
    6. 如果选择转移后退出，则执行退出逻辑
    """
    try:
        操作人ID = 当前用户["id"]
        团队id = 请求数据.团队id
        新所有者用户id = 请求数据.新所有者用户id

        # 验证不能转移给自己
        if 操作人ID == 新所有者用户id:
            return 统一响应模型.失败(状态码=400, 消息="不能将所有权转移给自己")

        # 获取团队基本信息
        from 数据.团队基础数据 import 获取团队基本信息

        团队信息 = await 获取团队基本信息(团队id)

        if not 团队信息:
            return 统一响应模型.失败(状态码=404, 消息="团队不存在")

        # 验证当前用户是否为团队创建者
        权限状态 = await 获取用户团队权限状态(团队id, 操作人ID)
        if not 权限状态 or not 权限状态.get("是否团队创建者"):
            return 统一响应模型.失败(状态码=403, 消息="只有团队创建者才能转移所有权")

        # 验证目标用户是否为团队正常成员
        目标用户权限状态 = await 获取用户团队权限状态(团队id, 新所有者用户id)
        if not 目标用户权限状态 or not 目标用户权限状态.get("是否团队成员"):
            return 统一响应模型.失败(状态码=400, 消息="目标用户不是团队成员或状态异常")

        # 调用数据层执行转移逻辑
        from 数据.团队基础数据 import 转移团队所有权

        转移结果 = await 转移团队所有权(
            团队id=团队id,
            原所有者id=操作人ID,
            新所有者id=新所有者用户id
        )

        if not 转移结果.get("success"):
            return 统一响应模型.失败(
                状态码=状态.团队管理.权限不足,
                消息=转移结果.get("message", "转移所有权失败")
            )

        # 5. 如果选择转移后退出，执行退出逻辑
        if 请求数据.转移后退出:
            退出结果 = await 踢出团队成员(
                团队id=团队id,
                被踢出用户id=操作人ID,
                操作人ID=操作人ID,
                踢出原因="转移所有权后主动退出",
            )

            if 退出结果["success"]:
                接口日志器.info(f"用户 {操作人ID} 转移所有权并退出团队 {团队id}")
                return 统一响应模型.成功(
                    数据={
                        "action": "transferred_and_left",
                        "team_name": 团队信息.get("团队名称"),
                        "new_owner_id": 新所有者用户id,
                    },
                    消息="所有权转移成功，您已退出团队",
                )
            else:
                # 转移成功但退出失败
                return 统一响应模型.成功(
                    数据={
                        "action": "transferred_only",
                        "team_name": 团队信息.get("团队名称"),
                        "new_owner_id": 新所有者用户id,
                        "warning": "所有权转移成功，但退出团队失败："
                        + 退出结果.get("message", "未知错误"),
                    },
                    消息="所有权转移成功，但退出团队失败",
                )
        else:
            接口日志器.info(
                f"用户 {操作人ID} 成功转移团队 {团队id} 所有权给用户 {新所有者用户id}"
            )
            return 统一响应模型.成功(
                数据={
                    "action": "transferred",
                    "team_name": 团队信息.get("团队名称"),
                    "new_owner_id": 新所有者用户id,
                },
                消息="所有权转移成功",
            )

    except Exception as e:
        错误日志器.error(f"转移团队所有权接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="转移所有权失败")


# =============== 团队达人管理接口 ===============


@团队管理路由.post(
    "/talent/stats",
    response_model=统一响应模型,
    summary="获取团队达人统计",
    description="获取团队达人统计数据，包括总数、活跃成员数、新增数量等",
)
async def 获取团队达人统计接口(
    request: Request,
    请求数据: 团队达人统计请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队达人统计接口
    获取指定团队内所有成员认领的达人统计数据
    """
    try:
        用户id = 当前用户["id"]
        团队id = 请求数据.团队id

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队达人统计"
        )
        if not 有权限:
            return 权限错误响应

        # 使用服务层获取团队达人统计，提供降级处理
        统计数据 = await 获取团队达人统计服务(
            团队id=团队id, 时间范围=请求数据.时间范围, 包含非活跃=请求数据.包含非活跃
        )

        接口日志器.info(f"用户 {用户id} 获取团队 {团队id} 达人统计成功")
        return 统一响应模型.成功(数据=统计数据, 消息="获取团队达人统计成功")

    except Exception as e:
        错误日志器.error(f"获取团队达人统计接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.达人管理.搜索失败, 消息="获取团队达人统计失败")


@团队管理路由.post(
    "/talent/list",
    response_model=统一响应模型,
    summary="获取团队达人列表",
    description="分页获取团队内所有成员认领的达人列表，支持抖音和微信平台，支持搜索、筛选和排序",
)
async def 获取团队达人列表接口(
    request: Request,
    请求数据: 团队达人列表请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队达人列表接口 - 支持抖音和微信平台
    根据平台参数获取指定团队内所有成员认领的达人详细列表
    """
    try:
        用户id = 当前用户["id"]
        团队id = 请求数据.团队id
        平台 = 请求数据.平台

        # 验证平台参数
        if 平台 not in ["douyin", "wechat"]:
            return 统一响应模型.失败(状态码=400, 消息="不支持的平台类型，仅支持 douyin 或 wechat")

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, f"无权限访问该团队{平台}达人列表"
        )
        if not 有权限:
            接口日志器.warning(f"用户 {用户id} 尝试访问无权限的团队 {团队id} {平台}达人列表")
            return 权限错误响应

        # 根据平台调用不同的服务
        if 平台 == "douyin":
            列表数据 = await 获取团队达人列表服务(
                团队id=团队id,
                页码=请求数据.页码,
                每页数量=请求数据.每页数量,
                成员id=请求数据.成员id,
                关键词=请求数据.关键词,
                排序字段=请求数据.排序字段,
                排序方式=请求数据.排序方式,
            )
            平台名称 = "抖音"
        else:  # wechat
            列表数据 = await 获取团队微信达人列表服务(
                团队id=团队id,
                页码=请求数据.页码,
                每页数量=请求数据.每页数量,
                成员id=请求数据.成员id,
                关键词=请求数据.关键词,
                排序字段=请求数据.排序字段,
                排序方式=请求数据.排序方式,
            )
            平台名称 = "微信"

        # {{ AURA-X: Fix - 使用中文字段名和业务状态码. Source: API设计最佳实践 }}
        接口日志器.info(
            f"用户 {用户id} 获取团队 {团队id} {平台名称}达人列表成功，共 {列表数据['总数']} 个达人，平台：{平台}"
        )
        return 统一响应模型.成功(数据=列表数据, 消息=f"获取团队{平台名称}达人列表成功")

    except Exception as e:
        错误日志器.error(f"获取团队达人列表接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.达人管理.搜索失败, 消息="获取团队达人列表失败")





@团队管理路由.post(
    "/talent/analysis",
    response_model=统一响应模型,
    summary="获取团队达人详细分析",
    description="获取团队达人的多维度详细分析数据，包括趋势图表、类别分布、成员绩效等",
)
async def 获取团队达人详细分析接口(
    request: Request,
    请求数据: 团队达人详细分析请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队达人详细分析接口

    产品价值：为团队管理者提供数据驱动的决策支持
    - 多维度图表分析：趋势图、饼图、雷达图、柱状图
    - 成员绩效分析：个人表现对比和排名
    - 业务洞察：价值评估、效率指数、活跃度评分
    """
    try:
        用户id = 当前用户["id"]
        团队id = 请求数据.团队id
        时间范围 = 请求数据.时间范围

        # 详细记录调试信息
        接口日志器.info(
            f"[调试] 获取团队达人详细分析请求 - 用户id: {用户id}, 团队id: {团队id}, 时间范围: {时间范围}"
        )
        接口日志器.info(f"[调试] 请求数据详情: {请求数据}")
        接口日志器.info(f"[调试] 当前用户详情: {当前用户}")

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队详细分析数据"
        )
        if not 有权限:
            错误日志器.warning(f"[权限验证失败] 用户 {用户id} 无权限访问团队 {团队id}")
            return 权限错误响应

        接口日志器.info("[调试] 团队权限验证通过，开始获取分析数据")

        # 使用服务层获取详细分析数据，提供降级处理机制
        分析数据 = await 获取团队达人详细分析服务(团队id=团队id, 时间范围=时间范围)

        接口日志器.info(
            f"用户 {用户id} 获取团队 {团队id} 达人详细分析成功，时间范围: {时间范围}"
        )
        return 统一响应模型.成功(
            数据=分析数据, 消息=f"获取团队达人详细分析成功，分析时间范围: {时间范围}"
        )

    except ValueError as ve:
        错误日志器.warning(f"团队达人详细分析参数错误: {ve}")
        return 统一响应模型.失败(状态码=400, 消息=f"参数错误: {str(ve)}")
    except Exception as e:
        错误日志器.error(f"获取团队达人详细分析接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(
            状态码=500, 消息="获取团队达人详细分析失败，请稍后重试"
        )


@团队管理路由.post(
    "/talent/detail",
    response_model=统一响应模型,
    summary="获取团队达人详情",
    description="获取指定团队达人的详细信息，包括基本信息、数据统计、商业价值分析等",
)
async def 获取团队达人详情接口(
    request: Request,
    请求数据: 团队达人详情请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队达人详情接口

    产品功能：
    - 获取达人基本信息（昵称、粉丝数、认领状态等）
    - 提供数据统计分析（粉丝增长、互动数据等）
    - 智能商业价值评估（基于粉丝数和活跃度）
    - 支持团队管理操作（转移、编辑、备注等）
    """
    try:
        用户id = 当前用户["id"]
        团队id = 请求数据.团队id
        达人id = 请求数据.达人id

        接口日志器.info(
            f"[获取团队达人详情] 用户 {用户id} 请求团队 {团队id} 的达人 {达人id} 详情"
        )

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队达人详情"
        )
        if not 有权限:
            错误日志器.warning(f"[权限验证失败] 用户 {用户id} 无权限访问团队 {团队id}")
            return 权限错误响应

        # 使用服务层获取团队达人详情，提供完整的降级处理
        详情结果 = await 获取团队达人详情服务(团队id=团队id, 达人id=达人id)

        详情数据 = 详情结果.get("data") if 详情结果.get("success") else None

        if not 详情数据:
            错误日志器.warning(f"[数据不存在] 团队 {团队id} 中不存在达人 {达人id}")
            return 统一响应模型.失败(状态码=状态.达人管理.达人不存在, 消息="达人不存在或不属于该团队")

        接口日志器.info(f"用户 {用户id} 成功获取团队 {团队id} 达人 {达人id} 详情")
        return 统一响应模型.成功(数据=详情数据, 消息="获取团队达人详情成功")

    except ValueError as ve:
        错误日志器.warning(f"团队达人详情参数错误: {ve}")
        return 统一响应模型.失败(状态码=400, 消息=f"参数错误: {str(ve)}")
    except Exception as e:
        错误日志器.error(f"获取团队达人详情接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取团队达人详情失败，请稍后重试")


# =============== 团队微信达人管理接口 ===============


@团队管理路由.post(
    "/wechat-talent/stats",
    response_model=统一响应模型,
    summary="获取团队微信达人统计",
    description="获取团队微信达人统计数据，包括总数、活跃成员数、新增数量等",
)
async def 获取团队微信达人统计接口(
    request: Request,
    请求数据: 团队微信达人统计请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队微信达人统计接口
    获取指定团队内所有成员认领的微信达人统计数据
    """
    try:
        用户id = 当前用户["id"]
        团队id = 请求数据.团队id

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队微信达人统计"
        )
        if not 有权限:
            接口日志器.warning(f"用户 {用户id} 尝试访问无权限的团队 {团队id} 统计数据")
            return 权限错误响应

        # 使用服务层获取团队微信达人统计，提供降级处理
        统计数据 = await 获取团队微信达人统计服务(
            团队id=团队id, 时间范围=请求数据.时间范围, 包含非活跃=请求数据.包含非活跃
        )

        接口日志器.info(f"用户 {用户id} 获取团队 {团队id} 微信达人统计成功")
        return 统一响应模型.成功(数据=统计数据, 消息="获取团队微信达人统计成功")

    except Exception as e:
        错误日志器.error(f"获取团队微信达人统计接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.达人管理.搜索失败, 消息="获取团队微信达人统计失败")


@团队管理路由.post(
    "/wechat-talent/list",
    response_model=统一响应模型,
    summary="获取团队微信达人列表",
    description="分页获取团队内所有成员认领的微信达人列表，支持搜索、筛选和排序",
)
async def 获取团队微信达人列表接口(
    request: Request,
    请求数据: 团队微信达人列表请求 = Body(...),
    当前用户: dict = Depends(获取当前用户),
):
    """
    获取团队微信达人列表接口
    获取指定团队内所有成员认领的微信达人详细列表
    """
    try:
        用户id = 当前用户["id"]
        团队id = 请求数据.团队id

        # 使用统一权限检查函数
        有权限, 权限错误响应 = await 检查团队访问权限(
            团队id, 用户id, "无权限访问该团队微信达人列表"
        )
        if not 有权限:
            接口日志器.warning(
                f"用户 {用户id} 尝试访问无权限的团队 {团队id} 微信达人列表"
            )
            return 权限错误响应

        # 使用服务层获取团队微信达人列表，提供降级处理
        列表数据 = await 获取团队微信达人列表服务(
            团队id=团队id,
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            成员id=请求数据.成员id,
            关键词=请求数据.关键词,
            排序字段=请求数据.排序字段,
            排序方式=请求数据.排序方式,
        )

        接口日志器.info(
            f"用户 {用户id} 获取团队 {团队id} 微信达人列表成功，共 {列表数据['总数']} 个达人"
        )
        return 统一响应模型.成功(数据=列表数据, 消息="获取团队微信达人列表成功")

    except Exception as e:
        错误日志器.error(f"获取团队微信达人列表接口异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=状态.达人管理.搜索失败, 消息="获取团队微信达人列表失败")
