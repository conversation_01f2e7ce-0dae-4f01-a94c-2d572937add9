import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// 设置中文语言包
dayjs.locale('zh-cn');

/**
 * 格式化日期时间
 * @param {string | Date} dateTime - 需要格式化的日期时间
 * @param {string} format - 目标格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的字符串，如果输入无效则返回 '--'
 */
export function formatDateTime(dateTime, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateTime) return '--';
  const formatted = dayjs(dateTime).format(format);
  return formatted !== 'Invalid Date' ? formatted : '--';
}

/**
 * 格式化日期
 * @param {string | Date} date - 需要格式化的日期
 * @param {string} format - 目标格式，默认为 'YYYY-MM-DD'
 * @returns {string} 格式化后的字符串，如果输入无效则返回 '--'
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '--';
  const formatted = dayjs(date).format(format);
  return formatted !== 'Invalid Date' ? formatted : '--';
}

export default {
  formatDateTime,
  formatDate,
}; 