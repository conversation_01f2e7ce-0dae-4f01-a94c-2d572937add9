# -*- coding: utf-8 -*-

"""
@File: 系统日志.py
@Author: limob AI
@Time: 2024/05/16
"""

# 统一使用应用日志器作为系统日志器
from .日志配置 import 应用日志器, 错误日志器

# 日志记录器
from typing import Optional
import sys

# 定义为别名，所有对系统日志器的调用都将使用应用日志器
系统日志器 = 应用日志器

def 记录系统启动(app_name: str = "默认应用", version: str = "1.0.0"):
    """
    记录系统启动事件
    """
    启动消息 = f"🚀 系统 '{app_name}' (版本 {version}) 启动成功！"
    应用日志器.info(启动消息)


def 记录系统关闭(app_name: str = "默认应用", exit_code: int = 0, run_time: Optional[float] = None):
    """
    记录系统关闭事件
    """
    if run_time is not None:
        关闭消息 = f"🔌 系统 '{app_name}' 已关闭。运行时长: {run_time:.2f}秒, 退出码: {exit_code}."
    else:
        关闭消息 = f"🔌 系统 '{app_name}' 已关闭。退出码: {exit_code}."
    应用日志器.info(关闭消息)


def 记录数据库操作(sql: str, params: tuple, duration: float, success: bool = True):
    """
    记录数据库操作
    """
    if success:
        状态 = "成功"
    else:
        # log_level = "ERROR"  # 暂时不使用
        状态 = "失败"

    数据库消息 = f"数据库操作 - {状态} ({duration:.4f}s) | SQL: {sql} | Params: {params}"
    
    if success:
        应用日志器.debug(数据库消息)
    else:
        错误日志器.error(数据库消息)


def 记录接口请求(method: str, path: str, status_code: int, duration: float):
    """
    记录接口请求
    """
    接口消息 = f"接口请求 - {method} {path} | 状态码: {status_code} | 耗时: {duration:.4f}s"
    应用日志器.info(接口消息)


def 记录系统资源(cpu: float, memory: float):
    """
    记录系统资源使用情况
    """
    资源消息 = f"系统资源 - CPU: {cpu}% | 内存: {memory}%"
    应用日志器.info(资源消息)

def 记录安全事件(事件类型: str, 用户id: Optional[int] = None, 
              事件描述: str = "", IP地址: str = "", 成功: bool = True):
    """记录安全相关事件"""
    结果 = "成功" if 成功 else "失败"
    安全消息 = f"安全事件: 类型={事件类型}, 结果={结果}, 用户id={用户id or 'None'}, IP={IP地址}, 描述={事件描述}"
    
    if 成功:
        应用日志器.info(安全消息)
    else:
        应用日志器.warning(安全消息)
        
    # 高风险事件记录到错误日志
    高风险事件类型 = ["登录失败", "权限提升", "配置修改", "敏感操作"]
    if 事件类型 in 高风险事件类型 and not 成功:
        错误日志器.warning(f"高风险安全事件: {安全消息}")

def 配置异常处理():
    """配置全局异常处理"""
    # 保存原始异常处理函数
    原始异常处理 = sys.excepthook
    
    def 异常处理(exc_type, exc_value, exc_traceback):
        """记录未捕获的异常"""
        错误日志器.critical(
            f"未捕获的异常: {exc_type.__name__}: {exc_value}",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
        # 调用原始处理函数
        原始异常处理(exc_type, exc_value, exc_traceback)
    
    # 设置全局异常处理函数
    sys.excepthook = 异常处理 