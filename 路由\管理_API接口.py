"""
管理员核心API路由
提供管理员认证和会员管理相关的API接口
主要功能：
1. 管理员认证
2. 会员统计和管理
"""

from fastapi import APIRouter, Depends, Request

# 导入依赖和模型
from 依赖项.认证 import 获取当前管理员用户

# 导入数据层函数
from 数据.管理_统一入口 import 异步获取会员统计, 异步验证管理员
from 数据模型.SuperAdmin_模型 import 管理员登录请求
from 数据模型.响应模型 import 统一响应模型

# 导入服务层
from 服务.异步用户服务 import 异步认证服务

# 导入日志
from 日志 import 安全日志器, 接口日志器, 错误日志器

# 创建API路由器
SuperAdminAPI路由 = APIRouter(tags=["管理后台-API"])

# =============== 管理员认证API ===============


@SuperAdminAPI路由.post("/login", response_model=统一响应模型)
async def 管理员登录(请求数据: 管理员登录请求, request: Request):
    """管理员登录"""
    try:
        客户端IP = request.client.host if request.client else "127.0.0.1"
        接口日志器.info(f"管理员登录尝试: {请求数据.用户名}, IP: {客户端IP}")

        # 验证管理员身份，传递IP地址
        验证结果 = await 异步验证管理员(请求数据.用户名, 请求数据.密码, 客户端IP)

        if 验证结果.get("成功"):
            # 生成JWT令牌
            用户数据 = {
                "id": 验证结果.get("用户id"),
                "username": 请求数据.用户名,
                "role": "admin"
            }
            
            # 使用异步认证服务生成真正的JWT令牌
            认证服务实例 = 异步认证服务()
            jwt_token = await 认证服务实例.生成令牌(用户数据)
            令牌数据 = {"access_token": jwt_token, "token_type": "bearer"}

            安全日志器.info(f"管理员 {请求数据.用户名} 登录成功, IP: {客户端IP}")
            return 统一响应模型.成功(数据=令牌数据, 消息="登录成功")
        else:
            安全日志器.warning(
                f"管理员 {请求数据.用户名} 登录失败: {验证结果.get('消息')}, IP: {客户端IP}"
            )
            return 统一响应模型.失败(状态码=401, 消息=验证结果.get("消息", "登录失败"))

    except Exception as e:
        错误日志器.error(f"管理员登录异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="登录服务异常")


# =============== 会员统计API ===============


@SuperAdminAPI路由.post(
    "/member-stats",
    dependencies=[Depends(获取当前管理员用户)],
    response_model=统一响应模型,
)
async def 获取会员统计(当前用户: dict = Depends(获取当前管理员用户)):
    """获取会员统计信息"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求会员统计")

        统计数据 = await 异步获取会员统计()
        return 统一响应模型.成功(数据=统计数据, 消息="获取会员统计成功")

    except Exception as e:
        错误日志器.error(f"获取会员统计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取会员统计失败")
