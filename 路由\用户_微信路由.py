from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse

import 状态

# 从依赖项模块导入获取当前用户函数
from 依赖项.认证 import 获取当前用户
from 数据模型 import 微信模型
from 数据模型.响应模型 import 统一响应模型

# 导入统一日志系统
from 日志 import 错误日志器
from 服务.异步用户服务 import (
    异步根据id获取微信好友信息服务,
    异步更新微信对接进度服务,
    异步查询微信对接进度服务,
    异步添加微信好友服务,
    异步获取或创建微信id服务,
)

微信路由 = APIRouter()


@微信路由.post(
    "/set_wechat_status", summary="设置微信对接状态", description="保存微信对接数据"
)
async def 路由_用户微信对接状态数据保存处理接口(
    请求数据: 微信模型.对接状态保存模型, 用户: dict = Depends(获取当前用户)
):
    try:
        # 将请求模型转换为字典格式，保持原有的参数名称
        数据 = 请求数据.model_dump()
        # 添加用户id
        数据["用户id"] = 用户["id"]  # 从当前登录用户获取用户id

        # 验证必要参数
        必要字段 = ["我方微信号id", "对方微信号id", "合作产品id"]
        缺少字段 = [字段 for 字段 in 必要字段 if not 数据.get(字段)]
        if 缺少字段:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    状态.通用.参数错误, f"缺少必要字段: {', '.join(缺少字段)}"
                ).转字典(),
            )

        # 调用服务层保存数据
        await 异步更新微信对接进度服务(数据)

        return 统一响应模型.成功(消息="微信对接状态更新成功")

    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 服务器内部错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.更新对接进度失败, f"处理请求时发生错误: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/get_wechat_status",
    summary="查询微信对接状态",
    description="根据微信号和合作产品id查询微信对接状态数据",
)
async def 路由_用户微信对接状态查询处理接口(
    请求数据: 微信模型.对接状态查询模型, 用户: dict = Depends(获取当前用户)
):
    try:
        # 验证必要参数
        必要字段 = ["我方微信号", "对方微信号", "合作产品id"]
        数据 = 请求数据.model_dump()
        缺少字段 = [字段 for 字段 in 必要字段 if not 数据.get(字段)]
        if 缺少字段:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    状态.通用.参数错误, f"缺少必要字段: {', '.join(缺少字段)}"
                ).转字典(),
            )

        # 调用服务层查询数据
        结果 = await 异步查询微信对接进度服务(
            请求数据.我方微信号, 请求数据.对方微信号, 请求数据.合作产品id
        )

        # 检查服务层返回的状态
        if 结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                状态.通用.失败, 结果.get("message", "查询微信对接状态失败")
                ).转字典(),
            )

        # 返回查询结果
        return 统一响应模型.成功(结果.get("data"), "查询微信对接状态成功")

    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 服务器内部错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取对接进度列表失败, f"处理请求时发生错误: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/get_wechat_id",
    summary="获取或创建微信id",
    description="根据微信号获取微信id，如果不存在则创建",
)
async def 路由_微信id获取或创建处理接口(
    请求数据: 微信模型.微信id查询模型, 用户: dict = Depends(获取当前用户)
):
    try:
        # 将微信号转换为小写
        微信号 = 请求数据.微信号.lower()

        # 调用服务层获取或创建微信id
        结果 = await 异步获取或创建微信id服务(微信号)

        # 检查服务层返回的状态
        if 结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    结果.get("status", 状态.通用.失败), 结果.get("message", "获取微信id失败")
                ).转字典(),
            )

        # 返回查询结果
        return 统一响应模型.成功(结果.get("data"), "获取微信id成功")

    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 服务器内部错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"处理请求时发生错误: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/add_wechat_friend",
    summary="添加微信好友",
    description="添加微信好友关系并生成识别id",
)
async def 路由_用户微信好友添加处理接口(
    请求数据: 微信模型.微信好友添加模型, 用户: dict = Depends(获取当前用户)
):
    try:
        # 调用服务层添加微信好友
        结果 = await 异步添加微信好友服务(
            请求数据.我方微信号, 请求数据.对方微信号, 请求数据.对方微信头像
        )

        # 检查服务层返回的状态
        if 结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    结果.get("status", 状态.通用.失败), 结果.get("message", "添加微信好友失败")
                ).转字典(),
            )

        # 返回结果
        return 统一响应模型.成功(结果.get("data"), "添加微信好友成功")

    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 服务器内部错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.添加好友失败, f"处理请求时发生错误: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/get_wechat_friend_by_id",
    summary="通过ID获取微信好友信息",
    description="通过我方微信号id和识别id获取微信好友信息，包含下次沟通时间字段",
)
async def 路由_通过识别id获取用户微信好友信息处理接口(
    请求数据: 微信模型.微信好友查询模型, 用户: dict = Depends(获取当前用户)
):
    try:
        # 验证必要参数
        if not 请求数据.我方微信号id or not 请求数据.好友识别id:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    状态.通用.参数错误, "缺少必要字段: 我方微信号id或好友识别id"
                ).转字典(),
            )

        # 调用服务层获取微信好友信息（传递用户id用于权限验证）
        # 服务层直接返回统一响应模型
        return await 异步根据id获取微信好友信息服务(
            用户["id"], 请求数据.我方微信号id, 请求数据.好友识别id
        )

    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 服务器内部错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取好友列表失败, f"处理请求时发生错误: {str(e)}"
            ).转字典(),
        )


# ================== 扩展微信管理API接口 ==================


@微信路由.post(
    "/accounts",
    summary="获取微信账号列表",
    description="获取微信账号列表，支持分页和搜索",
)
async def 获取微信账号列表(
    请求数据: 微信模型.微信账号列表请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取微信账号列表
    """
    try:
        # 调用服务层获取微信账号列表
        from 服务.异步微信服务 import 异步获取微信账号列表服务

        结果 = await 异步获取微信账号列表服务(
            用户id=用户["id"],
            页码=请求数据.页码,
            每页条数=请求数据.每页条数,
            查询状态=请求数据.状态,
            关键词=请求数据.关键词,
        )

        if 结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    结果.get("status", 状态.通用.失败), 结果.get("message", "获取微信账号列表失败")
                ).转字典(),
            )

        return 统一响应模型.成功(结果.get("data"), "获取微信账号列表成功")

    except Exception as e:
        错误日志器.error(f"获取微信账号列表失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取账号列表失败, f"获取微信账号列表失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/accounts/update", summary="更新微信账号", description="更新微信账号信息"
)
async def 更新微信账号(
    请求数据: 微信模型.微信账号更新模型, 用户: dict = Depends(获取当前用户)
):
    """
    更新微信账号信息
    """
    try:
        from 服务.异步微信服务 import 异步更新微信账号服务

        # 处理状态字段类型转换（模型中是str，服务层需要int）
        状态值 = None
        if 请求数据.状态 is not None:
            try:
                状态值 = int(请求数据.状态)
            except (ValueError, TypeError):
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content=统一响应模型.失败(状态.通用.参数错误, "状态字段必须是有效的整数").转字典(),
                )

        # 服务层直接返回统一响应模型
        return await 异步更新微信账号服务(
            用户id=用户["id"],
            账号id=请求数据.账号id,
            备注=请求数据.备注,
            状态=状态值,
        )

    except Exception as e:
        错误日志器.error(f"更新微信账号失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.更新账号失败, f"更新微信账号失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post("/accounts/delete", summary="删除微信账号", description="删除微信账号")
async def 删除微信账号(
    请求数据: 微信模型.微信账号删除模型, 用户: dict = Depends(获取当前用户)
):
    """
    删除微信账号
    """
    try:
        from 服务.异步微信服务 import 异步删除微信账号服务

        # 服务层直接返回统一响应模型
        return await 异步删除微信账号服务(用户id=用户["id"], 账号id=请求数据.账号id)

    except Exception as e:
        错误日志器.error(f"删除微信账号失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.删除账号失败, f"删除微信账号失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/friends/update",
    summary="更新微信好友信息",
    description="更新微信好友信息（识别id、失效状态、时间字段、备注等）",
)
async def 路由_用户微信好友信息更新处理接口(
    请求数据: 微信模型.微信好友更新模型, 用户: dict = Depends(获取当前用户)
):
    """
    路由层：处理用户微信好友信息更新请求

    支持更新以下字段：
    - 识别id: 识别id
    - 是否失效: 失效状态
    - 发送请求时间: 发送好友请求的时间
    - 好友入库时间: 好友入库的时间
    - 我方最后一条消息发送时间: 我方最后发送消息的时间
    - 对方最后一条消息发送时间: 对方最后发送消息的时间
    - 备注: 备注信息（最多500字符）
    """
    try:
        from 服务.异步微信服务 import 异步服务_通过识别id更新用户微信好友信息

        # 调用服务层处理业务逻辑
        # 类型转换：识别id从int转为str，时间戳转为datetime
        from datetime import datetime

        def 转换时间戳(时间值):
            """将时间戳转换为datetime对象"""
            if 时间值 is None:
                return None
            if isinstance(时间值, datetime):
                return 时间值
            if isinstance(时间值, int):
                return datetime.fromtimestamp(时间值)
            return None

        结果 = await 异步服务_通过识别id更新用户微信好友信息(
            用户id=用户["id"],
            我方微信id=请求数据.我方微信id,
            好友识别id=str(请求数据.识别id),  # int转str
            是否失效=请求数据.是否失效,
            发送请求时间=转换时间戳(请求数据.发送请求时间),
            好友入库时间=转换时间戳(请求数据.好友入库时间),
            我方最后一条消息发送时间=转换时间戳(请求数据.我方最后一条消息发送时间),
            对方最后一条消息发送时间=转换时间戳(请求数据.对方最后一条消息发送时间),
            备注=请求数据.备注,
        )

        # 根据服务层返回的状态码处理响应
        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"路由_用户微信好友信息更新处理接口异常: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.更新好友失败, f"更新好友信息失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/update-avatar", summary="更新微信头像", description="更新微信信息表中的头像字段"
)
async def 更新微信头像(
    请求数据: 微信模型.微信头像更新请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    更新微信头像

    更新微信信息表中指定微信号的头像字段，支持base64格式的头像数据。

    功能特性：
    - 支持base64编码的头像数据存储
    - 支持data:image格式的完整数据URL
    - 自动验证图片格式（JPEG、PNG、GIF、WebP）
    - 文件大小限制（最大2MB）
    - 权限验证确保只能更新自己的微信头像

    参数:
    - **微信号**: 要更新头像的微信号
    - **微信头像**: base64编码的头像数据，支持以下格式：
      - data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...
      - data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
      - 或直接的base64字符串

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 更新结果，包含微信号、更新时间和头像大小

    错误处理:
    - 400: 参数验证失败（格式错误、文件过大等）
    - 403: 权限不足（微信号不属于当前用户）
    - 500: 服务器内部错误
    """
    try:
        from 服务.异步微信服务 import 异步更新微信头像服务

        # 调用服务层更新头像
        # 服务层直接返回统一响应模型
        return await 异步更新微信头像服务(
            用户id=用户["id"], 微信号=请求数据.微信号, 微信头像=请求数据.微信头像
        )

    except ValueError as ve:
        # 参数验证错误
        错误日志器.error(f"微信头像参数验证失败: {str(ve)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.通用.参数错误, f"参数验证失败: {str(ve)}"
            ).转字典(),
        )
    except Exception as e:
        错误日志器.error(f"更新微信头像失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.更新好友失败, f"更新微信头像失败: {str(e)}"
            ).转字典(),
        )





@微信路由.post(
    "/product-progress",
    summary="获取产品对接进度列表",
    description="获取产品对接进度列表",
)
async def 获取产品对接进度列表(
    请求数据: 微信模型.产品对接进度列表请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取产品对接进度列表
    """
    try:
        from 服务.异步微信服务 import 异步获取产品对接进度列表服务

        结果 = await 异步获取产品对接进度列表服务(
            用户id=用户["id"],
            页码=请求数据.页码,
            每页条数=请求数据.每页条数,
            查询状态=请求数据.状态,
            筛选用户id=请求数据.用户id,
            产品名称=请求数据.产品名称,
        )

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"获取产品对接进度列表失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取对接进度列表失败, f"获取产品对接进度列表失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/product-progress/create",
    summary="创建产品对接进度",
    description="创建新的产品对接进度",
)
async def 创建产品对接进度(
    请求数据: 微信模型.产品对接进度创建模型, 用户: dict = Depends(获取当前用户)
):
    """
    创建新的产品对接进度
    """
    try:
        from 服务.异步微信服务 import 异步创建产品对接进度服务

        结果 = await 异步创建产品对接进度服务(
            用户id=用户["id"],
            产品id=请求数据.用户id,  # 使用模型中实际存在的字段
            微信账号id=请求数据.用户id,  # 临时使用用户id，需要根据实际业务逻辑调整
            对方微信号=请求数据.产品名称,  # 临时使用产品名称，需要根据实际业务逻辑调整
            备注=请求数据.备注,
        )

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"创建产品对接进度失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.创建对接进度失败, f"创建产品对接进度失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/product-progress/update",
    summary="更新产品对接进度",
    description="更新产品对接进度",
)
async def 更新产品对接进度(
    请求数据: 微信模型.产品对接进度更新模型, 用户: dict = Depends(获取当前用户)
):
    """
    更新产品对接进度
    """
    try:
        from 服务.异步微信服务 import 异步更新产品对接进度服务

        结果 = await 异步更新产品对接进度服务(
            用户id=用户["id"],
            进度ID=请求数据.进度ID,
            进度状态=请求数据.状态,  # 使用模型中实际存在的字段
            备注=请求数据.备注,
        )

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"更新产品对接进度失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.更新对接进度失败, f"更新产品对接进度失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/product-progress/delete",
    summary="删除产品对接进度",
    description="删除产品对接进度",
)
async def 删除产品对接进度(
    请求数据: 微信模型.产品对接进度删除模型, 用户: dict = Depends(获取当前用户)
):
    """
    删除产品对接进度
    """
    try:
        from 服务.异步微信服务 import 异步删除产品对接进度服务

        结果 = await 异步删除产品对接进度服务(用户id=用户["id"], 进度ID=请求数据.进度ID)

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"删除产品对接进度失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.删除对接进度失败, f"删除产品对接进度失败: {str(e)}"
            ).转字典(),
        )


# ============= 用户微信关联管理接口 =============


@微信路由.post(
    "/user-accounts",
    summary="获取用户绑定的微信账号",
    description="获取当前用户绑定的所有微信账号",
)
async def 路由_获取当前用户绑定的所有微信账号列表处理接口(
    用户: dict = Depends(获取当前用户),
):
    """
    获取当前用户绑定的所有微信账号
    """
    try:
        from 服务.微信用户关联服务 import 异步获取用户绑定微信服务

        # 调用服务层获取用户绑定的微信账号
        结果 = await 异步获取用户绑定微信服务(用户["id"])

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"获取用户绑定微信账号失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取用户绑定账号失败, f"获取用户微信账号失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/user-friends",
    summary="获取用户所有微信好友",
    description="获取用户所有绑定微信的好友列表",
)
async def 获取用户所有微信好友(
    请求数据: 微信模型.用户微信好友查询模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取用户所有绑定微信的好友列表，支持分页和搜索
    """
    try:
        from 服务.微信用户关联服务 import 异步获取用户所有微信好友服务

        查询参数 = 请求数据.model_dump()
        查询参数["用户id"] = 用户["id"]

        # 调用服务层获取用户所有微信好友
        结果 = await 异步获取用户所有微信好友服务(查询参数)

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"获取用户微信好友失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取用户好友失败, f"获取用户微信好友失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/bind-account",
    summary="绑定微信账号",
    description="将微信账号绑定到当前用户，支持完整微信信息",
)
async def 路由_用户微信账号绑定操作处理接口(
    请求数据: 微信模型.微信账号绑定模型, 用户: dict = Depends(获取当前用户)
):
    """
    将微信账号绑定到当前用户

    本接口已整合原添加微信账号的功能，支持完整的微信信息绑定
    包括微信号、昵称、绑定手机号、头像、备注等所有字段

    Args:
        请求数据: 包含微信号等信息的绑定请求数据，支持以下字段：
                 - 微信号 (必填): 要绑定的微信号
                 - 备注 (可选): 绑定备注信息
                 - 昵称 (可选): 微信昵称
                 - 绑定手机号 (可选): 绑定的手机号
                 - 头像 (可选): 头像URL
        用户: 当前登录用户信息

    Returns:
        JSON响应: 包含绑定结果的统一响应格式
    """
    try:
        from 服务.微信用户关联服务 import 异步绑定用户微信服务

        # 构建绑定参数，支持扩展字段
        绑定数据 = {
            "用户id": 用户["id"],
            "微信号": 请求数据.微信号,
            "备注": 请求数据.备注 or "",
            # 新增扩展字段支持
            "昵称": getattr(请求数据, "昵称", None) or "",
            "绑定手机号": getattr(请求数据, "绑定手机号", None) or "",
            "头像": getattr(请求数据, "头像", None) or "",
        }

        # 调用服务层执行绑定逻辑
        结果 = await 异步绑定用户微信服务(绑定数据)

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"绑定微信账号接口异常: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.绑定账号失败, "服务器内部错误"
            ).转字典(),
        )


@微信路由.post(
    "/unbind-account",
    summary="解绑微信账号",
    description="解除微信账号与当前用户的绑定关系",
)
async def 路由_用户微信账号解绑操作处理接口(
    请求数据: 微信模型.微信账号解绑模型, 用户: dict = Depends(获取当前用户)
):
    """
    解除微信账号与当前用户的绑定关系
    """
    try:
        from 服务.微信用户关联服务 import 异步解绑用户微信服务

        # 验证必要参数
        if not 请求数据.微信id:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(
                    状态.通用.参数错误, "微信id不能为空"
                ).转字典(),
            )

        解绑数据 = {"用户id": 用户["id"], "微信id": 请求数据.微信id}

        # 调用服务层解绑微信账号
        结果 = await 异步解绑用户微信服务(解绑数据)

        # 服务层直接返回统一响应模型
        return 结果

    except Exception as e:
        错误日志器.error(f"解绑微信账号失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.解绑账号失败, f"解绑微信账号失败: {str(e)}"
            ).转字典(),
        )


@微信路由.get(
    "/user/statistics/overview",
    summary="获取用户微信统计概览",
    description="获取用户微信账号和好友的统计概览信息",
)
async def 路由_获取当前用户微信账号和好友统计概览数据处理接口(
    用户: dict = Depends(获取当前用户),
):
    """
    获取用户微信账号和好友的统计概览信息
    """
    try:
        # 异步获取服务函数
        from 服务.异步微信服务 import 异步服务_获取指定用户的微信账号和好友统计概览数据

        # 调用服务层获取统计数据
        统计数据 = await 异步服务_获取指定用户的微信账号和好友统计概览数据(用户["id"])

        return 统一响应模型.成功(数据=统计数据)

    except Exception as e:
        错误日志器.error(f"获取用户微信统计概览失败: {e}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取概览失败, f"服务器错误: {e}"
            ).转字典(),
        )


@微信路由.post(
    "/friend-request-status",
    summary="查询当前登录用户的微信好友请求状态",
    description="根据当前登录用户id和微信信息表id查询微信好友请求状态，包括已有好友请求记录和需要补充的达人联系方式信息",
)
async def 路由接口_查询当前登录用户的微信好友请求状态和达人信息(
    请求数据: 微信模型.微信自动添加好友请求模型,
    当前登录用户: dict = Depends(获取当前用户),
):
    """
    查询用户微信好友请求状态路由接口

    功能说明：
    1. 查询用户_联系方式_微信添加记录表中是否有好友请求状态不为null的记录
    2. 如果有，返回相关的达人信息、联系方式和好友请求状态
    3. 如果没有，查询用户达人补充信息表中的记录
    4. 如果用户达人补充信息表中有记录但未关联微信添加记录表，则创建关联
    5. 如果所有记录都已关联且好友请求状态都不为null，提示需要补充达人联系方式

    业务逻辑：
    - 通过用户达人补充信息表id获取用户达人关联表id
    - 根据用户达人关联表的达人id和平台判断在达人表或微信达人表中获取达人昵称
    - 通过联系方式表id在联系方式表中获取联系方式和类型

    返回数据：
    - 状态类型：有好友请求 | 需要添加好友请求 | 需要补充达人联系方式
    - 微信添加记录id：用户_联系方式_微信添加记录表的ID
    - 达人昵称：从达人表或微信达人表获取
    - 联系方式：从联系方式表获取
    - 联系方式类型：从联系方式表获取
    - 用户达人补充信息表id：用户达人补充信息表的ID
    - 用户达人关联表id：用户达人关联表的ID
    - 好友请求状态：当前的好友请求状态
    """
    try:
        from 服务.异步微信服务 import 异步查询用户微信好友请求状态业务服务

        # 调用业务服务层查询当前登录用户的微信好友请求状态
        用户微信好友请求状态查询结果 = await 异步查询用户微信好友请求状态业务服务(
            当前登录用户["id"], 请求数据.微信信息表id
        )

        # 服务层直接返回统一响应模型
        return 用户微信好友请求状态查询结果

    except Exception as e:
        错误日志器.error(
            f"路由接口_查询当前登录用户的微信好友请求状态和达人信息异常: 用户id={当前登录用户['id']}, 错误={str(e)}"
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.微信.获取用户好友失败, f"查询微信好友请求状态失败: {str(e)}"
            ).转字典(),
        )


@微信路由.post(
    "/friend-communication-schedule",
    summary="获取微信好友下次沟通时间列表",
    description="查询微信好友表中所有微信好友的下次沟通时间信息，支持分页和筛选",
)
async def 路由接口_获取微信好友下次沟通时间列表(
    请求数据: 微信模型.微信好友下次沟通时间查询模型,
    当前登录用户: dict = Depends(获取当前用户),
):
    """
    获取微信好友下次沟通时间列表路由接口

    功能说明：
    1. 从微信好友表中查询并返回所有好友的下次沟通时间数据
    2. 只返回有下次沟通时间的好友记录（下次沟通时间 IS NOT NULL）
    3. 按下次沟通时间升序排列，优先显示需要尽快沟通的好友
    4. 支持分页查询，提高性能
    5. 支持指定微信号筛选，或获取用户所有微信号的好友
    6. 包含完整的权限验证，确保用户只能查看自己的微信好友数据

    返回数据包含：
    - 好友基本信息：好友ID、好友姓名、好友微信号、我方微信号等
    - 沟通时间信息：下次沟通时间、我方最后消息时间、对方最后消息时间
    - 其他信息：备注、识别id等
    - 分页信息：总数、页码、每页条数、总页数

    使用场景：
    - 客户关系管理：查看需要主动沟通的好友列表
    - 时间管理：按时间优先级安排沟通计划
    - 数据统计：了解沟通频率和客户维护情况
    """
    try:
        from 服务.异步微信服务 import 异步获取微信好友下次沟通时间列表服务

        # 调用服务层处理业务逻辑
        结果 = await 异步获取微信好友下次沟通时间列表服务(
            用户id=当前登录用户["id"],
            我方微信号id=请求数据.我方微信号id,
            页码=请求数据.页码,
            每页条数=请求数据.每页条数,
        )

        # 服务层直接返回统一响应模型
        return 结果

    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 服务器内部错误
        错误日志器.error(
            f"路由接口_获取微信好友下次沟通时间列表异常: 用户id={当前登录用户['id']}, 错误={str(e)}"
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"处理请求时发生错误: {str(e)}"
            ).转字典(),
        )
