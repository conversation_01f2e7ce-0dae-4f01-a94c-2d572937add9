<template>
  <div class="store-page knowledge-management-page">
    <!-- 页面头部 -->
    <div class="store-page-header">
      <h1 class="store-page-title">
        <database-outlined class="store-title-icon" />
        知识库管理
      </h1>
      <p class="store-page-description">管理产品知识库、FAQ和培训资料</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <KnowledgeManagementModule />
    </div>
  </div>
</template>

<script setup>
import { DatabaseOutlined } from '@ant-design/icons-vue'
import KnowledgeManagementModule from '@/components/store/KnowledgeManagementModule.vue'
import '@/assets/css/store-common.css'

defineOptions({
  name: 'KnowledgeManagement'
})
</script>

<style scoped>
/* 页面特有样式 */
.page-content {
  background: #ffffff;
}

/* 知识库管理特有样式 */
.page-content :deep(.knowledge-tree) {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
  padding: 16px;
}

.page-content :deep(.ant-tree) {
  background: transparent;
}

.page-content :deep(.ant-tree-node-content-wrapper) {
  border-radius: 4px;
  transition: all 0.2s ease;
}

.page-content :deep(.ant-tree-node-content-wrapper:hover) {
  background: #e6f7ff;
}

.page-content :deep(.ant-tree-node-selected) {
  background: #bae7ff !important;
}

.page-content :deep(.knowledge-item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #ffffff;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.page-content :deep(.knowledge-item:hover) {
  border-color: #40a9ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.page-content :deep(.knowledge-icon) {
  font-size: 16px;
  color: #1890ff;
}

.page-content :deep(.knowledge-title) {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.page-content :deep(.knowledge-meta) {
  font-size: 12px;
  color: #666;
}

.page-content :deep(.knowledge-actions) {
  display: flex;
  gap: 4px;
}

.page-content :deep(.knowledge-editor) {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  min-height: 400px;
}

.page-content :deep(.knowledge-preview) {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  min-height: 400px;
}
</style>
