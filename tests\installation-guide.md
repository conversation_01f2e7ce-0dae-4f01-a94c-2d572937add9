# CRM System Stress Testing - Installation Guide

## 🛠️ Environment Requirements

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 16.0+ (Recommended 18.0+)
- **Memory**: Minimum 4GB RAM (Recommended 8GB+)
- **Network**: Stable internet connection

### Required Tools

#### 1. Install K6 (Primary stress testing tool)
```bash
# Windows (using Chocolatey)
choco install k6

# Windows (using Scoop)
scoop install k6

# macOS (using Homebrew)
brew install k6

# Linux (Ubuntu/Debian)
sudo gpg -k
sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6

# Or install globally using npm
npm install -g k6
```

#### 2. Install Artillery (Quick testing tool)
```bash
npm install -g artillery
```

#### 3. Verify Installation
```bash
# Check K6 version
k6 version

# Check Artillery version
artillery version

# Check Node.js version
node --version
```

## 🔧 Configuration Setup

### 1. Environment Variables Configuration
Create `.env` file (in project root):
```bash
# Test account password
TEST_PASSWORD=your_actual_password

# Optional: Custom API address
VITE_API_BASE_URL=https://invite.limob.cn
```

### 2. Test Configuration Adjustment
Edit `tests/config/test-config.js`:
```javascript
// Modify test account password
testAccounts: {
  primary: {
    phone: '***********',
    password: process.env.TEST_PASSWORD || 'your_password'
  }
}
```

## 🚀 Quick Start

### 1. Install Project Dependencies
```bash
npm install
```

### 2. Run Basic Tests
```bash
# Light stress test (recommended for first use)
npm run test:stress:light

# Standard stress test
npm run test:stress

# Artillery quick test
npm run test:artillery
```

### 3. Real-time Monitoring
Run in another terminal window:
```bash
npm run test:monitor
```

## 📊 Test Results

### View Results
- Test results are saved in `tests/results/` directory
- JSON format detailed reports
- Includes performance metrics and system monitoring data

### Result File Description
- `test-report-*.json`: Complete test report
- `k6-result-*.json`: K6 raw output
- `monitor-report-*.json`: System monitoring report

## ⚠️ Important Notes

### Safety Reminders
1. **Test only during off-peak hours** (recommended 2-6 AM)
2. **Start with light tests** (5-10 concurrent users)
3. **Monitor system resources in real-time** (CPU < 80%, Memory < 85%)
4. **Prepare emergency stop** (Ctrl+C for safe exit)

### Pre-test Checklist
- [ ] Confirm test account password is correct
- [ ] Check network connection is stable
- [ ] Confirm system resources are sufficient
- [ ] Notify relevant personnel of test time
- [ ] Prepare database backup

### Troubleshooting

#### K6 Installation Issues
```bash
# If npm installation fails, try direct download
# Windows: https://github.com/grafana/k6/releases
# Download and add to PATH environment variable
```

#### Permission Issues
```bash
# Linux/macOS may require sudo privileges
sudo npm install -g k6 artillery
```

#### Network Connection Issues
```bash
# Test network connection
curl -I https://invite.limob.cn

# Check firewall settings
# Ensure access to target server
```

## 📞 Technical Support

If you encounter issues:
1. Check the troubleshooting section in this document
2. Review error logs in `tests/results/`
3. Confirm all dependency tools are correctly installed
4. Verify network connection and permission settings

## 🔄 Update Instructions

Regularly update testing tools:
```bash
# Update K6
npm update -g k6

# Update Artillery  
npm update -g artillery

# Update project dependencies
npm update
```
