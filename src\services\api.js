/**
 * CRM前端API客户端
 *
 * 使用统一的API客户端工厂，提供：
 * - 统一的请求/响应拦截器
 * - 自动重试机制
 * - 标准化错误处理
 * - 环境自适应配置
 * - 业务状态码标准化
 */

import { 主API客户端, 获取Cookie值, 处理Token错误 } from './apiClientFactory'

// 使用统一的API客户端工厂创建的主API客户端
const api = 主API客户端

/**
 * 增强的API请求方法集合
 * 提供中文命名的API方法，保持向后兼容性
 */
const 增强API客户端 = {
  ...api,

  /**
   * 发送POST请求（中文方法名）
   * @param {string} 请求地址 - API请求URL
   * @param {any} 请求数据 - 要发送的数据
   * @param {Object} 请求配置 - 请求配置选项
   * @returns {Promise} 请求响应结果
   */
  async 发送POST请求(请求地址, 请求数据, 请求配置 = {}) {
    return api.post(请求地址, 请求数据, 请求配置)
  },

  /**
   * 发送GET请求（中文方法名）
   * @param {string} 请求地址 - API请求URL
   * @param {Object} 请求配置 - 请求配置选项
   * @returns {Promise} 请求响应结果
   */
  async 发送GET请求(请求地址, 请求配置 = {}) {
    return api.get(请求地址, 请求配置)
  }
}

// 导出API实例和辅助方法
export default 增强API客户端
export { 处理Token错误 as handleTokenError, 获取Cookie值 as getCookie }