"""
LangChain初始化管理器 - 统一管理各组件的初始化状态
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, Optional

# 配置日志
初始化管理日志器 = logging.getLogger("LangChain.初始化管理器")


class LangChain初始化管理器:
    """LangChain组件初始化状态管理器"""

    def __init__(self):
        self.初始化状态 = {}
        self.初始化锁 = asyncio.Lock()
        self.初始化时间 = {}
        self.初始化错误 = {}

    async def 确保组件已初始化(
        self, 组件名称: str, 组件实例: Any, 初始化方法: str = "初始化"
    ) -> bool:
        """确保组件已初始化，避免重复初始化"""
        try:
            # 检查是否已初始化
            if self._检查组件状态(组件名称, 组件实例):
                return True

            async with self.初始化锁:
                # 双重检查，防止并发初始化
                if self._检查组件状态(组件名称, 组件实例):
                    return True

                初始化管理日志器.info(f"🔄 开始初始化组件: {组件名称}")
                开始时间 = datetime.now()

                # 执行初始化
                if hasattr(组件实例, 初始化方法):
                    初始化函数 = getattr(组件实例, 初始化方法)
                    if asyncio.iscoroutinefunction(初始化函数):
                        await 初始化函数()
                    else:
                        初始化函数()
                else:
                    raise AttributeError(f"组件 {组件名称} 没有 {初始化方法} 方法")

                # 记录初始化状态
                结束时间 = datetime.now()
                初始化耗时 = (结束时间 - 开始时间).total_seconds()

                self.初始化状态[组件名称] = True
                self.初始化时间[组件名称] = {
                    "开始时间": 开始时间,
                    "结束时间": 结束时间,
                    "耗时": 初始化耗时,
                }

                # 清除之前的错误记录
                if 组件名称 in self.初始化错误:
                    del self.初始化错误[组件名称]

                初始化管理日志器.info(
                    f"✅ 组件初始化成功: {组件名称} (耗时: {初始化耗时:.2f}s)"
                )
                return True

        except Exception as e:
            # 记录初始化错误
            self.初始化状态[组件名称] = False
            self.初始化错误[组件名称] = {
                "错误信息": str(e),
                "错误时间": datetime.now(),
                "错误类型": type(e).__name__,
            }

            初始化管理日志器.error(f"❌ 组件初始化失败: {组件名称}, 错误: {str(e)}")
            return False

    def _检查组件状态(self, 组件名称: str, 组件实例: Any) -> bool:
        """检查组件是否已初始化"""
        # 检查内部状态记录
        if self.初始化状态.get(组件名称):
            return True

        # 检查组件自身的初始化状态
        if hasattr(组件实例, "已初始化"):
            return getattr(组件实例, "已初始化", False)

        return False

    def 获取初始化状态(self, 组件名称: Optional[str] = None) -> Dict[str, Any]:
        """获取组件初始化状态"""
        if 组件名称:
            return {
                "组件名称": 组件名称,
                "已初始化": self.初始化状态.get(组件名称, False),
                "初始化时间": self.初始化时间.get(组件名称),
                "初始化错误": self.初始化错误.get(组件名称),
            }
        else:
            return {
                "初始化状态": self.初始化状态,
                "初始化时间": self.初始化时间,
                "初始化错误": self.初始化错误,
                "统计信息": {
                    "已初始化组件数": sum(
                        1 for status in self.初始化状态.values() if status
                    ),
                    "初始化失败组件数": sum(
                        1 for status in self.初始化状态.values() if not status
                    ),
                    "总组件数": len(self.初始化状态),
                },
            }

    def 重置组件状态(self, 组件名称: str):
        """重置组件初始化状态，强制重新初始化"""
        if 组件名称 in self.初始化状态:
            del self.初始化状态[组件名称]
        if 组件名称 in self.初始化时间:
            del self.初始化时间[组件名称]
        if 组件名称 in self.初始化错误:
            del self.初始化错误[组件名称]

        初始化管理日志器.info(f"🔄 重置组件状态: {组件名称}")

    async def 批量初始化组件(self, 组件配置列表: list) -> Dict[str, bool]:
        """批量初始化多个组件"""
        初始化结果 = {}

        for 配置 in 组件配置列表:
            组件名称 = 配置["名称"]
            组件实例 = 配置["实例"]
            初始化方法 = 配置.get("初始化方法", "初始化")

            结果 = await self.确保组件已初始化(组件名称, 组件实例, 初始化方法)
            初始化结果[组件名称] = 结果

        return 初始化结果

    def 清理所有状态(self):
        """清理所有初始化状态"""
        self.初始化状态.clear()
        self.初始化时间.clear()
        self.初始化错误.clear()
        初始化管理日志器.info("🧹 清理所有初始化状态")


# 创建全局实例
初始化管理器实例 = LangChain初始化管理器()


# 便捷函数 - 统一组件映射
_组件映射 = {
    "RAG引擎": lambda: __import__(
        "服务.LangChain_RAG引擎", fromlist=["RAG引擎实例"]
    ).RAG引擎实例,
    "文档处理器": lambda: __import__(
        "服务.LangChain_文档处理器", fromlist=["LangChain文档处理器实例"]
    ).LangChain文档处理器实例,
    "知识库服务": lambda: __import__(
        "服务.LangChain_知识库服务", fromlist=["LangChain知识库服务实例"]
    ).LangChain知识库服务实例,
}


async def _确保组件已初始化(组件名称: str):
    """通用组件初始化函数"""
    组件实例 = _组件映射[组件名称]()
    return await 初始化管理器实例.确保组件已初始化(组件名称, 组件实例)


# 导出便捷函数
确保RAG引擎已初始化 = lambda: _确保组件已初始化("RAG引擎")
确保文档处理器已初始化 = lambda: _确保组件已初始化("文档处理器")
确保知识库服务已初始化 = lambda: _确保组件已初始化("知识库服务")


async def 批量初始化核心组件():
    """批量初始化核心LangChain组件"""
    组件配置 = [
        {"名称": 组件名称, "实例": 获取实例()}
        for 组件名称, 获取实例 in _组件映射.items()
    ]
    return await 初始化管理器实例.批量初始化组件(组件配置)
