"""
SuperAdmin 统计分析数据操作模块
负责处理接口调用统计、会员统计、用户调用分析等相关的数据库操作
超级管理员专用 - 权限等级最高

功能包括：
- 接口调用统计分析（支持时间段筛选、分页）
- 会员类型统计（有效、过期用户统计）
- 接口调用详情列表
- 激活码类型管理和批量生成
"""

import secrets
import string
from datetime import datetime
from typing import Any, Dict, List, Optional

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 接口日志器, 系统日志器, 错误日志器


async def 异步获取接口调用统计(
    时间段: str = "all",
    开始日期: Optional[str] = None,
    结束日期: Optional[str] = None,
    page: int = 1,  # 新增：当前页码
    size: int = 10,  # 新增：每页数量
) -> Dict[str, Any]:  # 修改返回类型为字典
    """
    异步获取指定时间段内的接口调用统计数据，支持分页

    Args:
        时间段 (str): 预设的时间范围 ('today', 'yesterday', 'week', 'month', 'last_month', 'year', 'all', 'custom'). 默认为 'all'.
        开始日期 (Optional[str]): 自定义时间段的开始日期 (YYYY-MM-DD). 仅在 时间段='custom' 时有效。
        结束日期 (Optional[str]): 自定义时间段的结束日期 (YYYY-MM-DD). 仅在 时间段='custom' 时有效。
        page (int): 请求的页码，默认为 1。
        size (int): 每页显示的记录数，默认为 10。

    Returns:
        Dict[str, Any]: 包含统计列表、分页信息和总计数据的字典。
           {
               "total": 总记录数 (接口路径总数),
               "page": 当前页码,
               "size": 每页数量,
               "list": 按调用次数降序排列的接口统计列表，包含调用人数、成功率。,
               "总调用次数": int,
               "成功调用": int,
               "失败调用": int,
               "平均响应时间": float
           }
    """
    try:
        offset = (page - 1) * size
        参数 = []
        参数总数 = []  # 用于总数查询的参数列表
        条件 = ""

        now = datetime.now()

        # --- 使用统一的时间范围工具进行时间段条件构建 ---
        try:
            # 导入统一的时间范围工具
            from 工具.时间范围工具 import 时间范围工具

            # 参数映射：将英文参数转换为中文参数
            参数映射 = {
                "today": "今日",
                "yesterday": "昨日",
                "week": "本周",
                "month": "本月",
                "last_month": "上月",
                "year": "本年",  # 注意：这里可能需要特殊处理
                "custom": "自定义",
                "all": "all",  # 保持原样
            }

            标准时间段 = 参数映射.get(时间段, 时间段)

            if 标准时间段 == "all":
                # 查询全部，不添加时间条件
                条件 = ""
                参数 = []
                参数总数 = []
            elif 标准时间段 == "本年":
                # 特殊处理本年：从1月1日到12月31日
                起始 = now.replace(
                    month=1, day=1, hour=0, minute=0, second=0, microsecond=0
                )
                结束 = now.replace(
                    month=12, day=31, hour=23, minute=59, second=59, microsecond=999999
                )
                条件 = " WHERE ijl.创建时间 >= $1 AND ijl.创建时间 < $2 "
                参数.extend([起始, 结束])
                参数总数.extend([起始, 结束])
            elif 标准时间段 == "自定义" and 开始日期 and 结束日期:
                # 自定义时间范围，使用统一工具处理
                try:
                    开始日期_obj = datetime.strptime(开始日期, "%Y-%m-%d").date()
                    结束日期_obj = datetime.strptime(结束日期, "%Y-%m-%d").date()
                    起始, 结束 = 时间范围工具.解析时间范围(
                        "自定义", 开始日期_obj, 结束日期_obj
                    )
                    条件 = " WHERE ijl.创建时间 >= $1 AND ijl.创建时间 < $2 "
                    参数.extend([起始, 结束])
                    参数总数.extend([起始, 结束])
                except ValueError:
                    系统日志器.warning(
                        f"自定义日期格式错误: 开始={开始日期}, 结束={结束日期}"
                    )
                    # 重置为查询全部
                    条件 = ""
                    参数 = []
                    参数总数 = []
            else:
                # 使用统一的时间范围工具解析
                起始, 结束 = 时间范围工具.解析时间范围(标准时间段)
                条件 = " WHERE ijl.创建时间 >= $1 AND ijl.创建时间 < $2 "
                参数.extend([起始, 结束])
                参数总数.extend([起始, 结束])

        except Exception as e:
            系统日志器.warning(f"时间范围解析失败: {时间段}, 错误: {e}")
            # 默认查询全部
            条件 = ""
            参数 = []
            参数总数 = []

        # --- 查询总接口路径数量 ---
        总数查询SQL = f"""
        SELECT COUNT(DISTINCT ijl.请求路径) AS total
        FROM 接口日志表 ijl
        {条件}
        """
        系统日志器.debug(
            f"Executing API Stats Count SQL ({时间段}): \n{总数查询SQL} with params: {参数总数}"
        )
        总数结果 = await 异步连接池实例.执行查询(总数查询SQL, 参数总数)
        总数 = (
            总数结果[0]["total"] if 总数结果 and 总数结果[0]["total"] is not None else 0
        )
        系统日志器.debug(f"Total distinct API paths found: {总数}")

        # --- 查询接口统计列表 (分页) ---
        列表查询SQL = f"""
        SELECT 
            ijl.请求路径 AS 接口路径,  -- 使用别名匹配模板
            COUNT(*) AS 调用次数,
            AVG(ijl.耗时) AS 平均响应时间,
            COUNT(DISTINCT ijl.用户id) AS 调用人数,
            MAX(ijl.创建时间) AS 最后调用时间, -- 新增：获取最后调用时间
            -- 计算成功率: 成功调用次数 / 总调用次数 * 100
            SUM(CASE WHEN ijl.状态码 >= 200 AND ijl.状态码 < 300 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS 成功率
        FROM 接口日志表 ijl
        {条件}
        GROUP BY ijl.请求路径 -- 按 请求路径 分组
        ORDER BY 调用次数 DESC
        LIMIT $3 OFFSET $4  -- 使用 LIMIT 和 OFFSET 进行分页
        """
        分页参数 = 参数 + [size, offset]  # 合并分页参数
        系统日志器.debug(
            f"Executing API Stats List SQL ({时间段}, Page: {page}, Size: {size}): \n{列表查询SQL} with params: {分页参数}"
        )
        列表结果 = await 异步连接池实例.执行查询(列表查询SQL, 分页参数)

        # --- 查询总体统计数据 ---
        总体统计SQL = f"""
        SELECT
            COUNT(*) AS 总调用次数,
            SUM(CASE WHEN ijl.状态码 >= 200 AND ijl.状态码 < 300 THEN 1 ELSE 0 END) AS 成功调用,
            SUM(CASE WHEN ijl.状态码 < 200 OR ijl.状态码 >= 300 THEN 1 ELSE 0 END) AS 失败调用,
            AVG(ijl.耗时) AS 平均响应时间
        FROM 接口日志表 ijl
        {条件}
        """
        系统日志器.debug(
            f"Executing API Overall Stats SQL ({时间段}): \n{总体统计SQL} with params: {参数总数}"
        )
        总体统计结果 = await 异步连接池实例.执行查询(总体统计SQL, 参数总数)

        总调用次数 = 0
        成功调用 = 0
        失败调用 = 0
        平均响应时间 = 0.0

        if 总体统计结果 and 总体统计结果[0]:
            总调用次数 = 总体统计结果[0].get("总调用次数", 0) or 0
            成功调用 = 总体统计结果[0].get("成功调用", 0) or 0
            失败调用 = 总体统计结果[0].get("失败调用", 0) or 0
            平均响应时间 = 总体统计结果[0].get("平均响应时间", 0.0) or 0.0

        return {
            "total": 总数,
            "page": page,
            "size": size,
            "list": 列表结果 if 列表结果 else [],
            "总调用次数": 总调用次数,
            "成功调用": 成功调用,
            "失败调用": 失败调用,
            "平均响应时间": round(平均响应时间, 2),  # 保留两位小数
        }

    except Exception as e:
        接口日志器.error(
            f"异步获取接口调用统计失败 (时间段: {时间段}, Page: {page}): {e}",
            exc_info=True,
        )
        return {
            "total": 0,
            "page": page,
            "size": size,
            "list": [],
            "总调用次数": 0,
            "成功调用": 0,
            "失败调用": 0,
            "平均响应时间": 0.0,
        }


async def 异步获取会员统计() -> List[Dict[str, Any]]:
    """
    异步获取会员统计数据，区分有效、过期和总数

    Returns:
        List[Dict[str, Any]]: 会员统计数据列表
    """
    统计结果 = []
    总付费会员数 = 0
    try:
        # 1. 查询每种会员类型的详细统计 - 基于会员关联表的新架构
        付费会员查询 = """
        SELECT
            m.名称 AS 会员类型,
            COUNT(um.用户id) AS 总人数,
            SUM(CASE WHEN um.到期时间 > NOW() THEN 1 ELSE 0 END) AS 有效人数,
            SUM(CASE WHEN um.到期时间 <= NOW() THEN 1 ELSE 0 END) AS 过期人数
        FROM
            会员表 m
        LEFT JOIN
            用户_会员_关联表 um ON m.id = um.会员id
        GROUP BY
            m.id, m.名称
        ORDER BY
            有效人数 DESC, 总人数 DESC;
        """
        付费会员结果 = await 异步连接池实例.执行查询(付费会员查询)

        if 付费会员结果:
            for 行 in 付费会员结果:
                总付费会员数 += 行.get("总人数", 0)  # 累加所有付费会员
                统计结果.append(
                    {
                        "会员类型": 行["会员类型"],
                        "总人数": 行.get("总人数", 0),
                        "有效人数": 行.get("有效人数", 0),
                        "过期人数": 行.get("过期人数", 0),
                    }
                )

        # 2. 查询总用户数
        # 直接查询避免循环导入
        结果 = await 异步连接池实例.执行查询("SELECT COUNT(*) AS 用户总数 FROM 用户表")
        总用户数 = 结果[0]["用户总数"] if 结果 else 0

        # 3. 计算免费用户数 (总用户 - 总付费会员数)
        # 注意：这里的免费用户可能包含从未成为付费会员的用户，以及所有付费会员身份都已过期的用户
        # 如果逻辑需要区分"纯免费"和"曾付费但已过期"，则需要更复杂的查询
        免费用户数 = max(0, 总用户数 - 总付费会员数)  # 确保不为负数

        # 4. 将免费用户添加到结果列表开头
        统计结果.insert(
            0,
            {
                "会员类型": "免费用户",
                "总人数": 免费用户数,
                "有效人数": 免费用户数,  # 免费用户总是"有效"
                "过期人数": 0,
            },
        )

        return 统计结果

    except Exception as e:
        错误日志器.error(f"异步获取会员统计失败: {e}", exc_info=True)
        # 出错时返回包含错误信息的默认结构或空列表
        return [
            {
                "会员类型": "免费用户",
                "总人数": 0,
                "有效人数": 0,
                "过期人数": 0,
                "错误": str(e),
            },
            {
                "会员类型": "付费用户(示例)",
                "总人数": 0,
                "有效人数": 0,
                "过期人数": 0,
                "错误": str(e),
            },
        ]


async def 异步获取接口调用详情列表(
    page: int = 1, size: int = 10, search: Optional[str] = None
) -> Dict[str, Any]:
    """
    异步获取接口调用详情列表，支持分页和搜索（按请求路径或用户id）

    Args:
        page (int): 当前页码，默认为 1。
        size (int): 每页数量，默认为 10。
        search (Optional[str]): 搜索关键词，可以匹配请求路径或用户id。

    Returns:
        Dict[str, Any]: 包含调用记录列表和分页信息的字典。
            {
                "total": 总记录数,
                "page": 当前页码,
                "size": 每页数量,
                "list": [
                    {
                        "id": 日志ID,
                        "用户id": 用户id,
                        "用户名": 用户名 (如果能关联查询到),
                        "请求路径": 请求路径,
                        "请求方法": 请求方法,
                        "状态码": 状态码,
                        "IP地址": IP地址,
                        "耗时": 耗时 (秒),
                        "创建时间": 创建时间
                    },
                    ...
                ]
            }
    """
    try:
        offset = (page - 1) * size
        参数 = []
        参数总数 = []

        # 基础查询语句 (恢复完整查询)
        查询基础 = """
        SELECT 
            ijl.id AS 日志ID, # 使用别名明确指定并重命名
            ijl.用户id, 
            u.昵称 AS 用户名,
            ijl.请求路径, 
            ijl.请求方法, 
            ijl.状态码, 
            ijl.ip地址, 
            ijl.耗时, 
            ijl.创建时间
        FROM 
            接口日志表 ijl
        LEFT JOIN 
            用户表 u ON ijl.用户id = u.id
        """

        # 总数查询基础 (恢复完整查询)
        总数查询基础 = """
        SELECT COUNT(ijl.id) as 总数 # 使用别名明确指定计数的列
        FROM 接口日志表 ijl
        LEFT JOIN 用户表 u ON ijl.用户id = u.id
        """

        # 构建搜索条件 (恢复原始逻辑)
        条件 = ""
        if search:
            # 尝试将搜索词转为整数，判断是否为用户id搜索
            try:
                search_user_id = int(search)
                # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
                # {{ Source: PostgreSQL参数占位符最佳实践 }}
                条件 = " WHERE ijl.请求路径 LIKE $1 OR ijl.用户id = $2 "
                参数.extend([f"%{search}%", search_user_id])
                参数总数.extend([f"%{search}%", search_user_id])
            except ValueError:
                # 如果不是纯数字，则只按请求路径搜索
                条件 = " WHERE ijl.请求路径 LIKE $1 "
                参数.append(f"%{search}%")
                参数总数.append(f"%{search}%")

        # 组合查询语句
        # 动态计算参数索引
        limit_param = len(参数) + 1
        offset_param = len(参数) + 2
        查询 = 查询基础 + 条件 + f" ORDER BY ijl.创建时间 DESC LIMIT ${limit_param} OFFSET ${offset_param}"
        总数查询 = 总数查询基础 + 条件

        参数.extend([size, offset])  # 分页参数

        # 执行查询
        系统日志器.debug(
            f"Executing API Call Count SQL: {总数查询} with params: {参数总数}"
        )
        总数结果 = await 异步连接池实例.执行查询(总数查询, 参数总数)
        总记录数 = 总数结果[0]["总数"] if 总数结果 else 0
        系统日志器.debug(f"Total API call records found: {总记录数}")

        系统日志器.debug(f"Executing API Call List SQL: {查询} with params: {参数}")
        结果 = await 异步连接池实例.执行查询(查询, 参数)
        系统日志器.debug(f"API Call list query results: {结果}")

        # 处理结果 (恢复完整处理逻辑)
        调用记录列表 = []
        for 记录 in 结果:
            记录_字典 = dict(记录)
            # 格式化时间
            if "创建时间" in 记录_字典 and 记录_字典["创建时间"]:
                记录_字典["创建时间"] = 记录_字典["创建时间"].strftime(
                    "%Y-%m-%d %H:%M:%S"
                )

            # 格式化耗时
            if "耗时" in 记录_字典 and 记录_字典["耗时"] is not None:
                try:
                    记录_字典["耗时"] = round(
                        float(记录_字典["耗时"]), 4
                    )  # 保留4位小数
                except (ValueError, TypeError):
                    记录_字典["耗时"] = 0.0
            else:
                记录_字典["耗时"] = 0.0

            # 处理可能为空的字段，使用正确的类型转换
            记录_字典["用户名"] = (
                记录_字典.get("用户名") if 记录_字典.get("用户名") is not None else "-"
            )
            记录_字典["用户id"] = (
                记录_字典.get("用户id") if 记录_字典.get("用户id") is not None else "-"
            )
            记录_字典["请求路径"] = (
                记录_字典.get("请求路径")
                if 记录_字典.get("请求路径") is not None
                else "-"
            )
            记录_字典["请求方法"] = (
                记录_字典.get("请求方法")
                if 记录_字典.get("请求方法") is not None
                else "-"
            )
            记录_字典["状态码"] = (
                记录_字典.get("状态码") if 记录_字典.get("状态码") is not None else "-"
            )
            记录_字典["IP地址"] = (
                记录_字典.get("IP地址") if 记录_字典.get("IP地址") is not None else "-"
            )
            # 日志ID 应该总是存在，因为它来自主键
            记录_字典["日志ID"] = 记录_字典.get("日志ID")  # 确认键名

            调用记录列表.append(记录_字典)

        return {"total": 总记录数, "page": page, "size": size, "list": 调用记录列表}
    except Exception as e:
        接口日志器.error(f"异步获取接口调用详情列表失败: {e}", exc_info=True)
        return {"total": 0, "page": page, "size": size, "list": []}


async def 异步获取激活码类型列表() -> List[Dict[str, Any]]:
    """
    异步获取所有激活码类型（ID 和 名称）

    Returns:
        List[Dict[str, Any]]: 包含激活码类型信息的字典列表，例如 [{'id': 1, '名称': '类型A'}, ...]
                                如果查询失败或没有类型，则返回空列表。
    """
    try:
        查询 = "SELECT id, 名称, 描述, 价格, 会员表id, 会员天数 FROM 激活码类型表 ORDER BY id ASC"
        结果 = await 异步连接池实例.执行查询(查询)
        return 结果 if 结果 else []
    except Exception as e:
        错误日志器.error(f"异步获取激活码类型列表失败: {e}", exc_info=True)
        return []  # 出错时返回空列表


async def 异步批量生成激活码(
    数量: int, 类型id: int, 备注: str, 是否为一次性激活: int = 1
) -> Dict[str, Any]:
    """
    异步批量生成并插入激活码

    Args:
        数量 (int): 要生成的激活码数量。
        类型id (int): 激活码关联的 card_plans 表的 ID。
        备注 (str): 激活码的备注。
        是否为一次性激活 (int): 激活码类型，1=一次性激活码，0=永久激活码。

    Returns:
        Dict[str, Any]: 操作结果，包含成功状态、消息和成功插入的数量。
    """
    if 数量 <= 0:
        return {"success": False, "message": "生成数量必须大于 0"}
    if not 类型id:
        return {"success": False, "message": "必须提供类型 ID"}
    if not 备注:
        return {"success": False, "message": "必须提供备注"}

    成功计数 = 0
    失败列表 = []
    最大尝试次数 = 数量 * 2  # 允许一些碰撞重试
    成功激活码列表 = []  # 新增：存储成功生成的激活码

    # 检查类型id是否存在
    try:
        类型结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 激活码类型表 WHERE id = $1", (类型id,)
        )
        if not 类型结果:
            return {"success": False, "message": f"类型 ID {类型id} 不存在"}
    except Exception as e:
        错误日志器.error(f"查询激活码类型失败: {e}", exc_info=True)
        return {"success": False, "message": f"查询激活码类型时出错: {str(e)}"}

    激活码列表 = []
    已生成集合 = set()  # 用于快速检查本次生成是否重复
    尝试次数 = 0

    # 定义字符集
    字符集 = string.ascii_letters + string.digits  # 大小写字母 + 数字

    while len(激活码列表) < 数量 and 尝试次数 < 最大尝试次数:
        尝试次数 += 1
        # 生成 16 位随机码
        激活码 = "".join(secrets.choice(字符集) for _ in range(16))

        # 检查本次生成是否重复
        if 激活码 in 已生成集合:
            continue

        # 检查数据库中是否已存在 (理论上碰撞概率极低，但为保险起见)
        try:
            存在结果 = await 异步连接池实例.执行查询(
                "SELECT id FROM 激活码表 WHERE 激活码 = $1", (激活码,)
            )
            if 存在结果:
                系统日志器.warning(f"生成的激活码 {激活码} 已存在于数据库，跳过。")
                continue
        except Exception as e:
            错误日志器.error(f"检查激活码 {激活码} 是否存在时出错: {e}", exc_info=True)
            失败列表.append(激活码)  # 记录检查失败的码
            continue  # 跳过这个码

        # 如果都通过，添加到列表和集合
        激活码列表.append((激活码, 类型id, 备注, 是否为一次性激活))
        已生成集合.add(激活码)

    if not 激活码列表:
        失败消息 = f"未能成功生成任何激活码。尝试次数: {尝试次数}."
        if 失败列表:
            失败消息 += f" 检查失败的码: {', '.join(失败列表)}"
        return {"success": False, "message": 失败消息, "成功数量": 0}

    # 批量插入数据库
    try:
        # 添加是否为一次性激活字段到插入语句中
        插入语句 = "INSERT INTO 激活码表 (激活码, 激活码类型表id, 备注, 是否为一次性激活, 创建时间) VALUES ($1, $2, $3, $4, NOW())"
        # executemany 在某些驱动下可能不支持或效率不高，这里用循环插入，但用事务保证原子性
        # 注意：如果连接池是单个连接，事务效果才明显。如果是连接池，每次可能是不同连接。
        # 对于简单场景，逐条插入也可以接受。更优方案是用 `executemany` 或构建大的 `VALUES` 列表。
        # 这里为了兼容性和简单性，采用循环插入。

        for 码, tid, 备注内容, 激活类型 in 激活码列表:
            try:
                await 异步连接池实例.执行插入(插入语句, (码, tid, 备注内容, 激活类型))
                成功计数 += 1
                成功激活码列表.append(码)  # 记录成功生成的激活码
            except Exception as insert_err:
                错误日志器.error(f"插入激活码 {码} 失败: {insert_err}")
                失败列表.append(码)  # 记录插入失败的码

    except Exception as e:
        错误日志器.error(f"批量插入激活码时发生意外错误: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"数据库操作失败: {str(e)}",
            "成功数量": 成功计数,
        }

    if 成功计数 == 数量:
        系统日志器.info(f"成功生成并插入 {成功计数} 张激活码")
        return {
            "success": True,
            "message": f"成功生成并插入 {成功计数} 张激活码。",
            "成功数量": 成功计数,
            "激活码列表": 成功激活码列表,
        }
    else:
        失败消息 = f"成功生成 {成功计数} 张，失败 {数量 - 成功计数} 张。"
        if 失败列表:
            失败消息 += f" 失败的码（部分）: {', '.join(失败列表[:5])}{'...' if len(失败列表) > 5 else ''}"
        return {
            "success": False,
            "message": 失败消息,
            "成功数量": 成功计数,
            "激活码列表": 成功激活码列表,
        }


# ==================== 激活码列表管理 ====================


async def 异步获取激活码列表(
    page: int = 1,
    size: int = 10,
    搜索关键词: str = "",
    类型id: Optional[int] = None,
    状态: Optional[str] = None,
    开始日期: Optional[str] = None,
    结束日期: Optional[str] = None,
) -> Dict[str, Any]:
    """
    异步获取激活码列表，支持分页、搜索和筛选

    Args:
        page (int): 当前页码，默认为1
        size (int): 每页数量，默认为10
        搜索关键词 (str): 搜索激活码或备注
        类型id (Optional[int]): 激活码类型id筛选
        状态 (Optional[str]): 激活码状态筛选 ('unused', 'used')
        开始日期 (Optional[str]): 创建时间开始日期
        结束日期 (Optional[str]): 创建时间结束日期

    Returns:
        Dict[str, Any]: 包含激活码列表和分页信息的字典
    """
    try:
        offset = (page - 1) * size

        # 构建查询条件
        条件列表 = []
        参数列表 = []
        参数索引 = 1

        if 搜索关键词:
            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL参数占位符最佳实践 }}
            条件列表.append(f"(a.激活码 LIKE ${参数索引} OR a.备注 LIKE ${参数索引 + 1})")
            参数列表.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])
            参数索引 += 2

        if 类型id:
            条件列表.append(f"a.激活码类型表id = ${参数索引}")
            参数列表.append(类型id)
            参数索引 += 1

        if 状态:
            if 状态 == "unused":
                条件列表.append("a.激活用户id IS NULL")
            elif 状态 == "used":
                条件列表.append("a.激活用户id IS NOT NULL")

        if 开始日期:
            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符索引错误. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL参数占位符最佳实践 }}
            条件列表.append(f"a.创建时间::date >= ${参数索引}")
            参数列表.append(开始日期)
            参数索引 += 1

        if 结束日期:
            条件列表.append(f"a.创建时间::date <= ${参数索引}")
            参数列表.append(结束日期)
            参数索引 += 1

        # 构建WHERE子句
        where_clause = ""
        if 条件列表:
            where_clause = f"WHERE {' AND '.join(条件列表)}"

        # 查询总数
        总数查询 = f"""
            SELECT COUNT(*) as total
            FROM 激活码表 a
            LEFT JOIN 激活码类型表 t ON a.激活码类型表id = t.id
            {where_clause}
        """

        总数结果 = await 异步连接池实例.执行查询(总数查询, 参数列表)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据列表
        数据查询 = f"""
            SELECT
                a.id,
                a.激活码,
                a.激活码类型表id,
                t.名称 as 类型名称,
                t.描述 as 类型描述,
                t.价格 as 类型价格,
                t.会员天数 as 会员天数,
                a.是否为一次性激活,
                a.激活用户id,
                u.昵称 as 用户昵称,
                a.备注,
                a.创建时间,
                a.使用时间,
                CASE
                    WHEN a.激活用户id IS NULL THEN '未使用'
                    ELSE '已使用'
                END as 状态
            FROM 激活码表 a
            LEFT JOIN 激活码类型表 t ON a.激活码类型表id = t.id
            LEFT JOIN 用户表 u ON a.激活用户id = u.id
            {where_clause}
            ORDER BY a.创建时间 DESC
            LIMIT $1 OFFSET $2
        """

        数据参数 = 参数列表 + [size, offset]
        数据结果 = await 异步连接池实例.执行查询(数据查询, 数据参数)

        return {
            "total": 总数,
            "page": page,
            "size": size,
            "list": 数据结果 if 数据结果 else [],
        }

    except Exception as e:
        错误日志器.error(f"异步获取激活码列表失败: {e}", exc_info=True)
        return {"total": 0, "page": page, "size": size, "list": []}


async def 异步获取激活码详情(激活码id: int) -> Optional[Dict[str, Any]]:
    """
    异步获取激活码详情

    Args:
        激活码id (int): 激活码id

    Returns:
        Optional[Dict[str, Any]]: 激活码详情信息，如果不存在则返回None
    """
    try:
        查询 = """
            SELECT 
                a.id,
                a.激活码,
                a.激活码类型表id,
                t.名称 as 类型名称,
                t.描述 as 类型描述,
                t.价格 as 类型价格,
                t.会员天数 as 会员天数,
                a.激活用户id,
                u.昵称 as 用户昵称,
                u.邮箱 as 用户邮箱,
                a.备注,
                a.创建时间,
                a.使用时间,
                CASE 
                    WHEN a.激活用户id IS NULL THEN '未使用'
                    ELSE '已使用'
                END as 状态
            FROM 激活码表 a
            LEFT JOIN 激活码类型表 t ON a.激活码类型表id = t.id
            LEFT JOIN 用户表 u ON a.激活用户id = u.id
            WHERE a.id = $1
        """

        结果 = await 异步连接池实例.执行查询(查询, (激活码id,))
        return 结果[0] if 结果 else None

    except Exception as e:
        错误日志器.error(f"异步获取激活码详情失败: {e}", exc_info=True)
        return None


async def 异步删除激活码(激活码id: int) -> Dict[str, Any]:
    """
    异步删除激活码

    Args:
        激活码id (int): 激活码id

    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        # 先检查激活码是否存在
        检查查询 = "SELECT id, 激活用户id FROM 激活码表 WHERE id = $1"
        检查结果 = await 异步连接池实例.执行查询(检查查询, (激活码id,))

        if not 检查结果:
            return {"success": False, "message": "激活码不存在"}

        # 检查激活码是否已被使用
        if 检查结果[0]["激活用户id"] is not None:
            return {"success": False, "message": "已使用的激活码不能删除"}

        # 删除激活码
        删除查询 = "DELETE FROM 激活码表 WHERE id = $1"
        await 异步连接池实例.执行插入(删除查询, (激活码id,))

        系统日志器.info(f"成功删除激活码 ID: {激活码id}")
        return {"success": True, "message": "激活码删除成功"}

    except Exception as e:
        错误日志器.error(f"异步删除激活码失败: {e}", exc_info=True)
        return {"success": False, "message": f"删除激活码失败: {str(e)}"}


async def 异步批量删除激活码(激活码id列表: List[int]) -> Dict[str, Any]:
    """
    异步批量删除激活码

    Args:
        激活码id列表 (List[int]): 激活码id列表

    Returns:
        Dict[str, Any]: 批量删除结果
    """
    try:
        if not 激活码id列表:
            return {"success": False, "message": "请选择要删除的激活码"}

        成功计数 = 0
        失败计数 = 0
        失败原因列表 = []

        for 激活码id in 激活码id列表:
            删除结果 = await 异步删除激活码(激活码id)
            if 删除结果.get("success"):
                成功计数 += 1
            else:
                失败计数 += 1
                失败原因列表.append(f"ID {激活码id}: {删除结果.get('message')}")

        if 失败计数 == 0:
            return {"success": True, "message": f"成功删除 {成功计数} 个激活码"}
        else:
            return {
                "success": False,
                "message": f"成功删除 {成功计数} 个，失败 {失败计数} 个",
                "details": 失败原因列表,
            }

    except Exception as e:
        错误日志器.error(f"异步批量删除激活码失败: {e}", exc_info=True)
        return {"success": False, "message": f"批量删除激活码失败: {str(e)}"}


async def 异步获取激活码统计() -> Dict[str, Any]:
    """
    异步获取激活码统计信息

    Returns:
        Dict[str, Any]: 激活码统计数据
    """
    try:
        # 基础统计
        基础统计查询 = """
            SELECT 
                COUNT(*) as 总数,
                COUNT(CASE WHEN 激活用户id IS NULL THEN 1 END) as 未使用数量,
                COUNT(CASE WHEN 激活用户id IS NOT NULL THEN 1 END) as 已使用数量
            FROM 激活码表
        """

        基础统计结果 = await 异步连接池实例.执行查询(基础统计查询)
        基础统计 = 基础统计结果[0] if 基础统计结果 else {}

        # 按类型统计
        类型统计查询 = """
            SELECT 
                t.名称 as 类型名称,
                COUNT(a.id) as 激活码数量,
                COUNT(CASE WHEN a.激活用户id IS NULL THEN 1 END) as 未使用数量,
                COUNT(CASE WHEN a.激活用户id IS NOT NULL THEN 1 END) as 已使用数量
            FROM 激活码类型表 t
            LEFT JOIN 激活码表 a ON t.id = a.激活码类型表id
            GROUP BY t.id, t.名称
            ORDER BY 激活码数量 DESC
        """

        类型统计结果 = await 异步连接池实例.执行查询(类型统计查询)

        # 近期使用统计
        近期使用查询 = """
            SELECT
                使用时间::date as 使用日期,
                COUNT(*) as 使用数量
            FROM 激活码表
            WHERE 使用时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
            GROUP BY 使用时间::date
            ORDER BY 使用日期 DESC
        """

        近期使用结果 = await 异步连接池实例.执行查询(近期使用查询)

        return {
            "基础统计": 基础统计,
            "类型统计": 类型统计结果 if 类型统计结果 else [],
            "近期使用": 近期使用结果 if 近期使用结果 else [],
        }

    except Exception as e:
        错误日志器.error(f"异步获取激活码统计失败: {e}", exc_info=True)
        return {"基础统计": {}, "类型统计": [], "近期使用": []}


# ==================== 激活码类型管理 ====================


async def 异步创建激活码类型(
    名称: str, 描述: str, 价格: float, 会员表id: int, 会员天数: int
) -> Dict[str, Any]:
    """
    异步创建激活码类型

    Args:
        名称 (str): 类型名称
        描述 (str): 类型描述
        价格 (float): 类型价格
        会员表id (int): 关联的会员表id
        会员天数 (int): 会员天数

    Returns:
        Dict[str, Any]: 创建结果
    """
    try:
        # 检查名称是否已存在
        检查查询 = "SELECT id FROM 激活码类型表 WHERE 名称 = $1"
        检查结果 = await 异步连接池实例.执行查询(检查查询, (名称,))

        if 检查结果:
            return {"success": False, "message": "激活码类型名称已存在"}

        # 插入新的激活码类型
        插入查询 = """
            INSERT INTO 激活码类型表 (名称, 描述, 价格, 会员表id, 会员天数) 
            VALUES ($1, $2, $3, $4, $5)
        """

        插入结果 = await 异步连接池实例.执行插入(
            插入查询, (名称, 描述, 价格, 会员表id, 会员天数)
        )

        if 插入结果:
            # 获取插入的记录
            获取查询 = "SELECT * FROM 激活码类型表 WHERE id = LAST_INSERT_ID()"
            新记录 = await 异步连接池实例.执行查询(获取查询)

            系统日志器.info(f"成功创建激活码类型: {名称}")
            return {
                "success": True,
                "message": "激活码类型创建成功",
                "data": 新记录[0] if 新记录 else None,
            }
        else:
            return {"success": False, "message": "创建激活码类型失败"}

    except Exception as e:
        错误日志器.error(f"异步创建激活码类型失败: {e}", exc_info=True)
        return {"success": False, "message": f"创建激活码类型失败: {str(e)}"}


async def 异步更新激活码类型(
    类型id: int, 名称: str, 描述: str, 价格: float, 会员表id: int, 会员天数: int
) -> Dict[str, Any]:
    """
    异步更新激活码类型

    Args:
        类型id (int): 激活码类型id
        名称 (str): 类型名称
        描述 (str): 类型描述
        价格 (float): 类型价格
        会员表id (int): 关联的会员表id
        会员天数 (int): 会员天数

    Returns:
        Dict[str, Any]: 更新结果
    """
    try:
        # 检查类型是否存在
        检查查询 = "SELECT id FROM 激活码类型表 WHERE id = $1"
        检查结果 = await 异步连接池实例.执行查询(检查查询, (类型id,))

        if not 检查结果:
            return {"success": False, "message": "激活码类型不存在"}

        # 检查名称是否与其他记录冲突
        名称检查查询 = "SELECT id FROM 激活码类型表 WHERE 名称 = $1 AND id != $2"
        名称检查结果 = await 异步连接池实例.执行查询(名称检查查询, (名称, 类型id))

        if 名称检查结果:
            return {"success": False, "message": "激活码类型名称已存在"}

        # 更新激活码类型
        更新查询 = """
            UPDATE 激活码类型表 
            SET 名称 = $1, 描述 = $2, 价格 = $3, 会员表id = $4, 会员天数 = $5
            WHERE id = $1
        """

        await 异步连接池实例.执行插入(
            更新查询, (名称, 描述, 价格, 会员表id, 会员天数, 类型id)
        )

        # 获取更新后的记录
        获取查询 = "SELECT * FROM 激活码类型表 WHERE id = $1"
        更新记录 = await 异步连接池实例.执行查询(获取查询, (类型id,))

        系统日志器.info(f"成功更新激活码类型: {类型id}")
        return {
            "success": True,
            "message": "激活码类型更新成功",
            "data": 更新记录[0] if 更新记录 else None,
        }

    except Exception as e:
        错误日志器.error(f"异步更新激活码类型失败: {e}", exc_info=True)
        return {"success": False, "message": f"更新激活码类型失败: {str(e)}"}


async def 异步删除激活码类型(类型id: int) -> Dict[str, Any]:
    """
    异步删除激活码类型

    Args:
        类型id (int): 激活码类型id

    Returns:
        Dict[str, Any]: 删除结果
    """
    try:
        # 检查类型是否存在
        检查查询 = "SELECT id, 名称 FROM 激活码类型表 WHERE id = $1"
        检查结果 = await 异步连接池实例.执行查询(检查查询, (类型id,))

        if not 检查结果:
            return {"success": False, "message": "激活码类型不存在"}

        类型名称 = 检查结果[0]["名称"]

        # 检查是否有激活码使用此类型
        使用检查查询 = (
            "SELECT COUNT(*) as count FROM 激活码表 WHERE 激活码类型表id = $1"
        )
        使用检查结果 = await 异步连接池实例.执行查询(使用检查查询, (类型id,))

        if 使用检查结果 and 使用检查结果[0]["count"] > 0:
            return {
                "success": False,
                "message": f"无法删除类型'{类型名称}'，已有 {使用检查结果[0]['count']} 个激活码使用此类型",
            }

        # 删除激活码类型
        删除查询 = "DELETE FROM 激活码类型表 WHERE id = $1"
        await 异步连接池实例.执行插入(删除查询, (类型id,))

        系统日志器.info(f"成功删除激活码类型: {类型id} - {类型名称}")
        return {"success": True, "message": f"激活码类型'{类型名称}'删除成功"}

    except Exception as e:
        错误日志器.error(f"异步删除激活码类型失败: {e}", exc_info=True)
        return {"success": False, "message": f"删除激活码类型失败: {str(e)}"}


async def 异步获取激活码类型详情(类型id: int) -> Optional[Dict[str, Any]]:
    """
    异步获取激活码类型详情

    Args:
        类型id (int): 激活码类型id

    Returns:
        Optional[Dict[str, Any]]: 激活码类型详情，如果不存在则返回None
    """
    try:
        查询 = """
            SELECT 
                t.id,
                t.名称,
                t.描述,
                t.价格,
                t.会员表id,
                t.会员天数,
                COUNT(a.id) as 激活码数量,
                COUNT(CASE WHEN a.激活用户id IS NULL THEN 1 END) as 未使用数量,
                COUNT(CASE WHEN a.激活用户id IS NOT NULL THEN 1 END) as 已使用数量
            FROM 激活码类型表 t
            LEFT JOIN 激活码表 a ON t.id = a.激活码类型表id
            WHERE t.id = $1
            GROUP BY t.id
        """

        结果 = await 异步连接池实例.执行查询(查询, (类型id,))
        return 结果[0] if 结果 else None

    except Exception as e:
        错误日志器.error(f"异步获取激活码类型详情失败: {e}", exc_info=True)
        return None


async def 异步获取激活码类型列表带统计(
    page: int = 1, size: int = 10, 搜索关键词: str = ""
) -> Dict[str, Any]:
    """
    异步获取激活码类型列表，包含统计信息

    Args:
        page (int): 当前页码，默认为1
        size (int): 每页数量，默认为10
        搜索关键词 (str): 搜索关键词

    Returns:
        Dict[str, Any]: 包含激活码类型列表和分页信息的字典
    """
    try:
        offset = (page - 1) * size

        # 构建查询条件
        条件列表 = []
        参数列表 = []

        if 搜索关键词:
            条件列表.append("(t.名称 LIKE $1 OR t.描述 LIKE $2)")
            参数列表.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])

        # 构建WHERE子句
        where_clause = ""
        if 条件列表:
            where_clause = f"WHERE {' AND '.join(条件列表)}"

        # 查询总数
        总数查询 = f"SELECT COUNT(*) as total FROM 激活码类型表 t {where_clause}"
        总数结果 = await 异步连接池实例.执行查询(总数查询, 参数列表)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据列表
        数据查询 = f"""
            SELECT 
                t.id,
                t.名称,
                t.描述,
                t.价格,
                t.会员表id,
                t.会员天数,
                COUNT(a.id) as 激活码数量,
                COUNT(CASE WHEN a.激活用户id IS NULL THEN 1 END) as 未使用数量,
                COUNT(CASE WHEN a.激活用户id IS NOT NULL THEN 1 END) as 已使用数量
            FROM 激活码类型表 t
            LEFT JOIN 激活码表 a ON t.id = a.激活码类型表id
            {where_clause}
            GROUP BY t.id
            ORDER BY t.id DESC
            LIMIT $1 OFFSET $2
        """

        数据参数 = 参数列表 + [size, offset]
        数据结果 = await 异步连接池实例.执行查询(数据查询, 数据参数)

        return {
            "total": 总数,
            "page": page,
            "size": size,
            "list": 数据结果 if 数据结果 else [],
        }

    except Exception as e:
        错误日志器.error(f"异步获取激活码类型列表带统计失败: {e}", exc_info=True)
        return {"total": 0, "page": page, "size": size, "list": []}
