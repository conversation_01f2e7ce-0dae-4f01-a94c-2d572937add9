<template>
  <a-modal
    v-model:open="isModalOpen"
    title="创建团队"
    @ok="handleCreateTeam"
    :confirm-loading="createTeamLoading"
    @cancel="closeModal"
  >
    <a-form
      ref="createTeamFormRef"
      :model="createTeamForm"
      :rules="createTeamRules"
      layout="vertical"
    >
      <a-form-item label="团队名称" name="团队名称">
        <a-input 
          v-model:value="createTeamForm.团队名称"
          placeholder="请输入团队名称"
        />
      </a-form-item>
      
      <a-form-item label="所属公司" name="公司ID">
        <a-select
          v-model:value="createTeamForm.公司ID"
          placeholder="请选择所属公司"
          :loading="companiesLoading"
          show-search
          :filter-option="false"
          @search="searchCompanies"
          :options="companies.map(c => ({ value: c.公司ID, label: c.公司名称 }))"
        >
        </a-select>
      </a-form-item>
      
      <a-form-item label="团队描述" name="团队描述">
        <a-textarea 
          v-model:value="createTeamForm.团队描述"
          placeholder="请输入团队描述（可选）"
          :rows="3"
        />
      </a-form-item>
      
      <!-- 最大成员数已改为后端根据用户会员权限自动设置，无需前端输入 -->
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import teamService from '../../services/team';
import { TEAM_STATUS } from '../../constants/businessStatus';

// 定义props和emits，与父组件通信
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:open', 'success']);

// 控制弹窗显示，与props同步
const isModalOpen = computed({
  get: () => props.open,
  set: (val) => emit('update:open', val),
});

const closeModal = () => {
  isModalOpen.value = false;
};

// 创建团队相关逻辑
const createTeamLoading = ref(false);
const createTeamFormRef = ref();
const createTeamForm = reactive({
  团队名称: '',
  公司ID: undefined,
  团队描述: '',
  // 最大成员数已移除，由后端根据用户会员权限自动设置
});

const createTeamRules = {
  团队名称: [
    { required: true, message: '请输入团队名称', trigger: 'blur' },
    { min: 1, max: 255, message: '团队名称长度在1到255个字符', trigger: 'blur' },
  ],
  公司ID: [
    { required: true, message: '请选择所属公司', trigger: 'change' },
  ],
};

// 公司列表相关逻辑
const companies = ref([]);
const companiesLoading = ref(false);

let searchCompanyTimer = null;
const searchCompanies = (searchKeyword) => {
    clearTimeout(searchCompanyTimer);
    searchCompanyTimer = setTimeout(() => {
        loadCompanies(searchKeyword);
    }, 300);
};

const loadCompanies = async (searchKeyword = '') => {
  try {
    companiesLoading.value = true;
    const response = await teamService.getCompanyList({
      页码: 1,
      每页数量: 50,
      搜索关键词: searchKeyword,
    });
    if ([100, 0, 1].includes(response.status)) {
      const data = response.data || response.message;
      companies.value = data?.公司列表 || data?.list || [];
    }
  } catch (error) {
    console.error('加载公司列表失败:', error);
  } finally {
    companiesLoading.value = false;
  }
};

// 提交创建
const handleCreateTeam = async () => {
  try {
    await createTeamFormRef.value.validate();
    createTeamLoading.value = true;

    const response = await teamService.createTeam(createTeamForm);

    message.success('团队创建成功');
    closeModal();

    // 重置表单
    createTeamFormRef.value.resetFields();
    Object.assign(createTeamForm, {
      团队名称: '',
      公司ID: undefined,
      团队描述: '',
      // 最大成员数已移除，由后端根据用户会员权限自动设置
    });

    // 触发成功事件
    emit('success');
  } catch (error) {
    if (error.errorFields) return;

    // 处理特定的业务错误
    if (error.isLimitReached) {
      message.warning({
        content: error.message,
        duration: 6
      });
    } else {
      message.error(error.message);
    }
  } finally {
    createTeamLoading.value = false;
  }
};

// 初始加载公司
onMounted(() => {
    loadCompanies();
});

</script> 