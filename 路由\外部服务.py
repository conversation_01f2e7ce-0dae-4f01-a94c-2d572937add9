from typing import Any

import httpx
from fastapi import APIRouter, HTTPException, Query, Request

from 日志 import 应用日志器, 错误日志器

# 创建外部服务路由器
外部服务路由 = APIRouter(
    prefix="/proxy",
    tags=["外部服务代理"],
    responses={404: {"description": "接口未找到"}},
)

外部达人搜索API地址 = "https://daduoduo.com/ajax/dyLiveDataAjax.ashx"

# 定义一个通用的浏览器User-Agent
COMMON_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36"


@外部服务路由.get("/search-daren", response_model=Any)
async def 代理搜索外部达人(
    request: Request,
    keyword: str = Query(
        ..., title="搜索关键词", description="用于搜索外部达人的抖音号或名称"
    ),
):
    """
    代理前端请求，使用 GET 方法搜索外部达人数据。
    直接调用 daduoduo.com 的接口并返回其结果。
    """
    params = {
        "action": "GetSearchTipForPeople",
        "keyword": keyword,
        "sortType": "1",  # 根据您提供的URL，sortType=1，作为查询参数通常是字符串
    }

    headers = {
        "User-Agent": request.headers.get("User-Agent", "Mozilla/5.0")
    }  # 恢复为简单实现

    应用日志器.info(
        f"开始代理外部达人搜索 (GET)，关键词: {keyword}, 参数: {params}, 请求头: {headers}"
    )

    async with httpx.AsyncClient(timeout=15.0) as client:
        try:
            response = await client.get(
                外部达人搜索API地址, params=params, headers=headers
            )
            response.raise_for_status()

            应用日志器.info(
                f"外部达人搜索成功 (GET)，关键词: {keyword}, 状态码: {response.status_code}"
            )
            # 尝试解析JSON，如果内容不是JSON，会在这里失败并进入except块
            return response.json()
        except httpx.TimeoutException:
            错误日志器.error(f"代理外部达人搜索超时 (GET)，关键词: {keyword}")
            raise HTTPException(status_code=504, detail="外部服务请求超时")
        except httpx.HTTPStatusError as e:
            错误日志器.error(
                f"代理外部达人搜索HTTP状态错误 (GET): {e.response.status_code}，关键词: {keyword}, 响应体 (前200字符): {e.response.text[:200]}"
            )
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"外部服务返回错误: {e.response.status_code}",
            )
        except httpx.RequestError as e:  # 更广泛地捕获请求相关的错误
            错误日志器.error(
                f"代理外部达人搜索请求错误 (GET): {str(e)}，关键词: {keyword}"
            )
            raise HTTPException(
                status_code=503, detail=f"请求外部服务时发生错误: {str(e)}"
            )
        except Exception as e:  # 包括json.JSONDecodeError等
            错误日志器.error(
                f"代理外部达人搜索未知错误或JSON解析失败 (GET): {str(e)}，关键词: {keyword}, 响应文本 (前200字符): {response.text[:200] if 'response' in locals() else 'N/A'}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"代理请求处理时发生内部错误或无法解析响应: {str(e)}",
            )
