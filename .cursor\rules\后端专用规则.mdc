---
description: 
globs: 
alwaysApply: true
---
# 后端专用规则

## 🎯 项目特色

此项目是基于 Python FastAPI 的 CRM 邀请系统后端，与前端项目 [limob-crm-front-end](mdc:) 协同工作。

## 📋 核心规范引用

遵循工作区级别的开发规范：
- **开发规范**：[工作区核心规范.mdc](mdc:invitation-system-backend/invitation-system-backend/limob-crm-front-end/invitation-system-backend/limob-crm-front-end/.cursor/rules/工作区核心规范.mdc)
- **AI智能配置**：[Cursor智能助手配置.mdc](mdc:invitation-system-backend/invitation-system-backend/limob-crm-front-end/invitation-system-backend/limob-crm-front-end/.cursor/rules/Cursor智能助手配置.mdc)
- **数据库规范**：[Mysql命名规则.mdc](mdc:invitation-system-backend/invitation-system-backend/limob-crm-front-end/invitation-system-backend/limob-crm-front-end/.cursor/rules/Mysql命名规则.mdc)



## 🐍 Python FastAPI 特定规范

### API响应标准
```python
# 统一响应格式
{"status": 100, "message": 数据内容}  # 成功
{"status": 错误码, "message": 错误信息}  # 失败
部分旧接口status成功会返回0或1 请注意
```

### 路由组织规范
```
路由/
├── 用户管理.py
├── 团队管理.py
├── 邀请管理.py
└── 系统管理.py
```

### 数据层规范
```
数据/
├── 用户数据.py
├── 团队数据.py
├── 邀请数据.py
└── 系统数据.py
```

### 中文命名约定
- **路由函数**：`创建用户`, `获取用户列表`, `更新用户信息`
- **数据库操作**：`查询用户数据`, `插入新记录`, `更新记录状态`
- **业务逻辑**：`处理邀请逻辑`, `验证用户权限`, `生成邀请码`

## 🔗 前后端协作

### API接口对接
确保与前端项目的API调用方式保持一致：
```javascript
// 前端调用方式（参考）
const { executeRequest } = useApiService('用户管理API服务')
const 用户数据 = await executeRequest('getUserList', [查询参数])
```

### 数据格式统一
严格按照前后端约定的数据格式进行交互，确保字段名称和数据类型一致。

## 📝 开发注意事项

1. **编码前必读**：[memory.md](mdc:invitation-system-backend/invitation-system-backend/limob-crm-front-end/invitation-system-backend/limob-crm-front-end/memory.md) 了解项目历史和错误记录
2. **API文档更新**：新增或修改API后及时更新文档
3. **错误处理**：使用统一的异常处理机制
4. **日志记录**：重要操作必须记录详细日志

- 修改或者添加相关接口代码时，如果和数据库有关，请用mcp工具“mysql_query”来查看数据库表名和字段名
- 如果一个文件的代码将会超过1000行，那么请你把他根据函数拆分成多个文件，确保优雅。
## 🚀 部署相关

- 遵循生产环境的安全配置要求
- 确保数据库连接池配置正确
- API性能监控和优化


# 前后端协作规范

## 🌐 API响应标准
所有API响应必须遵循以下统一格式:
```json
{
  "status": 100,  // 状态码，100表示成功
  "message": "操作成功/错误信息",  // 用户通知消息
  "data": {...}  // 业务数据
}
```

**状态码规范**:
- `status: 100` - 表示操作成功
- 其他状态码 - 表示特定错误，具体含义参考各模块状态码定义
- **兼容说明**: 部分旧接口可能使用`0`或`1`表示成功，需查看后端代码确认

**字段用途严格区分**:
- `data`: 仅用于存放业务数据，前端从此字段获取数据进行展示和处理
- `message`: 仅用于存放用户通知消息，适合直接展示给用户
- 严禁在`message`字段中存放业务数据或在`data`字段中存放提示信息

