<template>
  <div class="business-process-panel">
    <a-card :loading="loading" :bordered="false">
      <template #title>
        <div class="panel-header">
          <span class="panel-title">业务流程</span>
          <div class="header-actions">
            <a-tooltip title="刷新数据">
              <a-button type="text" :disabled="loading" @click="refreshData">
                <template #icon><ReloadOutlined :spin="loading" /></template>
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </template>
      
      <!-- 业务流程阶段展示 -->
      <div class="process-stages">
        <a-steps :current="-1" direction="vertical">
          <a-step v-for="(stage, index) in processStages" :key="index" :title="stage.title">
            <template #description>
              <div class="stage-description">
                <div class="stage-metrics">
                  <div class="metric-item">
                    <span class="metric-label">总数:</span>
                    <span class="metric-value">{{ formatNumber(stage.总数) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">今日新增:</span>
                    <span class="metric-value">{{ formatNumber(stage.今日新增) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">转化率:</span>
                    <span class="metric-value">{{ stage.转化率 }}%</span>
                  </div>
                </div>
                <div class="stage-chart">
                  <a-progress
                    :percent="stage.完成率"
                    :stroke-color="getStatusColor(stage.title)"
                    size="small"
                  />
                </div>
              </div>
            </template>
          </a-step>
        </a-steps>
      </div>
      
      <!-- 业务流程统计卡片 -->
      <div class="process-stats">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(stat, index) in processStats" :key="index">
            <a-card class="stat-card" :bordered="false">
              <a-statistic
                :title="stat.title"
                :value="stat.value"
                :precision="stat.precision || 0"
                :value-style="{ color: stat.color }"
                :prefix="h(stat.icon)"
              />
              <div class="stat-footer">
                <span class="stat-trend" v-if="stat.trend">
                  <span :class="['trend-icon', stat.trend.direction]">
                    <component :is="stat.trend.direction === 'up' ? 'rise-outlined' : (stat.trend.direction === 'down' ? 'fall-outlined' : 'minus-outlined')" />
                  </span>
                  <span>{{ stat.trend.value }}%</span>
                </span>
                <span>{{ stat.period || '近7天' }}</span>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
      
      <!-- 业务流程漏斗图 -->
      <div class="process-funnel">
        <div class="funnel-title">业务转化漏斗</div>
        <div class="funnel-chart">
          <div v-for="(stage, index) in processStages" :key="index" class="funnel-stage" :style="{
            width: `${85 - index * 15}%`,
            backgroundColor: getStatusColor(stage.title)
          }">
            <span class="funnel-stage-title">{{ stage.title }}</span>
            <span class="funnel-stage-value">{{ formatNumber(stage.总数) }}</span>
          </div>
        </div>
        <div class="funnel-legend">
          <div v-for="(stage, index) in processStages" :key="index" class="legend-item">
            <span class="legend-color" :style="{ backgroundColor: getStatusColor(stage.title) }"></span>
            <span class="legend-text">{{ stage.title }}</span>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { 
  UserOutlined, 
  TeamOutlined, 
  CalendarOutlined, 
  MessageOutlined,
  UserAddOutlined,
  ReloadOutlined,
  GiftOutlined,
  PlayCircleOutlined,
  RiseOutlined,
  FallOutlined,
  MinusOutlined,
  LineChartOutlined
} from '@ant-design/icons-vue'
import { teamDashboardService } from '../../../services/team/teamDashboard'
import { 
  formatNumber, 
  getStatusColor,
  calculateCompletionRate
} from '../../../utils/dashboardUtils'

// 定义组件属性
const props = defineProps({
  teamId: {
    type: [Number, String],
    required: true,
    validator: (value) => {
      if (value === null || value === undefined) {
        console.warn('BusinessProcessPanel: teamId不能为null或undefined')
        return false
      }
      return true
    }
  }
})

// 定义事件
const emit = defineEmits(['refresh-completed'])

// 响应式数据
const loading = ref(false)
const processData = ref({})

// 计算属性：业务流程阶段
const processStages = computed(() => {
  if (!processData.value || !processData.value.阶段数据) return []
  
  const stages = [
    {
      title: '达人邀约',
      总数: processData.value.阶段数据.总邀约数量 || 0,
      今日新增: processData.value.阶段数据.今日邀约数量 || 0,
      转化率: 100, // 第一阶段转化率为100%
      完成率: 100 // 第一阶段完成率为100%
    },
    {
      title: '意向确认',
      总数: processData.value.阶段数据.意向确认数量 || 0,
      今日新增: processData.value.阶段数据.今日新增意向 || 0,
      转化率: calculateCompletionRate(
        processData.value.阶段数据.意向确认数量 || 0,
        processData.value.阶段数据.总邀约数量 || 1
      ),
      完成率: calculateCompletionRate(
        processData.value.阶段数据.意向确认数量 || 0,
        processData.value.阶段数据.总邀约数量 || 1
      )
    },
    {
      title: '样品确认',
      总数: processData.value.阶段数据.样品确认数量 || 0,
      今日新增: processData.value.阶段数据.今日新增样品 || 0,
      转化率: calculateCompletionRate(
        processData.value.阶段数据.样品确认数量 || 0,
        processData.value.阶段数据.意向确认数量 || 1
      ),
      完成率: calculateCompletionRate(
        processData.value.阶段数据.样品确认数量 || 0,
        processData.value.阶段数据.意向确认数量 || 1
      )
    },
    {
      title: '排期确认',
      总数: processData.value.阶段数据.排期确认数量 || 0,
      今日新增: processData.value.阶段数据.今日新增排期 || 0,
      转化率: calculateCompletionRate(
        processData.value.阶段数据.排期确认数量 || 0,
        processData.value.阶段数据.样品确认数量 || 1
      ),
      完成率: calculateCompletionRate(
        processData.value.阶段数据.排期确认数量 || 0,
        processData.value.阶段数据.样品确认数量 || 1
      )
    },
    {
      title: '开播完成',
      总数: processData.value.阶段数据.开播完成数量 || 0,
      今日新增: processData.value.阶段数据.今日新增开播 || 0,
      转化率: calculateCompletionRate(
        processData.value.阶段数据.开播完成数量 || 0,
        processData.value.阶段数据.排期确认数量 || 1
      ),
      完成率: calculateCompletionRate(
        processData.value.阶段数据.开播完成数量 || 0,
        processData.value.阶段数据.排期确认数量 || 1
      )
    }
  ]
  
  return stages
})

// 计算属性：业务流程统计
const processStats = computed(() => {
  if (!processData.value || !processData.value.阶段数据) return []
  
  return [
    {
      title: '总邀约数',
      value: processData.value.阶段数据.总邀约数量 || 0,
      icon: UserAddOutlined,
      color: '#1890ff',
      period: '累计'
    },
    {
      title: '今日邀约数',
      value: processData.value.阶段数据.今日邀约数量 || 0,
      icon: UserAddOutlined,
      color: '#52c41a'
    },
    {
      title: '今日意向确认',
      value: processData.value.阶段数据.今日新增意向 || 0,
      icon: MessageOutlined,
      color: '#faad14'
    },
    {
      title: '今日寄样数',
      value: processData.value.阶段数据.今日寄样数量 || 0,
      icon: GiftOutlined,
      color: '#722ed1'
    },
    {
      title: '今日排期数',
      value: processData.value.阶段数据.今日新增排期 || 0,
      icon: CalendarOutlined,
      color: '#fa8c16'
    },
    {
      title: '今日开播数',
      value: processData.value.阶段数据.今日新增开播 || 0,
      icon: PlayCircleOutlined,
      color: '#f5222d'
    },
    {
      title: '总转化率',
      value: calculateCompletionRate(
        processData.value.阶段数据.开播完成数量 || 0,
        processData.value.阶段数据.意向确认数量 || 1
      ),
      precision: 2,
      icon: LineChartOutlined,
      color: '#1890ff',
      suffix: '%'
    },
    {
      title: '平均周期',
      value: processData.value.阶段数据.平均周期天数 || 30,
      icon: CalendarOutlined,
      color: '#722ed1',
      suffix: '天'
    }
  ]
})

// 方法
const fetchData = async () => {
  // 临时禁用独立API调用，等待共享数据
  console.log('BusinessProcessPanel: 等待共享数据，跳过独立API调用')
  emit('refresh-completed', {
    component: 'business-process',
    success: true,
    timestamp: Date.now()
  })
  return
  
  // 检查teamId是否有效
  if (!props.teamId || props.teamId === null || props.teamId === undefined) {
    console.warn('BusinessProcessPanel: teamId无效，跳过数据获取')
    loading.value = false
    return
  }
  
  loading.value = true
  try {
    // 使用统一聚合接口获取业务流程数据
    console.log('BusinessProcessPanel: 获取业务流程数据...', props.teamId)
    const aggregatedResponse = await teamDashboardService.getTeamDashboardAggregated(props.teamId)
    
    if (aggregatedResponse.status === 100 && aggregatedResponse.data) {
      const { 业务流程 } = aggregatedResponse.data
      
      if (业务流程 && 业务流程.阶段数据) {
        processData.value = {
          阶段数据: {
            意向确认数量: 业务流程.阶段数据.意向确认数量 || 0,
            样品确认数量: 业务流程.阶段数据.样品确认数量 || 0,
            排期确认数量: 业务流程.阶段数据.排期确认数量 || 0,
            开播完成数量: 业务流程.阶段数据.开播完成数量 || 0,
            今日新增意向: 业务流程.阶段数据.今日新增意向 || 0,
            今日新增样品: 业务流程.阶段数据.今日新增样品 || 0,
            今日新增排期: 业务流程.阶段数据.今日新增排期 || 0,
            今日新增开播: 业务流程.阶段数据.今日新增开播 || 0
          }
        }
        
        console.log('BusinessProcessPanel: 业务流程数据加载成功:', processData.value)
      } else {
        console.warn('BusinessProcessPanel: 业务流程数据格式错误')
        processData.value = {
          阶段数据: {
            意向确认数量: 0, 样品确认数量: 0, 排期确认数量: 0, 开播完成数量: 0,
            今日新增意向: 0, 今日新增样品: 0, 今日新增排期: 0, 今日新增开播: 0
          }
        }
      }
    } else {
      console.error('BusinessProcessPanel: 数据响应格式错误', aggregatedResponse)
      processData.value = {
        阶段数据: {
          意向确认数量: 0, 样品确认数量: 0, 排期确认数量: 0, 开播完成数量: 0,
          今日新增意向: 0, 今日新增样品: 0, 今日新增排期: 0, 今日新增开播: 0
        }
      }
    }
    
  } catch (error) {
    console.error('BusinessProcessPanel: 获取业务流程数据失败:', error)
    processData.value = {
      阶段数据: {
        意向确认数量: 0, 样品确认数量: 0, 排期确认数量: 0, 开播完成数量: 0,
        今日新增意向: 0, 今日新增样品: 0, 今日新增排期: 0, 今日新增开播: 0
      }
    }
    
    // 可以在这里添加用户通知
    // message.error('获取业务流程数据失败，请稍后重试')
  } finally {
    loading.value = false
    // 通知父组件刷新完成
    emit('refresh-completed', {
      component: 'business-process',
      success: !!processData.value.阶段数据,
      hasData: Object.values(processData.value.阶段数据 || {}).some(v => v > 0),
      timestamp: Date.now()
    })
  }
}

// 刷新数据方法
const refreshData = async () => {
  console.log('BusinessProcessPanel: 手动刷新数据')
  await fetchData()
}

// 生命周期钩子
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.business-process-panel {
  margin-bottom: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
}

.process-stages {
  margin-bottom: 24px;
}

.stage-description {
  padding: 8px 0;
}

.stage-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.metric-item {
  display: flex;
  align-items: center;
}

.metric-label {
  margin-right: 4px;
  color: #666;
}

.metric-value {
  font-weight: 500;
}

.stage-chart {
  margin-top: 8px;
  width: 100%;
}

.process-stats {
  margin-bottom: 24px;
}

.stat-card {
  height: 100%;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-footer {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-icon {
  display: flex;
  align-items: center;
}

.trend-icon.up {
  color: #52c41a;
}

.trend-icon.down {
  color: #f5222d;
}

.trend-icon.flat {
  color: #d9d9d9;
}

.process-funnel {
  margin-top: 24px;
}

.funnel-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.funnel-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.funnel-stage {
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  border-radius: 4px;
  transition: all 0.3s;
}

.funnel-stage:hover {
  transform: translateX(-5px);
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.2);
}

.funnel-stage-title {
  margin-right: 8px;
  font-weight: 500;
}

.funnel-stage-value {
  font-weight: 600;
}

.funnel-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 8px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stage-metrics {
    flex-direction: column;
    gap: 8px;
  }
  
  .funnel-stage {
    height: 32px;
    font-size: 12px;
  }
}
</style> 