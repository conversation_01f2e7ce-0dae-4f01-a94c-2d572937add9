<template>
  <div class="store-layout" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
    <!-- 左侧导航栏 -->
    <div class="store-sidebar" :class="{ collapsed: sidebarCollapsed }">
      <!-- 侧边栏标题区域 -->
      <div class="sidebar-title">
        <div class="title-content">
          <h3 v-if="!sidebarCollapsed">内容管理</h3>
          <a-button
            type="text"
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <template #icon>
              <MenuFoldOutlined v-if="!sidebarCollapsed" />
              <MenuUnfoldOutlined v-else />
            </template>
          </a-button>
        </div>
      </div>

      <!-- 自定义菜单实现，避免Ant Design Vue的ResizeObserver问题 -->
      <div class="custom-store-menu">
        <div
          v-for="menuItem in storeMenuItems"
          :key="menuItem.key"
          :class="[
            'custom-store-menu-item',
            { 'active': selectedKeys.includes(menuItem.key) },
            { 'collapsed': sidebarCollapsed }
          ]"
          @click="handleCustomStoreMenuClick(menuItem.key)"
        >
          <div class="store-menu-item-content">
            <span class="store-menu-icon">
              <component :is="getStoreIconComponent(menuItem.icon)" />
            </span>
            <span v-if="!sidebarCollapsed" class="store-menu-label">{{ menuItem.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧主要内容区域 -->
    <div class="store-main">
      <!-- 内容区域 -->
      <div class="store-content">
        <router-view />
      </div>
    </div>

    <!-- 小屏幕下侧边栏收起时的浮动展开按钮 -->
    <a-button
      v-if="sidebarCollapsed"
      type="primary"
      @click="toggleSidebar"
      class="floating-menu-btn"
    >
      <template #icon>
        <MenuUnfoldOutlined />
      </template>
    </a-button>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ShopOutlined,
  AppstoreOutlined,
  ShoppingOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()

// 当前激活的菜单项
const activeTab = ref('store-info')
const selectedKeys = ref(['store-info'])

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 店铺菜单项配置
const storeMenuItems = ref([
  {
    key: 'store-info',
    icon: 'ShopOutlined',
    label: '店铺信息'
  },
  {
    key: 'product-management',
    icon: 'AppstoreOutlined',
    label: '产品管理'
  },
  {
    key: 'douyin-products',
    icon: 'ShoppingOutlined',
    label: '抖音商品'
  },
  {
    key: 'sample-management',
    icon: 'ExperimentOutlined',
    label: '样品管理'
  },
  {
    key: 'knowledge-management',
    icon: 'DatabaseOutlined',
    label: '知识库管理'
  },
  {
    key: 'order-management',
    icon: 'FileTextOutlined',
    label: '订单管理'
  }
])

// 店铺图标组件映射
const storeIconComponents = {
  ShopOutlined,
  AppstoreOutlined,
  ShoppingOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  FileTextOutlined
}

// 获取店铺图标组件
const getStoreIconComponent = (iconName) => {
  return storeIconComponents[iconName] || ShopOutlined
}

// 菜单配置
const menuConfig = {
  'store-info': { title: '店铺信息', path: '/store/store-info' },
  'product-management': { title: '产品管理', path: '/store/product-management' },
  'douyin-products': { title: '抖音商品', path: '/store/douyin-products' },
  'sample-management': { title: '样品管理', path: '/store/sample-management' },
  'knowledge-management': { title: '知识库管理', path: '/store/knowledge-management' },
  'order-management': { title: '订单管理', path: '/store/order-management' }
}

// 自定义店铺菜单点击处理
const handleCustomStoreMenuClick = (key) => {
  activeTab.value = key
  selectedKeys.value = [key]
  const targetPath = menuConfig[key]?.path
  if (targetPath && route.path !== targetPath) {
    router.push(targetPath)
  }
}

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 检查屏幕尺寸并设置侧边栏状态
const checkScreenSize = () => {
  const isMobile = window.innerWidth <= 768
  if (isMobile) {
    sidebarCollapsed.value = true
  }
}

// 监听窗口大小变化
const handleResize = () => {
  checkScreenSize()
}

// 组件挂载时检查屏幕尺寸
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 监听路由变化，更新激活菜单项
watch(() => route.path, (newPath) => {
  const menuKey = Object.keys(menuConfig).find(key =>
    menuConfig[key].path === newPath
  )
  if (menuKey) {
    activeTab.value = menuKey
    selectedKeys.value = [menuKey]
  }
}, { immediate: true })

defineOptions({
  name: 'StoreLayout'
})
</script>

<style scoped>
.store-layout {
  display: flex;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 左侧导航栏 */
.store-sidebar {
  width: 256px;
  flex-shrink: 0;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: width 0.3s ease;
  overflow: hidden;
}

.store-sidebar.collapsed {
  width: 80px;
}

/* 侧边栏标题区域 */
.sidebar-title {
  height: 56px;
  background: #fafafa;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sidebar-title h3 {
  color: #262626;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.collapse-btn {
  color: #666;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
}

.collapse-btn:hover {
  color: #1890ff;
  background-color: #f0f0f0;
}

/* 右侧主要内容区域 */
.store-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px 24px 24px 24px;
  background: #ffffff;
}

/* 自定义店铺菜单样式 - 替代Ant Design Menu避免ResizeObserver问题 */
.custom-store-menu {
  height: calc(100vh - 64px - 56px);
  overflow-y: auto;
  padding: 8px 0;
}

.custom-store-menu-item {
  position: relative;
  margin: 0 8px 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.custom-store-menu-item:hover {
  background-color: #f5f5f5;
}

.custom-store-menu-item.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.custom-store-menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
  border-radius: 0 2px 2px 0;
}

.store-menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
  min-height: 40px;
}

.custom-store-menu-item.collapsed .store-menu-item-content {
  justify-content: center;
  padding: 12px 8px;
}

.store-menu-icon {
  font-size: 16px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  transition: color 0.2s ease;
}

.custom-store-menu-item.active .store-menu-icon {
  color: #1890ff;
}

.store-menu-label {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  transition: color 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-store-menu-item.active .store-menu-label {
  color: #1890ff;
  font-weight: 500;
}

/* 浮动菜单按钮 */
.floating-menu-btn {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  display: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-layout {
    position: relative;
    overflow-x: hidden;
  }

  .store-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .store-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .store-main {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 16px;
    position: relative;
    z-index: 1;
  }

  .floating-menu-btn {
    display: block;
  }

  /* 添加遮罩层，当侧边栏展开时 */
  .store-layout:not(.sidebar-collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
  }
}
</style>
