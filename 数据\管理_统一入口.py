"""
SuperAdmin 统一入口模块
负责统一导出所有超级管理员专用的数据操作函数，保持向后兼容性
该文件已按功能模块重构，拆分为：
- 管理_系统监控.py: 系统性能监控、资源统计
- 管理_用户管理路由.py: 用户CRUD操作、用户信息管理
- 管理_统计分析.py: 接口调用统计、会员统计、激活码管理
- 管理_日志管理.py: 日志文件操作、日志查询

注意：此模块为超级管理员专用，权限等级最高
- 超级管理可以调用普通接口
- 普通接口禁止调用超级管理功能
"""

# 导入类型注解（如果需要的话可以在具体函数中导入）

# 从各个拆分后的模块导入函数，保持兼容性
from 数据.管理_日志管理 import (
    异步获取接口调用用户列表,
    异步获取日志文件列表,
    异步读取日志文件内容,
)
# 导入用户基础操作
from 数据.用户 import (
    删除用户 as 异步删除用户,
    添加用户 as 异步添加用户,
    获取用户信息 as 异步获取用户信息,
    获取用户列表 as 异步获取用户列表,
)

# 导入管理员专用功能
from 数据.用户 import (
    异步更新用户,
    异步检查管理员权限,
    异步获取用户关联店铺列表,
    异步获取用户接口调用历史分页,
    异步获取用户最后登录详情,
    异步获取用户登录历史分页,
    异步获取用户详细统计,
    异步验证管理员,
    记录管理员登录,
    # 新增的产品经理视角功能
    异步获取用户邀约统计,
    异步获取用户权限详情,
    异步获取用户安全审计,
    异步更新用户状态,
    异步批量操作用户,
    异步获取用户数据导出,
    异步获取用户行为分析,
)
from 数据.管理_系统监控 import (
    _异步获取今日API调用数,
    _异步获取今日新增用户,
    _异步获取活跃用户数,
    _异步获取用户总数,
    _异步获取通知统计,
    异步获取系统信息,
)
from 数据.管理_统计分析 import (
    异步批量生成激活码,
    异步获取会员统计,
    异步获取接口调用统计,
    异步获取接口调用详情列表,
    异步获取激活码类型列表,
    # 新增的激活码管理函数
    异步获取激活码列表,
    异步获取激活码详情,
    异步删除激活码,
    异步批量删除激活码,
    异步获取激活码统计,
    # 新增的激活码类型管理函数
    异步创建激活码类型,
    异步更新激活码类型,
    异步删除激活码类型,
    异步获取激活码类型详情,
    异步获取激活码类型列表带统计,
)
from 日志 import 系统日志器

# 重新导出所有函数，保持向后兼容性
__all__ = [
    # 系统监控相关
    "_异步获取用户总数",
    "_异步获取今日新增用户",
    "_异步获取活跃用户数",
    "_异步获取今日API调用数",
    "_异步获取通知统计",
    "异步获取系统信息",
    # 用户管理相关
    "异步获取用户列表",
    "异步添加用户",
    "异步更新用户",
    "异步验证管理员",
    "异步检查管理员权限",
    "记录管理员登录",
    "异步获取用户信息",
    "异步删除用户",
    "异步获取用户关联店铺列表",
    "异步获取用户最后登录详情",
    "异步获取用户详细统计",
    "异步获取用户登录历史分页",
    "异步获取用户接口调用历史分页",
    # 新增的产品经理视角功能
    "异步获取用户邀约统计",
    "异步获取用户权限详情",
    "异步获取用户安全审计",
    "异步更新用户状态",
    "异步批量操作用户",
    "异步获取用户数据导出",
    "异步获取用户行为分析",
    # 统计分析相关
    "异步获取接口调用统计",
    "异步获取会员统计",
    "异步获取接口调用详情列表",
    "异步获取激活码类型列表",
    "异步批量生成激活码",
    # 新增的激活码管理函数
    "异步获取激活码列表",
    "异步获取激活码详情",
    "异步删除激活码",
    "异步批量删除激活码",
    "异步获取激活码统计",
    # 新增的激活码类型管理函数
    "异步创建激活码类型",
    "异步更新激活码类型",
    "异步删除激活码类型",
    "异步获取激活码类型详情",
    "异步获取激活码类型列表带统计",
    # 日志管理相关
    "异步获取接口调用用户列表",
    "异步获取日志文件列表",
    "异步读取日志文件内容",
]

# 记录模块重构日志
系统日志器.info("SuperAdmin模块已成功重构并重命名，建立明确的权限层级体系")
