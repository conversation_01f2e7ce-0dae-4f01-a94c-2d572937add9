#!/usr/bin/env python3
"""
手动清除工具缓存脚本
强制重新加载工具，确保修改生效
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 服务.LangChain_工具管理器 import LangChain工具管理器实例
from 服务.LangChain_内部函数包装器 import 内部函数包装器实例


async def 清除工具缓存():
    """清除所有工具缓存，强制重新加载"""
    try:
        print("🧹 开始清除工具缓存...")
        
        # 1. 清除工具管理器缓存
        if hasattr(LangChain工具管理器实例, '工具注册表'):
            缓存前数量 = len(LangChain工具管理器实例.工具注册表)
            print(f"📊 工具管理器缓存中有 {缓存前数量} 个工具")
            
            # 清除特定工具缓存
            if "更新微信好友下次沟通时间" in LangChain工具管理器实例.工具注册表:
                del LangChain工具管理器实例.工具注册表["更新微信好友下次沟通时间"]
                print("✅ 已清除'更新微信好友下次沟通时间'工具缓存")
            
            if "获取当前时间" in LangChain工具管理器实例.工具注册表:
                del LangChain工具管理器实例.工具注册表["获取当前时间"]
                print("✅ 已清除'获取当前时间'工具缓存")
            
            # 清除工具配置缓存
            if hasattr(LangChain工具管理器实例, '工具配置'):
                LangChain工具管理器实例.工具配置.pop("更新微信好友下次沟通时间", None)
                LangChain工具管理器实例.工具配置.pop("获取当前时间", None)
                print("✅ 已清除工具配置缓存")
            
            缓存后数量 = len(LangChain工具管理器实例.工具注册表)
            print(f"📊 清除后工具管理器缓存中有 {缓存后数量} 个工具")
        
        # 2. 强制重新初始化内部函数包装器
        print("🔄 重新初始化内部函数包装器...")
        内部函数包装器实例.已初始化 = False
        await 内部函数包装器实例.初始化()
        print("✅ 内部函数包装器重新初始化完成")
        
        # 3. 强制工具管理器重新加载内部函数工具
        print("🔄 强制工具管理器重新加载内部函数工具...")
        if hasattr(LangChain工具管理器实例, '_初始化内部函数工具'):
            await LangChain工具管理器实例._初始化内部函数工具()
            print("✅ 工具管理器重新加载内部函数工具完成")
        else:
            print("⚠️ 工具管理器没有_初始化内部函数工具方法")
        
        # 4. 验证工具是否正确加载
        print("🔍 验证工具加载状态...")
        可用工具 = await 内部函数包装器实例.获取可用工具列表()
        
        if "更新微信好友下次沟通时间" in 可用工具:
            print("✅ '更新微信好友下次沟通时间'工具已正确加载")
        else:
            print("❌ '更新微信好友下次沟通时间'工具未找到")
        
        if "获取当前时间" in 可用工具:
            print("✅ '获取当前时间'工具已正确加载")
        else:
            print("❌ '获取当前时间'工具未找到")
        
        print(f"📊 内部函数包装器中共有 {len(可用工具)} 个工具")
        
        print("🎉 工具缓存清除完成！")
        
    except Exception as e:
        print(f"❌ 清除缓存失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("=" * 50)
    print("工具缓存清除工具")
    print("=" * 50)
    
    await 清除工具缓存()
    
    print("=" * 50)
    print("操作完成")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
