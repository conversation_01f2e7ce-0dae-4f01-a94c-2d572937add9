"""
IP限速器模块
提供IP访问频率限制功能，防止恶意请求和DDoS攻击
"""
import asyncio
import time
from collections import defaultdict
from typing import Dict, List

import config
from 日志 import 系统日志器, 错误日志器


class IP限速器:
    """IP访问频率限制器"""
    
    def __init__(self):
        """初始化IP限速器"""
        self.访问记录: Dict[str, List[float]] = defaultdict(list)
        self.锁 = asyncio.Lock()
        self.最大IP数 = 10000  # 防止内存泄漏
        self.清理计时器 = None
        
    async def 启动清理任务(self):
        """启动定期清理过期记录的任务"""
        if self.清理计时器 is None or self.清理计时器.done():
            async def 定期清理():
                """定期清理过期记录的后台任务"""
                try:
                    while True:
                        await asyncio.sleep(60)  # 每分钟清理一次
                        await self._清理过期记录()
                except asyncio.CancelledError:
                    系统日志器.info("IP限速器清理任务已被取消")
                    raise
                except Exception as e:
                    错误日志器.error(f"IP限速器清理任务异常: {e}", exc_info=True)
            
            # 启动异步任务
            self.清理计时器 = asyncio.create_task(定期清理())
            系统日志器.info("IP限速器清理任务已启动")
        return self

    async def _清理过期记录(self):
        """清理所有过期的访问记录"""
        try:
            async with self.锁:
                now = time.time()
                # 清理过期记录
                需要删除的IP = []
                for ip, 时间列表 in self.访问记录.items():
                    self.访问记录[ip] = [t for t in 时间列表 if now - t < 60]
                    if not self.访问记录[ip]:  # 如果清理后列表为空，标记为删除
                        需要删除的IP.append(ip)
                
                # 删除空记录
                for ip in 需要删除的IP:
                    del self.访问记录[ip]
                
                # 如果IP数量超过限制，删除最旧的记录
                if len(self.访问记录) > self.最大IP数:
                    # 计算每个IP的最新访问时间
                    IP最新访问 = {ip: max(times) if times else 0 for ip, times in self.访问记录.items()}
                    # 按最新访问时间排序，保留最近的
                    保留IP = sorted(IP最新访问.keys(), key=lambda ip: IP最新访问[ip], reverse=True)[:self.最大IP数]
                    # 创建新的字典只包含要保留的IP
                    self.访问记录 = {ip: self.访问记录[ip] for ip in 保留IP}
        except Exception as e:
            错误日志器.error(f"清理IP访问记录时出错: {e}", exc_info=True)

    async def 检查IP(self, ip: str, 路径: str) -> bool:
        """检查IP是否超过限制"""
        async with self.锁:
            # 清除过期记录
            now = time.time()
            self.访问记录[ip] = [t for t in self.访问记录[ip] if now - t < 60]

            # 白名单直接放行
            if ip in config.频率配置.白名单IP列表:
                return True

            # 特殊路径加强防护
            if 路径.startswith(tuple(config.频率配置.受保护路径)):
                return len(self.访问记录[ip]) < config.频率配置.单IP每分钟限制 // 2

            # 标准速率限制
            return len(self.访问记录[ip]) < config.频率配置.单IP每分钟限制

    async def 记录访问(self, ip: str):
        """记录IP访问"""
        async with self.锁:
            self.访问记录[ip].append(time.time())
            
    async def 关闭(self):
        """关闭限速器，取消定期清理任务"""
        if self.清理计时器 and not self.清理计时器.done():
            系统日志器.info("正在关闭IP限速器清理任务...")
            self.清理计时器.cancel()
            try:
                await self.清理计时器
            except asyncio.CancelledError:
                系统日志器.info("IP限速器清理任务已成功取消")
                pass
            except Exception as e:
                错误日志器.error(f"IP限速器关闭异常: {e}", exc_info=True)

    def 获取统计信息(self) -> dict:
        """获取当前限速器统计信息"""
        return {
            "活跃IP数量": len(self.访问记录),
            "总访问记录": sum(len(times) for times in self.访问记录.values()),
            "清理任务状态": "运行中" if self.清理计时器 and not self.清理计时器.done() else "未运行"
        }


# 创建全局实例
IP限速器实例 = IP限速器()
