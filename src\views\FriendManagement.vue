<template>
  <div class="friend-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>好友管理</h1>
          <p>微信好友管理和对接进度跟踪系统</p>
        </div>
        
        <div class="header-actions">
          <a-button type="primary" @click="bindWeChatAccount">
            <PlusOutlined />
            绑定微信账号
          </a-button>
          
          <a-button @click="refreshAllData" :loading="refreshing">
            <ReloadOutlined />
            刷新数据
          </a-button>
        </div>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="overview-cards" v-if="!loading">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="微信账号"
              :value="overview.微信账号总数"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <WechatOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="好友总数"
              :value="overview.好友总数"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <TeamOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="进行中对接"
              :value="overview.对接进行中"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <BarChartOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="成功对接"
              :value="overview.对接成功数"
              :value-style="{ color: '#f5222d' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能导航卡片 -->
    <div class="navigation-cards">
      <a-row :gutter="[16, 16]">
        <!-- 微信账号管理 -->
        <a-col :xs="24" :sm="12" :lg="8">
          <a-card 
            hoverable 
            class="nav-card"
            @click="navigateTo('wechat-accounts')"
          >
            <div class="nav-card-content">
              <div class="nav-icon">
                <WechatOutlined />
              </div>
              <div class="nav-info">
                <h3>微信账号管理</h3>
                <p>绑定和管理您的微信账号，查看账号状态和基本统计</p>
                <div class="nav-stats">
                  <span>{{ overview.微信账号数量 }} 个账号</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 好友列表管理 -->
        <a-col :xs="24" :sm="12" :lg="8">
          <a-card 
            hoverable 
            class="nav-card"
            @click="navigateTo('friend-list')"
          >
            <div class="nav-card-content">
              <div class="nav-icon">
                <TeamOutlined />
              </div>
              <div class="nav-info">
                <h3>好友列表管理</h3>
                <p>查看和管理所有微信好友，支持搜索、筛选和分类</p>
                <div class="nav-stats">
                  <span>{{ overview.好友总数 }} 位好友</span>
                  <span class="growth">今日 +{{ overview.今日新增好友 }}</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 对接进度管理 -->
        <a-col :xs="24" :sm="12" :lg="8">
          <a-card 
            hoverable 
            class="nav-card"
            @click="navigateTo('progress-management')"
          >
            <div class="nav-card-content">
              <div class="nav-icon">
                <BarChartOutlined />
              </div>
              <div class="nav-info">
                <h3>对接进度管理</h3>
                <p>跟踪和管理产品对接进度，实时更新对接状态</p>
                <div class="nav-stats">
                  <span>{{ overview.对接进行中 }} 进行中</span>
                  <span class="success">{{ overview.对接成功数 }} 已成功</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 数据统计分析 -->
        <a-col :xs="24" :sm="12" :lg="8">
          <a-card 
            hoverable 
            class="nav-card"
            @click="navigateTo('statistics')"
          >
            <div class="nav-card-content">
              <div class="nav-icon">
                <PieChartOutlined />
              </div>
              <div class="nav-info">
                <h3>数据统计分析</h3>
                <p>查看详细的数据统计和趋势分析，导出统计报表</p>
                <div class="nav-stats">
                  <span>转化率 {{ overview.转化率 }}</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 团队数据概览 -->
        <a-col v-if="userStore.hasPermission('team_manager')" :xs="24" :sm="12" :lg="8">
          <a-card 
            hoverable 
            class="nav-card"
            @click="navigateTo('team-overview')"
          >
            <div class="nav-card-content">
              <div class="nav-icon">
                <ApartmentOutlined />
              </div>
              <div class="nav-info">
                <h3>团队数据概览</h3>
                <p>查看团队成员的好友数据和对接情况（仅负责人）</p>
                <div class="nav-stats">
                  <span>团队管理</span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>


      </a-row>
    </div>

    <!-- 最近动态 -->
    <div class="recent-activities" v-if="!loading">
      <a-card title="最近动态" :bordered="false">
        <a-list
          :data-source="recentActivities"
          :loading="activitiesLoading"
          size="small"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: item.color }">
                    <component :is="item.icon" />
                  </a-avatar>
                </template>
                <template #title>
                  {{ item.title }}
                </template>
                <template #description>
                  {{ item.description }} · {{ formatRelativeTime(item.time) }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
        
        <div class="activity-footer" v-if="recentActivities.length === 0">
          <a-empty description="暂无最近动态" />
        </div>
      </a-card>
    </div>

    <!-- 绑定微信账号弹窗 -->
    <a-modal
      v-model:open="bindModalVisible"
      title="绑定微信账号"
      :confirm-loading="bindLoading"
      @ok="handleBindSubmit"
      @cancel="resetBindForm"
    >
      <a-form
        ref="bindFormRef"
        :model="bindForm"
        :rules="bindRules"
        layout="vertical"
      >
        <a-form-item label="微信号" name="微信号">
          <a-input 
            v-model:value="bindForm.微信号" 
            placeholder="请输入微信号（6-20位，字母开头）"
            :maxlength="20"
          />
        </a-form-item>
        
        <a-form-item label="备注信息" name="备注">
          <a-textarea 
            v-model:value="bindForm.备注" 
            placeholder="可选，为此微信账号添加备注信息"
            :rows="3"
            :maxlength="200"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  WechatOutlined,
  TeamOutlined,
  BarChartOutlined,
  CheckCircleOutlined,
  PieChartOutlined,
  ApartmentOutlined
} from '@ant-design/icons-vue'
import { useUserStore } from '@/store/user'
import { wechatService } from '@/services/friend'

defineOptions({
  name: 'FriendManagement'
})

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const refreshing = ref(false)
const activitiesLoading = ref(false)
const bindModalVisible = ref(false)
const bindLoading = ref(false)
const bindFormRef = ref()

// 数据概览
const overview = reactive({
  微信账号数量: 0,
  好友总数: 0,
  今日新增好友: 0,
  对接进行中: 0,
  对接成功数: 0,
  转化率: '0%'
})

// 绑定表单
const bindForm = reactive({
  微信号: '',
  备注: ''
})

// 绑定表单验证规则
const bindRules = {
  微信号: [
    { required: true, message: '请输入微信号' },
    { 
      validator: (rule, value) => {
        if (value && !wechatService.validateWeChatId(value)) {
          return Promise.reject('微信号格式不正确（6-20位，字母开头，允许字母、数字、下划线、减号）')
        }
        return Promise.resolve()
      }
    }
  ]
}

// 最近动态
const recentActivities = ref([])

// 计算属性

// 方法定义

/**
 * 页面初始化
 */
onMounted(() => {
  loadPageData()
})

/**
 * 加载页面数据
 */
const loadPageData = async () => {
  try {
    loading.value = true
    await Promise.all([
      loadOverviewData(),
      loadRecentActivities()
    ])
  } catch (error) {
    console.error('加载页面数据失败:', error)
    message.error('数据加载失败，请刷新页面重试')
  } finally {
    loading.value = false
  }
}

/**
 * 加载概览数据
 */
const loadOverviewData = async () => {
  try {
    const response = await wechatService.getWeChatStatistics()
    if (response.status === 100) {
      Object.assign(overview, response.data)
    }
  } catch (error) {
    console.error('加载概览数据失败:', error)
  }
}

/**
 * 加载最近动态
 */
const loadRecentActivities = async () => {
  try {
    activitiesLoading.value = true
    // 这里可以调用具体的API获取最近动态
    // 暂时使用模拟数据
    recentActivities.value = []
  } catch (error) {
    console.error('加载最近动态失败:', error)
  } finally {
    activitiesLoading.value = false
  }
}

/**
 * 刷新所有数据
 */
const refreshAllData = async () => {
  try {
    refreshing.value = true
    await loadPageData()
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    refreshing.value = false
  }
}

/**
 * 导航到指定页面
 * @param {string} routeName - 路由名称
 */
const navigateTo = (routeName) => {
  router.push({ name: routeName })
}

/**
 * 绑定微信账号
 */
const bindWeChatAccount = () => {
  bindModalVisible.value = true
}

/**
 * 处理绑定表单提交
 */
const handleBindSubmit = async () => {
  try {
    const valid = await bindFormRef.value.validate()
    if (!valid) return

    bindLoading.value = true
    
    const response = await wechatService.bindWeChatAccount(bindForm)
    
    if (response.status === 100) {
      message.success('微信账号绑定成功')
      bindModalVisible.value = false
      resetBindForm()
      loadOverviewData() // 刷新概览数据
    }
  } catch (error) {
    console.error('绑定微信账号失败:', error)
    message.error(error.message || '绑定失败，请重试')
  } finally {
    bindLoading.value = false
  }
}

/**
 * 重置绑定表单
 */
const resetBindForm = () => {
  bindForm.微信号 = ''
  bindForm.备注 = ''
  bindFormRef.value?.resetFields()
}



/**
 * 格式化相对时间
 * @param {string} time - 时间字符串
 * @returns {string} 相对时间
 */
const formatRelativeTime = (time) => {
  if (!time) return ''
  
  const date = new Date(time)
  const now = new Date()
  const diffTime = now - date
  const diffMinutes = Math.floor(diffTime / (1000 * 60))
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60))
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString()
  }
}
</script>

<style scoped>
.friend-management {
  padding: 0;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  margin-bottom: 24px;
  border-radius: 8px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-title h1 {
  color: white;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.header-title p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.overview-cards {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border-color: #d9d9d9;
}

.navigation-cards {
  margin-bottom: 24px;
}

.nav-card {
  height: 140px;
  border-radius: 8px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.nav-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.nav-card.quick-action {
  border: 2px dashed #d9d9d9;
  background: #fafafa;
}

.nav-card.quick-action:hover {
  border-color: #1890ff;
  background: #f0f9ff;
}

.nav-card-content {
  display: flex;
  height: 100%;
  align-items: center;
}

.nav-icon {
  font-size: 32px;
  color: #1890ff;
  margin-right: 16px;
  flex-shrink: 0;
}

.nav-info {
  flex: 1;
  min-width: 0;
}

.nav-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.nav-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.4;
}

.nav-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.nav-stats span {
  color: rgba(0, 0, 0, 0.45);
}

.nav-stats .growth {
  color: #52c41a;
}

.nav-stats .success {
  color: #1890ff;
}

.recent-activities {
  margin-bottom: 24px;
}

.activity-footer {
  text-align: center;
  padding: 24px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
    margin: -24px -24px 24px -24px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .nav-card {
    height: auto;
    min-height: 120px;
  }
  
  .nav-card-content {
    flex-direction: column;
    text-align: center;
    padding: 16px 0;
  }
  
  .nav-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .nav-stats {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .header-actions {
    flex-direction: column;
  }
  
  .header-actions .ant-btn {
    width: 100%;
  }
}
</style> 