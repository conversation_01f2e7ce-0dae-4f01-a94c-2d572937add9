<template>
  <a-drawer
    :open="visible"
    title="店铺详情"
    width="600"
    @close="handleClose"
    class="store-detail-drawer"
  >
    <div v-if="storeData" class="store-detail-content">
      <!-- 店铺基本信息 -->
      <div class="info-section">
        <div class="section-header">
          <h3>
            <shop-outlined />
            基本信息
          </h3>
          <a-button
            size="small"
            @click="toggleEdit"
            :type="isEditing ? 'primary' : 'default'"
          >
            {{ isEditing ? '取消编辑' : '编辑信息' }}
          </a-button>
        </div>

        <div class="store-avatar-section">
          <a-image
            :width="80"
            :height="80"
            :src="storeData.avatar || '/images/default_store.png'"
            :preview="false"
            class="store-avatar"
          />
          <div class="store-basic-info">
            <h4>{{ storeData.店铺名称 || storeData.shop_name }}</h4>
            <p>店铺id: {{ storeData.店铺id || storeData.shop_id }}</p>
            <a-tag :color="getStatusColor(storeData.店铺状态)">
              {{ storeData.店铺状态 || '正常' }}
            </a-tag>
          </div>
        </div>

        <a-form
          v-if="isEditing"
          ref="formRef"
          :model="editForm"
          :rules="formRules"
          layout="vertical"
          class="edit-form"
        >
          <a-form-item label="店铺名称" name="店铺名称">
            <a-input v-model:value="editForm.店铺名称" />
          </a-form-item>
          <a-form-item label="店铺分类" name="店铺分类">
            <a-select v-model:value="editForm.店铺分类">
              <a-select-option value="母婴用品">母婴用品</a-select-option>
              <a-select-option value="食品饮料">食品饮料</a-select-option>
              <a-select-option value="数码科技">数码科技</a-select-option>
              <a-select-option value="服装鞋帽">服装鞋帽</a-select-option>
              <a-select-option value="美妆护肤">美妆护肤</a-select-option>
              <a-select-option value="家居生活">家居生活</a-select-option>
              <a-select-option value="运动户外">运动户外</a-select-option>
              <a-select-option value="汽车用品">汽车用品</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="店铺状态" name="店铺状态">
            <a-select v-model:value="editForm.店铺状态">
              <a-select-option value="正常">正常</a-select-option>
              <a-select-option value="异常">异常</a-select-option>
              <a-select-option value="待审核">待审核</a-select-option>
              <a-select-option value="停用">停用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="备注信息" name="备注">
            <a-textarea v-model:value="editForm.备注" :rows="3" />
          </a-form-item>
          
          <div class="form-actions">
            <a-space>
              <a-button @click="cancelEdit">取消</a-button>
              <a-button type="primary" @click="saveEdit" :loading="saving">
                保存
              </a-button>
            </a-space>
          </div>
        </a-form>

        <a-descriptions v-else :column="2" bordered size="small">
          <a-descriptions-item label="店铺类型">
            {{ storeData.店铺类型 || storeData.main_category || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="关联时间">
            {{ formatDate(storeData.关联时间) }}
          </a-descriptions-item>
          <a-descriptions-item label="店铺分类" :span="2">
            {{ storeData.店铺分类 || storeData.main_category || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="备注信息" :span="2">
            {{ storeData.备注 || '暂无备注' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 店铺统计信息 -->
      <div class="info-section">
        <div class="section-header">
          <h3>
            <bar-chart-outlined />
            统计信息
          </h3>
        </div>
        
        <a-row :gutter="[16, 16]">
          <a-col :span="12">
            <a-card class="stat-card" size="small">
              <a-statistic
                title="信誉评分"
                :value="storeData.reputation_score || 0"
                suffix="分"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card class="stat-card" size="small">
              <a-statistic
                title="销售数量"
                :value="storeData.sales_cnt || 0"
                suffix="件"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 关联产品 -->
      <div class="info-section">
        <div class="section-header">
          <h3>
            <appstore-outlined />
            关联产品
          </h3>
          <a-button size="small" @click="refreshProducts">
            <template #icon>
              <reload-outlined />
            </template>
            刷新
          </a-button>
        </div>

        <div v-if="relatedProducts.length > 0" class="products-list">
          <a-list
            :data-source="relatedProducts"
            size="small"
            :pagination="productsPagination"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-image
                      :width="40"
                      :height="40"
                      :src="item.产品图片 || '/images/default_product.png'"
                      :preview="false"
                    />
                  </template>
                  <template #title>
                    {{ item.产品名称 }}
                  </template>
                  <template #description>
                    分类：{{ item.产品分类 }} | 状态：{{ item.状态 }}
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a @click="viewProductDetail(item)">查看</a>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </div>
        <a-empty v-else description="暂无关联产品" />
      </div>

      <!-- 操作记录 -->
      <div class="info-section">
        <div class="section-header">
          <h3>
            <history-outlined />
            操作记录
          </h3>
        </div>

        <a-timeline size="small">
          <a-timeline-item
            v-for="record in operationHistory"
            :key="record.id"
            :color="getTimelineColor(record.操作类型)"
          >
            <template #dot>
              <component :is="getOperationIcon(record.操作类型)" />
            </template>
            <div class="timeline-content">
              <div class="operation-title">{{ record.操作描述 }}</div>
              <div class="operation-meta">
                <span>操作人：{{ record.操作人 }}</span>
                <span>时间：{{ formatDateTime(record.操作时间) }}</span>
              </div>
              <div v-if="record.备注" class="operation-note">
                备注：{{ record.备注 }}
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <a-space>
          <a-button @click="handleClose">关闭</a-button>
          <a-button type="primary" danger @click="showUnbindConfirm">
            解除绑定
          </a-button>
        </a-space>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, defineProps, defineEmits } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  ShopOutlined,
  BarChartOutlined,
  AppstoreOutlined,
  HistoryOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  LinkOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

/**
 * 组件属性定义
 */
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  storeData: {
    type: Object,
    default: () => ({})
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['update:visible', 'update'])

// 表单引用
const formRef = ref()

// 编辑状态
const isEditing = ref(false)
const saving = ref(false)

// 编辑表单数据
const editForm = reactive({
  店铺名称: '',
  店铺分类: '',
  店铺状态: '',
  备注: ''
})

// 表单验证规则
const formRules = {
  店铺名称: [
    { required: true, message: '请输入店铺名称' }
  ],
  店铺分类: [
    { required: true, message: '请选择店铺分类' }
  ],
  店铺状态: [
    { required: true, message: '请选择店铺状态' }
  ]
}

// 关联产品列表
const relatedProducts = ref([
  {
    产品id: 1,
    产品名称: 'iPhone 15 Pro手机壳',
    产品图片: '/images/product1.jpg',
    产品分类: '数码配件',
    状态: '正常'
  },
  {
    产品id: 2,
    产品名称: '蓝牙耳机',
    产品图片: '/images/product2.jpg',
    产品分类: '数码配件',
    状态: '正常'
  }
])

// 产品分页配置
const productsPagination = reactive({
  pageSize: 5,
  showSizeChanger: false,
  showQuickJumper: false
})

// 操作记录
const operationHistory = ref([
  {
    id: 1,
    操作类型: '绑定',
    操作描述: '店铺绑定成功',
    操作人: '张三',
    操作时间: '2024-01-15 10:30:00',
    备注: '通过店铺id自动识别绑定'
  },
  {
    id: 2,
    操作类型: '编辑',
    操作描述: '更新店铺信息',
    操作人: '李四',
    操作时间: '2024-01-16 15:20:00',
    备注: '修改了店铺分类信息'
  },
  {
    id: 3,
    操作类型: '同步',
    操作描述: '同步店铺数据',
    操作人: '系统',
    操作时间: '2024-01-17 09:00:00',
    备注: '自动同步店铺基础信息'
  }
])

/**
 * 监听storeData变化，同步到编辑表单
 */
watch(() => props.storeData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(editForm, {
      店铺名称: newData.店铺名称 || newData.shop_name || '',
      店铺分类: newData.店铺分类 || newData.main_category || '',
      店铺状态: newData.店铺状态 || '正常',
      备注: newData.备注 || ''
    })
  }
}, { immediate: true, deep: true })

/**
 * 获取状态对应的颜色
 * @param {string} status - 状态值
 * @returns {string} 颜色值
 */
const getStatusColor = (status) => {
  const colorMap = {
    '正常': 'green',
    '异常': 'red',
    '待审核': 'orange',
    '停用': 'gray'
  }
  return colorMap[status] || 'blue'
}

/**
 * 获取时间线颜色
 * @param {string} type - 操作类型
 * @returns {string} 颜色值
 */
const getTimelineColor = (type) => {
  const colorMap = {
    '绑定': 'green',
    '编辑': 'blue',
    '同步': 'orange',
    '解绑': 'red'
  }
  return colorMap[type] || 'gray'
}

/**
 * 获取操作图标
 * @param {string} type - 操作类型
 * @returns {Component} 图标组件
 */
const getOperationIcon = (type) => {
  const iconMap = {
    '绑定': LinkOutlined,
    '编辑': EditOutlined,
    '同步': ReloadOutlined,
    '解绑': DeleteOutlined
  }
  return iconMap[type] || ExclamationCircleOutlined
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '未知'
  return date.toLocaleDateString('zh-CN')
}

/**
 * 格式化日期时间
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期时间
 */
const formatDateTime = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '未知'
  return `${date.toLocaleDateString('zh-CN')} ${date.toLocaleTimeString('zh-CN')}`
}

/**
 * 切换编辑模式
 */
const toggleEdit = () => {
  isEditing.value = !isEditing.value
  if (!isEditing.value) {
    // 取消编辑时重置表单
    Object.assign(editForm, {
      店铺名称: props.storeData.店铺名称 || props.storeData.shop_name || '',
      店铺分类: props.storeData.店铺分类 || props.storeData.main_category || '',
      店铺状态: props.storeData.店铺状态 || '正常',
      备注: props.storeData.备注 || ''
    })
  }
}

/**
 * 取消编辑
 */
const cancelEdit = () => {
  isEditing.value = false
  formRef.value?.resetFields()
}

/**
 * 保存编辑
 */
const saveEdit = async () => {
  try {
    await formRef.value.validate()
    saving.value = true
    
    console.log('保存店铺信息:', editForm)
    
    // 这里调用实际的API接口更新店铺信息
    // const response = await storeAPI.updateStore(props.storeData.店铺id, editForm)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success('店铺信息更新成功')
    
    // 通知父组件更新数据
    emit('update', {
      ...props.storeData,
      ...editForm
    })
    
    isEditing.value = false
    
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败，请检查输入信息')
  } finally {
    saving.value = false
  }
}

/**
 * 刷新关联产品
 */
const refreshProducts = async () => {
  try {
    console.log('刷新关联产品')
    // 这里调用API获取最新的关联产品
    // const response = await storeAPI.getRelatedProducts(props.storeData.店铺id)
    message.success('产品列表已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    message.error('刷新失败')
  }
}

/**
 * 查看产品详情
 * @param {Object} product - 产品信息
 */
const viewProductDetail = (product) => {
  console.log('查看产品详情:', product)
  message.info('产品详情功能开发中...')
}

/**
 * 显示解绑确认
 */
const showUnbindConfirm = () => {
  Modal.confirm({
    title: '确认解除绑定',
    content: `您确定要解除与店铺"${props.storeData.店铺名称 || props.storeData.shop_name}"的绑定吗？解绑后将无法查看该店铺的相关数据。`,
    okText: '确认解绑',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      handleUnbind()
    }
  })
}

/**
 * 处理解绑操作
 */
const handleUnbind = async () => {
  try {
    console.log('解除店铺绑定:', props.storeData)
    
    // 这里调用实际的API接口解除绑定
    // const response = await storeAPI.unbindStore(props.storeData.店铺id)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    message.success('店铺绑定已解除')
    
    // 通知父组件刷新数据
    emit('update')
    
    // 关闭抽屉
    handleClose()
    
  } catch (error) {
    console.error('解绑失败:', error)
    message.error('解绑失败，请稍后重试')
  }
}

/**
 * 处理关闭抽屉
 */
const handleClose = () => {
  isEditing.value = false
  emit('update:visible', false)
}

// 组件名称定义
defineOptions({
  name: 'StoreDetailDrawer'
})
</script>

<style scoped>
/* 抽屉内容样式 */
.store-detail-content {
  padding: 0;
}

/* 信息区块样式 */
.info-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.info-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1d;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 店铺头像区域 */
.store-avatar-section {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.store-avatar {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.store-basic-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1d;
}

.store-basic-info p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

/* 编辑表单样式 */
.edit-form {
  margin-top: 16px;
}

.form-actions {
  margin-top: 24px;
  text-align: right;
}

/* 统计卡片样式 */
.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

/* 产品列表样式 */
.products-list {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.products-list .ant-list-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
}

.products-list .ant-list-item:last-child {
  border-bottom: none;
}

/* 时间线样式 */
.timeline-content {
  padding-left: 8px;
}

.operation-title {
  font-weight: 500;
  color: #1d1d1d;
  margin-bottom: 4px;
}

.operation-meta {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.operation-meta span {
  margin-right: 16px;
}

.operation-note {
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}

/* 底部操作区域 */
.drawer-footer {
  text-align: right;
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
}

/* 描述列表样式优化 */
.info-section :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #333;
}

.info-section :deep(.ant-descriptions-item-content) {
  color: #666;
}

/* 空状态样式 */
.ant-empty {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .store-avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .operation-meta span {
    display: block;
    margin-right: 0;
    margin-bottom: 2px;
  }
}

/* 图标样式 */
.section-header .anticon {
  color: #1890ff;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}
</style> 