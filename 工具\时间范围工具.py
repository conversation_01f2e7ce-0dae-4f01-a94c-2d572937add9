"""
时间范围处理工具
提供中文语义化时间范围参数的解析和转换功能
"""

from datetime import date, datetime, timedelta
from typing import List, Optional, Tuple


class 时间范围工具:
    """时间范围处理工具类"""

    # 支持的中文时间范围参数
    支持的时间范围 = {
        "昨日",
        "今日",
        "本周",
        "上周",
        "本月",
        "上月",
        "本季度",
        "上季度",
        "自定义",
    }

    @classmethod
    def 验证时间范围参数(cls, 时间范围: str) -> bool:
        """
        验证时间范围参数是否有效

        参数:
            时间范围: 时间范围参数

        返回:
            是否为有效的时间范围参数
        """
        return 时间范围 in cls.支持的时间范围

    @classmethod
    def 标准化时间范围参数(cls, 时间范围: str) -> str:
        """
        标准化时间范围参数，只接受中文参数

        参数:
            时间范围: 中文时间范围参数

        返回:
            标准化的时间范围参数
        """
        # 只接受中文参数
        if 时间范围 in cls.支持的时间范围:
            return 时间范围

        # 默认返回今日
        return "今日"

    @classmethod
    def 解析时间范围(
        cls,
        时间范围: str,
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Tuple[datetime, datetime]:
        """
        解析时间范围，返回开始和结束时间（使用左闭右开区间）

        参数:
            时间范围: 中文时间范围参数（昨日、今日、本周、上周、本月、上月、本季度、上季度）
            开始日期: 自定义开始日期
            结束日期: 自定义结束日期

        返回:
            (开始时间, 结束时间) 元组，使用左闭右开区间 [开始时间, 结束时间)
        """
        # 标准化时间范围参数
        标准时间范围 = cls.标准化时间范围参数(时间范围)

        现在 = datetime.now()

        if 标准时间范围 == "昨日":
            # 昨日：昨天00:00:00 到 今天00:00:00（左闭右开）
            昨天 = 现在.date() - timedelta(days=1)
            今天 = 现在.date()
            开始时间 = datetime.combine(昨天, datetime.min.time())
            结束时间 = datetime.combine(今天, datetime.min.time())

        elif 标准时间范围 == "今日":
            # 今日：今天00:00:00 到 明天00:00:00（左闭右开）
            今天 = 现在.date()
            明天 = 今天 + timedelta(days=1)
            开始时间 = datetime.combine(今天, datetime.min.time())
            结束时间 = datetime.combine(明天, datetime.min.time())

        elif 标准时间范围 == "本周":
            # 本周：本周一00:00:00 到 下周一00:00:00（左闭右开）
            周一偏移 = 现在.weekday()  # 0=周一, 6=周日
            本周一 = 现在.date() - timedelta(days=周一偏移)
            下周一 = 本周一 + timedelta(days=7)
            开始时间 = datetime.combine(本周一, datetime.min.time())
            结束时间 = datetime.combine(下周一, datetime.min.time())

        elif 标准时间范围 == "上周":
            # 上周：上周一00:00:00 到 本周一00:00:00（左闭右开）
            周一偏移 = 现在.weekday()
            本周一 = 现在.date() - timedelta(days=周一偏移)
            上周一 = 本周一 - timedelta(days=7)
            开始时间 = datetime.combine(上周一, datetime.min.time())
            结束时间 = datetime.combine(本周一, datetime.min.time())

        elif 标准时间范围 == "本月":
            # 本月：本月1日00:00:00 到 下月1日00:00:00（左闭右开）
            本月一日 = 现在.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # 计算下月第一天
            if 现在.month == 12:
                下月一日 = datetime(现在.year + 1, 1, 1)
            else:
                下月一日 = datetime(现在.year, 现在.month + 1, 1)

            开始时间 = 本月一日
            结束时间 = 下月一日

        elif 标准时间范围 == "上月":
            # 上月：上月1日00:00:00 到 本月1日00:00:00（左闭右开）
            本月一日 = 现在.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # 计算上月第一天
            if 现在.month == 1:
                上月一日 = datetime(现在.year - 1, 12, 1)
            else:
                上月一日 = datetime(现在.year, 现在.month - 1, 1)

            开始时间 = 上月一日
            结束时间 = 本月一日

        elif 标准时间范围 == "本季度":
            # 本季度：本季度第一天00:00:00 到 下季度第一天00:00:00（左闭右开）
            当前季度 = (现在.month - 1) // 3 + 1
            季度开始月 = (当前季度 - 1) * 3 + 1
            本季度一日 = 现在.replace(
                month=季度开始月, day=1, hour=0, minute=0, second=0, microsecond=0
            )

            # 计算下季度第一天
            if 当前季度 == 4:
                下季度一日 = datetime(现在.year + 1, 1, 1)
            else:
                下季度开始月 = 当前季度 * 3 + 1
                下季度一日 = datetime(现在.year, 下季度开始月, 1)

            开始时间 = 本季度一日
            结束时间 = 下季度一日

        elif 标准时间范围 == "上季度":
            # 上季度：上季度第一天00:00:00 到 本季度第一天00:00:00（左闭右开）
            当前季度 = (现在.month - 1) // 3 + 1
            本季度开始月 = (当前季度 - 1) * 3 + 1
            本季度一日 = 现在.replace(
                month=本季度开始月, day=1, hour=0, minute=0, second=0, microsecond=0
            )

            # 计算上季度第一天
            if 当前季度 == 1:
                上季度开始月 = 10  # 上年第4季度从10月开始
                上季度一日 = datetime(现在.year - 1, 上季度开始月, 1)
            else:
                上季度开始月 = (当前季度 - 2) * 3 + 1
                上季度一日 = datetime(现在.year, 上季度开始月, 1)

            开始时间 = 上季度一日
            结束时间 = 本季度一日

        elif 时间范围 == "自定义" and 开始日期 and 结束日期:
            # 自定义时间范围：开始日期00:00:00 到 结束日期+1天00:00:00（左闭右开）
            开始时间 = datetime.combine(开始日期, datetime.min.time())
            结束日期_次日 = 结束日期 + timedelta(days=1)
            结束时间 = datetime.combine(结束日期_次日, datetime.min.time())

        else:
            # 默认今日：今天00:00:00 到 明天00:00:00（左闭右开）
            今天 = 现在.date()
            明天 = 今天 + timedelta(days=1)
            开始时间 = datetime.combine(今天, datetime.min.time())
            结束时间 = datetime.combine(明天, datetime.min.time())

        return 开始时间, 结束时间

    @classmethod
    def 获取时间范围描述(cls, 时间范围: str) -> str:
        """
        获取时间范围的中文描述

        参数:
            时间范围: 时间范围参数

        返回:
            中文描述
        """
        标准时间范围 = cls.标准化时间范围参数(时间范围)

        # 直接返回中文描述
        return 标准时间范围

    @classmethod
    def 获取时间范围列表(cls) -> List[str]:
        """
        获取所有支持的时间范围列表

        返回:
            时间范围列表
        """
        return list(cls.支持的时间范围)

    @classmethod
    def 获取时间范围条件(
        cls,
        时间范围: str,
        开始日期: Optional[str] = None,
        结束日期: Optional[str] = None,
    ) -> Tuple[Optional[str], Optional[list]]:
        """
        根据时间范围参数生成SQL条件和参数（兼容管理系统监控）

        参数:
            时间范围: 时间范围参数（支持中文和英文参数）
            开始日期: 自定义开始日期字符串 (YYYY-MM-DD)
            结束日期: 自定义结束日期字符串 (YYYY-MM-DD)

        返回:
            (SQL条件字符串, 参数列表) 元组
        """
        try:
            # 参数映射：将英文参数转换为中文参数
            参数映射 = {
                "今天": "今日",
                "昨天": "昨日",
                "本周": "本周",
                "上周": "上周",
                "本月": "本月",
                "上月": "上月",
                "自定义": "自定义",
            }

            # 标准化时间范围参数
            标准时间范围 = 参数映射.get(时间范围, 时间范围)

            if 标准时间范围 == "自定义" and 开始日期 and 结束日期:
                # 自定义时间范围
                条件 = "创建时间 >= $1 AND 创建时间 < $2"
                开始时间 = datetime.strptime(开始日期, "%Y-%m-%d")
                结束时间_date = datetime.strptime(结束日期, "%Y-%m-%d").date()
                结束时间 = datetime.combine(
                    结束时间_date + timedelta(days=1), datetime.min.time()
                )
                参数 = [开始时间, 结束时间]
            else:
                # 使用统一的时间范围解析
                开始时间, 结束时间 = cls.解析时间范围(标准时间范围)
                条件 = "创建时间 >= $1 AND 创建时间 < $2"
                参数 = [开始时间, 结束时间]

            return 条件, 参数

        except Exception:
            # 默认返回最近7天
            现在 = datetime.now()
            当天开始 = 现在.replace(hour=0, minute=0, second=0, microsecond=0)
            七天前 = 当天开始 - timedelta(days=7)
            条件 = "创建时间 >= %s"
            参数 = [七天前]
            return 条件, 参数
