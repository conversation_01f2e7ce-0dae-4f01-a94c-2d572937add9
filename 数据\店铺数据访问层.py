"""
店铺数据访问层
负责处理店铺相关的数据库操作

特性：
1. 店铺信息的查询和管理
2. 用户店铺关联管理
3. 店铺权限验证
4. 店铺统计数据查询
5. 遵循三层分离架构，只处理数据访问
"""

from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 店铺数据访问:
    """店铺数据访问类"""

    @staticmethod
    async def 验证用户店铺权限(用户id: int, 店铺id: int) -> bool:
        """
        验证用户是否有权限访问指定店铺
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID
            
        Returns:
            是否有权限
        """
        try:
            权限查询 = """
            SELECT COUNT(*) as count
            FROM 用户_店铺 us
            WHERE us.用户id = $1 AND us.店铺id = $2
            """

            权限结果 = await 异步连接池实例.执行查询(权限查询, (用户id, 店铺id))
            
            if 权限结果 and 权限结果[0]["count"] > 0:
                return True
            return False
            
        except Exception as e:
            错误日志器.error(f"验证用户店铺权限失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 获取店铺详情(用户id: int, 店铺id: int) -> Optional[Dict[str, Any]]:
        """
        获取店铺详细信息
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID
            
        Returns:
            店铺详细信息或None
        """
        try:
            店铺查询 = """
            SELECT
                s.id as 店铺id,
                s.shop_id as 店铺标识,
                s.shop_name as 店铺名称,
                s.avatar as 店铺头像,
                s.创建时间,
                s.创建时间 as 更新时间,
                COUNT(DISTINCT p.id) as 产品数量,
                COUNT(DISTINCT dy.id) as 抖音商品数量
            FROM 店铺 s
            LEFT JOIN 用户产品表 p ON p.用户id = $1
            LEFT JOIN 抖音商品表 dy ON dy.用户id = $1
            WHERE s.id = $2
            GROUP BY s.id, s.shop_id, s.shop_name, s.avatar, s.创建时间
            """

            店铺详情 = await 异步连接池实例.执行查询(店铺查询, (用户id, 店铺id))

            if 店铺详情:
                return 店铺详情[0]
            return None
            
        except Exception as e:
            错误日志器.error(f"获取店铺详情失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return None

    @staticmethod
    async def 解绑用户店铺(用户id: int, 店铺id: int) -> bool:
        """
        解绑用户和店铺的关联
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID
            
        Returns:
            解绑是否成功
        """
        try:
            删除查询 = """
            DELETE FROM 用户_店铺
            WHERE 用户id = $1 AND 店铺id = $2
            """

            删除结果 = await 异步连接池实例.执行更新(删除查询, (用户id, 店铺id))
            
            数据库日志器.info(f"解绑用户店铺成功: 用户id={用户id}, 店铺id={店铺id}")
            return 删除结果 > 0
            
        except Exception as e:
            错误日志器.error(f"解绑用户店铺失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return False

    @staticmethod
    async def 获取店铺统计(用户id: int, 店铺id: Optional[int] = None) -> Dict[str, int]:
        """
        获取店铺统计数据
        
        Args:
            用户id: 用户id
            店铺id: 店铺ID（可选）
            
        Returns:
            统计数据字典
        """
        try:
            if 店铺id:
                # 获取特定店铺统计
                统计查询 = """
                SELECT
                    COUNT(DISTINCT s.id) as 总数,
                    COUNT(DISTINCT CASE WHEN s.shop_name IS NOT NULL AND s.shop_name != '' THEN s.id END) as 活跃数量,
                    COUNT(DISTINCT CASE WHEN s.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN s.id END) as 本月新增
                FROM 用户_店铺 us
                JOIN 店铺 s ON us.店铺id = s.id
                WHERE us.用户id = $1 AND s.id = $2
                """
                统计结果 = await 异步连接池实例.执行查询(统计查询, (用户id, 店铺id))
            else:
                # 获取用户所有店铺统计
                统计查询 = """
                SELECT
                    COUNT(DISTINCT s.id) as 总数,
                    COUNT(DISTINCT CASE WHEN s.shop_name IS NOT NULL AND s.shop_name != '' THEN s.id END) as 活跃数量,
                    COUNT(DISTINCT CASE WHEN s.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN s.id END) as 本月新增
                FROM 用户_店铺 us
                JOIN 店铺 s ON us.店铺id = s.id
                WHERE us.用户id = $1
                """
                统计结果 = await 异步连接池实例.执行查询(统计查询, (用户id,))

            if 统计结果:
                统计数据 = 统计结果[0]
                return {
                    "总数": 统计数据["总数"] or 0,
                    "活跃数量": 统计数据["活跃数量"] or 0,
                    "本月新增": 统计数据["本月新增"] or 0,
                }
            else:
                return {"总数": 0, "活跃数量": 0, "本月新增": 0}
                
        except Exception as e:
            错误日志器.error(f"获取店铺统计失败: 用户id={用户id}, 店铺id={店铺id}, 错误={str(e)}")
            return {"总数": 0, "活跃数量": 0, "本月新增": 0}

    @staticmethod
    async def 更新店铺信息(店铺id: int, 更新数据: Dict[str, Any]) -> bool:
        """
        更新店铺信息
        
        Args:
            店铺id: 店铺ID
            更新数据: 要更新的数据字典
            
        Returns:
            更新是否成功
        """
        try:
            # 构建更新语句
            更新字段 = []
            更新参数 = []
            参数索引 = 1

            if "店铺名称" in 更新数据:
                更新字段.append(f"shop_name = ${参数索引}")
                更新参数.append(更新数据["店铺名称"])
                参数索引 += 1

            if 更新字段:
                更新字段.append("创建时间 = NOW()")  # 使用创建时间字段作为更新时间
                更新参数.append(店铺id)

                更新查询 = f"""
                UPDATE 店铺
                SET {", ".join(更新字段)}
                WHERE id = ${参数索引}
                """

                更新结果 = await 异步连接池实例.执行更新(更新查询, tuple(更新参数))
                
                数据库日志器.info(f"更新店铺信息成功: 店铺id={店铺id}")
                return 更新结果 > 0
            
            return True  # 没有需要更新的字段
            
        except Exception as e:
            错误日志器.error(f"更新店铺信息失败: 店铺id={店铺id}, 错误={str(e)}")
            return False


# 创建数据访问层实例
店铺数据访问实例 = 店铺数据访问()
