# 数据库日志模块 - 统一优化版本
"""
专门用于数据库操作的日志记录功能

提供：
- 数据库操作记录（SQL、参数、结果、耗时）
- 连接池事件记录
- 数据库函数调用装饰器
"""

import functools
import time
from typing import Any, Dict, Optional

# 使用统一的日志系统
from .日志配置 import 应用日志器, 错误日志器


async def 记录数据库操作(sql: str, 参数: Optional[tuple] = None, 类型: str = "查询",
                    结果: Any = None, 耗时: float = 0, 错误: Optional[Exception] = None):
    """
    记录数据库操作详情
    
    Args:
        sql: SQL语句
        参数: SQL参数
        类型: 操作类型（查询、插入、更新、删除）
        结果: 操作结果
        耗时: 操作耗时（秒）
        错误: 异常信息（如果有）
    """
    # 屏蔽敏感信息（如密码）
    安全SQL = sql
    安全参数 = 参数
    
    if 参数 and isinstance(参数, tuple):
        # 简单识别可能包含密码的参数
        安全参数列表 = list(参数)
        if "password" in sql.lower() or "密码" in sql:
            for i, 值 in enumerate(安全参数列表):
                if isinstance(值, str) and len(值) > 3:
                    安全参数列表[i] = "******"  # 替换密码字段
        安全参数 = tuple(安全参数列表)
            
    # 记录详细日志
    if 错误:
        错误日志器.error(
            f"数据库{类型}操作失败: {安全SQL}, 参数={安全参数}, 错误={str(错误)}, 耗时={耗时:.4f}秒"
        )
    else:
        # 对于查询，不记录具体结果内容，只记录结果集大小
        结果描述 = "无结果"
        if 结果 is not None:
            if isinstance(结果, list):
                结果描述 = f"返回{len(结果)}行数据"
            elif isinstance(结果, int):
                结果描述 = f"影响{结果}行" if 类型 != "插入" else f"插入ID={结果}"
            else:
                结果描述 = f"返回数据类型={type(结果).__name__}"
                
        应用日志器.debug(
            f"数据库{类型}操作: {安全SQL}, 参数={安全参数}, {结果描述}, 耗时={耗时:.4f}秒"
        )
        
        # 如果耗时过长，记录一个警告
        if 耗时 > 1.0:  # 耗时超过1秒
            应用日志器.warning(
                f"数据库{类型}操作耗时较长: {耗时:.4f}秒, SQL={安全SQL}"
            )


def 记录连接池事件(事件: str, 详情: Optional[Dict[str, Any]] = None, 错误: Optional[Exception] = None):
    """
    记录连接池相关事件
    
    Args:
        事件: 事件类型描述
        详情: 详细信息字典
        错误: 异常信息（如果有）
    """
    if 错误:
        错误日志器.error(f"连接池{事件}: {str(错误)}", exc_info=True)
    else:
        message = f"连接池{事件}"
        if 详情:
            # 将字典转换为字符串格式
            详情字符串 = ", ".join(f"{k}={v}" for k, v in 详情.items())
            message = f"{message}: {详情字符串}"
        应用日志器.info(message)


def 数据库函数装饰器(func):
    """
    装饰器：自动记录数据库函数调用的耗时和结果
    
    用法:
        @数据库函数装饰器
        async def 查询用户(用户id):
            # 数据库操作
            pass
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 提取函数名称和参数
        函数名 = func.__name__
        开始时间 = time.time()
        结果 = None
        错误 = None
        
        try:
            结果 = await func(*args, **kwargs)
            return 结果
        except Exception as e:
            错误 = e
            raise
        finally:
            耗时 = time.time() - 开始时间
            
            # 记录函数调用日志
            if 错误:
                错误日志器.error(
                    f"数据库函数 {函数名} 调用失败: 耗时={耗时:.4f}秒, 错误={str(错误)}"
                )
            else:
                应用日志器.debug(
                    f"数据库函数 {函数名} 调用成功: 耗时={耗时:.4f}秒"
                )
                
            # 记录耗时过长的函数调用
            if 耗时 > 1.0:  # 耗时超过1秒
                应用日志器.warning(
                    f"数据库函数 {函数名} 调用耗时较长: {耗时:.4f}秒"
                )
    
    return wrapper 