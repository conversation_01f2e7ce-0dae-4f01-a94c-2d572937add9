/**
 * LangChain相关工具方法统一管理
 * 避免在多个服务文件中重复实现相同功能
 */

// 智能体类型格式化函数已移除

/**
 * 格式化智能体状态显示名称
 * @param {string} 状态 - 智能体状态
 * @returns {string} 显示名称
 */
export function 格式化智能体状态(状态) {
  const 状态映射 = {
    // 英文状态映射（兼容旧数据）
    'initializing': '初始化中',
    'running': '运行中',
    'paused': '已暂停',
    'error': '错误',
    'destroyed': '已销毁',
    'active': '运行中',
    'inactive': '已停用',
    'loading': '加载中',
    'configuring': '配置中',
    // 中文状态直接返回
    '初始化中': '初始化中',
    '运行中': '运行中',
    '已暂停': '已暂停',
    '错误': '错误',
    '已销毁': '已销毁'
  }
  return 状态映射[状态] || 状态
}

/**
 * 获取智能体状态颜色
 * @param {string} 状态 - 智能体状态
 * @returns {string} 颜色值
 */
export function 获取智能体状态颜色(状态) {
  const 颜色映射 = {
    // 英文状态映射（兼容旧数据）
    'initializing': 'processing',
    'running': 'success',
    'paused': 'warning',
    'error': 'error',
    'destroyed': 'default',
    'active': 'green',
    'inactive': 'gray',
    'loading': 'blue',
    'configuring': 'orange',
    // 中文状态映射
    '初始化中': 'processing',
    '运行中': 'success',
    '已暂停': 'warning',
    '错误': 'error',
    '已销毁': 'default'
  }
  return 颜色映射[状态] || 'default'
}

/**
 * 格式化权限级别
 * @param {string} 级别 - 权限级别
 * @returns {string} 显示名称
 */
export function 格式化权限级别(级别) {
  if (!级别) return '公开'
  
  const 级别映射 = {
    'read': '只读',
    'write': '读写',
    'admin': '管理'
  }
  return 级别映射[级别] || 级别
}

/**
 * 获取权限级别颜色
 * @param {string} 级别 - 权限级别
 * @returns {string} 颜色值
 */
export function 获取权限级别颜色(级别) {
  if (!级别) return 'green'
  
  const 颜色映射 = {
    'read': 'blue',
    'write': 'green',
    'admin': 'red'
  }
  return 颜色映射[级别] || 'default'
}

/**
 * 格式化文件大小
 * @param {number} 字节数 - 文件大小（字节）
 * @returns {string} 格式化后的大小
 */
export function 格式化文件大小(字节数) {
  if (字节数 === 0) return '0 B'
  
  const 单位 = ['B', 'KB', 'MB', 'GB', 'TB']
  const 指数 = Math.floor(Math.log(字节数) / Math.log(1024))
  const 大小 = (字节数 / Math.pow(1024, 指数)).toFixed(2)
  
  return `${大小} ${单位[指数]}`
}

/**
 * 格式化时间显示
 * @param {string} 时间字符串 - ISO时间字符串
 * @returns {string} 格式化后的时间
 */
export function 格式化时间(时间字符串) {
  if (!时间字符串) return '-'
  
  const 时间 = new Date(时间字符串)
  const 现在 = new Date()
  const 差值 = 现在 - 时间
  
  // 小于1分钟
  if (差值 < 60000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (差值 < 3600000) {
    const 分钟 = Math.floor(差值 / 60000)
    return `${分钟}分钟前`
  }
  
  // 小于1天
  if (差值 < 86400000) {
    const 小时 = Math.floor(差值 / 3600000)
    return `${小时}小时前`
  }
  
  // 大于1天，显示具体日期
  return 时间.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 验证智能体配置
 * @param {Object} 配置 - 智能体配置
 * @returns {Object} 验证结果
 */
export function 验证智能体配置(配置) {
  const 错误列表 = []
  
  if (!配置.智能体名称 || 配置.智能体名称.trim() === '') {
    错误列表.push('智能体名称不能为空')
  }
  
  if (!配置.智能体类型) {
    错误列表.push('必须选择智能体类型')
  }
  
  if (!配置.模型名称) {
    错误列表.push('必须选择模型')
  }
  
  if (!配置.系统提示词 || 配置.系统提示词.trim() === '') {
    错误列表.push('系统提示词不能为空')
  }
  
  return {
    有效: 错误列表.length === 0,
    错误列表
  }
}

/**
 * 验证消息内容
 * @param {string} 消息内容 - 要发送的消息
 * @returns {Object} 验证结果
 */
export function 验证消息内容(消息内容) {
  const 错误列表 = []
  
  if (!消息内容 || 消息内容.trim() === '') {
    错误列表.push('消息内容不能为空')
  }
  
  if (消息内容.length > 2000) {
    错误列表.push('消息内容不能超过2000字符')
  }
  
  // 检查敏感词（这里可以添加敏感词过滤逻辑）
  const 敏感词列表 = ['违法', '暴力', '色情'] // 示例敏感词
  const 包含敏感词 = 敏感词列表.some(词 => 消息内容.includes(词))
  
  if (包含敏感词) {
    错误列表.push('消息内容包含敏感词汇')
  }
  
  return {
    有效: 错误列表.length === 0,
    错误列表
  }
}

/**
 * 生成会话ID
 * @returns {string} 新的会话ID
 */
export function 生成会话ID() {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 计算对话统计
 * @param {Array} 对话列表 - 对话记录列表
 * @returns {Object} 统计信息
 */
export function 计算对话统计(对话列表) {
  if (!对话列表 || 对话列表.length === 0) {
    return {
      总对话数: 0,
      总字符数: 0,
      平均响应时间: 0,
      最长对话: 0,
      最短对话: 0
    }
  }
  
  const 总对话数 = 对话列表.length
  const 总字符数 = 对话列表.reduce((sum, 对话) => 
    sum + (对话.用户消息?.length || 0) + (对话.智能体回复?.length || 0), 0)
  
  const 响应时间列表 = 对话列表
    .map(对话 => 对话.处理时长)
    .filter(时长 => 时长 > 0)
  
  const 平均响应时间 = 响应时间列表.length > 0 
    ? 响应时间列表.reduce((sum, 时长) => sum + 时长, 0) / 响应时间列表.length
    : 0
  
  const 对话长度列表 = 对话列表.map(对话 => 
    (对话.用户消息?.length || 0) + (对话.智能体回复?.length || 0))
  
  return {
    总对话数,
    总字符数,
    平均响应时间: Math.round(平均响应时间 * 100) / 100,
    最长对话: Math.max(...对话长度列表, 0),
    最短对话: Math.min(...对话长度列表, 0)
  }
}
