// 统一文件大小分类标准
export const 文件大小分类 = {
  // 通用分类标准
  通用: {
    小文件: 100 * 1024,      // 100KB
    中等文件: 1024 * 1024,   // 1MB
    大文件: 10 * 1024 * 1024, // 10MB
  },
  // 策略特定分类标准
  策略特定: {
    '行级精准分块': {
      小文件: 20 * 1024,     // 20KB - 理想范围
      警告阈值: 50 * 1024,   // 50KB - 性能警告
      最大限制: 100 * 1024,  // 100KB - 硬限制
    },
    '语义优化分块': {
      小文件: 50 * 1024,     // 50KB
      中等文件: 500 * 1024,  // 500KB
      大文件: 5 * 1024 * 1024, // 5MB
    },
    '智能递归分块': {
      小文件: 200 * 1024,    // 200KB
      中等文件: 2 * 1024 * 1024, // 2MB
      大文件: 20 * 1024 * 1024,  // 20MB
    },
    '固定大小分块': {
      小文件: 500 * 1024,    // 500KB
      中等文件: 5 * 1024 * 1024, // 5MB
      大文件: 50 * 1024 * 1024,  // 50MB
    }
  }
}

// 获取文件大小分类
export const getFileSizeCategory = (fileSize, strategy = null) => {
  if (strategy && 文件大小分类.策略特定[strategy]) {
    const limits = 文件大小分类.策略特定[strategy]
    if (fileSize <= limits.小文件) return 'small'
    if (fileSize <= (limits.中等文件 || limits.警告阈值 || limits.最大限制)) return 'medium'
    return 'large'
  }

  // 使用通用分类
  const limits = 文件大小分类.通用
  if (fileSize <= limits.小文件) return 'small'
  if (fileSize <= limits.中等文件) return 'medium'
  return 'large'
}

// 获取文件大小描述
export const getFileSizeDescription = (fileSize, strategy = null) => {
  const category = getFileSizeCategory(fileSize, strategy)
  const sizeKB = fileSize / 1024
  const sizeMB = fileSize / (1024 * 1024)

  if (strategy === '行级精准分块') {
    if (fileSize > 文件大小分类.策略特定[strategy].最大限制) {
      return `超大文件（${sizeMB.toFixed(1)}MB）- 不适合行级分块`
    }
    if (fileSize > 文件大小分类.策略特定[strategy].警告阈值) {
      return `较大文件（${sizeKB.toFixed(0)}KB）- 可能影响性能`
    }
    return `适中文件（${sizeKB.toFixed(0)}KB）- 适合行级分块`
  }

  switch (category) {
    case 'small': return `小文件（${sizeKB.toFixed(0)}KB）`
    case 'medium': return `中等文件（${sizeMB.toFixed(1)}MB）`
    case 'large': return `大文件（${sizeMB.toFixed(1)}MB）`
    default: return `文件（${sizeMB.toFixed(1)}MB）`
  }
}

// 分块策略配置 - 统一配置文件
export const 分块策略配置 = {
  '智能递归分块': {
    icon: '🧠',
    description: '通用策略，按段落、句子递归分割',
    advantages: '语义连贯、通用性强、处理效果稳定',
    useCases: '文章、报告、说明文档、通用文本',
    fileTypes: ['pdf', 'docx', 'txt', 'md'],
    minSize: 200,
    recommendedSize: 1000,
    maxSize: 4000
  },
  '语义优化分块': {
    icon: '📝',
    description: '中文优化，适合列表和结构化数据',
    advantages: '中文处理优秀、列表数据友好、语义保持好',
    useCases: '中文文档、产品介绍、功能列表、结构化内容',
    fileTypes: ['txt', 'md', 'csv'],
    minSize: 100,
    recommendedSize: 500,
    maxSize: 2000
  },
  '行级精准分块': {
    icon: '📋',
    description: '每行独立分块，专为价格列表设计',
    advantages: '精确匹配、检索准确、适合结构化数据',
    useCases: '价格列表、产品目录、联系人信息、数据表格',
    fileTypes: ['csv', 'txt'],
    minSize: 20,
    recommendedSize: 100,
    maxSize: 500,
    // 文件大小限制配置
    fileSizeLimit: {
      warning: 50 * 1024,    // 50KB - 警告阈值
      maximum: 100 * 1024,   // 100KB - 硬限制
    }
  },
  '固定大小分块': {
    icon: '📏',
    description: '严格按字符数分割，不考虑语义',
    advantages: '分块大小一致、处理速度快、内存占用可控',
    useCases: '大文件处理、性能优先场景、特殊格式文件',
    fileTypes: ['txt', 'log'],
    minSize: 500,
    recommendedSize: 2000,
    maxSize: 8000
  }
}

// 智能推荐逻辑
export const getRecommendedStrategy = (files) => {
  if (!files?.length) return null

  const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0)
  const fileTypes = [...new Set(files.map(file => {
    const ext = file.name.split('.').pop()?.toLowerCase()
    return ext
  }))]

  // 基于文件大小和类型推荐
  if (totalSize < 2 * 1024) { // 小于2KB
    if (fileTypes.includes('csv') || files.some(f => f.name.includes('价格') || f.name.includes('列表'))) {
      return '行级精准分块'
    }
    return '语义优化分块'
  }

  if (totalSize < 50 * 1024) { // 小于50KB
    if (fileTypes.includes('csv')) return '行级精准分块'
    if (fileTypes.includes('txt') || fileTypes.includes('md')) return '语义优化分块'
    return '智能递归分块'
  }

  return '智能递归分块'
}

// 获取可用策略列表
export const getAvailableStrategies = (files) => {
  const recommended = getRecommendedStrategy(files)
  return Object.entries(分块策略配置).map(([key, config]) => ({
    value: key,
    name: key, // 简化：直接使用key作为name
    icon: config.icon,
    recommended: key === recommended,
    ...config
  }))
}

// 获取策略显示名称
export const getStrategyDisplayName = (strategy) => {
  return strategy // 简化：直接返回策略名
}

// 获取策略描述
export const getStrategyDescription = (strategy) => {
  const config = 分块策略配置[strategy]
  return config ? config.description : '选择合适的分块策略'
}

// 应用推荐策略
export const applyRecommendedStrategy = (files, 上传选项, message) => {
  const recommended = getRecommendedStrategy(files)
  if (recommended) {
    上传选项.分块策略 = recommended
    const config = 分块策略配置[recommended]
    上传选项.分块大小 = config.recommendedSize
    上传选项.分块重叠 = Math.min(config.recommendedSize * 0.2, 200)

    message.success(`已应用推荐策略：${recommended}`)
  }
}

// 策略变更处理（优化版）
export const onStrategyChange = (strategy, 上传选项, 文件列表 = []) => {
  const config = 分块策略配置[strategy]
  if (config) {
    // 自动调整分块大小到推荐值
    if (上传选项.分块大小 < config.minSize || 上传选项.分块大小 > config.maxSize) {
      上传选项.分块大小 = config.recommendedSize
      上传选项.分块重叠 = Math.min(config.recommendedSize * 0.2, 200)
    }

    // 根据文件大小和策略给出智能建议
    if (文件列表.length > 0) {
      const totalSize = 文件列表.reduce((sum, file) => sum + (file.size || 0), 0)

      // 根据文件大小类别调整参数
      if (strategy === '行级精准分块') {
        if (totalSize > 文件大小分类.策略特定[strategy].最大限制) {
          return {
            success: false,
            message: '文件过大，建议切换到其他分块策略',
            suggestedStrategy: '语义优化分块'
          }
        }
      }
    }
  }
  return { success: true }
}

// 智能快速配置
export const getSmartQuickConfig = (fileSize, strategy) => {
  const category = getFileSizeCategory(fileSize, strategy)
  const config = 分块策略配置[strategy]

  if (!config) return null

  switch (category) {
    case 'small':
      return {
        chunk_size: Math.max(config.minSize, Math.min(config.recommendedSize * 0.5, config.maxSize)),
        chunk_overlap: Math.min(config.recommendedSize * 0.1, 100),
        description: '小文件优化配置'
      }
    case 'medium':
      return {
        chunk_size: config.recommendedSize,
        chunk_overlap: Math.min(config.recommendedSize * 0.2, 200),
        description: '中等文件标准配置'
      }
    case 'large':
      return {
        chunk_size: Math.min(config.recommendedSize * 1.5, config.maxSize),
        chunk_overlap: Math.min(config.recommendedSize * 0.3, 400),
        description: '大文件优化配置'
      }
    default:
      return {
        chunk_size: config.recommendedSize,
        chunk_overlap: Math.min(config.recommendedSize * 0.2, 200),
        description: '默认配置'
      }
  }
}

// 智能分块大小计算
export const getMinChunkSize = (files) => {
  const totalSize = files?.reduce((sum, file) => sum + (file.size || 0), 0) || 0

  if (totalSize === 0) return 50
  if (totalSize < 1024) return 20        // 小于1KB
  if (totalSize < 5 * 1024) return 50    // 小于5KB  
  if (totalSize < 50 * 1024) return 100  // 小于50KB
  if (totalSize < 500 * 1024) return 200 // 小于500KB
  return 500                             // 大文件
}

export const getChunkStep = (files) => {
  const minSize = getMinChunkSize(files)
  return minSize < 100 ? 10 : (minSize < 500 ? 50 : 100)
}

export const getChunkSizeHint = (files) => {
  const totalSize = files?.reduce((sum, file) => sum + (file.size || 0), 0) || 0

  if (totalSize === 0) return '建议：根据文件大小自动调整'

  const sizeKB = totalSize / 1024
  if (sizeKB < 1) return '小文件：建议20-100字符分块'
  if (sizeKB < 5) return '小文件：建议50-200字符分块'
  if (sizeKB < 50) return '中等文件：建议100-500字符分块'
  if (sizeKB < 500) return '中等文件：建议200-1000字符分块'
  return '大文件：建议500-4000字符分块'
}
