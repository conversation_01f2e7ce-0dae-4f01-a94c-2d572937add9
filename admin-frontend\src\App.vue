<template>
  <router-view />
</template>

<script setup>
// App.vue 通常不需要太多逻辑，主要作为路由的渲染出口
// 全局的布局由 AdminLayout.vue 或其他布局组件通过路由配置实现
</script>

<style>
/* 
  可以考虑在这里引入一些全局基础样式，或者在 main.js 中引入一个 CSS 文件。
  例如：
  @import './assets/base.css'; 

  确保路径正确，并且该CSS文件存在。
  Ant Design Vue 的样式已经在 main.js 中通过 import 'ant-design-vue/dist/reset.css'; 引入。
*/
#app {
  height: 100vh; /* 让app容器至少占满整个视口高度 */
  display: flex;
  flex-direction: column;
}
</style> 