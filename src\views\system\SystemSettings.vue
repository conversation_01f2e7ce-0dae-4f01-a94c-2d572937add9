<template>
  <div class="system-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">系统设置</h1>
        <p class="page-description">个性化定制您的使用体验</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-container">
      <a-row :gutter="24">
        <!-- 左侧设置菜单 -->
        <a-col :xs="24" :lg="6">
          <a-card class="settings-menu" :bordered="false">
            <div class="custom-menu">
              <div
                v-for="item in visibleMenuItems"
                :key="item.key"
                :class="['menu-item', { active: selectedKeys.includes(item.key) }]"
                @click="handleMenuClick({ key: item.key })"
              >
                <component :is="item.icon" class="menu-icon" />
                <span class="menu-text">{{ item.label }}</span>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧设置内容 -->
        <a-col :xs="24" :lg="18">
          <div class="settings-content">
            <!-- 个人信息设置 -->
            <div v-if="currentSetting === 'personal'">
              <PersonalInfoSettings @save="handleSaveSettings" />
            </div>

            <!-- 通知设置 -->
            <div v-else-if="currentSetting === 'notifications'">
              <NotificationSettings @save="handleSaveSettings" />
            </div>

            <!-- 移除界面主题设置 -->

            <!-- AI设置 -->
            <div v-else-if="currentSetting === 'ai'">
              <AISettings @save="handleSaveSettings" />
            </div>

            <!-- 微信自动化设置 -->
            <div v-else-if="currentSetting === 'wechat-automation'">
              <WeChatAutomationSettings @save="handleSaveSettings" />
            </div>

            <!-- 公司管理 -->
            <div v-else-if="currentSetting === 'company'">
              <CompanyManagement @save="handleSaveSettings" />
            </div>

            <!-- 推广管理 -->
            <div v-else-if="currentSetting === 'customer-invitation'">
              <CustomerInvitation @save="handleSaveSettings" />
            </div>

            <!-- 移除数据管理 -->

            <!-- 付费管理（新增的重点功能） -->
            <div v-else-if="currentSetting === 'payment'">
              <PaymentManagement @save="handleSaveSettings" />
            </div>

            <!-- 移除隐私安全 -->

            <!-- 移除关于系统 -->

            <!-- 默认显示个人信息设置 -->
            <div v-else>
              <PersonalInfoSettings @save="handleSaveSettings" />
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 保存成功提示 -->
    <a-float-button
      v-if="hasUnsavedChanges"
      type="primary"
      shape="square"
      :style="{ right: '24px', bottom: '24px' }"
      @click="handleSaveAll"
    >
      <template #icon>
        <SaveOutlined />
      </template>
      保存设置
    </a-float-button>
  </div>
</template>

<script setup>
import { 主API客户端 as apiClient } from '@/services/apiClientFactory'
import { useUserStore } from '@/store/user'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 导入图标
import {
    SaveOutlined,
    WechatOutlined
} from '@ant-design/icons-vue'

// 导入设置组件
import AISettings from '../../components/system/AISettings.vue'
import CompanyManagement from '../../components/system/CompanyManagement.vue'
import CustomerInvitation from '../../components/system/CustomerInvitation.vue'; // 新增推广管理组件
import NotificationSettings from '../../components/system/NotificationSettings.vue'
import PaymentManagement from '../../components/system/PaymentManagement.vue'; // 新增付费管理组件
import PersonalInfoSettings from '../../components/system/PersonalInfoSettings.vue'
import WeChatAutomationSettings from '../../components/system/WeChatAutomationSettings.vue'

// 响应式状态
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 当前选中的菜单项
const selectedKeys = ref([route.query.tab || 'personal'])

// 设置数据
const settingsData = ref({
  personal: {},
  notifications: {},
  ai: {},
  'wechat-automation': {},
  company: {},
  'customer-invitation': {},
  payment: {}
})

// 是否有未保存的修改
const hasUnsavedChanges = ref(false)

// 用户权限信息
const userPermissions = ref(null)

// 是否正在加载用户权限
const loadingPermissions = ref(false)

// 基础菜单项配置
const baseMenuItems = [
  { key: 'personal', icon: 'UserOutlined', label: '个人信息' },
  { key: 'notifications', icon: 'BellOutlined', label: '通知设置' },
  { key: 'ai', icon: 'RobotOutlined', label: 'AI设置' },
  { key: 'wechat-automation', icon: 'WechatOutlined', label: '微信自动化' },
  { key: 'company', icon: 'BankOutlined', label: '公司管理' },
  { key: 'customer-invitation', icon: 'UserAddOutlined', label: '推广管理' },
  { key: 'payment', icon: 'CreditCardOutlined', label: '付费管理' }
]

// 根据用户权限计算可见的菜单项
const visibleMenuItems = computed(() => {
  // 如果权限信息还未加载，返回除了推广管理外的所有菜单项
  if (!userPermissions.value) {
    return baseMenuItems.filter(item => item.key !== 'customer-invitation')
  }

  // 检查代理等级
  const agentLevel = userPermissions.value.代理类型信息?.代理等级
  
  // 如果代理等级为0，隐藏推广管理
  if (agentLevel === 0) {
    return baseMenuItems.filter(item => item.key !== 'customer-invitation')
  }

  // 其他情况显示所有菜单项
  return baseMenuItems
})

// 获取用户完整权限信息
const getUserPermissions = async () => {
  try {
    loadingPermissions.value = true
    
    // 调用后端接口获取用户完整权限信息
    const response = await apiClient.get('/get_permission')
    
    if (response.status === 100) {
      userPermissions.value = response
      
      // 记录代理等级信息用于调试
      console.log('用户权限信息已加载:', {
        代理等级: userPermissions.value.代理类型信息?.代理等级,
        用户id: userPermissions.value.用户信息?.id,
        会员状态: userPermissions.value.会员信息?.是否有效
      })
      
      // 如果当前选中的是推广管理，但用户代理等级为0，则切换到个人信息
      if (selectedKeys.value[0] === 'customer-invitation' && 
          userPermissions.value.代理类型信息?.代理等级 === 0) {
        selectedKeys.value = ['personal']
        router.replace({
          path: route.path,
          query: { ...route.query, tab: 'personal' }
        })
      }
    } else {
      console.error('获取用户权限失败:', response.message)
      message.error('获取用户权限失败，请刷新页面重试')
    }
  } catch (error) {
    console.error('获取用户权限异常:', error)
    message.error('获取用户权限失败，请检查网络连接')
  } finally {
    loadingPermissions.value = false
  }
}

// 计算当前选中的设置类型
const currentSetting = computed(() => {
  return selectedKeys.value[0] || 'personal'
})

// 处理菜单点击事件
const handleMenuClick = ({ key }) => {
  // 检查是否有权限访问推广管理
  if (key === 'customer-invitation' && userPermissions.value) {
    const agentLevel = userPermissions.value.代理类型信息?.代理等级
    if (agentLevel === 0) {
      message.warning('您当前的代理等级无法访问推广管理功能')
      return
    }
  }
  
  selectedKeys.value = [key]

  // 更新URL参数，但不刷新页面
  router.replace({
    path: route.path,
    query: { ...route.query, tab: key }
  })

  // 记录用户操作用于分析
  console.log(`用户切换到设置页面: ${key}`)
}

// 处理单个设置项保存
const handleSaveSettings = (settingType, data) => {
  try {
    // 更新设置数据
    settingsData.value[settingType] = data

    // 这里应该调用API保存设置到后端
    // await saveUserSettings(settingType, data)

    // 移除重复的成功提示，让子组件自己处理提示
    // message.success('设置保存成功')
    hasUnsavedChanges.value = false

    console.log(`保存设置: ${settingType}`, data)
  } catch (error) {
    console.error('保存设置失败:', error)
    message.error('保存设置失败，请重试')
  }
}

// 处理保存所有设置
const handleSaveAll = async () => {
  try {
    // 这里应该调用API批量保存所有设置
    // await saveAllUserSettings(settingsData.value)
    
    message.success('所有设置保存成功')
    hasUnsavedChanges.value = false
  } catch (error) {
    console.error('保存设置失败:', error)
    message.error('保存设置失败，请重试')
  }
}

// 监听路由变化，更新选中的tab
watch(() => route.query.tab, (newTab) => {
  if (newTab && newTab !== selectedKeys.value[0]) {
    // 检查是否有权限访问推广管理
    if (newTab === 'customer-invitation' && userPermissions.value) {
      const agentLevel = userPermissions.value.代理类型信息?.代理等级
      if (agentLevel === 0) {
        message.warning('您当前的代理等级无法访问推广管理功能')
        // 重定向到个人信息页面
        router.replace({
          path: route.path,
          query: { ...route.query, tab: 'personal' }
        })
        return
      }
    }

    selectedKeys.value = [newTab]
    console.log('路由变化，切换到tab:', newTab)
  }
}, { immediate: true })

// 组件挂载时获取用户权限信息
onMounted(async () => {
  await getUserPermissions()
})
</script>

<style scoped>
.system-settings {
  min-height: calc(100vh - 64px - 48px - 70px);
  background: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #74b9ff 0%, #6c5ce7 100%);
  color: white;
  padding: 40px 0;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px 24px;
  background: transparent; /* 确保内容区域背景透明 */
}

.settings-menu {
  border-radius: 12px;
  background: #ffffff; /* 确保菜单背景为白色 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 88px; /* 考虑到固定头部的高度 */
}

.settings-content {
  background: transparent; /* 设置内容区域背景透明 */
  min-height: 500px; /* 确保有足够的高度 */
}

.custom-menu {
  border: none;
  background: transparent;
}

.menu-item {
  border-radius: 8px;
  margin: 4px 0;
  height: 48px;
  line-height: 48px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.menu-icon {
  font-size: 16px;
  margin-right: 12px;
}

.menu-text {
  font-size: 14px;
}

.settings-content {
  min-height: 600px;
}

/* 为不同设置页面添加过渡动画 */
.settings-content > div {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 0;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .content-container {
    padding: 0 16px 16px;
  }
  
  .settings-menu {
    position: static;
    margin-bottom: 16px;
  }
  
  .menu-list {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .menu-list :deep(.ant-menu-item) {
    min-width: 120px;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .menu-list :deep(.ant-menu-item span) {
    display: none;
  }
  
  .menu-list :deep(.ant-menu-item) {
    min-width: 48px;
  }
}
</style> 