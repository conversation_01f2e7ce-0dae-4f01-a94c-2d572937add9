<template>
  <div class="leads-center">
    <!-- 线索统计概览 -->
    <div class="leads-overview">
      <a-card :loading="loading.overview">
        <template #title>
          <span>
            <database-outlined />
            线索概览
          </span>
          <a-button
            type="text"
            size="small"
            @click="refreshOverview"
            :loading="loading.overview"
            style="float: right;"
          >
            <reload-outlined />
            刷新
          </a-button>
        </template>

        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="总线索数"
              :value="formatTotalCount(overviewStats.totalLeads, userPermissions.isVip, 'leads')"
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <span style="font-size: 14px;">个</span>
              </template>
              <template #prefix>
                <database-outlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <a-tag :color="overviewStats.trendAnalysis === 'increasing' ? 'green' : 'orange'">
                {{ getTrendText(overviewStats.trendAnalysis) }}
              </a-tag>
            </div>
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="今日新增"
              :value="formatTotalCount(overviewStats.dailyNew, userPermissions.isVip, 'leads')"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <plus-circle-outlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="growth-rate">
                较昨日 {{ overviewStats.growthRate > 0 ? '+' : '' }}{{ overviewStats.growthRate }}%
              </span>
            </div>
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="有联系方式"
              :value="formatTotalCount(overviewStats.hasContactCount, userPermissions.isVip, 'contacts')"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <phone-outlined />
              </template>
            </a-statistic>
            <div class="stat-trend" v-if="userPermissions.isVip">
              <span class="percentage">
                占比 {{ ((overviewStats.hasContactCount / overviewStats.totalLeads) * 100).toFixed(1) }}%
              </span>
            </div>
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="高价值线索"
              :value="formatTotalCount(overviewStats.highValueCount, userPermissions.isVip, 'leads')"
              :value-style="{ color: '#eb2f96' }"
            >
              <template #prefix>
                <star-outlined />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="percentage">
                占比 {{ ((overviewStats.highValueCount / overviewStats.totalLeads) * 100).toFixed(1) }}%
              </span>
            </div>
          </a-col>
        </a-row>

        <!-- 数据分布图表 -->
        <a-row :gutter="16" style="margin-top: 24px;">
          <a-col :span="8">
            <div class="chart-container">
              <h4>线索来源分布</h4>
              <div ref="sourceChart" style="height: 200px;"></div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="chart-container">
              <h4>地域分布TOP5</h4>
              <div ref="locationChart" style="height: 200px;"></div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="chart-container">
              <h4>类别分布TOP5</h4>
              <div ref="categoryChart" style="height: 200px;"></div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <template #title>
          <span>
            <search-outlined />
            搜索筛选
          </span>
        </template>

        <!-- 基础搜索 -->
        <div class="basic-search">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-input-search
                v-model:value="searchForm.关键词"
                placeholder="搜索达人名称、城市、类别..."
                allow-clear
                @search="handleSearch"
                size="large"
              >
                <template #prefix>
                  <search-outlined />
                </template>
              </a-input-search>
            </a-col>
            <a-col :span="6">
              <a-select
                v-model:value="searchForm.线索来源"
                placeholder="线索来源"
                allow-clear
                size="large"
                @change="handleSearch"
              >
                <a-select-option
                  v-for="source in sourceOptions"
                  :key="source.value"
                  :value="source.value"
                >
                  {{ source.label }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="6">
              <a-button
                type="primary"
                size="large"
                @click="handleSearch"
                :loading="loading.list"
              >
                <search-outlined />
                搜索
              </a-button>
            </a-col>
          </a-row>
        </div>

        <!-- 筛选操作 -->
        <div class="filter-actions">
          <a-space>
            <a-button @click="resetSearch">
              <clear-outlined />
              重置筛选
            </a-button>
            <a-button @click="handleSearch">
              <reload-outlined />
              刷新数据
            </a-button>
          </a-space>
        </div>
      </a-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="operation-toolbar">
      <a-card>
        <div class="toolbar-content">
          <div class="left-actions">
            <a-space>
              <a-checkbox
                v-model:checked="selectAll"
                @change="handleSelectAll"
                :indeterminate="indeterminate"
              >
                全选
              </a-checkbox>
              <span class="selected-info" v-if="selectedRowKeys.length > 0">
                已选择 {{ selectedRowKeys.length }} 项
              </span>
            </a-space>
          </div>

          <div class="right-actions">
            <a-space>
              <!-- 批量操作 -->
              <a-dropdown v-if="selectedRowKeys.length > 0">
                <template #overlay>
                  <a-menu @click="handleBatchOperation">
                    <a-menu-item key="export">
                      <export-outlined />
                      导出选中
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>
                  批量操作
                  <down-outlined />
                </a-button>
              </a-dropdown>

              <!-- 导出功能 -->
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleExport">
                    <a-menu-item key="current">
                      <file-excel-outlined />
                      导出当前页
                    </a-menu-item>
                    <a-menu-item key="all">
                      <database-outlined />
                      导出全部数据
                    </a-menu-item>
                    <a-menu-item key="filtered">
                      <filter-outlined />
                      导出筛选结果
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>
                  <export-outlined />
                  导出数据
                  <down-outlined />
                </a-button>
              </a-dropdown>

              <!-- 刷新按钮 -->
              <a-button @click="refreshList" :loading="loading.list">
                <reload-outlined />
                刷新
              </a-button>
            </a-space>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 线索列表 -->
    <div class="leads-list">
      <a-card>
        <template #title>
          <span>
            <unordered-list-outlined />
            线索列表
          </span>
        </template>

        <a-table
          :columns="tableColumns"
          :data-source="leadsList"
          :pagination="false"
          :loading="loading.list"
          :scroll="{ x: 1400 }"
          :row-selection="rowSelection"
          size="middle"
          row-key="id"
        >
          <!-- 达人信息列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'leadInfo'">
              <div class="lead-info">
                <div class="lead-name">
                  <a @click="viewLeadDetail(record)" class="lead-link">
                    {{ record.displayName }}
                  </a>
                  <a-tag
                    v-if="record.isHighValue"
                    color="gold"
                    size="small"
                  >
                    高价值
                  </a-tag>
                </div>
                <div class="lead-meta">
                  <span class="location">
                    <environment-outlined />
                    {{ record.displayLocation }}
                  </span>
                  <a-divider type="vertical" />
                  <span class="category">
                    <tag-outlined />
                    {{ record.displayCategory }}
                  </span>
                </div>
              </div>
            </template>

            <!-- 联系方式列 -->
            <template v-else-if="column.key === 'contact'">
              <div class="contact-info">
                <span v-if="record.关联_联系方式内容" class="contact-content">
                  <a-tag :color="getContactTypeColor(record.关联_联系方式类型)">
                    {{ record.关联_联系方式类型 || '联系方式' }}
                  </a-tag>
                  <span class="contact-text">{{ record.关联_联系方式内容 }}</span>
                </span>
                <span v-else class="no-contact">
                  <a-tag color="default">
                    <minus-outlined />
                    暂无联系方式
                  </a-tag>
                </span>
              </div>
            </template>





            <!-- 时间列 -->
            <template v-else-if="column.key === 'updateTime'">
              <div class="time-info">
                <div>{{ formatDate(record.更新时间) }}</div>
                <div class="time-ago">{{ getTimeAgo(record.更新时间) }}</div>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button
                  type="link"
                  size="small"
                  @click="viewLeadDetail(record)"
                >
                  <eye-outlined />
                  详情
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="editLead(record)"
                >
                  <edit-outlined />
                  编辑
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleLeadAction(key, record)">
                      <a-menu-item key="export">
                        <export-outlined />
                        导出
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    <ellipsis-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>

        <!-- 智能分页组件 -->
        <smart-pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :has-next-page="hasNextPage"
          :loading="loading.list"
          :pagination-limit-info="paginationLimitInfo"
          @change="handleSmartPageChange"
          @showSizeChange="handleSmartPageSizeChange"
        />
      </a-card>
    </div>

    <!-- 线索详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="线索详情"
      :width="600"
      placement="right"
      @close="handleDetailDrawerClose"
    >
      <lead-detail-component
        v-if="selectedLead"
        :lead="selectedLead"
        @update="handleLeadUpdate"
        @close="handleDetailDrawerClose"
      />
    </a-drawer>

    <!-- 编辑线索抽屉 -->
    <a-drawer
      v-model:open="editDrawerVisible"
      title="编辑线索"
      :width="500"
      placement="right"
      @close="handleEditDrawerClose"
    >
      <lead-edit-component
        v-if="editingLead"
        :lead="editingLead"
        @save="handleLeadSave"
        @cancel="handleEditDrawerClose"
      />
    </a-drawer>
  </div>
</template>

<script setup>
import {
    ClearOutlined,
    DatabaseOutlined,
    DownOutlined,
    EditOutlined,
    EllipsisOutlined,
    EnvironmentOutlined,
    ExportOutlined,
    EyeOutlined,
    FileExcelOutlined,
    FilterOutlined,
    MinusOutlined,
    PhoneOutlined,
    PlusCircleOutlined,
    ReloadOutlined,
    SearchOutlined,
    StarOutlined,
    TagOutlined,
    UnorderedListOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'

// 导入服务和组件
import SmartPagination from '../../components/common/SmartPagination.vue'
import LeadDetailComponent from '../../components/leads/LeadDetailComponent.vue'
import LeadEditComponent from '../../components/leads/LeadEditComponent.vue'
import leadsService from '../../services/leadsService'

// 导入数据隐私保护工具
import {
    checkUserVipStatus,
    formatPaginationText,
    formatTotalCount
} from '../../utils/dataPrivacy'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

defineOptions({
  name: 'LeadsCenter'
})

// ==================== 响应式数据 ====================

// 加载状态
const loading = reactive({
  overview: false,
  list: false
})

// 用户权限状态
const userPermissions = reactive({
  isVip: false,
  memberLevel: 0,
  canViewFullStats: false
})

// 分页限制信息（每个用户每日限制翻页10页）
const paginationLimitInfo = reactive({
  can_paginate: true,
  used_count: 0,
  limit_count: 100,  // 修改为每日100页限制
  remaining_count: 100,
  warning_level: 'none',
  message: ''
})

// 是否有下一页（用于智能分页）
const hasNextPage = ref(true)

// 概览统计数据
const overviewStats = reactive({
  totalLeads: 0,
  dailyNew: 0,
  weeklyNew: 0,
  monthlyNew: 0,
  hasContactCount: 0,
  highValueCount: 0,
  conversionRate: 0,
  growthRate: 0,
  formattedTotal: '0',
  trendAnalysis: 'stable',
  sourceDistribution: [],
  locationDistribution: [],
  categoryDistribution: []
})

// 搜索表单
const searchForm = reactive({
  关键词: '',
  线索来源: undefined
})

// 线索列表数据
const leadsList = ref([])
const listStats = reactive({
  hasContactCount: 0,
  highValueCount: 0,
  topSources: [],
  topLocations: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => formatPaginationText(total, range, userPermissions.isVip)
})

// 表格选择
const selectedRowKeys = ref([])
const selectAll = ref(false)
const indeterminate = ref(false)

// 抽屉状态
const detailDrawerVisible = ref(false)
const editDrawerVisible = ref(false)
const selectedLead = ref(null)
const editingLead = ref(null)

// 图表引用
const sourceChart = ref(null)
const locationChart = ref(null)
const categoryChart = ref(null)

// ==================== 计算属性 ====================

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '达人信息',
    key: 'leadInfo',
    width: 300,
    fixed: 'left'
  },
  {
    title: '联系方式',
    key: 'contact',
    width: 200,
    ellipsis: true
  },


  {
    title: '更新时间',
    key: 'updateTime',
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
])

// 分页配置
const paginationConfig = computed(() => ({
  ...pagination,
  onChange: handlePageChange,
  onShowSizeChange: handlePageSizeChange
}))

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: handleSelectionChange,
  onSelectAll: handleSelectAllRows
}))

// 筛选选项
const sourceOptions = computed(() => [
  { label: '全网达人Excel', value: '全网达人(1).xlsx' },
  { label: '主播数据', value: '一万个主播.xlsx' },
  { label: '短视频达人', value: '短视频达人.xlsx' },
  { label: '其他来源', value: 'other' }
])



// ==================== 生命周期 ====================

onMounted(async () => {
  // 初始化用户权限状态
  await initUserPermissions()

  await Promise.all([
    loadOverviewStats(),
    loadLeadsList()
  ])

  // 初始化图表
  nextTick(() => {
    initCharts()
  })
})

// ==================== 数据加载方法 ====================

/**
 * 初始化用户权限状态
 */
async function initUserPermissions() {
  try {
    // 这里可以调用API获取用户权限信息
    // 暂时使用模拟数据，实际项目中应该从用户信息或权限接口获取
    const userInfo = {
      isVip: false, // 可以从localStorage、vuex或API获取
      memberLevel: 0
    }

    userPermissions.isVip = checkUserVipStatus(userInfo)
    userPermissions.memberLevel = userInfo.memberLevel || 0
    userPermissions.canViewFullStats = userPermissions.isVip

    console.log('用户权限初始化完成:', userPermissions)
  } catch (error) {
    console.error('初始化用户权限失败:', error)
    // 默认为非VIP用户
    userPermissions.isVip = false
    userPermissions.memberLevel = 0
    userPermissions.canViewFullStats = false
  }
}

/**
 * 加载概览统计数据
 */
async function loadOverviewStats() {
  try {
    loading.overview = true
    const response = await leadsService.getLeadsOverview({
      时间范围: '30d',
      包含详细分析: true
    })

    if (response.status === 100) {
      Object.assign(overviewStats, response.data)
      console.log('概览统计数据加载成功')
    }
  } catch (error) {
    console.error('加载概览统计失败:', error)
    message.error('加载概览统计失败')
  } finally {
    loading.overview = false
  }
}

/**
 * 加载线索列表数据
 * @param {boolean} isPagination - 是否为翻页操作
 */
async function loadLeadsList(isPagination = false) {
  try {
    loading.list = true
    const response = await leadsService.getLeadsList({
      页码: pagination.current,
      每页数量: pagination.pageSize,
      ...searchForm,
      // 添加翻页标识，用于后端记录翻页次数
      is_pagination: isPagination
    })

    if (response.status === 100) {
      leadsList.value = response.data.列表 || []
      pagination.total = response.data.总数 || 0
      Object.assign(listStats, response.data.listStats || {})

      // 更新是否有下一页的状态（用于智能分页）
      const currentTotal = pagination.current * pagination.pageSize
      hasNextPage.value = leadsList.value.length === pagination.pageSize && currentTotal < pagination.total

      // 更新翻页限制信息
      if (response.data.pagination_limit_info) {
        Object.assign(paginationLimitInfo, response.data.pagination_limit_info)
      }

      console.log('线索列表数据加载成功:', leadsList.value.length, '条')
    }
  } catch (error) {
    console.error('加载线索列表失败:', error)
    message.error('加载线索列表失败')
  } finally {
    loading.list = false
  }
}

// ==================== 搜索筛选方法 ====================

/**
 * 处理搜索
 */
async function handleSearch() {
  pagination.current = 1 // 重置到第一页
  await loadLeadsList()
}



/**
 * 重置搜索条件
 */
function resetSearch() {
  Object.assign(searchForm, {
    关键词: '',
    线索来源: undefined
  })
  handleSearch()
}

/**
 * 刷新概览数据
 */
async function refreshOverview() {
  await loadOverviewStats()
  nextTick(() => {
    initCharts()
  })
}

/**
 * 刷新列表数据
 */
async function refreshList() {
  await loadLeadsList()
}

/**
 * 获取联系方式类型颜色
 */
function getContactTypeColor(type) {
  const colorMap = {
    '微信': 'green',
    '电话': 'blue',
    '手机': 'blue',
    '邮箱': 'orange',
    'QQ': 'purple',
    '其他': 'default'
  }
  return colorMap[type] || 'cyan'
}

// ==================== 表格操作方法 ====================

/**
 * 处理表格变化（分页、排序、筛选）
 */
function handleTableChange(pag, filters, sorter) {
  console.log('表格变化:', { pag, filters, sorter })

  // 处理分页
  if (pag) {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
  }

  // 处理排序
  if (sorter && sorter.field) {
    searchForm.排序字段 = sorter.field
    searchForm.排序方式 = sorter.order === 'ascend' ? 'asc' : 'desc'
  }

  // 处理筛选 - 已移除高级筛选功能

  loadLeadsList()
}

/**
 * 处理页码变化
 */
function handlePageChange(page, pageSize) {
  pagination.current = page
  pagination.pageSize = pageSize
  loadLeadsList()
}

/**
 * 处理页面大小变化
 */
function handlePageSizeChange(current, size) {
  pagination.current = 1
  pagination.pageSize = size
  loadLeadsList()
}

/**
 * 处理智能分页页码变化
 * 每次翻页操作（包括上一页、下一页、点击指定页数）都算一次查询
 */
async function handleSmartPageChange(page, pageSize) {
  console.log('智能分页页码变化:', page, pageSize)

  try {
    // 先更新分页信息，确保UI响应
    const oldPage = pagination.current
    pagination.current = page
    pagination.pageSize = pageSize || pagination.pageSize

    // 检查翻页权限（每次翻页都检查，包括回到第1页）
    const isPageChange = page !== oldPage
    if (isPageChange) {
      try {
        // 异步检查翻页限制，不阻塞翻页操作
        const response = await leadsService.checkPaginationLimit()
        if (response && response.status === 100 && response.data) {
          Object.assign(paginationLimitInfo, response.data)

          // 如果不能翻页，显示警告但不阻止操作
          if (!paginationLimitInfo.can_paginate) {
            message.warning(paginationLimitInfo.message || '翻页次数已达上限')
          } else if (paginationLimitInfo.warning_level === 'severe') {
            message.warning(paginationLimitInfo.message || '翻页次数即将达到上限')
          } else if (paginationLimitInfo.warning_level === 'warning') {
            message.info(paginationLimitInfo.message || '建议使用搜索功能')
          }
        }
      } catch (limitError) {
        console.warn('翻页限制检查失败，继续执行翻页:', limitError)
        // 重置翻页限制信息为默认值（每日100页限制）
        Object.assign(paginationLimitInfo, {
          can_paginate: true,
          used_count: 0,
          limit_count: 100,  // 修改为每日100页限制
          remaining_count: 100,
          warning_level: 'none',
          message: ''
        })
      }
    }

    // 重新加载数据，标记为翻页操作
    await loadLeadsList(isPageChange)

  } catch (error) {
    console.error('处理分页变化失败:', error)
    message.error('翻页失败，请稍后重试')
    // 恢复原来的页码
    pagination.current = oldPage || 1
  }
}

/**
 * 处理智能分页页面大小变化
 */
async function handleSmartPageSizeChange(page, pageSize) {
  console.log('智能分页页面大小变化:', page, pageSize)

  // 更新分页信息
  pagination.current = page
  pagination.pageSize = pageSize

  // 重新加载数据
  await loadLeadsList()
}

// ==================== 选择操作方法 ====================

/**
 * 处理行选择变化
 */
function handleSelectionChange(selectedKeys) {
  selectedRowKeys.value = selectedKeys
  updateSelectAllState()
}

/**
 * 处理全选
 */
function handleSelectAll(e) {
  if (e.target.checked) {
    selectedRowKeys.value = leadsList.value.map(item => item.id)
  } else {
    selectedRowKeys.value = []
  }
  updateSelectAllState()
}

/**
 * 处理表格全选
 */
function handleSelectAllRows(selected, selectedRows, changeRows) {
  console.log('表格全选:', { selected, selectedRows, changeRows })
}

/**
 * 更新全选状态
 */
function updateSelectAllState() {
  const totalCount = leadsList.value.length
  const selectedCount = selectedRowKeys.value.length

  selectAll.value = selectedCount === totalCount && totalCount > 0
  indeterminate.value = selectedCount > 0 && selectedCount < totalCount
}

// ==================== 线索操作方法 ====================

/**
 * 查看线索详情
 */
function viewLeadDetail(lead) {
  selectedLead.value = lead
  detailDrawerVisible.value = true
}

/**
 * 编辑线索
 */
function editLead(lead) {
  editingLead.value = { ...lead }
  editDrawerVisible.value = true
}

/**
 * 处理线索操作
 */
async function handleLeadAction(action, lead) {
  switch (action) {
    case 'export':
      await exportSingleLead(lead)
      break
  }
}



/**
 * 导出单个线索
 */
async function exportSingleLead(lead) {
  try {
    await leadsService.exportLeads({
      导出格式: 'excel',
      筛选条件: { 线索ID列表: [lead.id] }
    })
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// ==================== 批量操作方法 ====================

/**
 * 处理批量操作
 */
async function handleBatchOperation({ key }) {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的线索')
    return
  }

  switch (key) {
    case 'export':
      await batchExport()
      break
  }
}

/**
 * 批量导出
 */
async function batchExport() {
  try {
    await leadsService.exportLeads({
      导出格式: 'excel',
      筛选条件: { 线索ID列表: selectedRowKeys.value }
    })
    message.success(`成功导出 ${selectedRowKeys.value.length} 条线索`)
  } catch (error) {
    console.error('批量导出失败:', error)
    message.error('批量导出失败')
  }
}



// ==================== 导出方法 ====================

/**
 * 处理导出
 */
async function handleExport({ key }) {
  try {
    let exportParams = {
      导出格式: 'excel',
      导出字段: ['名称', '地域', '类别', '联系方式', '更新时间']
    }

    switch (key) {
      case 'current':
        exportParams.筛选条件 = {
          页码: pagination.current,
          每页数量: pagination.pageSize,
          ...searchForm
        }
        break
      case 'all':
        exportParams.筛选条件 = {}
        break
      case 'filtered':
        exportParams.筛选条件 = { ...searchForm }
        break
    }

    await leadsService.exportLeads(exportParams)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// ==================== 抽屉操作方法 ====================

/**
 * 关闭详情抽屉
 */
function handleDetailDrawerClose() {
  detailDrawerVisible.value = false
  selectedLead.value = null
}

/**
 * 关闭编辑抽屉
 */
function handleEditDrawerClose() {
  editDrawerVisible.value = false
  editingLead.value = null
}

/**
 * 处理线索更新
 */
async function handleLeadUpdate(updatedLead) {
  try {
    await leadsService.updateLead(updatedLead)
    message.success('线索更新成功')
    await loadLeadsList()
    handleDetailDrawerClose()
  } catch (error) {
    console.error('更新线索失败:', error)
    message.error('更新线索失败')
  }
}

/**
 * 处理线索保存
 */
async function handleLeadSave(leadData) {
  try {
    await leadsService.updateLead(leadData)
    message.success('线索保存成功')
    await loadLeadsList()
    handleEditDrawerClose()
  } catch (error) {
    console.error('保存线索失败:', error)
    message.error('保存线索失败')
  }
}

// ==================== 工具方法 ====================

/**
 * 获取趋势文本
 */
function getTrendText(trend) {
  const trendMap = {
    increasing: '上升趋势',
    decreasing: '下降趋势',
    stable: '稳定'
  }
  return trendMap[trend] || '稳定'
}



/**
 * 格式化日期
 */
function formatDate(dateString) {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

/**
 * 获取相对时间
 */
function getTimeAgo(dateString) {
  return dayjs(dateString).fromNow()
}

// ==================== 图表初始化方法 ====================

/**
 * 初始化图表
 */
function initCharts() {
  initSourceChart()
  initLocationChart()
  initCategoryChart()
}

/**
 * 初始化来源分布图表
 */
function initSourceChart() {
  if (!sourceChart.value) return

  const chart = echarts.init(sourceChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '线索来源',
      type: 'pie',
      radius: ['40%', '70%'],
      data: overviewStats.sourceDistribution.map(item => ({
        name: item.name,
        value: item.value
      }))
    }]
  }
  chart.setOption(option)
}

/**
 * 初始化地域分布图表
 */
function initLocationChart() {
  if (!locationChart.value) return

  const chart = echarts.init(locationChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    xAxis: {
      type: 'category',
      data: overviewStats.locationDistribution.map(item => item.name)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '线索数量',
      type: 'bar',
      data: overviewStats.locationDistribution.map(item => item.value),
      itemStyle: {
        color: '#1890ff'
      }
    }]
  }
  chart.setOption(option)
}

/**
 * 初始化类别分布图表
 */
function initCategoryChart() {
  if (!categoryChart.value) return

  const chart = echarts.init(categoryChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    xAxis: {
      type: 'category',
      data: overviewStats.categoryDistribution.map(item => item.name)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '线索数量',
      type: 'bar',
      data: overviewStats.categoryDistribution.map(item => item.value),
      itemStyle: {
        color: '#52c41a'
      }
    }]
  }
  chart.setOption(option)
}
</script>

<style scoped>
.leads-center {
  padding: 0;
}

.leads-overview {
  margin-bottom: 16px;
}

.leads-overview .ant-card-head-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-trend {
  margin-top: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.growth-rate {
  color: #52c41a;
}

.percentage {
  color: #1890ff;
}

.chart-container {
  text-align: center;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
}

.chart-container h4 {
  margin-bottom: 12px;
  color: #262626;
  font-weight: 500;
}

.search-filter-section {
  margin-bottom: 16px;
}

.basic-search {
  margin-bottom: 16px;
}

.advanced-filter {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.filter-actions {
  margin-top: 16px;
  text-align: right;
}

.operation-toolbar {
  margin-bottom: 16px;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-info {
  color: #1890ff;
  font-weight: 500;
}

.leads-list {
  margin-bottom: 16px;
}

.list-summary {
  font-size: 14px;
  color: #8c8c8c;
  margin-left: 16px;
}

.lead-info {
  display: flex;
  flex-direction: column;
}

.lead-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.lead-link {
  font-weight: 500;
  color: #1890ff;
  text-decoration: none;
}

.lead-link:hover {
  color: #40a9ff;
}

.lead-meta {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #8c8c8c;
}

.location,
.category {
  display: flex;
  align-items: center;
  gap: 4px;
}

.contact-info {
  .contact-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .contact-text {
      font-family: 'Courier New', monospace;
      font-size: 13px;
      color: #666;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .no-contact {
    color: #999;
  }
}





.time-info {
  text-align: center;
}

.time-ago {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .basic-search .ant-col {
    margin-bottom: 8px;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 12px;
  }

  .left-actions,
  .right-actions {
    width: 100%;
    justify-content: center;
  }
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
  font-weight: 600;
}

/* 筛选表单样式 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

/* 操作按钮样式 */
.ant-btn-link {
  padding: 0;
  height: auto;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 抽屉样式 */
:deep(.ant-drawer-header) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-drawer-body) {
  padding: 24px;
}
</style>