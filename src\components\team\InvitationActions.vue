<template>
  <a-space size="small">
    <!-- 查看邀请链接 -->
    <a-tooltip v-if="hasInviteLink" title="查看邀请链接">
      <a-button
        type="link"
        size="small"
        @click="handleViewInviteLink"
      >
        <template #icon>
          <LinkOutlined />
        </template>
        链接
      </a-button>
    </a-tooltip>

    <!-- 重发邀请 -->
    <a-tooltip v-if="canResend" title="重新发送邀请">
      <a-button
        type="link"
        size="small"
        @click="handleResendInvitation"
        :loading="resendLoading"
      >
        <template #icon>
          <SendOutlined />
        </template>
        重发
      </a-button>
    </a-tooltip>

    <!-- 撤销邀请 -->
    <a-tooltip v-if="canRevoke" title="撤销邀请">
      <a-button
        type="link"
        danger
        size="small"
        @click="handleRevokeInvitation"
        :loading="revokeLoading"
      >
        <template #icon>
          <DeleteOutlined />
        </template>
        撤销
      </a-button>
    </a-tooltip>

    <!-- 查看详情 -->
    <a-tooltip title="查看邀请详情">
      <a-button
        type="link"
        size="small"
        @click="handleViewDetail"
      >
        <template #icon>
          <EyeOutlined />
        </template>
        详情
      </a-button>
    </a-tooltip>
  </a-space>
</template>

<script setup>
import { computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SendOutlined,
  DeleteOutlined,
  EyeOutlined,
  LinkOutlined
} from '@ant-design/icons-vue'
import teamService from '../../services/team'

defineOptions({
  name: 'InvitationActions'
})

const props = defineProps({
  // 邀请记录
  invitation: {
    type: Object,
    required: true
  },
  // 重发加载状态
  resendLoading: {
    type: Boolean,
    default: false
  },
  // 撤销加载状态
  revokeLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'view-link',
  'resend',
  'revoke',
  'view-detail',
  'action-success'
])

// 计算属性
const hasInviteLink = computed(() => {
  // 检查是否有邀请令牌，不管状态如何都可以查看历史邀请链接
  return props.invitation.邀请令牌 || (props.invitation.备注 && props.invitation.备注.includes('令牌:'))
})

const canResend = computed(() => {
  return props.invitation.状态 === '邀请待处理'
})

const canRevoke = computed(() => {
  return props.invitation.状态 === '邀请待处理'
})

/**
 * 处理查看邀请链接
 */
const handleViewInviteLink = () => {
  emit('view-link', props.invitation)
}

/**
 * 处理重发邀请
 */
const handleResendInvitation = () => {
  emit('resend', props.invitation)
}

/**
 * 处理撤销邀请
 */
const handleRevokeInvitation = () => {
  Modal.confirm({
    title: '确认撤销邀请',
    content: `确定要撤销对 ${props.invitation.被邀请人姓名 || props.invitation.手机号} 的邀请吗？`,
    okText: '确认撤销',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const response = await teamService.revokeInvitation({
          邀请id: props.invitation.id,
          撤销原因: '管理员撤销'
        })

        if ([100, 0, 1].includes(response.status)) {
          message.success('邀请已撤销')
          emit('action-success', {
            action: 'revoke',
            invitation: props.invitation
          })
        } else {
          throw new Error(response.message || '撤销邀请失败')
        }
      } catch (error) {
        console.error('撤销邀请失败:', error)
        message.error('撤销邀请失败: ' + error.message)
      }
    }
  })
}

/**
 * 查看邀请详情
 */
const handleViewDetail = () => {
  emit('view-detail', props.invitation)
}
</script>

<style scoped>
/* 操作按钮样式在父组件表格中控制 */
</style> 