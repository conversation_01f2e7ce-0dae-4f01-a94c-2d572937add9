<template>
  <div class="invitation-accept-container">
    <!-- 加载中状态 -->
    <div v-if="loading" class="loading-section">
      <a-spin size="large" />
      <p class="loading-text">正在验证邀请信息...</p>
    </div>

    <!-- 邀请有效 -->
    <div v-else-if="invitationData && invitationData.status === 100" class="invitation-content">
      <a-card class="invitation-card" :bordered="false">
        <template #cover>
          <div class="invitation-header">
            <div class="team-icon">
              <a-avatar :size="80" style="background-color: #1890ff;">
                <template #icon>
                  <TeamOutlined />
                </template>
              </a-avatar>
            </div>
            <h1 class="invitation-title">您已被邀请加入团队</h1>
            <p class="invitation-subtitle">{{ invitationData.data?.团队名称 || '团队' }}</p>
          </div>
        </template>

        <div class="invitation-details">
          <!-- 基本信息 -->
          <a-descriptions :column="1" size="large">
            <a-descriptions-item label="团队名称">
              <span class="team-name">{{ invitationData.data?.团队名称 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="邀请人">
              <div class="inviter-info">
                <a-avatar :size="32" :style="{ backgroundColor: getAvatarColor(invitationData.data?.邀请人姓名) }">
                  {{ getAvatarText(invitationData.data?.邀请人姓名) }}
                </a-avatar>
                <span class="inviter-name">{{ invitationData.data?.邀请人姓名 }}</span>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="您的角色">
              <a-tag color="blue" style="font-size: 14px; padding: 4px 12px;">
                {{ invitationData.data?.职位 || invitationData.data?.角色 || '成员' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="邀请时间">
              {{ formatDate(invitationData.data?.创建时间) }}
            </a-descriptions-item>
            <a-descriptions-item v-if="invitationData.data?.邀请消息" label="邀请消息">
              <div class="invitation-message">
                {{ invitationData.data.邀请消息 }}
              </div>
            </a-descriptions-item>
          </a-descriptions>

          <!-- 【新增功能】身份验证状态显示 -->
          <div v-if="userStore.isAuthenticated" class="identity-verification-section" style="margin: 16px 0;">
            <a-divider>身份验证</a-divider>
            <a-alert
              :type="getIdentityAlertType()"
              :message="getIdentityAlertTitle()"
              :description="invitationData.data?.验证提示消息"
              show-icon
              style="margin-bottom: 16px;"
            />
            
            <!-- 显示当前登录账号信息 -->
            <div class="current-user-info" style="background: #f5f5f5; padding: 12px; border-radius: 6px;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <a-avatar :size="24" style="background-color: #1890ff;">
                  {{ userStore.userInfo?.nickname?.charAt(0) || 'U' }}
                </a-avatar>
                <span>当前登录账号：{{ userStore.userInfo?.nickname || '未知' }}</span>
                <span v-if="userStore.userInfo?.phone" style="color: #666;">
                  ({{ userStore.userInfo.phone }})
                </span>
              </div>
            </div>
          </div>

          <!-- 优化的团队信息 -->
          <div class="team-info-section">
            <a-divider>团队信息</a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-statistic 
                  title="团队成员" 
                  :value="invitationData.data?.当前成员数 || invitationData.data?.团队成员数 || teamInfo.当前成员数 || teamInfo.成员数量 || 0" 
                  suffix="人"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic 
                  title="团队状态" 
                  value="活跃"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
            </a-row>
            
            <div v-if="invitationData.data?.团队描述 || teamInfo.团队描述" class="team-description" style="margin-top: 16px;">
              <h4>团队简介</h4>
              <p>{{ invitationData.data?.团队描述 || teamInfo.团队描述 }}</p>
            </div>
          </div>
        </div>

        <!-- 用户已登录的操作 -->
        <div v-if="userStore.isAuthenticated" class="action-section">
          <!-- 【优化】根据身份验证状态显示不同的操作界面 -->
          <div v-if="!invitationData.data?.是否匹配的被邀请人" style="margin-bottom: 16px;">
            <a-alert
              message="无法接受邀请"
              :description="invitationData.data?.验证提示消息 || '身份验证失败，您无法接受此邀请'"
              type="error"
              show-icon
              style="margin-bottom: 16px;"
            />
            <a-space size="large">
              <a-button @click="goToHome">返回首页</a-button>
              <a-button type="primary" @click="handleLogout">切换账号</a-button>
            </a-space>
          </div>
          
          <div v-else>
          <a-alert
            message="确认加入团队"
              description="身份验证通过，点击下方按钮确认加入该团队，您将获得相应的角色权限。"
              type="success"
            show-icon
            style="margin-bottom: 24px;"
          />
          <a-space size="large">
            <a-button size="large" @click="handleReject" :loading="actionLoading">
              拒绝邀请
            </a-button>
            <a-button type="primary" size="large" @click="handleAccept" :loading="actionLoading">
              接受邀请
            </a-button>
          </a-space>
          </div>
        </div>

        <!-- 用户未登录的操作 -->
        <div v-else class="action-section">
          <!-- 特殊提示：针对手机号邀请 -->
          <div v-if="invitationData.data?.被邀请手机号" style="margin-bottom: 16px;">
            <a-alert
              message="专属邀请"
              :description="`此邀请是专门发给手机号 ${invitationData.data.被邀请手机号} 的用户。请使用此手机号登录或注册账户来接受邀请。`"
              type="info"
              show-icon
              style="margin-bottom: 16px;"
            />
          </div>
          
          <a-alert
            message="需要登录或注册"
            description="请先登录现有账户或注册新账户，然后即可加入团队。"
            type="warning"
            show-icon
            style="margin-bottom: 24px;"
          />
          
          <div class="login-register-guide">
            <a-space direction="vertical" size="large" style="width: 100%;">
              <a-button 
                size="large" 
                block
                @click="goToLogin"
                style="height: 48px; display: flex; align-items: center; justify-content: center;"
              >
                <span style="font-size: 16px;">已有账户，立即登录</span>
            </a-button>
              <a-button 
                type="primary" 
                size="large" 
                block
                @click="goToRegister"
                style="height: 48px; display: flex; align-items: center; justify-content: center;"
              >
                <span style="font-size: 16px;">
                  {{ invitationData.data?.被邀请手机号 ? '使用此手机号注册' : '注册新账户' }}
                </span>
            </a-button>
          </a-space>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 邀请无效或错误 -->
    <div v-else class="error-section">
      <a-result
        :status="getErrorStatus()"
        :title="getErrorTitle()"
        :sub-title="getErrorDescription()"
      >
        <template #extra>
          <a-space>
            <a-button @click="goToHome">返回首页</a-button>
            <a-button type="primary" @click="goToLogin">登录</a-button>
          </a-space>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { TeamOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '../store/user'
import teamService from '../services/team'
import { formatDate } from '../utils/teamUtils'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

// 设置dayjs为中文
dayjs.locale('zh-cn')

defineOptions({
  name: 'InvitationAccept'
})

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const actionLoading = ref(false)
const invitationData = ref(null)
const errorInfo = ref(null)
const teamInfo = ref({})
// 【新增功能】身份验证相关计算属性
const getIdentityAlertType = () => {
  if (!invitationData.value?.data) return 'info'
  
  const status = invitationData.value.data.身份验证状态
  switch (status) {
    case 'ID匹配':
    case '手机号匹配':
    case '通用邀请':
      return 'success'
    case '手机号不匹配':
    case '用户id不匹配':
      return 'error'
    case '未登录':
    default:
      return 'warning'
  }
}

const getIdentityAlertTitle = () => {
  if (!invitationData.value?.data) return '身份验证'
  
  const status = invitationData.value.data.身份验证状态
  switch (status) {
    case 'ID匹配':
      return '身份验证通过 - 用户id匹配'
    case '手机号匹配':
      return '身份验证通过 - 手机号匹配'
    case '通用邀请':
      return '通用邀请链接'
    case '手机号不匹配':
      return '手机号不匹配'
    case '用户id不匹配':
      return '用户身份不匹配'
    case '未登录':
      return '请先登录'
    default:
      return '身份验证'
  }
}

// 获取邀请详情
const fetchInvitationDetails = async () => {
  try {
    loading.value = true
    const token = route.params.token || route.query.token
    
    if (!token) {
      throw new Error('邀请令牌缺失')
    }

    // 获取邀请详情
    const response = await teamService.getInvitationDetails(token)
    console.log('邀请详情响应:', response)
    
    if (response.status === 100 && response.data) {
      invitationData.value = response
      
      // 直接使用邀请详情中的团队信息，无需额外调用团队详情接口
      teamInfo.value = {
        团队id: response.data.团队id,
        团队名称: response.data.团队名称,
        团队描述: response.data.团队描述,
        公司名称: response.data.公司名称,
        邀请人姓名: response.data.邀请人姓名,
        创建时间: response.data.创建时间,
        角色: response.data.角色,
        状态: response.data.状态,
        当前成员数: response.data.当前成员数 || response.data.团队成员数
      }
      
      console.log('团队信息:', teamInfo.value)
      console.log('身份验证状态:', response.data.身份验证状态)
      console.log('是否匹配的被邀请人:', response.data.是否匹配的被邀请人)
    } else {
      throw new Error(response.message || '获取邀请详情失败')
    }
  } catch (error) {
    console.error('获取邀请详情失败:', error)
    
    const errorInfo = getErrorInfo(error.message)
    
    // 使用优雅的错误提示
    message.error(errorInfo.description)
  } finally {
    loading.value = false
  }
}

// 错误分类和处理
const getErrorInfo = (errorMessage) => {
  const errorText = errorMessage || ''
  
  // 身份验证失败
  if (errorText.includes('身份验证失败') || errorText.includes('此邀请是发给其他用户的')) {
    return {
      type: 'identity_mismatch',
      title: '身份验证失败',
      description: '此邀请不是发给您的账号，请使用正确的账号登录',
      icon: 'warning',
      actions: ['重新登录', '联系邀请人']
    }
  }
  
  // 手机号不匹配
  if (errorText.includes('手机号') && errorText.includes('身份验证失败')) {
    return {
      type: 'phone_mismatch', 
      title: '手机号不匹配',
      description: errorText,
      icon: 'warning',
      actions: ['使用正确账号', '联系邀请人']
    }
  }
  
  // 邀请已处理
  if (errorText.includes('已被处理') || errorText.includes('无法重复操作')) {
    return {
      type: 'already_processed',
      title: '邀请已处理',
      description: '此邀请已经被处理过了',
      icon: 'info',
      actions: ['返回首页']
  }
}

  // 邀请过期
  if (errorText.includes('已过期')) {
    return {
      type: 'expired',
      title: '邀请已过期',
      description: '此邀请链接已超过有效期',
      icon: 'warning',
      actions: ['联系邀请人重发']
    }
  }
  
  // 用户已在团队
  if (errorText.includes('已是该团队成员') || errorText.includes('已是团队成员')) {
    return {
      type: 'already_member',
      title: '您已是团队成员',
      description: '无需重复加入团队',
      icon: 'success',
      actions: ['查看我的团队']
    }
  }
  
  // 邀请不存在
  if (errorText.includes('不存在')) {
    return {
      type: 'not_found',
      title: '邀请不存在',
      description: '邀请链接无效或已被删除',
      icon: 'error',
      actions: ['联系邀请人重发']
    }
  }
  
  // 默认错误
  return {
    type: 'unknown',
    title: '操作失败',
    description: errorText || '处理邀请时发生未知错误',
    icon: 'error',
    actions: ['重试', '联系客服']
  }
}

// 处理错误操作
const handleErrorAction = (action, errorInfo) => {
  switch (action) {
    case '重新登录':
      // 智能跳转：如果有手机号信息，传递给登录页面
      const loginQuery = { redirect: route.fullPath }
      if (invitationData.value?.data?.被邀请手机号) {
        loginQuery.phone = invitationData.value.data.被邀请手机号
      }
      router.push({ name: 'Login', query: loginQuery })
      break
    case '联系邀请人':
    case '联系邀请人重发':
      message.info('请联系邀请人重新发送邀请链接')
      break
    case '使用正确账号':
      // 引导用户注销并重新登录
      message.info('请退出当前账号，使用正确的手机号登录')
      break
    case '返回首页':
      router.push('/dashboard')
      break
    case '查看我的团队':
      router.push('/team/overview')
      break
    case '重试':
      // 可以重新获取邀请详情或重试操作
      fetchInvitationDetails()
      break
    case '联系客服':
      message.info('如有疑问请联系客服')
      break
  }
}

// 接受邀请
const handleAccept = async () => {
  try {
    // 【新增功能】身份验证检查
    if (!invitationData.value?.data?.是否匹配的被邀请人) {
      message.error('身份验证失败，您无法接受此邀请')
      return
    }
    
    actionLoading.value = true
    const token = route.params.token || route.query.token
    
    const response = await teamService.processInvitation({
      邀请令牌: token,
      操作: 'accept'
    })
    
    console.log('接受邀请响应:', response)
    
    // 【修正状态码处理】注意这里的status是业务状态码，不是HTTP状态码
    if ([100, 0, 1].includes(response.status)) {
      message.success('成功加入团队！')
      // 跳转到团队页面或仪表板
      setTimeout(() => {
        router.push('/team')
      }, 1500)
    } else {
      throw new Error(response.message || '接受邀请失败')
    }
  } catch (error) {
    console.error('接受邀请失败:', error)
    
    const errorInfo = getErrorInfo(error.message)
    
    // 使用优雅的错误提示
    message.error(errorInfo.description)
  } finally {
    actionLoading.value = false
  }
}

// 拒绝邀请
const handleReject = async () => {
  try {
    // 【新增功能】身份验证检查
    if (!invitationData.value?.data?.是否匹配的被邀请人) {
      message.error('身份验证失败，您无法拒绝此邀请')
      return
    }
    
    actionLoading.value = true
    const token = route.params.token || route.query.token
    
    const response = await teamService.processInvitation({
      邀请令牌: token,
      操作: 'reject'
    })
    
    console.log('拒绝邀请响应:', response)
    
    // 【修正状态码处理】注意这里的status是业务状态码，不是HTTP状态码
    if ([100, 0, 1].includes(response.status)) {
      message.info('已拒绝加入团队')
      // 跳转到首页
      setTimeout(() => {
        router.push('/dashboard')
      }, 1500)
    } else {
      throw new Error(response.message || '拒绝邀请失败')
    }
  } catch (error) {
    console.error('拒绝邀请失败:', error)
    
    const errorInfo = getErrorInfo(error.message)
    
    // 使用优雅的错误提示
    message.error(errorInfo.description)
  } finally {
    actionLoading.value = false
  }
}

// 【新增功能】切换账号（注销当前账号）
const handleLogout = async () => {
  try {
    await userStore.logout()
    message.success('已退出当前账号，请使用正确的账号登录')
    // 跳转到登录页面，并保存当前邀请链接
    const loginQuery = { redirect: route.fullPath }
    // 如果有被邀请手机号，传递给登录页面作为提示
    if (invitationData.value?.data?.被邀请手机号) {
      loginQuery.phone = invitationData.value.data.被邀请手机号
    }
    router.push({ name: 'Login', query: loginQuery })
  } catch (error) {
    message.error('退出账号失败')
  }
}

// 跳转到登录页面
const goToLogin = () => {
  const query = { redirect: route.fullPath }
  
  // 如果是手机号邀请，预填手机号
  if (invitationData.value?.data?.被邀请手机号) {
    query.phone = invitationData.value.data.被邀请手机号
  }
  
  router.push({
    name: 'Login',
    query
  })
}

// 跳转到注册页面
const goToRegister = () => {
  const query = { redirect: route.fullPath }
  
  // 如果是手机号邀请，预填手机号
  if (invitationData.value?.data?.被邀请手机号) {
    query.phone = invitationData.value.data.被邀请手机号
    query.invitation = 'phone' // 标识这是手机号邀请注册
  }
  
  router.push({
    name: 'Register',
    query
  })
}

// 跳转到首页
const goToHome = () => {
  router.push('/dashboard')
}

// 获取错误状态
const getErrorStatus = () => {
  if (!errorInfo.value) return '404'
  
  switch (errorInfo.value.type) {
    case 'expired':
      return 'warning'
    case 'invalid':
      return '404'
    case 'used':
      return 'info'
    default:
      return 'error'
  }
}

// 获取错误标题
const getErrorTitle = () => {
  if (!errorInfo.value) return '邀请链接无效'
  
  switch (errorInfo.value.type) {
    case 'expired':
      return '邀请已过期'
    case 'invalid':
      return '邀请链接无效'
    case 'used':
      return '邀请已使用'
    default:
      return '访问出错'
  }
}

// 获取错误描述
const getErrorDescription = () => {
  if (!errorInfo.value) return '请检查链接是否正确或联系邀请人重新发送'
  
  return errorInfo.value.message || '请联系邀请人重新发送邀请链接'
}

// formatDate 函数已从 teamUtils 导入，删除重复实现

// 格式化团队创建时间（相对时间）
const formatTeamCreateDate = (dateString) => {
  if (!dateString) return '--'
  try {
    return dayjs(dateString).fromNow()
  } catch (error) {
    return dateString
  }
}

// 生成头像颜色
const getAvatarColor = (name) => {
  if (!name) return '#1890ff'
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae']
  const hash = name.charCodeAt(0) + name.charCodeAt(name.length - 1)
  return colors[hash % colors.length]
}

// 生成头像文字
const getAvatarText = (name) => {
  if (!name) return '用'
  return name.length > 1 ? name.slice(-2) : name
}

// 组件挂载时获取邀请详情
onMounted(() => {
  fetchInvitationDetails()
})
</script>

<style scoped>
.invitation-accept-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 40px 20px 20px;
}

.loading-section {
  text-align: center;
  color: white;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.invitation-content {
  width: 100%;
  max-width: 480px;
}

.invitation-card {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.invitation-header {
  text-align: center;
  padding: 30px 20px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.team-icon {
  margin-bottom: 20px;
}

.invitation-title {
  font-size: 28px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.invitation-subtitle {
  font-size: 18px;
  color: #595959;
  margin: 0;
}

.invitation-details {
  padding: 20px;
}

.team-name {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.inviter-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.inviter-name {
  font-size: 16px;
  font-weight: 500;
}

.invitation-message {
  background-color: #f6f8fa;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
  font-style: italic;
  color: #595959;
}

.action-section {
  padding: 0 20px 20px;
  text-align: center;
}

.login-register-guide {
  text-align: left;
}

.login-register-guide h4 {
  font-weight: 600;
  text-align: center;
}

.error-section {
  width: 100%;
  max-width: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px 20px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #262626;
}

:deep(.ant-descriptions-item-content) {
  color: #595959;
}

:deep(.ant-card-body) {
  padding: 0;
}

.team-info-section {
  margin-top: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.team-description {
  margin-top: 16px;
}

.team-description h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-weight: 600;
}

.team-description p {
  margin: 0;
  color: #595959;
  line-height: 1.6;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
  margin-bottom: 4px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .invitation-content {
    max-width: 100%;
  }
  
  .invitation-header {
    padding: 30px 16px 16px;
  }
  
  .invitation-title {
    font-size: 24px;
  }
  
  .invitation-subtitle {
    font-size: 16px;
  }
  
  .invitation-details {
    padding: 16px;
  }
  
  .team-info-section {
    padding: 12px;
  }
  
  :deep(.ant-col) {
    margin-bottom: 16px;
  }
}
</style> 