<template>
  <div class="sample-management-module">
    <!-- 统计概览卡片 -->
    <div class="stats-row">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="样品总数"
              :value="样品统计.总数"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <experiment-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="待审核"
              :value="样品统计.待审核"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="已发货"
              :value="样品统计.已发货"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <car-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card">
            <a-statistic
              title="已送达"
              :value="样品统计.已送达"
              :value-style="{ color: '#13c2c2' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 操作栏 -->
    <a-card class="action-card" :bordered="false">
      <div class="action-bar">
        <div class="search-filters">
          <a-input-search
            v-model:value="搜索关键词"
            placeholder="搜索收件人姓名"
            style="width: 300px"
            @search="执行搜索"
            allow-clear
          />
          <a-select
            v-model:value="筛选状态"
            placeholder="审核状态"
            style="width: 120px"
            @change="执行搜索"
            allow-clear
          >
            <a-select-option :value="null">全部状态</a-select-option>
            <a-select-option :value="0">待审核</a-select-option>
            <a-select-option :value="1">已审核</a-select-option>
            <a-select-option :value="2">审核拒绝</a-select-option>
            <a-select-option value="已拒绝">已拒绝</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="日期范围"
            :placeholder="['开始日期', '结束日期']"
            @change="执行搜索"
          />
        </div>

        <!-- 快递查询配额信息 -->
        <div class="quota-info" v-if="!快递查询配额.是否管理员">
          <a-space>
            <a-tag
              :color="快递查询配额.配额状态 === '充足' ? 'green' :
                     快递查询配额.配额状态 === '紧张' ? 'orange' : 'red'"
            >
              <template #icon>
                <search-outlined />
              </template>
              快递查询: {{ 快递查询配额.剩余次数 }}/{{ 快递查询配额.每日限额 }}
            </a-tag>
            <a-tooltip
              title="每日快递查询次数限制，管理员无限制。查询失败不计入次数。"
              placement="top"
            >
              <question-circle-outlined style="color: #999;" />
            </a-tooltip>
          </a-space>
        </div>

        <div class="action-buttons">
          <a-button @click="刷新样品列表" :loading="加载中">
            <template #icon>
              <reload-outlined />
            </template>
            刷新
          </a-button>
          <a-button type="primary" @click="显示样品申请弹窗">
            <template #icon>
              <plus-outlined />
            </template>
            新增样品申请
          </a-button>
          <a-button @click="导出样品数据">
            <template #icon>
              <download-outlined />
            </template>
            导出数据
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- 样品列表 -->
    <a-card class="list-card" :bordered="false">
      <a-table
        :columns="表格列配置"
        :data-source="样品列表"
        :loading="加载中"
        :pagination="分页配置"
        row-key="id"
        size="middle"
        @change="处理表格变化"
      >
        <!-- 表格内容自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 产品名称列 -->
          <template v-if="column.key === '产品名称'">
            <span v-if="record.产品名称">{{ record.产品名称 }}</span>
            <span v-else style="color: #999;">未关联产品</span>
          </template>

          <!-- 地址列 -->
          <template v-else-if="column.key === '地址'">
            <a-tooltip :title="record.地址">
              <span>{{ record.地址 || '-' }}</span>
            </a-tooltip>
          </template>

          <!-- 规格列 -->
          <template v-else-if="column.key === '规格'">
            <span v-if="record.规格">{{ record.规格 }}</span>
            <span v-else style="color: #999;">-</span>
          </template>

          <!-- 审核状态列 -->
          <template v-else-if="column.key === '审核状态'">
            <a-tag :color="获取审核状态颜色(record.审核状态)">
              {{ sampleService.获取审核状态文本(record.审核状态) }}
            </a-tag>
          </template>

          <!-- 快递状态列 -->
          <template v-else-if="column.key === '快递状态'">
            <a-tag :color="获取快递状态颜色(record.快递状态)">
              {{ sampleService.获取快递状态文本(record.快递状态) }}
            </a-tag>
          </template>

          <!-- 快递单号列 -->
          <template v-else-if="column.key === '快递单号'">
            <span v-if="record.快递单号">{{ record.快递单号 }}</span>
            <span v-else style="color: #999;">-</span>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === '创建时间'">
            <span>{{ 格式化时间(record.创建时间) }}</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === '操作'">
            <a-space>
              <a-button size="small" @click="查看样品详情(record)">
                <eye-outlined />
                详情
              </a-button>
              <a-button
                v-if="record.审核状态 === 0"
                size="small"
                type="primary"
                @click="审核样品(record)"
              >
                <check-circle-outlined />
                审核
              </a-button>
              <a-button
                v-if="record.审核状态 === 1 && (record.快递状态 === 0 || record.快递状态 === null)"
                size="small"
                @click="发货样品(record)"
              >
                <car-outlined />
                发货
              </a-button>
              <a-button
                v-if="record.快递单号"
                size="small"
                @click="更新物流(record)"
              >
                <search-outlined />
                物流
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 样品申请弹窗 -->
    <a-modal
      v-model:open="申请弹窗可见"
      title="样品申请"
      width="600px"
      @ok="提交样品申请"
      @cancel="关闭申请弹窗"
    >
      <a-form
        ref="申请表单引用"
        :model="申请表单数据"
        :rules="申请表单规则"
        layout="vertical"
      >
        <a-form-item label="样品名称" name="样品名称">
          <a-input v-model:value="申请表单数据.样品名称" placeholder="请输入样品名称" />
        </a-form-item>
        <a-form-item label="申请数量" name="申请数量">
          <a-input-number v-model:value="申请表单数据.申请数量" :min="1" :max="10" />
        </a-form-item>
        <a-form-item label="申请原因" name="申请原因">
          <a-textarea v-model:value="申请表单数据.申请原因" :rows="4" placeholder="请说明申请样品的原因和用途" />
        </a-form-item>
        <a-form-item label="收货地址" name="收货地址">
          <a-textarea v-model:value="申请表单数据.收货地址" :rows="3" placeholder="请输入详细的收货地址" />
        </a-form-item>
        <a-form-item label="联系方式" name="联系方式">
          <a-input v-model:value="申请表单数据.联系方式" placeholder="请输入联系电话" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 样品详情弹窗 -->
    <a-modal
      v-model:open="详情弹窗可见"
      title="样品详情"
      width="800px"
      :footer="null"
    >
      <div v-if="当前样品详情" class="sample-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="产品名称">
            <span v-if="当前样品详情.产品名称">{{ 当前样品详情.产品名称 }}</span>
            <span v-else style="color: #999;">未关联产品</span>
          </a-descriptions-item>
          <a-descriptions-item label="样品数量">{{ 当前样品详情.数量 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="收件人">{{ 当前样品详情.收件人 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ 当前样品详情.电话 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="收货地址" :span="2">{{ 当前样品详情.地址 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="规格">{{ 当前样品详情.规格 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="寄样备注">{{ 当前样品详情.寄样备注 || '-' }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ 格式化时间(当前样品详情.创建时间) }}</a-descriptions-item>
          <a-descriptions-item label="审核状态">
            <a-tag :color="获取审核状态颜色(当前样品详情.审核状态)">
              {{ sampleService.获取审核状态文本(当前样品详情.审核状态) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="快递状态">
            <a-tag :color="获取快递状态颜色(当前样品详情.快递状态)">
              {{ sampleService.获取快递状态文本(当前样品详情.快递状态) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="快递单号" v-if="当前样品详情.快递单号">{{ 当前样品详情.快递单号 }}</a-descriptions-item>
          <a-descriptions-item label="快递公司" v-if="当前样品详情.快递公司">{{ 当前样品详情.快递公司 }}</a-descriptions-item>
          <a-descriptions-item label="审核备注" :span="2" v-if="当前样品详情.审核备注">{{ 当前样品详情.审核备注 }}</a-descriptions-item>
          <a-descriptions-item label="审核时间" v-if="当前样品详情.审核时间">{{ 格式化时间(当前样品详情.审核时间) }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ 格式化时间(当前样品详情.更新时间) }}</a-descriptions-item>
        </a-descriptions>
        
        <!-- 审核记录 -->
        <div v-if="当前样品详情.审核记录" class="audit-history">
          <h4>审核记录</h4>
          <a-timeline>
            <a-timeline-item
              v-for="记录 in 当前样品详情.审核记录"
              :key="记录.时间"
              :color="记录.状态 === '通过' ? 'green' : '红色'"
            >
              <p>{{ 记录.操作 }} - {{ 记录.操作人 }}</p>
              <p>时间：{{ 格式化日期(记录.时间) }}</p>
              <p v-if="记录.备注">备注：{{ 记录.备注 }}</p>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-modal>

    <!-- 审核弹窗 -->
    <a-modal
      v-model:open="审核弹窗可见"
      title="样品审核"
      width="500px"
      @ok="提交审核结果"
      @cancel="关闭审核弹窗"
    >
      <a-form
        ref="审核表单引用"
        :model="审核表单数据"
        :rules="审核表单规则"
        layout="vertical"
      >
        <a-form-item label="审核结果" name="审核结果">
          <a-radio-group v-model:value="审核表单数据.审核结果">
            <a-radio value="通过">通过</a-radio>
            <a-radio value="拒绝">拒绝</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审核备注" name="审核备注">
          <a-textarea v-model:value="审核表单数据.审核备注" :rows="4" placeholder="请输入审核意见" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  ExperimentOutlined,
  ClockCircleOutlined,
  CarOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  DownloadOutlined,
  EyeOutlined,
  SearchOutlined,
  QuestionCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'

// 导入API服务
import sampleService from '@/services/sampleService'

// 组件名称定义
defineOptions({
  name: 'SampleManagementModule'
})

// 响应式数据定义
const 加载中 = ref(false)
const 搜索关键词 = ref('')
const 筛选状态 = ref(null)
const 日期范围 = ref([])

// 弹窗状态
const 申请弹窗可见 = ref(false)
const 详情弹窗可见 = ref(false)
const 审核弹窗可见 = ref(false)

// 当前操作的样品
const 当前样品详情 = ref(null)
const 当前审核样品 = ref(null)

// 样品统计数据
const 样品统计 = reactive({
  总数: 0,
  待审核: 0,
  已发货: 0,
  已送达: 0
})

// 快递查询配额信息
const 快递查询配额 = reactive({
  每日限额: 30,
  今日已用: 0,
  剩余次数: 30,
  是否可查询: true,
  是否管理员: false,
  配额状态: '充足'
})

// 样品列表数据
const 样品列表 = ref([])

// 申请表单数据
const 申请表单数据 = reactive({
  样品名称: '',
  产品id: null,
  申请人: '',
  申请数量: 1,
  申请原因: '',
  收货地址: '',
  联系方式: ''
})

// 审核表单数据
const 审核表单数据 = reactive({
  审核结果: '',
  审核备注: ''
})

// 表单引用
const 申请表单引用 = ref()
const 审核表单引用 = ref()

// 表单验证规则
const 申请表单规则 = {
  样品名称: [{ required: true, message: '请输入样品名称' }],
  申请数量: [{ required: true, message: '请输入申请数量' }],
  申请原因: [{ required: true, message: '请输入申请原因' }],
  收货地址: [{ required: true, message: '请输入收货地址' }],
  联系方式: [{ required: true, message: '请输入联系方式' }]
}

const 审核表单规则 = {
  审核结果: [{ required: true, message: '请选择审核结果' }],
  审核备注: [{ required: true, message: '请输入审核备注' }]
}

// 表格列配置 - 使用数据库实际字段名
const 表格列配置 = [
  {
    title: '产品名称',
    dataIndex: '产品名称',
    key: '产品名称',
    width: 200
  },
  {
    title: '收件人',
    dataIndex: '收件人',
    key: '收件人',
    width: 120
  },
  {
    title: '地址',
    dataIndex: '地址',
    key: '地址',
    width: 200,
    ellipsis: true
  },
  {
    title: '数量',
    dataIndex: '数量',
    key: '数量',
    width: 80,
    align: 'center'
  },
  {
    title: '规格',
    dataIndex: '规格',
    key: '规格',
    width: 100
  },
  {
    title: '审核状态',
    dataIndex: '审核状态',
    key: '审核状态',
    width: 100,
    align: 'center'
  },
  {
    title: '快递状态',
    dataIndex: '快递状态',
    key: '快递状态',
    width: 100,
    align: 'center'
  },
  {
    title: '快递单号',
    dataIndex: '快递单号',
    key: '快递单号',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: '创建时间',
    key: '创建时间',
    width: 150
  },
  {
    title: '操作',
    key: '操作',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 10,
  total: 156,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

/**
 * 获取审核状态对应的颜色
 * @param {number} 状态值 - 审核状态值
 * @returns {string} 颜色值
 */
const 获取审核状态颜色 = (状态值) => {
  return sampleService.获取审核状态颜色(状态值)
}

/**
 * 获取快递状态对应的颜色
 * @param {number} 状态值 - 快递状态值
 * @returns {string} 颜色值
 */
const 获取快递状态颜色 = (状态值) => {
  return sampleService.获取快递状态颜色(状态值)
}

/**
 * 格式化时间显示
 * @param {string} 时间字符串 - 时间字符串
 * @returns {string} 格式化后的时间
 */
const 格式化时间 = (时间字符串) => {
  if (!时间字符串) return '-'

  try {
    const date = new Date(时间字符串)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return 时间字符串
  }
}

/**
 * 获取状态对应的颜色（兼容旧版本）
 * @param {string} 状态 - 样品状态
 * @returns {string} 颜色值
 */
const 获取状态颜色 = (状态) => {
  const 颜色映射 = {
    '待审核': 'orange',
    '已审核': 'blue',
    '已发货': 'green',
    '已送达': 'cyan',
    '已拒绝': 'red'
  }
  return 颜色映射[状态] || 'default'
}

/**
 * 格式化日期显示
 * @param {string} 日期字符串 - 原始日期字符串
 * @returns {string} 格式化后的日期
 */
const 格式化日期 = (日期字符串) => {
  if (!日期字符串) return '-'
  const 日期 = new Date(日期字符串)
  return `${日期.getFullYear()}-${(日期.getMonth() + 1).toString().padStart(2, '0')}-${日期.getDate().toString().padStart(2, '0')} ${日期.getHours().toString().padStart(2, '0')}:${日期.getMinutes().toString().padStart(2, '0')}`
}

/**
 * 加载样品列表
 */
const 加载样品列表 = async () => {
  try {
    加载中.value = true

    // 调用现有的样品列表API
    const response = await sampleService.获取样品列表({
      页码: 分页配置.current || 1,
      每页数量: 分页配置.pageSize || 10,
      收件人: 搜索关键词.value || undefined,  // 使用收件人字段进行搜索
      审核状态: 筛选状态.value !== null ? 筛选状态.value : undefined,
      快递状态: undefined // 可以后续添加快递状态筛选
    })

    if (response.status === 100) {
      const data = response.data || {}
      样品列表.value = data.列表 || []  // 使用现有API的字段名

      // 更新分页信息
      if (data.分页) {
        分页配置.total = data.分页.总记录数
        分页配置.current = data.分页.当前页码
        分页配置.pageSize = data.分页.每页数量
      }

      console.log('✅ 样品列表加载成功:', {
        样品数量: 样品列表.value.length,
        总数: data.分页?.总记录数
      })
    } else {
      console.warn('⚠️ 样品列表加载失败:', response.message)
      message.error(response.message || '加载样品列表失败')
      样品列表.value = []
    }

  } catch (error) {
    console.error('❌ 加载样品列表失败:', error)
    message.error('加载样品列表失败，请稍后重试')
    样品列表.value = []
  } finally {
    加载中.value = false
  }
}

/**
 * 刷新样品列表
 */
const 刷新样品列表 = async () => {
  try {
    message.loading('正在刷新样品列表...', 0.5)
    await 加载样品列表()
    message.success('样品列表刷新成功')
  } catch (error) {
    console.error('刷新样品列表失败:', error)
    message.error('刷新失败，请稍后重试')
  }
}

/**
 * 加载样品统计数据
 */
const 加载样品统计 = async () => {
  try {
    const response = await sampleService.获取样品统计()

    if (response.status === 100) {
      const stats = response.data || {}
      Object.assign(样品统计, {
        总数: stats.总数 || 0,        // 使用现有API的字段名
        待审核: stats.待审核 || 0,
        已发货: stats.已发货 || 0,
        已送达: stats.已送达 || 0
      })

      console.log('✅ 样品统计数据加载成功:', stats)
    }
  } catch (error) {
    console.error('❌ 加载样品统计失败:', error)
  }
}

/**
 * 执行搜索功能
 * 根据搜索关键词、状态筛选、日期范围等条件过滤样品列表
 */
const 执行搜索 = () => {
  加载样品列表()
}

/**
 * 显示样品申请弹窗
 */
const 显示样品申请弹窗 = () => {
  申请弹窗可见.value = true
  // 重置表单数据
  Object.assign(申请表单数据, {
    样品名称: '',
    产品id: null,
    申请人: '',
    申请数量: 1,
    申请原因: '',
    收货地址: '',
    联系方式: ''
  })
}

/**
 * 关闭申请弹窗
 */
const 关闭申请弹窗 = () => {
  申请弹窗可见.value = false
  申请表单引用.value?.resetFields()
}

/**
 * 提交样品申请
 */
const 提交样品申请 = async () => {
  try {
    await 申请表单引用.value.validate()

    console.log('提交样品申请:', 申请表单数据)

    // 调用样品申请API
    const response = await sampleService.addSample({
      收件人: 申请表单数据.申请人,
      地址: 申请表单数据.收货地址,
      电话: 申请表单数据.联系方式,
      产品id: 申请表单数据.产品id,
      数量: 申请表单数据.申请数量,
      寄样备注: 申请表单数据.申请原因
    })

    if (response.status === 100) {
      message.success('样品申请提交成功')
      关闭申请弹窗()
      加载样品列表() // 刷新列表
      加载样品统计() // 刷新统计
    } else {
      message.error(response.message || '申请提交失败')
    }

  } catch (error) {
    console.error('❌ 申请提交失败:', error)
    message.error('申请提交失败，请检查输入信息')
  }
}

/**
 * 查看样品详情
 * @param {Object} 样品记录 - 样品数据记录
 */
const 查看样品详情 = async (样品记录) => {
  try {
    console.log('🔍 查看样品详情:', 样品记录)

    // 获取样品详细信息
    const response = await sampleService.获取样品详情(样品记录.id)

    if (response.status === 100) {
      当前样品详情.value = response.data || 样品记录
      console.log('✅ 获取样品详情成功:', response.data)
    } else {
      // 如果API失败，使用基础信息
      当前样品详情.value = { ...样品记录 }
      console.warn('⚠️ 获取样品详情失败，使用基础信息:', response.message)
    }

    详情弹窗可见.value = true
  } catch (error) {
    console.error('❌ 获取样品详情失败:', error)
    // 如果API失败，使用基础信息
    当前样品详情.value = { ...样品记录 }
    详情弹窗可见.value = true
    message.warning('获取详细信息失败，显示基础信息')
  }
}

/**
 * 审核样品
 * @param {Object} 样品记录 - 样品数据记录
 */
const 审核样品 = (样品记录) => {
  当前审核样品.value = { ...样品记录 }
  审核弹窗可见.value = true
  // 重置审核表单
  Object.assign(审核表单数据, {
    审核结果: '',
    审核备注: ''
  })
}

/**
 * 关闭审核弹窗
 */
const 关闭审核弹窗 = () => {
  审核弹窗可见.value = false
  审核表单引用.value?.resetFields()
}

/**
 * 提交审核结果
 */
const 提交审核结果 = async () => {
  try {
    await 审核表单引用.value.validate()

    console.log('提交审核结果:', {
      样品id: 当前审核样品.value.样品id,
      ...审核表单数据
    })

    // 调用样品审核API
    const response = await sampleService.auditSample({
      样品id: 当前审核样品.value.样品id || 当前审核样品.value.id,
      审核状态: 审核表单数据.审核结果 === '通过' ? 1 : 2,
      审核备注: 审核表单数据.审核备注
    })

    if (response.status === 100) {
      message.success('审核结果提交成功')
      关闭审核弹窗()
      加载样品列表() // 刷新列表
      加载样品统计() // 刷新统计
    } else {
      message.error(response.message || '审核提交失败')
    }

  } catch (error) {
    console.error('❌ 审核提交失败:', error)
    message.error('审核提交失败，请检查输入信息')
  }
}

/**
 * 发货样品
 * @param {Object} 样品记录 - 样品数据记录
 */
const 发货样品 = async (样品记录) => {
  try {
    // 这里可以打开发货弹窗或直接处理
    const response = await sampleService.updateExpress({
      样品id: 样品记录.样品id || 样品记录.id,
      快递状态: 1, // 已发货
      快递状态变更时间: new Date().toISOString()
    })

    if (response.status === 100) {
      message.success('发货成功')
      加载样品列表() // 刷新列表
      加载样品统计() // 刷新统计
    } else {
      message.error(response.message || '发货失败')
    }
  } catch (error) {
    console.error('❌ 发货失败:', error)
    message.error('发货失败，请稍后重试')
  }
}

/**
 * 加载快递查询配额信息
 */
const 加载快递查询配额 = async () => {
  try {
    const response = await sampleService.getExpressQueryQuota()

    if (response && response.status === 100) {
      Object.assign(快递查询配额, response.data)
      console.log('✅ 快递查询配额信息加载成功:', response.data)
    } else {
      console.warn('⚠️ 获取快递查询配额信息失败:', response)
    }
  } catch (error) {
    console.error('❌ 加载快递查询配额信息失败:', error)
    // 不显示错误提示，避免影响用户体验
  }
}

/**
 * 更新物流信息
 * @param {Object} 样品记录 - 样品数据记录
 */
const 更新物流 = async (样品记录) => {
  try {
    if (!样品记录.快递单号) {
      message.error('该样品暂无快递单号')
      return
    }

    // 检查快递查询次数限制
    if (!快递查询配额.是否可查询) {
      message.error(`快递查询次数已达上限（${快递查询配额.每日限额}次），请明日再试`)
      return
    }

    // 查询物流信息
    const response = await sampleService.queryLogistics(样品记录.快递单号)

    if (response.status === 100) {
      message.success('物流信息更新成功')
      加载样品列表() // 刷新列表
      加载快递查询配额() // 刷新配额信息
    } else {
      // 检查是否是次数限制错误
      if (response.status === 429 || response.message?.includes('查询次数')) {
        message.error(response.message || '快递查询次数已达上限')
        加载快递查询配额() // 刷新配额信息
      } else {
        message.error(response.message || '物流信息更新失败')
      }
    }
  } catch (error) {
    console.error('❌ 物流信息更新失败:', error)

    // 检查是否是次数限制错误
    if (error.response?.data?.message?.includes('查询次数')) {
      message.error(error.response.data.message)
      加载快递查询配额() // 刷新配额信息
    } else {
      message.error('物流信息更新失败，请稍后重试')
    }
  }
}

/**
 * 导出样品数据
 */
const 导出样品数据 = async () => {
  try {
    const response = await sampleService.exportSamples({
      筛选条件: {
        收件人: 搜索关键词.value,
        审核状态: 筛选状态.value
      }
    })

    if (response.status === 100) {
      // 处理文件下载
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `样品数据_${new Date().toISOString().slice(0, 10)}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)

      message.success('导出成功')
    } else {
      message.error(response.message || '导出失败')
    }
  } catch (error) {
    console.error('❌ 导出样品数据失败:', error)
    message.error('导出失败，请稍后重试')
  }
}

/**
 * 处理表格变化（分页、排序等）
 * @param {Object} 分页信息 - 包含当前页、页大小等信息
 */
const 处理表格变化 = (分页信息) => {
  Object.assign(分页配置, 分页信息)
  加载样品列表()
}

/**
 * 刷新数据
 */
const 刷新数据 = () => {
  加载样品列表()
  加载样品统计()
}

/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  加载样品列表()
  加载样品统计()
  加载快递查询配额()
})
</script>

<style scoped>
/* 样品管理模块整体样式 */
.sample-management-module {
  padding: 0;
}

/* 统计卡片行样式 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 操作卡片样式 */
.action-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 快递查询配额信息样式 */
.quota-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.quota-info .ant-tag {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
}

/* 列表卡片样式 */
.list-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 产品图片样式 */
.product-image {
  border-radius: 4px;
  object-fit: cover;
}

/* 样品详情样式 */
.sample-detail {
  max-height: 600px;
  overflow-y: auto;
}

.audit-history {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.audit-history h4 {
  margin-bottom: 16px;
  color: #1d1d1d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filters .ant-input-search,
  .search-filters .ant-select,
  .search-filters .ant-picker {
    width: 100% !important;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  /* 移动端表格滚动 */
  .list-card :deep(.ant-table-wrapper) {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .sample-management-module {
    padding: 0 8px;
  }
  
  .stats-row .ant-col {
    margin-bottom: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .action-buttons .ant-btn {
    width: 100%;
  }
}

/* 表格样式优化 */
.list-card :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

.list-card :deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 标签样式优化 */
.list-card :deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 按钮样式优化 */
.action-buttons .ant-btn,
.list-card .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 弹窗样式优化 */
.ant-modal :deep(.ant-modal-header) {
  border-radius: 8px 8px 0 0;
}

.ant-modal :deep(.ant-modal-content) {
  border-radius: 8px;
}
</style> 