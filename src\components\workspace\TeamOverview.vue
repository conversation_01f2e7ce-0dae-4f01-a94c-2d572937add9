<template>
  <div class="team-overview">
    <a-spin :spinning="loading" tip="加载团队数据...">
      <div v-if="data && !loading" class="team-content">
        <!-- 团队基础信息 Team Basic Info -->
        <div class="team-basic">
          <div class="basic-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <TeamOutlined :style="{ color: '#1890ff' }" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ data.参与团队数 || 0 }}</div>
                <div class="stat-label">参与团队</div>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon">
                <UserOutlined :style="{ color: '#52c41a' }" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ data.团队成员总数 || 0 }}</div>
                <div class="stat-label">团队成员</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队绩效指标 Team Performance Metrics -->
        <div class="team-performance">
          <h4 class="section-title">团队绩效</h4>
          <div class="performance-grid">
            <div class="performance-item">
              <div class="performance-label">团队邀约</div>
              <div class="performance-value">
                {{ formatNumber(data.团队邀约总数) }}
                <span class="performance-unit">次</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队指标卡片 Team Metric Cards -->
        <div v-if="data.指标卡片 && data.指标卡片.length" class="team-metrics">
          <h4 class="section-title">关键指标</h4>
          <div class="metrics-list">
            <div 
              v-for="(metric, index) in data.指标卡片" 
              :key="index"
              class="metric-card"
            >
              <div class="metric-header">
                <component 
                  :is="getMetricIcon(metric.图标)"
                  :style="{ color: metric.颜色 || '#1890ff' }"
                  class="metric-icon"
                />
                <span class="metric-title">{{ metric.标题 }}</span>
              </div>
              <div class="metric-value">{{ metric.格式化数值 }}</div>
              <div v-if="metric.趋势" class="metric-trend">
                <component 
                  :is="getTrendIcon(metric.趋势类型)"
                  :class="getTrendClass(metric.趋势类型)"
                  class="trend-icon"
                />
                <span 
                  class="trend-text"
                  :class="getTrendClass(metric.趋势类型)"
                >
                  {{ metric.趋势 }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 Quick Actions -->
        <div class="team-actions">
          <a-button
            type="primary"
            size="small"
            @click="handleViewTeamDetails"
            block
          >
            <TeamOutlined />
            {{ data?.参与团队数 > 0 ? '查看团队详情' : '查看团队列表' }}
          </a-button>
        </div>
      </div>

      <!-- 空状态 Empty State -->
      <div v-else-if="!loading" class="empty-state">
        <a-empty 
          description="暂无团队数据"
          :image="h(TeamOutlined)"
        >
          <a-button type="primary" @click="handleJoinTeam">
            加入团队
          </a-button>
        </a-empty>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { h } from 'vue'
import {
  TeamOutlined,
  UserOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  BarChartOutlined,
  FireOutlined
} from '@ant-design/icons-vue'

// Props definition
const props = defineProps({
  data: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits definition
const emit = defineEmits(['view-details', 'join-team'])

// Icon mapping
const iconMap = {
  'team': TeamOutlined,
  'user': UserOutlined,
  'fire': FireOutlined,
  'bar-chart': BarChartOutlined
}

const trendIconMap = {
  'up': ArrowUpOutlined,
  'down': ArrowDownOutlined,
  'stable': MinusOutlined
}

// Methods
const formatNumber = (value) => {
  if (value === null || value === undefined) return '0'
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return value.toString()
}

const formatCurrency = (value) => {
  if (value === null || value === undefined) return '0.00'
  const num = typeof value === 'number' ? value : parseFloat(value) || 0
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 移除排名相关方法，因为数据库中无对应数据源

const getMetricIcon = (iconName) => {
  return iconMap[iconName] || BarChartOutlined
}

const getTrendIcon = (trendType) => {
  return trendIconMap[trendType] || MinusOutlined
}

const getTrendClass = (trendType) => {
  return {
    'trend-up': trendType === 'up',
    'trend-down': trendType === 'down',
    'trend-stable': trendType === 'stable'
  }
}

const handleViewTeamDetails = () => {
  emit('view-details', props.data)
}

const handleJoinTeam = () => {
  emit('join-team')
}
</script>

<style scoped>
.team-overview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.team-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.team-basic {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.basic-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
}

.stat-icon {
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 12px 0;
}

.team-performance {
  flex: 1;
}

.performance-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.performance-label {
  font-size: 13px;
  color: #8c8c8c;
}

.performance-value {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.performance-unit {
  font-size: 12px;
  font-weight: normal;
  color: #8c8c8c;
  margin-left: 2px;
}

/* 移除排名相关样式，因为已删除排名功能 */

.team-metrics {
  flex: 1;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-card {
  padding: 8px 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.metric-icon {
  font-size: 14px;
}

.metric-title {
  font-size: 12px;
  color: #8c8c8c;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 2px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-icon {
  font-size: 10px;
}

.trend-text {
  font-size: 11px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-stable {
  color: #8c8c8c;
}

.team-actions {
  margin-top: auto;
  padding-top: 16px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* Responsive design */
@media (max-width: 768px) {
  .basic-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    padding: 8px;
  }
  
  .stat-value {
    font-size: 18px;
  }
}
</style>
