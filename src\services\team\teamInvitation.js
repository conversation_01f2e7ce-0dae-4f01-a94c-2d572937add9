import api from '../api'
import { isApiSuccess, getApiData, debugApiResponse, processPaginationResponse, processFormResponse } from '../../utils/apiUtils'

/**
 * 团队邀请服务
 * 处理团队邀请相关操作：发送邀请、查看邀请、处理邀请等
 */
export const teamInvitationService = {
  /**
   * 获取团队邀请列表
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.页码 - 页码
   * @param {number} params.每页数量 - 每页数量
   * @param {string} params.邀请状态 - 邀请状态筛选
   * @param {string} params.搜索关键词 - 搜索关键词
   * @returns {Promise} API响应
   */
  async getTeamInvitations(params) {
    try {
      const response = await api.post('/team/invite/list', {
        团队id: params.团队id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10,
        邀请状态: params.邀请状态,
        搜索关键词: params.搜索关键词
      })
      
      debugApiResponse(response, '获取团队邀请列表')
      
      const result = processPaginationResponse(response, '邀请列表')
      
      if (result.success) {
        // 优化邀请数据格式，适配后端新增的邀请令牌和确认链接字段
        result.list = result.list.map(invitation => ({
          ...invitation,
          // 格式化时间字段（适配后端实际返回字段）
          邀请时间格式化: invitation.创建时间 ? 
            new Date(invitation.创建时间).toLocaleString('zh-CN') : '',
          过期时间格式化: invitation.过期时间 ? 
            new Date(invitation.过期时间).toLocaleString('zh-CN') : '',
          处理时间格式化: invitation.处理时间 ? 
            new Date(invitation.处理时间).toLocaleString('zh-CN') : '',
          
          // 状态标签（适配后端状态字段）
          状态标签: {
            '邀请待处理': { color: 'warning', text: '待处理' },
            '正常': { color: 'success', text: '已接受' },
            '已拒绝邀请': { color: 'error', text: '已拒绝' },
            '已过期': { color: 'default', text: '已过期' },
            '已移除': { color: 'default', text: '已撤销' }
          }[invitation.状态] || { color: 'default', text: '未知' },
          
          // 是否可以操作（基于实际状态字段）
          可撤销: invitation.状态 === '邀请待处理',
          可重发: ['已过期', '已拒绝邀请'].includes(invitation.状态),
          可查看详情: true,
          
          // 新增：邀请确认链接处理
          邀请确认链接: invitation.邀请确认链接 || null,
          有邀请链接: !!(invitation.邀请令牌 || invitation.邀请确认链接)
        }))
        
        return {
          status: 100,
          data: {
            邀请列表: result.list,
            总数: result.total
          }
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('获取团队邀请列表失败:', error)
      throw new Error('获取团队邀请列表失败: ' + error.message)
    }
  },

  /**
   * 撤销邀请
   * @param {Object} params - 参数
   * @param {number} params.邀请id - 邀请id
   * @param {string} params.撤销原因 - 撤销原因（可选）
   * @returns {Promise} API响应
   */
  async revokeInvitation(params) {
    try {
      const response = await api.post('/team/invite/revoke', {
        邀请id: params.邀请id,
        撤销原因: params.撤销原因 || ''
      })
      
      debugApiResponse(response, '撤销邀请')
      
      const result = processFormResponse(response, '邀请已撤销', '撤销邀请失败')
      
      if (result.success) {
        return {
          status: 100,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('撤销邀请失败:', error)
      throw new Error('撤销邀请失败: ' + error.message)
    }
  },

  /**
   * 重发邀请
   * @param {Object} params - 参数
   * @param {number} params.邀请id - 邀请id
   * @returns {Promise} API响应
   */
  async resendInvitation(params) {
    try {
      const response = await api.post('/team/invite/resend', {
        邀请id: params.邀请id
      })
      
      debugApiResponse(response, '重发邀请')
      
      const result = processFormResponse(response, '邀请已重新发送', '重发邀请失败')
      
      if (result.success) {
        return {
          status: 100,
          data: result.data,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('重发邀请失败:', error)
      throw new Error('重发邀请失败: ' + error.message)
    }
  },

  /**
   * 获取邀请详情
   * @param {string} token - 邀请令牌
   * @returns {Promise} API响应
   */
  async getInvitationDetails(token) {
    try {
      const response = await api.get(`/team/invite/detail?token=${encodeURIComponent(token)}`)
      
      debugApiResponse(response, '获取邀请详情')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        // 优化邀请详情数据
        const invitationDetail = {
          ...data,
          // 格式化时间字段 - 修正字段映射
          邀请时间格式化: data?.创建时间 ? 
            new Date(data.创建时间).toLocaleString('zh-CN') : '',
          过期时间格式化: data?.过期时间 ? 
            new Date(data.过期时间).toLocaleString('zh-CN') : '',
          
          // 状态检查 - 修正字段映射
          是否有效: data?.状态 === '邀请待处理' && data?.过期时间 && new Date(data.过期时间) > new Date(),
          是否过期: data?.过期时间 && new Date(data.过期时间) <= new Date(),
          
          // 团队信息处理
          团队信息: {
            ...data?.团队信息,
            创建时间格式化: data?.团队信息?.创建时间 ? 
              new Date(data.团队信息.创建时间).toLocaleString('zh-CN') : ''
          }
        }
        
        return {
          status: 100,
          data: invitationDetail
        }
      } else {
        throw new Error(response.message || '获取邀请详情失败')
      }
    } catch (error) {
      console.error('获取邀请详情失败:', error)
      throw new Error('获取邀请详情失败: ' + error.message)
    }
  },

  /**
   * 处理邀请（接受或拒绝）
   * @param {Object} params - 参数
   * @param {string} params.邀请令牌 - 邀请令牌
   * @param {string} params.操作 - 操作：accept（接受）或reject（拒绝）
   * @param {string} params.拒绝原因 - 拒绝原因（拒绝时提供）
   * @returns {Promise} API响应
   */
  async processInvitation(params) {
    try {
      const response = await api.post('/team/invite/handle', {
        邀请令牌: params.邀请令牌,
        操作: params.操作,
        备注: params.拒绝原因 || ''
      })
      
      debugApiResponse(response, '处理邀请')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        const isAccept = params.操作 === 'accept'
        
        return {
          status: 100,
          data: data,
          message: isAccept ? '邀请接受成功，欢迎加入团队！' : '邀请已拒绝'
        }
      } else {
        throw new Error(response.message || '处理邀请失败')
      }
    } catch (error) {
      console.error('处理邀请失败:', error)
      throw new Error('处理邀请失败: ' + error.message)
    }
  },

  /**
   * 获取团队统计信息
   * @param {number} teamId - 团队id
   * @returns {Promise} API响应
   */
  async getTeamStats(teamId) {
    try {
      const response = await api.post('/team/overview/stats', {
        团队id: teamId
      })
      
      debugApiResponse(response, '获取团队统计信息')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        // 映射后端返回的新字段到前端期望的格式
        return {
          status: 100,
          data: {
            // 基础统计信息 - 匹配前端 teamStats 结构
            成员数量: data?.成员数量 || data?.当前成员数 || 0,
            最大成员数: data?.最大成员数 || 100,
            在线成员数: data?.在线成员数 || 0,
            今日活跃成员: data?.今日活跃数 || 0,
            运行天数: data?.运行天数 || 0,
            
            // 详细统计信息 - 保持原有结构以兼容其他组件
            成员统计: data?.成员统计 || {},
            职位分布: data?.职位分布 || {},
            最近加入成员: data?.最近加入成员 || [],
            
            // 兼容旧格式
            邀请统计: data?.邀请统计 || {},
            权限统计: data?.权限统计 || {},
            活动统计: data?.活动统计 || {}
          }
        }
      } else {
        throw new Error(response.message || '获取团队统计失败')
      }
    } catch (error) {
      console.error('获取团队统计信息失败:', error)
      throw new Error('获取团队统计失败: ' + error.message)
    }
  },

  /**
   * 获取团队活动记录（最近活动列表）
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.天数 - 查询天数，默认30天
   * @param {number} params.页码 - 页码，默认1
   * @param {number} params.每页数量 - 每页数量，默认10
   * @param {number} params.限制数量 - 限制数量（用于概览页面）
   * @returns {Promise} API响应
   */
  async getTeamActivities(params) {
    try {
      // 调用正确的活动日志接口
      const response = await api.post('/team/activity/logs', {
        团队id: params.团队id,
        页码: params.页码 || 1,
        每页数量: params.限制数量 || params.每页数量 || 10,
        天数: params.天数 || 30,
        操作类型: params.操作类型
      })
      
      debugApiResponse(response, '获取团队活动日志')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        // 处理活动列表数据
        const activities = data?.活动列表 || []
        
        const formattedActivities = activities.map(activity => {
          // 获取活动类型标签
          const getActivityTypeTag = (operationType) => {
            const tagMap = {
              '创建团队': { color: 'blue', icon: 'PlusOutlined' },
              '加入团队': { color: 'success', icon: 'UserAddOutlined' },
              '邀请成员': { color: 'cyan', icon: 'MailOutlined' },
              '踢出成员': { color: 'warning', icon: 'UserDeleteOutlined' },
              '离开团队': { color: 'orange', icon: 'LogoutOutlined' },
              '更新权限': { color: 'purple', icon: 'SafetyOutlined' },
              '更新角色': { color: 'geekblue', icon: 'TeamOutlined' },
              '解散团队': { color: 'red', icon: 'CloseOutlined' }
            }
            
            return tagMap[operationType] || { color: 'default', icon: 'InfoOutlined' }
          }
          
          return {
            活动类型: activity.操作类型,
            活动描述: activity.操作内容 || activity.活动描述,
            活动时间: activity.操作时间,
            活动时间格式化: activity.操作时间 ? new Date(activity.操作时间).toLocaleString('zh-CN') : '',
            操作人: activity.操作人姓名,
            目标用户: activity.目标用户姓名,
            详细描述: activity.详细描述,
            类型标签: getActivityTypeTag(activity.操作类型)
          }
        })
        
        return {
          status: 100,
          data: {
            活动列表: formattedActivities,
            总数: data?.总数量 || formattedActivities.length,
            页码: data?.当前页码 || 1,
            每页数量: data?.每页数量 || 10,
            总页数: data?.总页数 || 1
          }
        }
      } else {
        throw new Error(response.message || '获取团队活动日志失败')
      }
    } catch (error) {
      console.error('获取团队活动记录失败:', error)
      throw new Error('获取团队活动失败: ' + error.message)
    }
  },

  /**
   * 获取团队活动统计数据
   * @param {Object} params - 查询参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.天数 - 查询天数，默认30天
   * @returns {Promise} API响应
   */
  async getTeamActivityStats(params) {
    try {
      const response = await api.post('/team/activity/stats', {
        团队id: params.团队id,
        天数: params.天数 || 30
      })
      
      debugApiResponse(response, '获取团队活动统计')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        return {
          status: 100,
          data: {
            统计数据: data,
            新增成员: data?.新增成员 || 0,
            离开成员: data?.离开成员 || 0,
            活跃度: data?.活跃度 || '未知',
            最近活动: data?.最近活动 || []
          }
        }
      } else {
        throw new Error(response.message || '获取团队活动统计失败')
      }
    } catch (error) {
      console.error('获取团队活动统计失败:', error)
      throw new Error('获取团队活动统计失败: ' + error.message)
    }
  },



  /**
   * 转移团队所有权
   * @param {Object} params - 参数
   * @param {number} params.团队id - 团队id
   * @param {number} params.新所有者用户id - 新所有者用户id
   * @param {string} params.转移原因 - 转移原因
   * @returns {Promise} API响应
   */
  async transferOwnership(params) {
    try {
      const response = await api.post('/team/transfer-ownership', {
        团队id: params.团队id,
        新所有者用户id: params.新所有者用户id,
        转移原因: params.转移原因 || ''
      })
      
      debugApiResponse(response, '转移团队所有权')
      
      const result = processFormResponse(response, '团队所有权转移成功', '转移所有权失败')
      
      if (result.success) {
        return {
          status: 100,
          data: result.data,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('转移团队所有权失败:', error)
      throw new Error('转移所有权失败: ' + error.message)
    }
  }
} 