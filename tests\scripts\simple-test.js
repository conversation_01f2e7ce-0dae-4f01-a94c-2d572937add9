/**
 * 简化版CRM压力测试脚本
 * 使用ES模块语法
 */

import { spawn, exec } from 'child_process'
import path from 'path'
import fs from 'fs'
import { fileURLToPath } from 'url'

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🚀 开始CRM系统压力测试')
console.log('📊 目标URL: https://invite.limob.cn')

// 获取测试类型
const testType = process.argv[2] || 'light'
console.log(`📋 测试类型: ${testType}`)

// 测试配置
const testScenarios = {
  light: {
    stages: [
      { duration: '30s', target: 5 },
      { duration: '2m', target: 10 },
      { duration: '30s', target: 0 }
    ]
  },
  standard: {
    stages: [
      { duration: '1m', target: 20 },
      { duration: '3m', target: 50 },
      { duration: '1m', target: 0 }
    ]
  },
  peak: {
    stages: [
      { duration: '1m', target: 30 },
      { duration: '2m', target: 80 },
      { duration: '2m', target: 100 },
      { duration: '1m', target: 0 }
    ]
  }
}

// 检查K6是否安装
function checkK6Installation() {
  return new Promise((resolve, reject) => {
    exec('k6 version', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ K6未安装或不在PATH中')
        console.log('请运行: npm install -g k6')
        reject(error)
      } else {
        console.log('✅ K6工具检查通过')
        console.log(`K6版本: ${stdout.trim()}`)
        resolve()
      }
    })
  })
}

// 检查网络连接
function checkNetworkConnection() {
  return new Promise((resolve, reject) => {
    console.log('🔍 检查网络连接...')
    
    // 使用Node.js内置模块检查连接
    const https = await import('https')
    const url = await import('url')
    
    const options = {
      ...url.default.parse('https://invite.limob.cn'),
      method: 'HEAD',
      timeout: 5000
    }

    const req = https.default.request(options, (res) => {
      if (res.statusCode === 200 || res.statusCode === 404) {
        console.log('✅ 网络连接检查通过')
        resolve()
      } else {
        console.log(`⚠️  服务器响应: ${res.statusCode}`)
        resolve() // 即使状态码不是200也继续，可能是正常的
      }
    })
    
    req.on('error', (error) => {
      console.error('❌ 网络连接失败:', error.message)
      reject(error)
    })
    
    req.on('timeout', () => {
      console.error('❌ 网络连接超时')
      req.destroy()
      reject(new Error('网络连接超时'))
    })
    
    req.end()
  })
}

// 运行K6测试
function runK6Test(testType) {
  return new Promise((resolve, reject) => {
    console.log(`🎯 开始执行${testType}压力测试...`)
    
    // 构建K6命令
    const scriptPath = path.join(__dirname, '../k6/crm-stress-test.js')
    const outputDir = path.join(__dirname, '../results')
    
    // 确保结果目录存在
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    const outputPath = path.join(outputDir, `k6-result-${Date.now()}.json`)
    
    const k6Args = [
      'run',
      '--out', `json=${outputPath}`,
      '--tag', `testType=${testType}`,
      scriptPath
    ]
    
    console.log(`执行命令: k6 ${k6Args.join(' ')}`)
    
    // 启动K6进程
    const k6Process = spawn('k6', k6Args, {
      stdio: ['pipe', 'pipe', 'pipe']
    })
    
    let output = ''
    let errorOutput = ''
    
    // 收集输出
    k6Process.stdout.on('data', (data) => {
      const text = data.toString()
      output += text
      process.stdout.write(text) // 实时显示输出
    })
    
    k6Process.stderr.on('data', (data) => {
      const text = data.toString()
      errorOutput += text
      process.stderr.write(text) // 实时显示错误
    })
    
    // 处理进程结束
    k6Process.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ K6测试执行完成')
        console.log(`📄 结果文件: ${outputPath}`)
        
        // 解析并显示关键指标
        parseAndDisplayResults(output)
        resolve({ code, output, outputPath })
      } else {
        console.error(`\n❌ K6测试失败，退出码: ${code}`)
        if (errorOutput) {
          console.error('错误信息:', errorOutput)
        }
        reject(new Error(`K6测试失败: ${code}`))
      }
    })
    
    // 处理进程错误
    k6Process.on('error', (error) => {
      console.error('❌ K6进程启动失败:', error.message)
      reject(error)
    })
    
    // 处理中断信号
    process.on('SIGINT', () => {
      console.log('\n🛑 收到中断信号，正在停止测试...')
      k6Process.kill('SIGTERM')
      process.exit(0)
    })
  })
}

// 解析并显示测试结果
function parseAndDisplayResults(output) {
  console.log('\n📊 测试结果摘要:')
  
  try {
    const lines = output.split('\n')
    
    lines.forEach(line => {
      // 解析响应时间
      if (line.includes('http_req_duration')) {
        const match = line.match(/avg=([0-9.]+)ms.*p\(95\)=([0-9.]+)ms/)
        if (match) {
          console.log(`   📈 平均响应时间: ${match[1]}ms`)
          console.log(`   📈 95%响应时间: ${match[2]}ms`)
        }
      }
      
      // 解析请求速率
      if (line.includes('http_reqs')) {
        const match = line.match(/([0-9.]+)\/s/)
        if (match) {
          console.log(`   🚀 请求速率: ${match[1]} req/s`)
        }
      }
      
      // 解析错误率
      if (line.includes('http_req_failed')) {
        const match = line.match(/([0-9.]+)%/)
        if (match) {
          console.log(`   ❌ 错误率: ${match[1]}%`)
        }
      }
    })
  } catch (error) {
    console.warn('⚠️  解析测试结果失败:', error.message)
  }
}

// 主函数
async function main() {
  try {
    // 检查K6安装
    await checkK6Installation()
    
    // 检查网络连接
    await checkNetworkConnection()
    
    // 运行测试
    const result = await runK6Test(testType)
    
    console.log('\n🎉 测试执行完成!')
    console.log('📁 详细结果请查看 tests/results/ 目录')
    
  } catch (error) {
    console.error('\n💥 测试执行失败:', error.message)
    process.exit(1)
  }
}

// 启动测试
main()
