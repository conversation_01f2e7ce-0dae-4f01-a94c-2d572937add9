"""
用户联系人数据访问层 - PostgreSQL版本
基于asyncpg实现的用户联系人数据访问层

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的联系人查询和管理
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的关联查询和数据处理
5. 完整的错误处理和日志记录
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 用户联系人数据访问:
    """用户联系人表数据访问"""

    @staticmethod
    async def 查询用户联系人_通过ID(联系人id: UUID) -> Optional[Dict[str, Any]]:
        """
        通过ID查询用户联系人

        Args:
            联系人id: 用户联系人UUID

        Returns:
            联系人信息或None
        """
        try:
            查询SQL = """
            SELECT
                用户联系人id, 姓名, 用户表id
            FROM 用户联系人表
            WHERE 用户联系人id = $1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (联系人id,))
            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(f"查询用户联系人失败: ID={联系人id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 查询用户联系人_通过用户id(用户id: int, 关键词: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        通过用户id查询所有联系人及其关联的联系方式

        Args:
            用户id: 用户表id
            关键词: 搜索关键词（姓名或联系方式）

        Returns:
            联系人列表，包含关联的联系方式信息
        """
        try:
            # 构建查询参数
            查询参数: List[Any] = [用户id]
            关键词条件 = ""

            if 关键词 and 关键词.strip():
                关键词条件 = """
                AND (
                    uc.姓名 ILIKE $2
                    OR EXISTS (
                        SELECT 1 FROM 用户达人补充信息表 usi2
                        INNER JOIN 用户达人关联表 uar2 ON usi2.用户达人关联表id = uar2.id
                        INNER JOIN 联系方式表 ct2 ON usi2.联系方式表id = ct2.id
                        WHERE usi2.用户联系人表id = uc.用户联系人id
                            AND uar2.用户id = $1
                            AND uar2.状态 = 1
                            AND ct2.联系方式 ILIKE $3
                    )
                )
                """
                # 姓名使用模糊匹配，联系方式使用开头匹配
                查询参数.append(f"%{关键词.strip()}%")  # 姓名模糊匹配
                查询参数.append(f"{关键词.strip()}%")   # 联系方式开头匹配

            查询SQL = f"""
            SELECT
                uc.用户联系人id,
                uc.姓名,
                uc.用户表id,
                -- 使用JSON_AGG构建联系方式数组
                COALESCE(
                    JSON_AGG(
                        JSON_BUILD_OBJECT(
                            '联系方式', ct.联系方式,
                            '联系方式类型', ct.类型,
                            '补充信息id', usi.id
                        )
                    ) FILTER (WHERE ct.联系方式 IS NOT NULL),
                    '[]'::json
                ) as 关联联系方式列表
            FROM 用户联系人表 uc
            LEFT JOIN 用户达人补充信息表 usi ON uc.用户联系人id = usi.用户联系人表id
            LEFT JOIN 用户达人关联表 uar ON usi.用户达人关联表id = uar.id AND uar.用户id = $1 AND uar.状态 = 1
            LEFT JOIN 联系方式表 ct ON usi.联系方式表id = ct.id
            WHERE uc.用户表id = $1
            {关键词条件}
            GROUP BY uc.用户联系人id, uc.姓名, uc.用户表id, uc.创建时间
            ORDER BY uc.创建时间 DESC
            LIMIT 5
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, tuple(查询参数))
            return 结果

        except Exception as e:
            错误日志器.error(f"查询用户联系人列表失败: 用户id={用户id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 创建用户联系人(
        用户id: int,
        姓名: str
    ) -> Optional[Dict[str, Any]]:
        """
        创建新的用户联系人记录

        Args:
            用户id: 用户表id
            姓名: 联系人姓名

        Returns:
            新创建的联系人信息或None
        """
        try:
            插入SQL = """
            INSERT INTO 用户联系人表 (用户表id, 姓名)
            VALUES ($1, $2)
            RETURNING 用户联系人id, 姓名, 用户表id
            """

            结果 = await 异步连接池实例.执行查询(插入SQL, (用户id, 姓名))

            if 结果:
                联系人信息 = 结果[0]
                数据库日志器.info(f"创建用户联系人成功: ID={联系人信息['用户联系人id']}, 姓名={姓名}")
                return 联系人信息
            else:
                错误日志器.error("创建用户联系人失败，未返回数据")
                return None

        except Exception as e:
            错误日志器.error(f"创建用户联系人失败: 用户id={用户id}, 姓名={姓名}, 错误={str(e)}")
            raise

    @staticmethod
    async def 更新用户联系人(
        联系人id: UUID,
        姓名: Optional[str] = None
    ) -> bool:
        """
        更新用户联系人信息

        Args:
            联系人id: 用户联系人UUID
            姓名: 新的姓名（可选）

        Returns:
            是否更新成功
        """
        try:
            if not 姓名:
                return True  # 没有要更新的字段

            更新SQL = """
            UPDATE 用户联系人表 
            SET 姓名 = $1
            WHERE 用户联系人id = $2
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, (姓名, 联系人id))

            if 影响行数 > 0:
                数据库日志器.info(f"更新用户联系人成功: ID={联系人id}")
                return True
            else:
                数据库日志器.warning(f"更新用户联系人未影响任何行: ID={联系人id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新用户联系人失败: ID={联系人id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 删除用户联系人(联系人id: UUID) -> bool:
        """
        删除用户联系人记录

        Args:
            联系人id: 用户联系人UUID

        Returns:
            是否删除成功
        """
        try:
            删除SQL = "DELETE FROM 用户联系人表 WHERE 用户联系人id = $1"

            影响行数 = await 异步连接池实例.执行更新(删除SQL, (联系人id,))

            if 影响行数 > 0:
                数据库日志器.info(f"删除用户联系人成功: ID={联系人id}")
                return True
            else:
                数据库日志器.warning(f"删除用户联系人未影响任何行: ID={联系人id}")
                return False

        except Exception as e:
            错误日志器.error(f"删除用户联系人失败: ID={联系人id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 检查联系人是否存在(联系人id: UUID) -> bool:
        """
        检查联系人是否存在

        Args:
            联系人id: 用户联系人UUID

        Returns:
            是否存在
        """
        try:
            查询SQL = """
            SELECT 1 FROM 用户联系人表 
            WHERE 用户联系人id = $1
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (联系人id,))
            return len(结果) > 0

        except Exception as e:
            错误日志器.error(f"检查联系人是否存在失败: ID={联系人id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 检查用户是否拥有联系人(用户id: int, 联系人id: UUID) -> bool:
        """
        检查用户是否拥有指定联系人

        Args:
            用户id: 用户表id
            联系人id: 用户联系人UUID

        Returns:
            是否拥有
        """
        try:
            查询SQL = """
            SELECT 1 FROM 用户联系人表 
            WHERE 用户表id = $1 AND 用户联系人id = $2
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (用户id, 联系人id))
            return len(结果) > 0

        except Exception as e:
            错误日志器.error(f"检查用户联系人所有权失败: 用户id={用户id}, 联系人id={联系人id}, 错误={str(e)}")
            raise


class 达人补充信息数据访问:
    """达人补充信息表数据访问"""

    @staticmethod
    async def 关联用户联系人(补充信息id: int, 联系人id: UUID) -> bool:
        """
        将用户联系人关联到达人补充信息

        Args:
            补充信息id: 用户达人补充信息表id
            联系人id: 用户联系人UUID

        Returns:
            是否关联成功
        """
        try:
            更新SQL = """
            UPDATE 用户达人补充信息表 
            SET 用户联系人表id = $1, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, (联系人id, 补充信息id))

            if 影响行数 > 0:
                数据库日志器.info(f"关联用户联系人成功: 补充信息id={补充信息id}, 联系人id={联系人id}")
                return True
            else:
                数据库日志器.warning(f"关联用户联系人未影响任何行: 补充信息id={补充信息id}")
                return False

        except Exception as e:
            错误日志器.error(f"关联用户联系人失败: 补充信息id={补充信息id}, 联系人id={联系人id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 取消关联用户联系人(补充信息id: int) -> bool:
        """
        取消达人补充信息与用户联系人的关联

        Args:
            补充信息id: 用户达人补充信息表id

        Returns:
            是否取消关联成功
        """
        try:
            更新SQL = """
            UPDATE 用户达人补充信息表 
            SET 用户联系人表id = NULL, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $1
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, (补充信息id,))

            if 影响行数 > 0:
                数据库日志器.info(f"取消关联用户联系人成功: 补充信息id={补充信息id}")
                return True
            else:
                数据库日志器.warning(f"取消关联用户联系人未影响任何行: 补充信息id={补充信息id}")
                return False

        except Exception as e:
            错误日志器.error(f"取消关联用户联系人失败: 补充信息id={补充信息id}, 错误={str(e)}")
            raise

    @staticmethod
    async def 创建联系人并关联到补充信息(
        用户id: int,
        姓名: str,
        补充信息id: int
    ) -> Optional[Dict[str, Any]]:
        """
        在事务中创建用户联系人并关联到达人补充信息
        确保事务一致性：如果关联失败，则不创建联系人

        Args:
            用户id: 用户表id
            姓名: 联系人姓名
            补充信息id: 用户达人补充信息表id

        Returns:
            新创建的联系人信息或None
        """
        try:
            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.transaction():
                    # 1. 创建用户联系人
                    插入SQL = """
                    INSERT INTO 用户联系人表 (用户表id, 姓名)
                    VALUES ($1, $2)
                    RETURNING 用户联系人id, 姓名, 用户表id
                    """

                    联系人结果 = await 连接.fetch(插入SQL, 用户id, 姓名)

                    if not 联系人结果:
                        错误日志器.error("创建用户联系人失败，未返回数据")
                        return None

                    联系人信息 = dict(联系人结果[0])
                    联系人id = 联系人信息['用户联系人id']

                    # 2. 关联到达人补充信息
                    更新SQL = """
                    UPDATE 用户达人补充信息表
                    SET 用户联系人表id = $1, 更新时间 = CURRENT_TIMESTAMP
                    WHERE id = $2
                    """

                    更新结果 = await 连接.execute(更新SQL, 联系人id, 补充信息id)

                    # 检查更新是否成功
                    影响行数 = int(更新结果.split()[-1]) if 更新结果.startswith("UPDATE") else 0

                    if 影响行数 == 0:
                        # 如果关联失败，事务会自动回滚
                        错误日志器.error(f"关联用户联系人失败: 补充信息id={补充信息id}不存在")
                        raise Exception(f"补充信息记录不存在: id={补充信息id}")

                    # 事务成功提交
                    数据库日志器.info(f"创建联系人并关联成功: 联系人ID={联系人id}, 补充信息id={补充信息id}")
                    return 联系人信息

        except Exception as e:
            错误日志器.error(f"创建联系人并关联失败: 用户id={用户id}, 姓名={姓名}, 补充信息id={补充信息id}, 错误={str(e)}")
            raise


# 创建数据访问层实例
用户联系人数据访问实例 = 用户联系人数据访问()
达人补充信息数据访问实例 = 达人补充信息数据访问()
