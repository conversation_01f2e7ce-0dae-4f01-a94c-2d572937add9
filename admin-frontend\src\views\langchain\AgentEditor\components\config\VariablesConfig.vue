<template>
  <div class="variables-config">
    <div class="section-header">
      <h2><SettingOutlined /> 自定义变量配置</h2>
      <p>配置智能体的自定义变量</p>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <a-card title="自定义变量" class="config-card">
        <!-- 变量列表 -->
        <div class="variables-list">
          <div 
            v-for="(variable, index) in localForm.自定义变量" 
            :key="index"
            class="variable-item"
          >
            <a-card size="small" class="variable-card">
              <template #title>
                <div class="variable-title">
                  <span>变量 {{ index + 1 }}</span>
                  <a-button 
                    type="text" 
                    size="small" 
                    danger
                    @click="removeVariable(index)"
                  >
                    <DeleteOutlined />
                  </a-button>
                </div>
              </template>

              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="变量名" required>
                    <a-input
                      v-model:value="variable.变量名"
                      placeholder="输入变量名"
                      @change="handleFormChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="变量类型" required>
                    <a-select
                      v-model:value="variable.变量类型"
                      placeholder="选择类型"
                      @change="handleFormChange"
                    >
                      <a-select-option value="string">文本</a-select-option>
                      <a-select-option value="number">数字</a-select-option>
                      <a-select-option value="boolean">布尔值</a-select-option>
                      <a-select-option value="array">数组</a-select-option>
                      <a-select-option value="object">对象</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="默认值">
                    <a-input
                      v-if="variable.变量类型 === 'string'"
                      v-model:value="variable.默认值"
                      placeholder="默认文本值"
                      @change="handleFormChange"
                    />
                    <a-input-number
                      v-else-if="variable.变量类型 === 'number'"
                      v-model:value="variable.默认值"
                      placeholder="默认数字值"
                      style="width: 100%"
                      @change="handleFormChange"
                    />
                    <a-switch
                      v-else-if="variable.变量类型 === 'boolean'"
                      v-model:checked="variable.默认值"
                      @change="handleFormChange"
                    />
                    <a-textarea
                      v-else
                      v-model:value="variable.默认值"
                      placeholder="JSON格式"
                      :rows="2"
                      @change="handleFormChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="变量描述">
                <a-textarea
                  v-model:value="variable.变量描述"
                  placeholder="描述变量的用途和含义"
                  :rows="2"
                  @change="handleFormChange"
                />
              </a-form-item>

              <a-form-item label="是否必需">
                <a-switch
                  v-model:checked="variable.是否必需"
                  checked-children="必需"
                  un-checked-children="可选"
                  @change="handleFormChange"
                />
              </a-form-item>
            </a-card>
          </div>
        </div>

        <!-- 添加变量按钮 -->
        <div class="add-variable">
          <a-button type="dashed" block @click="addVariable">
            <PlusOutlined /> 添加自定义变量
          </a-button>
        </div>

        <!-- 变量预览 -->
        <div v-if="localForm.自定义变量?.length > 0" class="variables-preview">
          <h4>变量预览</h4>
          <div class="preview-content">
            <a-descriptions size="small" :column="1" bordered>
              <a-descriptions-item 
                v-for="(variable, index) in localForm.自定义变量" 
                :key="index"
                :label="variable.变量名 || `变量${index + 1}`"
              >
                <a-space>
                  <a-tag :color="getTypeColor(variable.变量类型)">
                    {{ getTypeLabel(variable.变量类型) }}
                  </a-tag>
                  <span>{{ variable.默认值 || '无默认值' }}</span>
                  <a-tag v-if="variable.是否必需" color="red" size="small">必需</a-tag>
                </a-space>
                <div v-if="variable.变量描述" class="variable-desc">
                  {{ variable.变量描述 }}
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>

        <!-- 变量使用说明 -->
        <a-alert
          type="info"
          show-icon
          message="变量使用说明"
          style="margin-top: 16px"
        >
          <template #description>
            <ul>
              <li>在提示词中使用 <code>{变量名}</code> 格式引用变量</li>
              <li>变量值可以在调用智能体时动态传入</li>
              <li>必需变量在调用时必须提供值</li>
              <li>可选变量未提供时使用默认值</li>
            </ul>
          </template>
        </a-alert>
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { SettingOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 类型标签映射
const typeLabels = {
  'string': '文本',
  'number': '数字',
  'boolean': '布尔值',
  'array': '数组',
  'object': '对象'
}

// 类型颜色映射
const typeColors = {
  'string': 'blue',
  'number': 'green',
  'boolean': 'orange',
  'array': 'purple',
  'object': 'cyan'
}

// 获取类型标签
const getTypeLabel = (type) => {
  return typeLabels[type] || '未知'
}

// 获取类型颜色
const getTypeColor = (type) => {
  return typeColors[type] || 'default'
}

// 添加变量
const addVariable = () => {
  if (!localForm.value.自定义变量) {
    localForm.value.自定义变量 = []
  }
  
  localForm.value.自定义变量.push({
    变量名: '',
    变量类型: 'string',
    默认值: '',
    变量描述: '',
    是否必需: false
  })
  
  handleFormChange()
}

// 移除变量
const removeVariable = (index) => {
  localForm.value.自定义变量.splice(index, 1)
  handleFormChange()
}

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}

// 初始化自定义变量数组
if (!localForm.value.自定义变量) {
  localForm.value.自定义变量 = []
}
</script>

<style scoped>
.variables-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: 800px;
}

.config-card {
  margin-bottom: 16px;
}

.variables-list {
  margin-bottom: 16px;
}

.variable-item {
  margin-bottom: 16px;
}

.variable-card {
  border: 1px solid #f0f0f0;
}

.variable-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-variable {
  margin: 16px 0;
}

.variables-preview {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.variables-preview h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.preview-content {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.variable-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-descriptions-item-content) {
  padding: 8px 16px;
}

:deep(.ant-alert-description ul) {
  margin: 0;
  padding-left: 16px;
}

:deep(.ant-alert-description li) {
  margin: 4px 0;
}

:deep(.ant-alert-description code) {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
