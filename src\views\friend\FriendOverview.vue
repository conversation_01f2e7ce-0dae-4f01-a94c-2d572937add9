<template>
  <div class="friend-overview">
    <!-- 数据概览卡片 -->
    <div class="overview-cards">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="6">
          <div class="overview-card wechat-accounts">
            <div class="card-content">
              <div class="card-info">
                <h3>微信账号</h3>
                <div class="number">{{ statisticsData.微信账号总数 || 0 }}</div>
                <p class="description">已绑定的微信账号数量</p>
              </div>
              <div class="card-icon">
                <WechatOutlined />
              </div>
            </div>
            <div class="card-action">
              <a-button type="link" @click="navigateTo('wechat-accounts')">
                管理账号 <ArrowRightOutlined />
              </a-button>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="overview-card friends">
            <div class="card-content">
              <div class="card-info">
                <h3>好友总数</h3>
                <div class="number">{{ statisticsData.好友总数 || 0 }}</div>
                <p class="description">所有微信账号的好友总和</p>
              </div>
              <div class="card-icon">
                <TeamOutlined />
              </div>
            </div>
            <div class="card-action">
              <a-button type="link" @click="navigateTo('friend-list')">
                查看好友 <ArrowRightOutlined />
              </a-button>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="overview-card progress">
            <div class="card-content">
              <div class="card-info">
                <h3>进行中对接</h3>
                <div class="number">{{ statisticsData.进行中对接 || 0 }}</div>
                <p class="description">正在进行的产品对接</p>
              </div>
              <div class="card-icon">
                <SyncOutlined :spin="true" />
              </div>
            </div>
            <div class="card-action">
              <a-button type="link" @click="navigateTo('progress-management')">
                查看进度 <ArrowRightOutlined />
              </a-button>
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <div class="overview-card success">
            <div class="card-content">
              <div class="card-info">
                <h3>成功对接</h3>
                <div class="number">{{ statisticsData.成功对接 || 0 }}</div>
                <p class="description">已完成的对接项目</p>
              </div>
              <div class="card-icon">
                <CheckCircleOutlined />
              </div>
            </div>
            <div class="card-action">
              <a-button type="link" @click="navigateTo('progress-management')">
                查看详情 <ArrowRightOutlined />
              </a-button>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 功能快捷入口 -->
    <div class="function-grid">
      <h3 class="section-title">功能导航</h3>
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :lg="8">
          <div class="function-card" @click="navigateTo('wechat-accounts')">
            <div class="function-icon wechat">
              <WechatOutlined />
            </div>
            <div class="function-content">
              <h4>微信账号管理</h4>
              <p>绑定和管理您的微信账号，查看账号状态</p>
            </div>
            <div class="function-arrow">
              <ArrowRightOutlined />
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="8">
          <div class="function-card" @click="navigateTo('friend-list')">
            <div class="function-icon friends">
              <TeamOutlined />
            </div>
            <div class="function-content">
              <h4>好友列表管理</h4>
              <p>查看所有好友信息，支持搜索和分类</p>
            </div>
            <div class="function-arrow">
              <ArrowRightOutlined />
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="8">
          <div class="function-card" @click="navigateTo('progress-management')">
            <div class="function-icon progress">
              <BarChartOutlined />
            </div>
            <div class="function-content">
              <h4>对接进度管理</h4>
              <p>跟踪产品对接进度，管理对接状态</p>
            </div>
            <div class="function-arrow">
              <ArrowRightOutlined />
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="8">
          <div class="function-card" @click="navigateTo('auto-add')">
            <div class="function-icon auto-add">
              <UserAddOutlined />
            </div>
            <div class="function-content">
              <h4>自动添加好友</h4>
              <p>管理微信自动添加好友记录和状态</p>
            </div>
            <div class="function-arrow">
              <ArrowRightOutlined />
            </div>
          </div>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="8">
          <div class="function-card" @click="addWeChatAccount">
            <div class="function-icon add">
              <PlusOutlined />
            </div>
            <div class="function-content">
              <h4>绑定微信账号</h4>
              <p>添加新的微信账号到系统</p>
            </div>
            <div class="function-arrow">
              <ArrowRightOutlined />
            </div>
          </div>
        </a-col>


      </a-row>
    </div>

    <!-- 最近动态 -->
    <div class="recent-activities">
      <h3 class="section-title">最近动态</h3>
      <div class="activity-timeline">
        <a-timeline>
          <a-timeline-item 
            v-for="activity in recentActivities" 
            :key="activity.id"
            :color="activity.color"
          >
            <template #dot>
              <component :is="activity.icon" :style="{ color: activity.color }" />
            </template>
            <div class="activity-content">
              <div class="activity-text">{{ activity.text }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
        
        <div class="view-more">
          <a-button type="link" @click="viewMoreActivities">
            查看更多动态 <ArrowRightOutlined />
          </a-button>
        </div>
      </div>
    </div>

    <!-- 绑定微信账号弹窗 -->
    <a-modal
      v-model:open="bindWeChatVisible"
      title="绑定微信账号"
      :width="500"
      @ok="handleBindWeChat"
      :confirmLoading="bindLoading"
    >
      <a-form :form="bindForm" layout="vertical">
        <a-form-item label="微信号" required>
          <a-input
            v-model:value="bindForm.微信号"
            placeholder="请输入微信号（6-20位，字母开头）"
            :maxlength="20"
          />
          <div class="form-tip">
            微信号格式：6-20位字符，必须以字母开头，可包含字母、数字、下划线
          </div>
        </a-form-item>
        
        <a-form-item label="备注名称">
          <a-input
            v-model:value="bindForm.备注"
            placeholder="为这个微信号添加备注（可选）"
            :maxlength="50"
          />
        </a-form-item>
      </a-form>
    </a-modal>


  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  WechatOutlined,
  TeamOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  ArrowRightOutlined,
  BarChartOutlined,
  PieChartOutlined,
  PlusOutlined,
  UserOutlined,
  UserAddOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 导入服务
import { wechatService } from '@/services/friend'
import { friendService } from '@/services/friend'

defineOptions({
  name: 'FriendOverview'
})

const router = useRouter()

// 响应式数据
const loading = ref(false)
const statisticsData = ref({})
const bindWeChatVisible = ref(false)
const bindLoading = ref(false)

// 表单数据
const bindForm = reactive({
  微信号: '',
  备注: ''
})

// 最近动态数据
const recentActivities = ref([
  {
    id: 1,
    text: '成功绑定新微信账号',
    time: '2小时前',
    color: '#52c41a',
    icon: 'CheckCircleOutlined'
  },
  {
    id: 2,
    text: '添加了5个新好友',
    time: '4小时前',
    color: '#1890ff',
    icon: 'UserOutlined'
  },
  {
    id: 3,
    text: '更新了产品对接进度',
    time: '1天前',
    color: '#fa8c16',
    icon: 'ClockCircleOutlined'
  },
  {
    id: 4,
    text: '完成了产品样品寄送',
    time: '2天前',
    color: '#722ed1',
    icon: 'ExclamationCircleOutlined'
  }
])

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()

  // 监听全局事件
  window.addEventListener('add-wechat-account', handleAddWeChatEvent)
  window.addEventListener('refresh-data', loadStatistics)
})

// 组件卸载时清理
onBeforeUnmount(() => {
  // 清理全局事件监听
  try {
    window.removeEventListener('add-wechat-account', handleAddWeChatEvent)
    window.removeEventListener('refresh-data', loadStatistics)
  } catch (error) {
    console.warn('清理事件监听器时出现警告:', error)
  }
})

// 方法定义

/**
 * 加载统计数据
 */
const loadStatistics = async () => {
  try {
    loading.value = true
    const response = await wechatService.getWeChatStatistics()
    if (response.status === 100) {
      statisticsData.value = response.data
      console.log('统计数据加载成功:', response.data) // 调试日志
    } else {
      throw new Error(response.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    message.error(error.message || '加载统计数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 导航到指定页面
 */
const navigateTo = (routeName) => {
  const routeMap = {
    'wechat-accounts': 'FriendWechatAccounts',
    'friend-list': 'FriendList',
    'progress-management': 'FriendProgressManagement',
    'auto-add': 'AutoAddFriends',
    'team-overview': 'FriendTeamOverview'
  }
  
  router.push({ name: routeMap[routeName] || routeName })
}

/**
 * 处理添加微信账号事件
 */
const handleAddWeChatEvent = () => {
  addWeChatAccount()
}



/**
 * 显示绑定微信账号弹窗
 */
const addWeChatAccount = () => {
  bindWeChatVisible.value = true
  // 重置表单
  bindForm.微信号 = ''
  bindForm.备注 = ''
}

/**
 * 处理绑定微信账号
 */
const handleBindWeChat = async () => {
  try {
    // 验证微信号格式
    if (!wechatService.validateWeChatNumber(bindForm.微信号)) {
      message.error('微信号格式不正确，请输入6-20位字符，以字母开头')
      return
    }

    bindLoading.value = true
    
    await wechatService.bindWeChatAccount({
      微信号: bindForm.微信号,
      备注: bindForm.备注
    })
    
    message.success('微信账号绑定成功')
    bindWeChatVisible.value = false
    
    // 刷新统计数据
    loadStatistics()
    
  } catch (error) {
    console.error('绑定微信账号失败:', error)
    message.error(error.message || '绑定失败，请重试')
  } finally {
    bindLoading.value = false
  }
}



/**
 * 查看更多动态
 */
const viewMoreActivities = () => {
  message.info('更多动态功能开发中...')
}
</script>

<style scoped>
.friend-overview {
  padding: 24px;
}

/* 数据概览卡片样式 */
.overview-cards {
  margin-bottom: 32px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.overview-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

.card-info .number {
  font-size: 32px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
  line-height: 1;
}

.card-info .description {
  margin: 0;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

.card-icon {
  font-size: 32px;
  opacity: 0.3;
}

.wechat-accounts .card-icon {
  color: #52c41a;
}

.friends .card-icon {
  color: #1890ff;
}

.progress .card-icon {
  color: #fa8c16;
}

.success .card-icon {
  color: #722ed1;
}

.card-action {
  margin-top: 12px;
}

.card-action .ant-btn {
  padding: 0;
  height: auto;
  font-size: 12px;
}

/* 功能导航样式 */
.function-grid {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 24px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.function-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.function-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.function-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
}

.function-icon.wechat {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
}

.function-icon.friends {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
}

.function-icon.progress {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
  color: white;
}



.function-icon.add {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
  color: white;
}

.function-icon.auto-add {
  background: linear-gradient(135deg, #722ed1, #9254de);
  color: white;
}

.function-icon.user-add {
  background: linear-gradient(135deg, #eb2f96, #f759ab);
  color: white;
}

.function-content {
  flex: 1;
}

.function-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.function-content p {
  margin: 0;
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
}

.function-arrow {
  color: #bfbfbf;
  font-size: 16px;
  margin-left: 16px;
}

/* 最近动态样式 */
.recent-activities {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.activity-timeline {
  margin-top: 24px;
}

.activity-content {
  margin-left: 16px;
}

.activity-text {
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

.view-more {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 表单样式 */
.form-tip {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .friend-overview {
    padding: 16px;
  }
  
  .overview-card {
    height: auto;
    padding: 16px;
  }
  
  .card-info .number {
    font-size: 24px;
  }
  
  .function-card {
    padding: 16px;
  }
  
  .function-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
    margin-right: 12px;
  }
  
  .function-content h4 {
    font-size: 14px;
  }
  
  .function-content p {
    font-size: 12px;
  }
}
</style> 