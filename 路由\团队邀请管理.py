import traceback
from typing import Optional, List, Literal

from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户, 获取当前用户_可选
from 工具.团队工具 import 标准化API响应
from 数据.异步邀请管理 import (
    异步通过手机号邀请成员,
    异步获取团队邀请列表,
    异步处理邀请,
    异步获取邀请详情,
    异步生成注册邀请链接,
    异步撤销邀请_通过ID,
    异步重新发送邀请_通过ID,
    异步创建直接邀请
)
from 日志 import 错误日志器
from 状态 import 状态

# 创建新的路由
团队邀请管理路由 = APIRouter(
    tags=["团队邀请管理"]
)

# =============== Pydantic模型 ===============

class 手机号邀请请求(BaseModel):
    """通过手机号邀请成员的请求模型"""
    手机号: str
    团队id: int
    角色: str = "成员"  # 只支持：创建者、负责人、成员
    权限列表: Optional[List[str]] = []

class 直接邀请请求(BaseModel):
    """直接邀请请求模型"""
    用户id: int
    团队id: int
    角色: str = "成员"  # 只支持：创建者、负责人、成员
    权限列表: Optional[List[str]] = []

class 团队邀请列表请求(BaseModel):
    """团队邀请列表请求模型"""
    团队id: int
    页码: int = 1
    每页数量: int = 20
    状态: Optional[str] = None

class 处理邀请请求(BaseModel):
    """处理邀请请求模型"""
    邀请令牌: str = Field(..., description="用于识别邀请的唯一令牌")
    操作: Literal['accept', 'reject'] = Field(..., description="处理邀请的操作，接受或拒绝")
    用户id: Optional[int] = Field(None, description="可选，用于新用户注册后系统自动接受邀请时传递新用户id")
    备注: Optional[str] = Field(None, description="可选，用户接受或拒绝邀请时可能填写的备注")

class 邀请详情请求(BaseModel):
    """邀请详情请求模型"""
    邀请令牌: str = Field(..., min_length=1, description="邀请令牌")

class 注册邀请链接请求(BaseModel):
    """注册邀请链接请求模型"""
    团队id: int = Field(..., gt=0, description="团队id")
    手机号: str = Field(..., min_length=11, max_length=11, description="被邀请人手机号")
    角色: str = Field("成员", description="角色名称")
    权限列表: Optional[List[str]] = Field([], description="权限代码数组")
    有效期天数: int = Field(7, ge=1, le=30, description="链接有效期天数")

class 撤销邀请请求(BaseModel):
    """撤销邀请请求模型"""
    邀请id: int
    撤销原因: Optional[str] = ""

class 重发邀请请求(BaseModel):
    """重发邀请请求模型"""
    邀请id: int


# =============== 路由端点 ===============

@团队邀请管理路由.post("/by-phone", summary="通过手机号邀请成员")
async def 通过手机号邀请成员接口(
    请求数据: 手机号邀请请求,
    当前用户: dict = Depends(获取当前用户)
):
    try:
        结果 = await 异步通过手机号邀请成员(
            手机号=请求数据.手机号,
            团队id=请求数据.团队id,
            邀请人id=当前用户["id"],
            角色=请求数据.角色,
            权限列表=请求数据.权限列表
        )
        if 结果["success"]:
            return 标准化API响应(100, 结果["data"], 结果["message"])
        else:
            return 标准化API响应(状态.团队邀请.邀请发送失败, None, 结果["message"])
    except Exception as e:
        错误日志器.error(f"手机号邀请失败: {e}")
        return 标准化API响应(状态.团队邀请.系统错误, None, "邀请发送失败")

@团队邀请管理路由.post("/direct", summary="直接邀请已注册用户")
async def 直接邀请已注册用户接口(
    请求数据: 直接邀请请求,
    当前用户: dict = Depends(获取当前用户)
):
    try:
        结果 = await 异步创建直接邀请(
            被邀请用户id=请求数据.用户id,
            团队id=请求数据.团队id,
            邀请人id=当前用户["id"],
            角色=请求数据.角色,
            权限列表=请求数据.权限列表
        )
        if 结果["success"]:
            return 标准化API响应(100, 结果.get("data"), 结果["message"])
        else:
            return 标准化API响应(状态.团队邀请.邀请发送失败, None, 结果["message"])
    except Exception as e:
        错误日志器.error(f"直接邀请失败: {e}")
        return 标准化API响应(状态.团队邀请.系统错误, None, "邀请发送失败")

@团队邀请管理路由.post("/list", summary="获取团队邀请列表")
async def 获取团队邀请列表接口(
    请求数据: 团队邀请列表请求,
    当前用户: dict = Depends(获取当前用户)
):
    try:
        错误日志器.info(f"用户 {当前用户['id']} 请求获取团队 {请求数据.团队id} 的邀请列表")
        
        结果 = await 异步获取团队邀请列表(
            团队id=请求数据.团队id,
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            状态筛选=请求数据.状态,
            操作者ID=当前用户["id"]
        )
        
        if 结果.get("success"):
            错误日志器.info(f"成功获取团队 {请求数据.团队id} 的邀请列表，返回 {len(结果['data'].get('邀请列表', []))} 条记录")
            return 标准化API响应(100, 结果["data"], 结果.get("message") or "获取邀请列表成功")
        else:
            错误日志器.warning(f"获取团队 {请求数据.团队id} 邀请列表失败: {结果.get('message')}")
            return 标准化API响应(状态.团队邀请.系统错误, None, 结果.get("message") or "获取邀请列表失败")
            
    except Exception as e:
        import traceback
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取邀请列表接口异常: {e}\n完整错误堆栈:\n{错误详情}")
        return 标准化API响应(状态.团队邀请.系统错误, None, f"获取邀请列表失败: {str(e)}")

@团队邀请管理路由.post(
    "/handle",
    summary="处理团队邀请"
)
async def 处理邀请接口(
    请求数据: 处理邀请请求,
    当前登录用户: Optional[dict] = Depends(获取当前用户_可选)
):
    try:
        操作用户id = 当前登录用户["id"] if 当前登录用户 else 请求数据.用户id
        
        if not 操作用户id:
            return 标准化API响应(状态.团队邀请.用户不存在, None, "接受邀请失败：请先登录您的账号。")

        结果 = await 异步处理邀请(
            邀请令牌=请求数据.邀请令牌,
            操作=请求数据.操作,
            用户id=操作用户id,
            备注=请求数据.备注,
            当前登录用户=当前登录用户
        )
        
        if 结果.get("success"):
            return 标准化API响应(100, 结果.get("data"), 结果.get("message") or "处理邀请成功")
        else:
            return 标准化API响应(结果.get("status") or 状态.团队邀请.邀请操作失败, None, 结果.get("message") or "处理邀请失败")
            
    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"处理邀请接口异常: {e}\n{错误详情}")
        return 标准化API响应(状态.团队邀请.系统错误, None, "处理邀请时发生服务器内部错误")

@团队邀请管理路由.get(
    "/detail",
    summary="获取邀请详情"
)
async def 获取邀请详情接口(
    token: str,
    当前登录用户: Optional[dict] = Depends(获取当前用户_可选)
):
    try:
        结果 = await 异步获取邀请详情(token, 当前登录用户)
        if 结果["success"]:
            return 标准化API响应(100, 结果["data"])
        else:
            return 标准化API响应(结果.get("status") or 状态.团队邀请.邀请不存在, None, 结果["message"])
    except Exception as e:
        错误日志器.error(f"获取邀请详情接口异常: {e}", exc_info=True)
        return 标准化API响应(状态.团队邀请.系统错误, None, "获取邀请详情失败")

@团队邀请管理路由.post(
    "/generate-link",
    summary="生成注册邀请链接"
)
async def 生成注册邀请链接接口(
    请求数据: 注册邀请链接请求,
    当前用户: dict = Depends(获取当前用户)
):
    try:
        结果 = await 异步生成注册邀请链接(
            团队id=请求数据.团队id,
            手机号=请求数据.手机号,
            邀请人id=当前用户["id"],
            角色=请求数据.角色,
            权限列表=请求数据.权限列表,
            有效期天数=请求数据.有效期天数
        )
        if 结果["success"]:
            return 标准化API响应(100, 结果["data"], 结果["message"])
        else:
            return 标准化API响应(结果.get("status") or 状态.团队邀请.生成链接失败, None, 结果["message"])
    except Exception as e:
        错误日志器.error(f"生成注册邀请链接接口异常: {e}", exc_info=True)
        return 标准化API响应(状态.团队邀请.系统错误, None, "生成邀请链接失败")

@团队邀请管理路由.post("/revoke", summary="撤销邀请")
async def 撤销团队邀请接口(
    请求数据: 撤销邀请请求,
    当前用户: dict = Depends(获取当前用户)
):
    try:
        结果 = await 异步撤销邀请_通过ID(
            邀请id=请求数据.邀请id,
            操作人ID=当前用户["id"]
        )
        if 结果["success"]:
            return 标准化API响应(100, None, 结果["message"])
        else:
            return 标准化API响应(状态.团队邀请.撤销失败, None, 结果["message"])
    except Exception as e:
        错误日志器.error(f"撤销邀请失败: {e}")
        return 标准化API响应(状态.团队邀请.系统错误, None, "撤销邀请失败")

@团队邀请管理路由.post("/resend", summary="重新发送邀请")
async def 重新发送团队邀请接口(
    请求数据: 重发邀请请求,
    当前用户: dict = Depends(获取当前用户)
):
    try:
        结果 = await 异步重新发送邀请_通过ID(
            邀请id=请求数据.邀请id,
            操作人ID=当前用户["id"]
        )
        if 结果["success"]:
            return 标准化API响应(100, 结果.get("data"), 结果["message"])
        else:
            return 标准化API响应(状态.团队邀请.重发失败, None, 结果["message"])
    except Exception as e:
        错误日志器.error(f"重发邀请失败: {e}")
        return 标准化API响应(状态.团队邀请.系统错误, None, "重发邀请失败")