<template>
  <div class="basic-config">
    <div class="section-header">
      <h2><UserOutlined /> 基础信息配置</h2>
      <p>设置智能体的基本信息</p>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <a-card title="基本信息" class="config-card">
        <a-form-item 
          label="智能体名称" 
          required
          :validate-status="errors.智能体名称 ? 'error' : ''"
          :help="errors.智能体名称"
        >
          <a-input 
            v-model:value="localForm.智能体名称"
            placeholder="请输入智能体名称"
            :maxlength="50"
            show-count
            @blur="validateField('智能体名称')"
            @change="handleFormChange"
          />
        </a-form-item>
        
        <a-form-item 
          label="智能体描述"
          :validate-status="errors.智能体描述 ? 'error' : ''"
          :help="errors.智能体描述"
        >
          <a-textarea 
            v-model:value="localForm.智能体描述"
            placeholder="简要描述智能体的功能和用途"
            :rows="3"
            :maxlength="200"
            show-count
            @blur="validateField('智能体描述')"
            @change="handleFormChange"
          />
        </a-form-item>

        <!-- 智能体类型字段已移除 -->

        <!-- 智能体状态 -->
        <a-form-item label="智能体状态" v-if="isEditMode">
          <a-switch
            v-model:checked="localForm.是否启用"
            checked-children="启用"
            un-checked-children="禁用"
            @change="handleFormChange"
          />
          <span class="status-description">
            {{ localForm.是否启用 ? '智能体当前可用' : '智能体已禁用' }}
          </span>
        </a-form-item>

        <!-- 配置预览 -->
        <a-form-item label="配置预览" v-if="showPreview">
          <div class="config-preview">
            <a-descriptions size="small" :column="1">
              <a-descriptions-item label="名称">
                {{ localForm.智能体名称 || '未设置' }}
              </a-descriptions-item>
              <a-descriptions-item label="描述">
                {{ localForm.智能体描述 || '未设置' }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-form-item>
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { UserOutlined } from '@ant-design/icons-vue'
import { useFormValidation } from '../../composables/useFormValidation'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  showPreview: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 表单验证
const { 验证智能体名称, 验证智能体描述 } = useFormValidation()

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 智能体类型标签映射已移除

// 验证单个字段
const validateField = (fieldName) => {
  let error = null
  
  switch (fieldName) {
    case '智能体名称':
      error = 验证智能体名称(localForm.value.智能体名称)
      break
    case '智能体描述':
      error = 验证智能体描述(localForm.value.智能体描述)
      break
  }
  
  emit('validate', { [fieldName]: error })
}

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}

// 监听表单变化，自动验证
watch(() => localForm.value.智能体名称, () => {
  if (props.errors.智能体名称) {
    validateField('智能体名称')
  }
})

watch(() => localForm.value.智能体描述, () => {
  if (props.errors.智能体描述) {
    validateField('智能体描述')
  }
})
</script>

<style scoped>
.basic-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: 600px;
}

.config-card {
  margin-bottom: 16px;
}

.config-card :deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

.config-card :deep(.ant-card-body) {
  padding: 24px;
}

.status-description {
  margin-left: 12px;
  color: #8c8c8c;
  font-size: 12px;
}

.config-preview {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
}

.config-preview :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #595959;
}

.config-preview :deep(.ant-descriptions-item-content) {
  color: #262626;
}
</style>
