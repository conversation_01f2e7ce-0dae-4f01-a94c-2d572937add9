/**
 * CRM System Stress Test Runner
 *
 * Features:
 * - Automated test execution workflow
 * - Integrated monitoring and report generation
 * - Safety checks and risk control
 * - Test result analysis
 */

const { spawn, exec } = require('child_process')
const { promisify } = require('util')
const fs = require('fs').promises
const path = require('path')

// 简化配置，避免ES模块导入问题
const testConfig = {
  baseUrl: 'https://invite.limob.cn',
  safetyLimits: {
    maxConcurrentUsers: 100,
    maxTestDuration: 600,
    cpuThreshold: 80,
    memoryThreshold: 85
  }
}

const execAsync = promisify(exec)

class TestRunner {
  constructor() {
    this.testProcess = null
    this.results = {
      startTime: null,
      endTime: null,
      duration: 0,
      success: false,
      errors: [],
      metrics: {}
    }
  }

  /**
   * Execute stress test
   */
  async runStressTest(testType = 'standard') {
    console.log('🚀 Starting CRM System Stress Test')
    console.log(`📋 Test Type: ${testType}`)
    
    try {
      // Pre-test checks
      await this.preTestChecks()
      
      // Start monitoring
      this.startMonitoring()
      
      // Execute test
      await this.executeTest(testType)
      
      // Generate report
      await this.generateReport()
      
      console.log('✅ Stress test completed')
      
    } catch (error) {
      console.error('❌ Stress test failed:', error.message)
      this.results.errors.push(error.message)
      this.results.success = false
    } finally {
      // Cleanup resources
      await this.cleanup()
    }
  }

  /**
   * Pre-test checks
   */
  async preTestChecks() {
    console.log('🔍 Performing pre-test checks...')
    
    // Check test tools installation
    await this.checkTestTools()
    
    // Check network connection
    await this.checkNetworkConnection()
    
    // Check system resources
    await this.checkSystemResources()
    
    // Check test time window
    this.checkTestTimeWindow()
    
    console.log('✅ Pre-test checks passed')
  }

  /**
   * Check test tools
   */
  async checkTestTools() {
    try {
      await execAsync('k6 version')
      console.log('✅ K6 tool check passed')
    } catch (error) {
      throw new Error('K6 tool not installed, please run: npm install -g k6')
    }
  }

  /**
   * Check network connection
   */
  async checkNetworkConnection() {
    try {
      const response = await fetch(`${testConfig.baseUrl}/health`, {
        method: 'GET',
        timeout: 5000
      })
      
      if (response.ok) {
        console.log('✅ Network connection check passed')
      } else {
        throw new Error(`Server response abnormal: ${response.status}`)
      }
    } catch (error) {
      throw new Error(`Unable to connect to target server: ${error.message}`)
    }
  }

  /**
   * Check system resources
   */
  async checkSystemResources() {
    const metrics = await this.monitor.collectMetrics()
    
    if (metrics.cpu > 70) {
      throw new Error(`Current CPU usage too high: ${metrics.cpu}%, not suitable for stress testing`)
    }
    
    if (metrics.memory > 80) {
      throw new Error(`Current memory usage too high: ${metrics.memory}%, not suitable for stress testing`)
    }
    
    console.log('✅ System resource check passed')
  }

  /**
   * Check test time window
   */
  checkTestTimeWindow() {
    const now = new Date()
    const hour = now.getHours()
    
    if (!testConfig.environment.allowedHours.includes(hour)) {
      throw new Error(`Current time (${hour}:00) is not within allowed test time window`)
    }
    
    if (testConfig.environment.skipWeekends) {
      const day = now.getDay()
      if (day === 0 || day === 6) {
        throw new Error('Weekend stress testing is not allowed')
      }
    }
    
    console.log('✅ Test time window check passed')
  }

  /**
   * Start monitoring
   */
  startMonitoring() {
    console.log('🔍 Starting system monitoring...')
    
    // Start monitoring in background
    setTimeout(() => {
      this.monitor.startMonitoring()
    }, 1000)
  }

  /**
   * Execute test
   */
  async executeTest(testType) {
    this.results.startTime = new Date()
    console.log(`🎯 Starting ${testType} stress test...`)
    
    return new Promise((resolve, reject) => {
      // Build K6 command
      const k6Command = this.buildK6Command(testType)
      
      // Start K6 process
      this.testProcess = spawn('k6', k6Command, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      })
      
      let output = ''
      let errorOutput = ''
      
      // Collect output
      this.testProcess.stdout.on('data', (data) => {
        const text = data.toString()
        output += text
        console.log(text.trim())
      })
      
      this.testProcess.stderr.on('data', (data) => {
        const text = data.toString()
        errorOutput += text
        console.error(text.trim())
      })
      
      // Handle process end
      this.testProcess.on('close', (code) => {
        this.results.endTime = new Date()
        this.results.duration = this.results.endTime - this.results.startTime
        
        if (code === 0) {
          console.log('✅ K6 test execution completed')
          this.results.success = true
          this.parseK6Output(output)
          resolve()
        } else {
          console.error(`❌ K6 test failed, exit code: ${code}`)
          this.results.errors.push(`K6 process abnormal exit: ${code}`)
          this.results.errors.push(errorOutput)
          reject(new Error(`K6 test failed: ${code}`))
        }
      })
      
      // Handle process error
      this.testProcess.on('error', (error) => {
        console.error('❌ K6 process startup failed:', error.message)
        reject(error)
      })
    })
  }

  /**
   * Build K6 command
   */
  buildK6Command(testType) {
    const scriptPath = path.join('tests/k6/crm-stress-test.js')
    const outputPath = path.join('tests/results', `k6-result-${Date.now()}.json`)
    
    return [
      'run',
      '--out', `json=${outputPath}`,
      '--summary-export', outputPath.replace('.json', '-summary.json'),
      '--tag', `testType=${testType}`,
      '--tag', `timestamp=${Date.now()}`,
      scriptPath
    ]
  }

  /**
   * Parse K6 output
   */
  parseK6Output(output) {
    try {
      // Extract key metrics
      const lines = output.split('\n')
      
      lines.forEach(line => {
        // Parse response time
        if (line.includes('http_req_duration')) {
          const match = line.match(/avg=([0-9.]+)ms.*p\(95\)=([0-9.]+)ms/)
          if (match) {
            this.results.metrics.avgResponseTime = parseFloat(match[1])
            this.results.metrics.p95ResponseTime = parseFloat(match[2])
          }
        }
        
        // Parse request rate
        if (line.includes('http_reqs')) {
          const match = line.match(/([0-9.]+)\/s/)
          if (match) {
            this.results.metrics.requestRate = parseFloat(match[1])
          }
        }
        
        // Parse error rate
        if (line.includes('http_req_failed')) {
          const match = line.match(/([0-9.]+)%/)
          if (match) {
            this.results.metrics.errorRate = parseFloat(match[1])
          }
        }
      })
      
      console.log('📊 Test metrics parsing completed')
    } catch (error) {
      console.warn('⚠️  Failed to parse K6 output:', error.message)
    }
  }

  /**
   * Generate test report
   */
  async generateReport() {
    console.log('📊 Generating test report...')
    
    // Get monitoring report
    const monitorReport = await this.monitor.generateReport()
    
    // Merge test results
    const fullReport = {
      testInfo: {
        type: 'CRM Stress Test',
        timestamp: new Date().toISOString(),
        duration: this.results.duration,
        success: this.results.success
      },
      performance: this.results.metrics,
      system: monitorReport.summary,
      alerts: monitorReport.alerts,
      errors: this.results.errors
    }
    
    // Save report
    const reportPath = path.join('tests/results', `test-report-${Date.now()}.json`)
    await fs.writeFile(reportPath, JSON.stringify(fullReport, null, 2))
    
    // Output summary
    this.printSummary(fullReport)
    
    console.log(`📄 Detailed report saved: ${reportPath}`)
  }

  /**
   * Print test summary
   */
  printSummary(report) {
    console.log('\n📋 Test Summary:')
    console.log(`   ⏱️  Test Duration: ${Math.round(report.testInfo.duration / 1000)} seconds`)
    console.log(`   ✅ Test Status: ${report.testInfo.success ? 'Success' : 'Failed'}`)
    
    if (report.performance.avgResponseTime) {
      console.log(`   📈 Average Response Time: ${report.performance.avgResponseTime}ms`)
    }
    
    if (report.performance.requestRate) {
      console.log(`   🚀 Request Rate: ${report.performance.requestRate} req/s`)
    }
    
    if (report.performance.errorRate !== undefined) {
      console.log(`   ❌ Error Rate: ${report.performance.errorRate}%`)
    }
    
    console.log(`   🔍 System Alerts: ${report.alerts.length} times`)
    console.log(`   💻 Max CPU: ${report.system.maxCpu}%`)
    console.log(`   🧠 Max Memory: ${report.system.maxMemory}%`)
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log('🧹 Cleaning up test resources...')
    
    // Stop monitoring
    this.monitor.stopMonitoring()
    
    // Stop test process
    if (this.testProcess && !this.testProcess.killed) {
      this.testProcess.kill('SIGTERM')
    }
    
    console.log('✅ Resource cleanup completed')
  }
}

// Export test runner
export default TestRunner

// Command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const testType = process.argv[2] || 'standard'
  const runner = new TestRunner()
  
  // Handle exit signals
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received interrupt signal, safely exiting...')
    await runner.cleanup()
    process.exit(0)
  })
  
  // Execute test
  runner.runStressTest(testType)
    .then(() => {
      console.log('🎉 Test execution completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Test execution failed:', error.message)
      process.exit(1)
    })
}
