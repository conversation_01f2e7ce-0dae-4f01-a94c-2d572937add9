<template>
  <a-modal
    :open="open"
    title="邮箱绑定"
    @cancel="handleCancel"
    @update:open="$emit('update:open', $event)"
  >
    <a-empty description="邮箱绑定功能开发中..." />
  </a-modal>
</template>

<script setup>
defineOptions({
  name: 'EmailBindModal'
})

const props = defineProps({
  open: Boolean,
  currentEmail: String
})

const emit = defineEmits(['update:open', 'success'])

const handleCancel = () => {
  emit('update:open', false)
}
</script>