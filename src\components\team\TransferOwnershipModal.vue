<template>
  <a-modal
    v-model:open="visible"
    title="转移团队所有权"
    :ok-button-props="{ disabled: !selectedMember || loading }"
    :cancel-button-props="{ disabled: loading }"
    :confirm-loading="loading"
    ok-text="确认转移"
    cancel-text="取消"
    width="600px"
    @ok="handleTransfer"
    @cancel="handleCancel"
  >
    <!-- 警告提示 -->
    <a-alert
      message="重要提醒"
      description="转移所有权后，您将失去对团队的管理权限，此操作不可撤销！"
      type="warning"
      show-icon
      style="margin-bottom: 16px"
    />

    <!-- 团队信息 -->
    <div class="team-info" style="margin-bottom: 20px">
      <h4>{{ teamInfo?.团队名称 }}</h4>
      <p style="color: #666; margin: 0;">
        当前成员数：{{ teamInfo?.当前成员数 || 0 }} 人
      </p>
    </div>

    <!-- 成员选择 -->
    <div class="member-selection">
      <h4 style="margin-bottom: 12px">选择新的团队所有者：</h4>
      
      <!-- 如果没有可选成员 -->
      <a-empty 
        v-if="!availableMembers || availableMembers.length === 0"
        description="暂无其他成员可选择"
        style="margin: 20px 0"
      />
      
      <!-- 成员列表 -->
      <div v-else class="members-list">
        <a-radio-group v-model:value="selectedMember" class="full-width">
          <div
            v-for="member in availableMembers"
            :key="member.用户id"
            class="member-item"
          >
            <a-radio :value="member.用户id" class="member-radio">
              <div class="member-info">
                <div class="member-basic">
                  <span class="member-name">{{ member.用户名 }}</span>
                  <a-tag
                    v-if="member.角色"
                    :color="getRoleColor(member.角色)"
                    size="small"
                  >
                    {{ member.角色 }}
                  </a-tag>
                </div>
                <div class="member-detail">
                  <span v-if="member.加入时间" class="join-time">
                    加入时间：{{ formatDate(member.加入时间) }}
                  </span>
                </div>
              </div>
            </a-radio>
          </div>
        </a-radio-group>
      </div>
    </div>

    <!-- 转移后退出选项 -->
    <div class="transfer-options" style="margin-top: 20px">
      <a-checkbox v-model:checked="transferAndLeave">
        转移所有权后立即退出团队
      </a-checkbox>
      <div style="color: #666; font-size: 12px; margin-top: 4px">
        勾选此项，转移成功后您将自动退出团队
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { teamBasicService } from '@/services/team/teamBasic.js'

// 组件属性定义
const props = defineProps({
  /**
   * 模态框显示状态
   */
  modelValue: {
    type: Boolean,
    default: false
  },
  /**
   * 团队信息
   */
  teamInfo: {
    type: Object,
    default: () => ({})
  },
  /**
   * 可转移的成员列表
   */
  availableMembers: {
    type: Array,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'success', 'cancel'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false) // 加载状态
const selectedMember = ref(null) // 选中的成员id
const transferAndLeave = ref(false) // 是否转移后退出

// 监听模态框打开，重置选择状态
watch(visible, (newValue) => {
  if (newValue) {
    selectedMember.value = null
    transferAndLeave.value = false
  }
})

/**
 * 获取角色颜色
 * @param {string} role - 角色名称
 * @returns {string} 颜色值
 */
const getRoleColor = (role) => {
  const roleColors = {
    '团队负责人': 'blue',
    '负责人': 'blue',
    '成员': 'default',
    '管理员': 'purple'
  }
  return roleColors[role] || 'default'
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    return new Date(dateString).toLocaleDateString('zh-CN')
  } catch {
    return dateString
  }
}

/**
 * 处理转移操作
 */
const handleTransfer = async () => {
  if (!selectedMember.value) {
    message.warning('请选择新的团队所有者')
    return
  }

  if (!props.teamInfo?.id) {
    message.error('团队信息异常，无法转移')
    return
  }

  try {
    loading.value = true

    // 调用转移所有权接口
    const response = await teamBasicService.transferOwnership({
      团队id: props.teamInfo.id,
      新所有者用户id: selectedMember.value,
      转移后退出: transferAndLeave.value
    })

    if (response.status === 100) {
      message.success(response.message || '所有权转移成功')
      
      // 发送成功事件，传递转移结果数据
      emit('success', {
        action: response.data?.action,
        newOwnerId: selectedMember.value,
        transferAndLeave: transferAndLeave.value,
        teamName: props.teamInfo.团队名称
      })
      
      // 关闭模态框
      visible.value = false
    } else {
      throw new Error(response.message || '转移失败')
    }
  } catch (error) {
    console.error('转移所有权失败:', error)
    message.error('转移所有权失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

/**
 * 处理取消操作
 */
const handleCancel = () => {
  emit('cancel')
  visible.value = false
}
</script>

<style scoped>
/* 团队信息样式 */
.team-info h4 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

/* 成员选择区域样式 */
.member-selection {
  max-height: 300px;
  overflow-y: auto;
}

.members-list {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px 0;
}

.member-item {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-item:hover {
  background-color: #fafafa;
}

/* 成员单选框样式 */
.member-radio {
  width: 100%;
  align-items: flex-start;
}

.member-radio .ant-radio {
  margin-top: 2px;
}

/* 成员信息样式 */
.member-info {
  margin-left: 8px;
  flex: 1;
}

.member-basic {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.member-name {
  font-weight: 500;
  color: #262626;
}

.member-detail {
  color: #8c8c8c;
  font-size: 12px;
}

.join-time {
  font-size: 12px;
}

/* 转移选项样式 */
.transfer-options {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e6f7ff;
}

/* 工具类 */
.full-width {
  width: 100%;
}
</style> 