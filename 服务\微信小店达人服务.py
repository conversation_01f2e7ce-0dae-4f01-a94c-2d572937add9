"""
微信达人管理服务层 - 整合微信小店达人和微信达人功能
统一处理微信平台达人的业务逻辑

功能概述：
- 微信小店达人管理
- 微信达人公海列表
- 达人认领和取消认领
- 联系方式管理
- 统一的错误处理和日志记录

作者: CRM系统开发团队
"""

from typing import Dict, Optional, Any

# 微信小店达人数据操作
from 数据.微信小店达人数据 import (
    写入或更新微信小店达人,
    根据finderUsername获取微信小店达人,
    获取或创建联系方式,
    关联微信小店达人与联系方式,
    检查用户是否已认领微信小店达人,
    为用户认领微信小店达人,
    获取微信小店达人公海列表,
    获取用户认领的微信小店达人列表,
    根据id获取微信小店达人,
    获取微信小店达人关联的联系方式,
    取消认领微信小店达人
)

# 微信达人数据操作
from 数据.微信达人数据 import (
    异步获取微信达人列表, 异步获取微信达人详情,
    异步认领微信达人, 异步取消认领微信达人, 异步获取用户认领微信达人列表, 异步创建或更新微信达人
)

from 数据模型.微信小店模型 import (
    微信小店达人写入模型,
    提交微信达人联系方式模型,
    分页请求模型,
    微信达人公海筛选模型,
    微信达人操作模型
)
from 日志.日志配置 import logger
from 日志 import 错误日志器, 接口日志器

async def 服务_写入或更新微信小店达人(达人数据: 微信小店达人写入模型):
    """
    服务层：处理写入或更新微信小店达人的业务逻辑。
    - 成功时返回统一的成功响应。
    - 失败时返回具体的业务错误码和信息。
    """
    try:
        # 调用数据层函数执行数据库操作
        result = await 写入或更新微信小店达人(达人数据)
        
        # 成功的响应
        return {"status": 100, "message": "OK", "data": result}

    except ValueError as e:
        # 捕获数据校验错误
        logger.warning(f"写入微信小店达人失败，数据校验错误: {e}")
        return {"status": 10011, "message": "您提交的数据不符合要求，请检查后重试。", "data": None}
        
    except IOError as e:
        # 捕获数据库IO错误
        logger.error(f"写入微信小店达人时发生服务层错误: {e}")
        return {"status": 10012, "message": "系统繁忙，保存达人信息失败，请稍后重试。", "data": None}
        
    except Exception as e:
        # 捕获其他所有未知异常
        logger.exception(f"处理微信小店达人写入时发生未知异常: {e}")
        return {"status": 10013, "message": "服务器发生未知错误", "data": None}

async def 服务_提交微信小店达人联系方式(联系方式数据: 提交微信达人联系方式模型, 用户id: int):
    """
    服务层：处理用户提交微信达人联系方式的业务逻辑。
    """
    try:
        # 1. 根据finderUsername查找达人
        达人 = await 根据finderUsername获取微信小店达人(联系方式数据.finderUsername)
        if not 达人:
            return {"status": 404, "message": "未找到指定的达人"}

        微信达人id = 达人['id']
        processed_contacts = []

        # 2. 处理电话
        if 联系方式数据.电话:
            电话ID = await 获取或创建联系方式(联系方式数据.电话, "电话")
            await 关联微信小店达人与联系方式(微信达人id, 电话ID)
            processed_contacts.append("电话")

        # 3. 处理微信
        if 联系方式数据.微信:
            微信id = await 获取或创建联系方式(联系方式数据.微信, "微信")
            await 关联微信小店达人与联系方式(微信达人id, 微信id)
            processed_contacts.append("微信")

        if not processed_contacts:
            return {"status": 400, "message": "请至少提供一种联系方式（电话或微信）"}

        # 4. 检查并为用户认领达人
        已认领 = await 检查用户是否已认领微信小店达人(用户id, 微信达人id)
        if not 已认领:
            await 为用户认领微信小店达人(用户id, 微信达人id)
            claim_status = "首次认领成功"
        else:
            claim_status = "您已认领过该达人"

        return {
            "status": 100,
            "message": "联系方式处理成功",
            "data": {
                "processed_contacts": processed_contacts,
                "claim_status": claim_status
            }
        }

    except Exception as e:
        logger.exception(f"提交达人联系方式时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_获取微信小店达人公海(筛选条件: 微信达人公海筛选模型):
    """服务层：获取未被认领的微信小店达人列表。"""
    try:
        records = await 获取微信小店达人公海列表(筛选条件)
        return {
            "status": 100,
            "message": "OK",
            "data": {"list": records}
        }
    except Exception as e:
        logger.exception(f"获取达人公海列表时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_获取用户认领的微信小店达人列表(用户id: int, 分页: 分页请求模型):
    """服务层：获取当前用户认领的达人列表。"""
    try:
        records, total = await 获取用户认领的微信小店达人列表(用户id, 分页.页码, 分页.每页条数)
        return {
            "status": 100,
            "message": "OK",
            "data": {"list": records, "total": total, "page": 分页.页码, "pageSize": 分页.每页条数}
        }
    except Exception as e:
        logger.exception(f"获取用户认领的达人列表时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_获取微信小店达人详情(操作数据: 微信达人操作模型):
    """服务层：获取单个微信小店达人的详细信息。"""
    try:
        达人信息 = None
        if 操作数据.id:
            达人信息 = await 根据id获取微信小店达人(操作数据.id)
        elif 操作数据.finderUsername:
            达人信息 = await 根据finderUsername获取微信小店达人(操作数据.finderUsername)

        if not 达人信息:
            return {"status": 404, "message": "未找到指定的达人"}

        联系方式 = await 获取微信小店达人关联的联系方式(达人信息['id'])
        达人信息['联系方式'] = 联系方式

        return {"status": 100, "message": "OK", "data": 达人信息}
    except Exception as e:
        logger.exception(f"获取达人详情时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_认领微信小店达人(操作数据: 微信达人操作模型, 用户id: int):
    """服务层：认领一个微信小店达人。"""
    try:
        达人信息 = None
        if 操作数据.id:
            达人信息 = await 根据id获取微信小店达人(操作数据.id)
        elif 操作数据.finderUsername:
            达人信息 = await 根据finderUsername获取微信小店达人(操作数据.finderUsername)

        if not 达人信息:
            return {"status": 404, "message": "未找到指定的达人"}

        微信达人id = 达人信息['id']
        已认领 = await 检查用户是否已认领微信小店达人(用户id, 微信达人id)
        if 已认领:
            return {"status": 400, "message": "您已经认领过该达人，请勿重复操作"}

        await 为用户认领微信小店达人(用户id, 微信达人id)
        return {"status": 100, "message": "认领成功"}
    except Exception as e:
        logger.exception(f"认领达人时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}

async def 服务_取消认领微信小店达人(操作数据: 微信达人操作模型, 用户id: int):
    """服务层：取消认领一个微信小店达人。"""
    try:
        达人信息 = None
        if 操作数据.id:
            达人信息 = await 根据id获取微信小店达人(操作数据.id)
        elif 操作数据.finderUsername:
            达人信息 = await 根据finderUsername获取微信小店达人(操作数据.finderUsername)

        if not 达人信息:
            return {"status": 404, "message": "未找到指定的达人"}

        微信达人id = 达人信息['id']
        rows_affected = await 取消认领微信小店达人(用户id, 微信达人id)

        if rows_affected > 0:
            return {"status": 100, "message": "取消认领成功"}
        else:
            return {"status": 400, "message": "您尚未认领该达人或操作失败"}
    except Exception as e:
        logger.exception(f"取消认领达人时发生未知异常: {e}")
        return {"status": 500, "message": "服务器内部错误"}


# ==================== 微信达人公海功能 ====================

async def 获取微信达人公海列表(
    页码: int = 1,
    每页数量: int = 20,
    最后id: int = 0,
    筛选条件: Optional[Dict[str, Any]] = None,
    有联系方式: Optional[bool] = None,
    关键词: Optional[str] = None,
    当前用户id: Optional[int] = None,
    当前团队id: Optional[int] = None
) -> Dict[str, Any]:
    """
    获取微信达人公海列表的服务

    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        最后id: 上一页最后的达人id，默认为0
        筛选条件: 可选的筛选条件，如地区、性别等
        有联系方式: 可选，筛选是否有联系方式的达人
        关键词: 可选，通过微信号、昵称搜索达人
        当前用户id: 可选，当前用户id，用于检查达人是否被当前用户认领
        当前团队id: 可选，当前团队id，用于团队维度的认领状态判断

    返回:
        包含分页信息和微信达人列表的字典
    """
    try:
        接口日志器.info(f"获取微信达人公海列表 - 页码: {页码}, 每页数量: {每页数量}, 最后id: {最后id}")

        # 调用数据层获取达人列表
        结果 = await 异步获取微信达人列表(
            页码=页码,
            每页数量=每页数量,
            最后id=最后id,
            筛选条件=筛选条件,
            有联系方式=有联系方式,
            关键词=关键词,
            当前用户id=当前用户id,
            当前团队id=当前团队id
        )

        接口日志器.info(f"微信达人公海列表获取成功 - 返回 {len(结果.get('达人列表', []))} 条记录")
        return 结果

    except Exception as e:
        错误日志器.error(f"获取微信达人公海列表失败: {str(e)}")
        raise


async def 获取微信达人详情(达人id: int, 用户id: int) -> Dict[str, Any]:
    """
    获取微信达人详情信息

    参数:
        达人id: 达人的唯一标识ID
        用户id: 当前用户id

    返回:
        包含达人详情、联系方式和认领状态的字典
    """
    try:
        接口日志器.info(f"获取微信达人详情 - 达人id: {达人id}, 用户id: {用户id}")

        # 调用数据层获取达人详情
        达人详情 = await 异步获取微信达人详情(达人id, 用户id)

        if not 达人详情:
            raise ValueError(f"微信达人 {达人id} 不存在")

        接口日志器.info(f"微信达人详情获取成功 - 达人id: {达人id}")
        return 达人详情

    except Exception as e:
        错误日志器.error(f"获取微信达人详情失败: {str(e)}")
        raise


async def 认领微信达人(达人id: int, 用户id: int, 团队id: Optional[int] = None) -> Dict[str, Any]:
    """
    认领微信达人

    参数:
        达人id: 要认领的达人id
        用户id: 认领用户id
        团队id: 可选，团队id

    返回:
        认领操作结果
    """
    try:
        接口日志器.info(f"认领微信达人 - 达人id: {达人id}, 用户id: {用户id}, 团队id: {团队id}")

        # 调用数据层执行认领操作
        成功 = await 异步认领微信达人(达人id, 用户id)

        if 成功:
            接口日志器.info(f"微信达人认领成功 - 达人id: {达人id}, 用户id: {用户id}")
            return {
                "状态": "成功",
                "消息": "微信达人认领成功",
                "达人id": 达人id
            }
        else:
            raise Exception("认领操作失败")

    except ValueError as ve:
        # 业务逻辑错误，如达人不存在、已被认领等
        错误日志器.warning(f"微信达人认领失败 - 业务错误: {str(ve)}")
        return {
            "状态": "失败",
            "消息": str(ve),
            "达人id": 达人id
        }
    except Exception as e:
        错误日志器.error(f"微信达人认领失败: {str(e)}")
        raise


async def 取消认领微信达人(达人id: int, 用户id: int) -> Dict[str, Any]:
    """
    取消认领微信达人

    参数:
        达人id: 要取消认领的达人id
        用户id: 用户id

    返回:
        取消认领操作结果
    """
    try:
        接口日志器.info(f"取消认领微信达人 - 达人id: {达人id}, 用户id: {用户id}")

        # 调用数据层执行取消认领操作
        成功 = await 异步取消认领微信达人(达人id, 用户id)

        if 成功:
            接口日志器.info(f"微信达人取消认领成功 - 达人id: {达人id}, 用户id: {用户id}")
            return {
                "状态": "成功",
                "消息": "微信达人取消认领成功",
                "达人id": 达人id
            }
        else:
            raise Exception("取消认领操作失败")

    except ValueError as ve:
        # 业务逻辑错误，如未认领过等
        错误日志器.warning(f"微信达人取消认领失败 - 业务错误: {str(ve)}")
        return {
            "状态": "失败",
            "消息": str(ve),
            "达人id": 达人id
        }
    except Exception as e:
        错误日志器.error(f"微信达人取消认领失败: {str(e)}")
        raise


async def 搜索用户认领微信达人列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "认领时间",
    排序方式: str = "desc",
    筛选条件: Optional[Dict[str, Any]] = None,
    关键词: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取用户认领的微信达人列表

    参数:
        用户id: 用户id
        页码: 当前页码
        每页数量: 每页显示数量
        排序字段: 排序字段
        排序方式: 排序方式
        筛选条件: 筛选条件
        关键词: 搜索关键词

    返回:
        包含分页信息和达人列表的字典
    """
    try:
        接口日志器.info(f"获取用户认领微信达人列表 - 用户id: {用户id}, 页码: {页码}")

        # 调用数据层获取用户认领的达人列表
        结果 = await 异步获取用户认领微信达人列表(
            用户id=用户id,
            页码=页码,
            每页数量=每页数量,
            排序字段=排序字段,
            排序方式=排序方式,
            筛选条件=筛选条件,
            关键词=关键词
        )

        接口日志器.info(f"用户认领微信达人列表获取成功 - 用户id: {用户id}, 返回 {len(结果.get('达人列表', []))} 条记录")
        return 结果

    except Exception as e:
        错误日志器.error(f"获取用户认领微信达人列表失败: {str(e)}")
        raise


async def 查询或更新微信达人(微信号: str, 达人数据: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    通过微信号查询或创建微信达人

    参数:
        微信号: 微信号
        达人数据: 可选，达人数据用于创建或更新

    返回:
        字典，包含操作结果信息和达人id
    """
    try:
        接口日志器.info(f"查询或更新微信达人 - 微信号: {微信号}")

        # 参数校验
        if not 微信号 or not 微信号.strip():
            raise ValueError("微信号不能为空")

        # 如果没有提供达人数据，使用默认数据
        if not 达人数据:
            达人数据 = {
                "昵称": None,
                "头像": None,
                "个人简介": None,
                "地区": None,
                "性别": None,
                "账号状态": "正常",
                "好友数": 0,
                "朋友圈发布数": 0
            }

        # 调用数据层创建或更新达人
        达人id = await 异步创建或更新微信达人(微信号.strip(), 达人数据)

        接口日志器.info(f"微信达人查询或更新成功 - 微信号: {微信号}, 达人id: {达人id}")

        return {
            "状态": "成功",
            "消息": "微信达人处理成功",
            "达人id": 达人id,
            "微信号": 微信号.strip()
        }

    except ValueError as ve:
        错误日志器.warning(f"微信达人查询或更新失败 - 参数错误: {str(ve)}")
        return {
            "状态": "失败",
            "消息": str(ve),
            "达人id": None
        }
    except Exception as e:
        错误日志器.error(f"微信达人查询或更新失败: {str(e)}")
        return {
            "状态": "失败",
            "消息": f"系统错误: {str(e)}",
            "达人id": None
        }