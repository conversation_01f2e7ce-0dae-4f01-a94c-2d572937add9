import { message, notification } from 'ant-design-vue'

/**
 * 统一错误处理机制
 * 提供标准化的错误处理和用户提示系统
 * 
 * 功能特性：
 * - 统一的错误分类和处理
 * - 用户友好的错误提示
 * - 错误日志记录
 * - 可配置的错误处理策略
 */

/**
 * 错误类型枚举
 */
export const 错误类型 = {
  网络错误: 'NETWORK_ERROR',
  认证错误: 'AUTH_ERROR',
  权限错误: 'PERMISSION_ERROR',
  业务错误: 'BUSINESS_ERROR',
  验证错误: 'VALIDATION_ERROR',
  服务器错误: 'SERVER_ERROR',
  未知错误: 'UNKNOWN_ERROR'
}

/**
 * 错误严重程度枚举
 */
export const 错误严重程度 = {
  信息: 'info',
  警告: 'warning',
  错误: 'error',
  严重: 'critical'
}

/**
 * 错误处理配置
 */
const 错误处理配置 = {
  // 是否显示详细错误信息（开发环境）
  显示详细信息: import.meta.env.DEV,
  // 是否自动上报错误
  自动上报错误: !import.meta.env.DEV,
  // 错误提示持续时间
  提示持续时间: {
    [错误严重程度.信息]: 3,
    [错误严重程度.警告]: 5,
    [错误严重程度.错误]: 8,
    [错误严重程度.严重]: 0 // 0表示不自动关闭
  }
}

/**
 * 标准化错误对象
 * @param {Error|Object} error - 原始错误对象
 * @param {string} context - 错误上下文
 * @returns {Object} 标准化的错误对象
 */
export const 标准化错误 = (error, context = '') => {
  const 标准错误 = {
    类型: 错误类型.未知错误,
    严重程度: 错误严重程度.错误,
    消息: '发生未知错误',
    详细信息: '',
    上下文: context,
    时间戳: new Date().toISOString(),
    原始错误: error
  }

  // 处理axios错误
  if (error?.response) {
    const { status, data } = error.response
    标准错误.详细信息 = `HTTP ${status}: ${error.config?.url || ''}`

    switch (status) {
      case 400:
        标准错误.类型 = 错误类型.验证错误
        标准错误.严重程度 = 错误严重程度.警告
        标准错误.消息 = data?.message || '请求参数错误'
        // 特殊处理激活相关的业务错误
        if (data?.status === 101) {
          标准错误.类型 = 错误类型.业务错误
          标准错误.消息 = data?.message || '激活失败'
          // 已被邀请等业务状态不视为系统错误
          if (data?.message?.includes('已经被邀请') || data?.message?.includes('今日邀请次数已达上限')) {
            标准错误.严重程度 = 错误严重程度.信息
          }
        }
        break
      case 401:
        标准错误.类型 = 错误类型.认证错误
        标准错误.严重程度 = 错误严重程度.错误
        标准错误.消息 = '登录已过期，请重新登录'
        break
      case 403:
        标准错误.类型 = 错误类型.权限错误
        标准错误.严重程度 = 错误严重程度.错误
        标准错误.消息 = '权限不足，无法访问'
        break
      case 404:
        标准错误.类型 = 错误类型.业务错误
        标准错误.严重程度 = 错误严重程度.警告
        标准错误.消息 = '请求的资源不存在'
        break
      case 422:
        标准错误.类型 = 错误类型.验证错误
        标准错误.严重程度 = 错误严重程度.警告
        标准错误.消息 = data?.message || '数据验证失败'
        break
      case 500:
        标准错误.类型 = 错误类型.服务器错误
        标准错误.严重程度 = 错误严重程度.严重
        标准错误.消息 = '服务器内部错误，请稍后重试'
        break
      case 502:
      case 503:
        标准错误.类型 = 错误类型.认证错误
        标准错误.严重程度 = 错误严重程度.错误
        标准错误.消息 = data?.message || '认证失败'
        break
      default:
        标准错误.消息 = data?.message || `请求失败 (${status})`
    }
  }
  // 处理网络错误
  else if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    标准错误.类型 = 错误类型.网络错误
    标准错误.严重程度 = 错误严重程度.警告
    标准错误.消息 = '请求超时，请检查网络连接'
    标准错误.详细信息 = `超时时间: ${error.config?.timeout || 'unknown'}ms`
  }
  else if (error?.code === 'ERR_NETWORK' || error?.message?.includes('Network Error')) {
    标准错误.类型 = 错误类型.网络错误
    标准错误.严重程度 = 错误严重程度.错误
    标准错误.消息 = '网络连接失败，请检查网络设置'
  }
  // 处理业务错误（后端返回的业务状态码）
  else if (error?.status && error?.message) {
    标准错误.类型 = 错误类型.业务错误
    标准错误.严重程度 = 错误严重程度.警告
    标准错误.消息 = error.message
    标准错误.详细信息 = `业务状态码: ${error.status}`
  }
  // 处理JavaScript错误
  else if (error instanceof Error) {
    标准错误.消息 = error.message
    标准错误.详细信息 = error.stack || ''
    
    // 根据错误类型进行分类
    if (error.name === 'TypeError') {
      标准错误.类型 = 错误类型.验证错误
      标准错误.严重程度 = 错误严重程度.警告
    } else if (error.name === 'ReferenceError') {
      标准错误.类型 = 错误类型.业务错误
      标准错误.严重程度 = 错误严重程度.错误
    }
  }
  // 处理字符串错误
  else if (typeof error === 'string') {
    标准错误.消息 = error
  }

  return 标准错误
}

/**
 * 显示错误提示
 * @param {Object} 标准错误对象 - 标准化的错误对象
 * @param {Object} options - 显示选项
 */
export const 显示错误提示 = (标准错误对象, options = {}) => {
  const {
    使用通知 = false,
    自定义消息 = '',
    显示详情按钮 = false
  } = options

  const 显示消息 = 自定义消息 || 标准错误对象.消息
  const 持续时间 = 错误处理配置.提示持续时间[标准错误对象.严重程度]

  if (使用通知) {
    // 使用通知组件显示
    const 通知配置 = {
      message: '操作失败',
      description: 显示消息,
      duration: 持续时间,
      placement: 'topRight'
    }

    // 根据严重程度选择通知类型
    switch (标准错误对象.严重程度) {
      case 错误严重程度.信息:
        notification.info(通知配置)
        break
      case 错误严重程度.警告:
        notification.warning(通知配置)
        break
      case 错误严重程度.错误:
        notification.error(通知配置)
        break
      case 错误严重程度.严重:
        notification.error({
          ...通知配置,
          message: '严重错误',
          duration: 0 // 不自动关闭
        })
        break
    }
  } else {
    // 使用消息组件显示
    switch (标准错误对象.严重程度) {
      case 错误严重程度.信息:
        message.info(显示消息, 持续时间)
        break
      case 错误严重程度.警告:
        message.warning(显示消息, 持续时间)
        break
      case 错误严重程度.错误:
        message.error(显示消息, 持续时间)
        break
      case 错误严重程度.严重:
        message.error(显示消息, 0) // 不自动关闭
        break
    }
  }
}

/**
 * 记录错误日志
 * @param {Object} 标准错误对象 - 标准化的错误对象
 */
export const 记录错误日志 = (标准错误对象) => {
  // 信息级别的内容不记录为错误日志
  if (标准错误对象.严重程度 === 错误严重程度.信息) {
    return
  }

  const 日志信息 = {
    时间: 标准错误对象.时间戳,
    类型: 标准错误对象.类型,
    严重程度: 标准错误对象.严重程度,
    消息: 标准错误对象.消息,
    上下文: 标准错误对象.上下文,
    详细信息: 标准错误对象.详细信息,
    用户代理: navigator.userAgent,
    页面URL: window.location.href
  }

  // 开发环境：输出到控制台
  if (错误处理配置.显示详细信息) {
    console.group(`🚨 错误日志 [${标准错误对象.严重程度.toUpperCase()}]`)
    console.error('错误消息:', 标准错误对象.消息)
    console.log('错误类型:', 标准错误对象.类型)
    console.log('上下文:', 标准错误对象.上下文)
    console.log('详细信息:', 标准错误对象.详细信息)
    console.log('原始错误:', 标准错误对象.原始错误)
    console.log('完整日志:', 日志信息)
    console.groupEnd()
  }

  // 生产环境：可以发送到错误监控服务
  if (错误处理配置.自动上报错误) {
    // 这里可以集成错误监控服务，如Sentry、LogRocket等
    // 示例：sendToErrorService(日志信息)
    console.log('📊 错误已记录到监控系统:', 日志信息)
  }
}

/**
 * 统一错误处理函数
 * @param {Error|Object} error - 原始错误
 * @param {string} context - 错误上下文
 * @param {Object} options - 处理选项
 * @returns {Object} 标准化的错误对象
 */
export const 处理错误 = (error, context = '', options = {}) => {
  const {
    显示提示 = true,
    记录日志 = true,
    使用通知 = false,
    自定义消息 = '',
    抛出错误 = false
  } = options

  // 标准化错误
  const 标准错误对象 = 标准化错误(error, context)

  // 记录错误日志
  if (记录日志) {
    记录错误日志(标准错误对象)
  }

  // 显示错误提示
  if (显示提示) {
    显示错误提示(标准错误对象, { 使用通知, 自定义消息 })
  }

  // 是否重新抛出错误
  if (抛出错误) {
    throw 标准错误对象
  }

  return 标准错误对象
}

/**
 * 创建错误处理装饰器
 * @param {string} context - 错误上下文
 * @param {Object} options - 处理选项
 * @returns {Function} 装饰器函数
 */
export const 创建错误处理装饰器 = (context, options = {}) => {
  return (target, propertyKey, descriptor) => {
    const 原始方法 = descriptor.value

    descriptor.value = async function (...args) {
      try {
        return await 原始方法.apply(this, args)
      } catch (error) {
        return 处理错误(error, `${context}.${propertyKey}`, options)
      }
    }

    return descriptor
  }
}

// 默认导出统一错误处理函数
export default 处理错误
