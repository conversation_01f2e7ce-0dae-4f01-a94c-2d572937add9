<template>
  <div class="activation-code-type-management">
    <!-- 搜索筛选区域 -->
    <a-card class="search-section" title="类型搜索">
      <div class="search-container">
        <a-row :gutter="16" align="middle">
          <a-col :xs="24" :sm="16" :md="12" :lg="8">
            <a-input-search
              v-model:value="搜索表单.搜索关键词"
              placeholder="搜索类型名称或描述"
              enter-button="搜索"
              size="large"
              allow-clear
              @search="处理搜索"
              @press-enter="处理搜索"
              :loading="loading"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input-search>
          </a-col>
          <a-col :xs="24" :sm="8" :md="12" :lg="16">
            <a-space>
              <a-button
                @click="重置搜索表单"
                :icon="h(ReloadOutlined)"
                size="large"
              >
                重置
              </a-button>
              <a-button
                @click="显示类型创建模态框"
                :icon="h(PlusOutlined)"
                type="primary"
                size="large"
              >
                创建类型
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 类型数据表格 -->
    <a-card class="table-section" title="激活码类型列表">
      <template #extra>
        <a-tooltip title="刷新数据">
          <a-button 
            type="text" 
            :icon="h(ReloadOutlined)" 
            @click="获取激活码类型列表"
            :loading="loading"
          />
        </a-tooltip>
      </template>

      <a-table
        :columns="表格列配置"
        :data-source="数据源"
        :loading="loading"
        row-key="id"
        :pagination="分页配置"
        @change="处理表格变化"
        :scroll="{ x: 1200 }"
        class="type-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name_desc'">
            <div class="name-desc">
              <div class="type-name">{{ record.名称 }}</div>
              <div class="type-desc">{{ record.描述 }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'price'">
            <span class="price">¥{{ record.价格 }}</span>
          </template>

          <template v-else-if="column.key === 'member_info'">
            <div class="member-info">
              <a-tag color="blue">{{ record.会员天数 }}天会员</a-tag>
              <div class="member-id">会员id: {{ record.会员表id }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'statistics'">
            <div class="statistics">
              <div class="stat-item">
                <span class="stat-label">总数:</span>
                <span class="stat-value">{{ record.激活码数量 || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">未使用:</span>
                <span class="stat-value unused">{{ record.未使用数量 || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">已使用:</span>
                <span class="stat-value used">{{ record.已使用数量 || 0 }}</span>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button 
                type="link" 
                size="small"
                @click="查看类型详情(record)"
              >
                详情
              </a-button>
              <a-button 
                type="link" 
                size="small"
                @click="显示类型编辑模态框(record)"
              >
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个激活码类型吗？删除后无法恢复。"
                @confirm="删除激活码类型(record.id)"
                :disabled="record.激活码数量 > 0"
              >
                <a-button 
                  type="link" 
                  size="small"
                  danger
                  :disabled="record.激活码数量 > 0"
                >
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑类型模态框 -->
    <a-modal
      v-model:open="类型模态框可见"
      :title="编辑模式 ? '编辑激活码类型' : '创建激活码类型'"
      @ok="确认保存类型"
      @cancel="取消类型操作"
      :confirm-loading="保存loading"
      width="600px"
    >
      <a-form
        :model="类型表单"
        :rules="类型表单规则"
        ref="类型表单引用"
        layout="vertical"
      >
        <a-form-item label="类型名称" name="名称">
          <a-input
            v-model:value="类型表单.名称"
            placeholder="请输入类型名称"
            size="large"
          />
        </a-form-item>

        <a-form-item label="类型描述" name="描述">
          <a-textarea
            v-model:value="类型表单.描述"
            :rows="3"
            placeholder="请输入类型描述"
            :maxlength="200"
            show-count
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="类型价格" name="价格">
              <a-input-number
                v-model:value="类型表单.价格"
                :min="0"
                :precision="2"
                size="large"
                style="width: 100%"
                placeholder="请输入价格"
              >
                <template #addonBefore>¥</template>
              </a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="会员天数" name="会员天数">
              <a-input-number
                v-model:value="类型表单.会员天数"
                :min="1"
                :max="3650"
                size="large"
                style="width: 100%"
                placeholder="请输入会员天数"
              >
                <template #addonAfter>天</template>
              </a-input-number>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="会员表id" name="会员表id">
          <a-input-number
            v-model:value="类型表单.会员表id"
            :min="1"
            size="large"
            style="width: 100%"
            placeholder="请输入关联的会员表id"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 类型详情模态框 -->
    <a-modal
      v-model:open="详情模态框可见"
      title="激活码类型详情"
      :footer="null"
      width="700px"
    >
      <div v-if="当前类型详情" class="type-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="类型id">
            {{ 当前类型详情.id }}
          </a-descriptions-item>
          <a-descriptions-item label="类型名称">
            {{ 当前类型详情.名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="类型描述" :span="2">
            {{ 当前类型详情.描述 }}
          </a-descriptions-item>
          <a-descriptions-item label="类型价格">
            ¥{{ 当前类型详情.价格 }}
          </a-descriptions-item>
          <a-descriptions-item label="会员天数">
            {{ 当前类型详情.会员天数 }}天
          </a-descriptions-item>
          <a-descriptions-item label="会员表id">
            {{ 当前类型详情.会员表id }}
          </a-descriptions-item>
          <a-descriptions-item label="激活码总数">
            {{ 当前类型详情.激活码数量 || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="未使用数量">
            <a-tag color="green">{{ 当前类型详情.未使用数量 || 0 }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="已使用数量">
            <a-tag color="orange">{{ 当前类型详情.已使用数量 || 0 }}</a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined
} from '@ant-design/icons-vue'
import activationCodeService from '../services/activationCodeService.js'

// ==================== 响应式数据 ====================

const loading = ref(false)
const 保存loading = ref(false)

// 数据源
const 数据源 = ref([])

// 搜索表单
const 搜索表单 = reactive({
  搜索关键词: ''
})

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，显示 ${range[0]}-${range[1]} 条`
})

// 类型表单相关
const 类型模态框可见 = ref(false)
const 编辑模式 = ref(false)
const 当前编辑id = ref(null)

const 类型表单 = reactive({
  名称: '',
  描述: '',
  价格: 0,
  会员表id: 1,
  会员天数: 30
})

const 类型表单规则 = {
  名称: [
    { required: true, message: '请输入类型名称' },
    { min: 1, max: 50, message: '名称长度应在1-50个字符之间' }
  ],
  描述: [
    { required: true, message: '请输入类型描述' },
    { min: 1, max: 200, message: '描述长度应在1-200个字符之间' }
  ],
  价格: [
    { required: true, message: '请输入类型价格' },
    { type: 'number', min: 0, message: '价格不能为负数' }
  ],
  会员表id: [
    { required: true, message: '请输入会员表id' },
    { type: 'number', min: 1, message: '会员表id必须大于0' }
  ],
  会员天数: [
    { required: true, message: '请输入会员天数' },
    { type: 'number', min: 1, max: 3650, message: '会员天数应在1-3650天之间' }
  ]
}

const 类型表单引用 = ref()

// 详情模态框
const 详情模态框可见 = ref(false)
const 当前类型详情 = ref(null)

// ==================== 表格列配置 ====================

const 表格列配置 = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    sorter: true
  },
  {
    title: '类型信息',
    key: 'name_desc',
    width: 200
  },
  {
    title: '价格',
    key: 'price',
    width: 100,
    sorter: true
  },
  {
    title: '会员信息',
    key: 'member_info',
    width: 150
  },
  {
    title: '激活码统计',
    key: 'statistics',
    width: 200
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// ==================== 方法 ====================

/**
 * 获取激活码类型列表
 */
const 获取激活码类型列表 = async () => {
  try {
    loading.value = true
    
    const 查询参数 = {
      page: 分页配置.current,
      size: 分页配置.pageSize,
      搜索关键词: 搜索表单.搜索关键词
    }
    
    const 响应 = await activationCodeService.获取激活码类型列表带统计(查询参数)
    
    if (响应.status === 100) {
      数据源.value = 响应.data.list || []
      分页配置.total = 响应.data.total || 0
    } else {
      message.error(响应.message || '获取激活码类型列表失败')
    }
  } catch (error) {
    console.error('获取激活码类型列表失败:', error)
    message.error('获取激活码类型列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const 处理搜索 = () => {
  分页配置.current = 1
  获取激活码类型列表()
}

/**
 * 重置搜索表单
 */
const 重置搜索表单 = () => {
  搜索表单.搜索关键词 = ''
  分页配置.current = 1
  获取激活码类型列表()
}

/**
 * 处理表格变化
 */
const 处理表格变化 = (pagination, filters, sorter) => {
  分页配置.current = pagination.current
  分页配置.pageSize = pagination.pageSize
  获取激活码类型列表()
}

/**
 * 显示类型创建模态框
 */
const 显示类型创建模态框 = () => {
  编辑模式.value = false
  当前编辑id.value = null
  类型表单.名称 = ''
  类型表单.描述 = ''
  类型表单.价格 = 0
  类型表单.会员表id = 1
  类型表单.会员天数 = 30
  类型模态框可见.value = true
}

/**
 * 显示类型编辑模态框
 */
const 显示类型编辑模态框 = (record) => {
  编辑模式.value = true
  当前编辑id.value = record.id
  类型表单.名称 = record.名称
  类型表单.描述 = record.描述
  类型表单.价格 = record.价格
  类型表单.会员表id = record.会员表id
  类型表单.会员天数 = record.会员天数
  类型模态框可见.value = true
}

/**
 * 取消类型操作
 */
const 取消类型操作 = () => {
  类型模态框可见.value = false
}

/**
 * 确认保存类型
 */
const 确认保存类型 = async () => {
  try {
    await 类型表单引用.value.validate()
    
    保存loading.value = true
    
    let 响应
    if (编辑模式.value) {
      响应 = await activationCodeService.更新激活码类型(当前编辑id.value, 类型表单)
    } else {
      响应 = await activationCodeService.创建激活码类型(类型表单)
    }
    
    if (响应.status === 100) {
      message.success(响应.message || `激活码类型${编辑模式.value ? '更新' : '创建'}成功`)
      类型模态框可见.value = false
      获取激活码类型列表()
    } else {
      message.error(响应.message || `${编辑模式.value ? '更新' : '创建'}激活码类型失败`)
    }
  } catch (error) {
    console.error('保存激活码类型失败:', error)
    message.error('保存激活码类型失败')
  } finally {
    保存loading.value = false
  }
}

/**
 * 查看类型详情
 */
const 查看类型详情 = async (record) => {
  try {
    const 响应 = await activationCodeService.获取激活码类型详情(record.id)
    
    if (响应.status === 100) {
      当前类型详情.value = 响应.data
      详情模态框可见.value = true
    } else {
      message.error(响应.message || '获取激活码类型详情失败')
    }
  } catch (error) {
    console.error('获取激活码类型详情失败:', error)
    message.error('获取激活码类型详情失败')
  }
}

/**
 * 删除激活码类型
 */
const 删除激活码类型 = async (类型id) => {
  try {
    const 响应 = await activationCodeService.删除激活码类型(类型id)
    
    if (响应.status === 100) {
      message.success('激活码类型删除成功')
      获取激活码类型列表()
    } else {
      message.error(响应.message || '删除激活码类型失败')
    }
  } catch (error) {
    console.error('删除激活码类型失败:', error)
    message.error('删除激活码类型失败')
  }
}

// 暴露方法给父组件
defineExpose({
  显示类型创建模态框
})

// ==================== 生命周期 ====================

onMounted(() => {
  获取激活码类型列表()
})
</script>

<style scoped>
/* 搜索区域样式 */
.search-section {
  margin-bottom: 24px;
}

.search-container {
  padding: 16px 0;
}

/* 表格样式 */
.table-section {
  margin-bottom: 24px;
}

.type-table .name-desc .type-name {
  font-weight: 600;
  color: #1890ff;
}

.type-table .name-desc .type-desc {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.type-table .price {
  font-weight: 600;
  color: #52c41a;
}

.type-table .member-info .member-id {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.type-table .statistics {
  font-size: 12px;
}

.type-table .statistics .stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.type-table .statistics .stat-label {
  color: #666;
}

.type-table .statistics .stat-value {
  font-weight: 500;
}

.type-table .statistics .stat-value.unused {
  color: #52c41a;
}

.type-table .statistics .stat-value.used {
  color: #faad14;
}

/* 详情模态框样式 */
.type-detail {
  margin-top: 16px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .search-container .ant-row {
    gap: 8px;
  }
}
</style> 