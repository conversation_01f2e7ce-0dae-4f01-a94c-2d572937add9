import { 主API客户端 } from './apiClientFactory'
import { 
  判断API成功, 
  获取API数据, 
  获取API消息, 
  调试API响应,
  处理分页响应,
  处理表单响应,
  创建API请求包装器
} from '../utils/apiResponseHandler'
import { 处理错误 } from '../utils/errorHandler'

/**
 * 服务基类
 * 提供统一的API调用模式和错误处理
 * 
 * 功能特性：
 * - 统一的API客户端
 * - 标准化的请求方法
 * - 自动错误处理
 * - 请求日志记录
 * - 响应数据处理
 */
export class BaseService {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   * @param {string} options.serviceName - 服务名称
   * @param {string} options.baseURL - 基础URL路径
   * @param {Object} options.defaultConfig - 默认请求配置
   */
  constructor(options = {}) {
    this.serviceName = options.serviceName || 'BaseService'
    this.baseURL = options.baseURL || ''
    this.defaultConfig = options.defaultConfig || {}
    this.apiClient = 主API客户端
    
    // 绑定方法到实例，确保this指向正确
    this._handleRequest = this._handleRequest.bind(this)
    this._buildUrl = this._buildUrl.bind(this)
    this._logRequest = this._logRequest.bind(this)
  }

  /**
   * 构建完整的URL
   * @param {string} path - API路径
   * @returns {string} 完整URL
   */
  _buildUrl(path) {
    if (!path) return this.baseURL
    
    // 处理路径拼接
    const cleanBaseURL = this.baseURL.replace(/\/$/, '')
    const cleanPath = path.replace(/^\//, '')
    
    return cleanBaseURL ? `${cleanBaseURL}/${cleanPath}` : `/${cleanPath}`
  }

  /**
   * 记录请求日志
   * @param {string} method - HTTP方法
   * @param {string} url - 请求URL
   * @param {any} data - 请求数据
   * @param {Object} config - 请求配置
   */
  _logRequest(method, url, data, config) {
    if (!import.meta.env.DEV) return
    
    console.group(`📡 ${this.serviceName} API请求`)
    console.log('方法:', method.toUpperCase())
    console.log('URL:', url)
    if (data) console.log('数据:', data)
    if (config && Object.keys(config).length > 0) console.log('配置:', config)
    console.groupEnd()
  }

  /**
   * 统一请求处理方法
   * @param {Function} requestFn - 请求函数
   * @param {string} context - 上下文信息
   * @param {Object} options - 处理选项
   * @returns {Promise} 请求结果
   */
  async _handleRequest(requestFn, context = '', options = {}) {
    const {
      showSuccessMessage = false,
      showErrorMessage = true,
      successMessage = '操作成功',
      errorMessage = '操作失败',
      useNotification = false,
      logResponse = true
    } = options

    try {
      const startTime = Date.now()
      const fullContext = `${this.serviceName}.${context}`
      
      // 执行请求
      const response = await requestFn()
      
      const duration = Date.now() - startTime
      
      // 调试响应
      if (logResponse) {
        调试API响应(response, fullContext)
      }
      
      if (import.meta.env.DEV) {
        console.log(`✅ ${fullContext} 请求完成，耗时: ${duration}ms`)
      }
      
      return response
    } catch (error) {
      const fullContext = `${this.serviceName}.${context}`
      
      // 使用统一错误处理
      处理错误(error, fullContext, {
        显示提示: showErrorMessage,
        记录日志: true,
        使用通知: useNotification,
        自定义消息: errorMessage
      })
      
      // 重新抛出错误，让调用者决定如何处理
      throw error
    }
  }

  /**
   * 发送GET请求
   * @param {string} path - API路径
   * @param {Object} params - 查询参数
   * @param {Object} config - 请求配置
   * @param {Object} options - 处理选项
   * @returns {Promise} 请求结果
   */
  async get(path, params = {}, config = {}, options = {}) {
    const url = this._buildUrl(path)
    const finalConfig = { ...this.defaultConfig, ...config, params }
    
    this._logRequest('GET', url, null, finalConfig)
    
    return this._handleRequest(
      () => this.apiClient.get(url, finalConfig),
      `GET ${path}`,
      options
    )
  }

  /**
   * 发送POST请求
   * @param {string} path - API路径
   * @param {any} data - 请求数据
   * @param {Object} config - 请求配置
   * @param {Object} options - 处理选项
   * @returns {Promise} 请求结果
   */
  async post(path, data = {}, config = {}, options = {}) {
    const url = this._buildUrl(path)
    const finalConfig = { ...this.defaultConfig, ...config }
    
    this._logRequest('POST', url, data, finalConfig)
    
    return this._handleRequest(
      () => this.apiClient.post(url, data, finalConfig),
      `POST ${path}`,
      options
    )
  }

  /**
   * 发送PUT请求
   * @param {string} path - API路径
   * @param {any} data - 请求数据
   * @param {Object} config - 请求配置
   * @param {Object} options - 处理选项
   * @returns {Promise} 请求结果
   */
  async put(path, data = {}, config = {}, options = {}) {
    const url = this._buildUrl(path)
    const finalConfig = { ...this.defaultConfig, ...config }
    
    this._logRequest('PUT', url, data, finalConfig)
    
    return this._handleRequest(
      () => this.apiClient.put(url, data, finalConfig),
      `PUT ${path}`,
      options
    )
  }

  /**
   * 发送DELETE请求
   * @param {string} path - API路径
   * @param {Object} config - 请求配置
   * @param {Object} options - 处理选项
   * @returns {Promise} 请求结果
   */
  async delete(path, config = {}, options = {}) {
    const url = this._buildUrl(path)
    const finalConfig = { ...this.defaultConfig, ...config }
    
    this._logRequest('DELETE', url, null, finalConfig)
    
    return this._handleRequest(
      () => this.apiClient.delete(url, finalConfig),
      `DELETE ${path}`,
      options
    )
  }

  /**
   * 获取分页数据的通用方法
   * @param {string} path - API路径
   * @param {Object} params - 查询参数
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 分页数据
   */
  async getPaginatedData(path, params = {}, options = {}) {
    const {
      listKey = '',
      defaultPage = 1,
      defaultPageSize = 10,
      ...requestOptions
    } = options

    // 设置默认分页参数
    const finalParams = {
      页码: defaultPage,
      每页数量: defaultPageSize,
      page: defaultPage,
      pageSize: defaultPageSize,
      ...params
    }

    try {
      const response = await this.get(path, finalParams, {}, requestOptions)
      return 处理分页响应(response, listKey)
    } catch (error) {
      console.error(`❌ ${this.serviceName} 获取分页数据失败:`, error)
      return {
        success: false,
        list: [],
        total: 0,
        page: finalParams.页码 || finalParams.page,
        pageSize: finalParams.每页数量 || finalParams.pageSize,
        totalPages: 0,
        message: error.message || '获取数据失败',
        error
      }
    }
  }

  /**
   * 提交表单数据的通用方法
   * @param {string} path - API路径
   * @param {Object} formData - 表单数据
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 提交结果
   */
  async submitForm(path, formData = {}, options = {}) {
    const {
      method = 'POST',
      successMessage = '提交成功',
      errorMessage = '提交失败',
      showSuccessMessage = true,
      ...requestOptions
    } = options

    try {
      let response
      if (method.toUpperCase() === 'PUT') {
        response = await this.put(path, formData, {}, {
          showSuccessMessage,
          successMessage,
          errorMessage,
          ...requestOptions
        })
      } else {
        response = await this.post(path, formData, {}, {
          showSuccessMessage,
          successMessage,
          errorMessage,
          ...requestOptions
        })
      }
      
      return 处理表单响应(response, successMessage, errorMessage)
    } catch (error) {
      console.error(`❌ ${this.serviceName} 表单提交失败:`, error)
      return {
        success: false,
        data: null,
        message: error.message || errorMessage,
        error
      }
    }
  }

  /**
   * 批量操作的通用方法
   * @param {Array} operations - 操作数组
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 批量操作结果
   */
  async batchOperation(operations, options = {}) {
    const {
      concurrency = 3,
      failureStrategy = 'continue',
      showProgress = false
    } = options

    const results = {
      successCount: 0,
      failureCount: 0,
      totalCount: operations.length,
      successResults: [],
      failureResults: [],
      details: []
    }

    try {
      // 分批处理操作
      for (let i = 0; i < operations.length; i += concurrency) {
        const batch = operations.slice(i, i + concurrency)
        
        const batchResults = await Promise.allSettled(
          batch.map(async (operation, index) => {
            try {
              const result = await operation()
              return { index: i + index, status: 'fulfilled', data: result }
            } catch (error) {
              return { index: i + index, status: 'rejected', error }
            }
          })
        )

        // 处理批次结果
        batchResults.forEach(result => {
          if (result.status === 'fulfilled') {
            results.successCount++
            results.successResults.push(result.value.data)
            results.details.push(result.value)
          } else {
            results.failureCount++
            results.failureResults.push(result.reason)
            results.details.push({
              index: result.value?.index || -1,
              status: 'rejected',
              error: result.reason
            })

            if (failureStrategy === 'stop') {
              throw new Error(`批量操作在第${result.value?.index || -1}个操作时失败`)
            }
          }
        })

        // 显示进度
        if (showProgress) {
          const progress = Math.min(i + concurrency, operations.length)
          console.log(`📊 ${this.serviceName} 批量操作进度: ${progress}/${operations.length}`)
        }
      }

      return results
    } catch (error) {
      处理错误(error, `${this.serviceName}.batchOperation`)
      throw error
    }
  }
}

// 导出基类
export default BaseService
