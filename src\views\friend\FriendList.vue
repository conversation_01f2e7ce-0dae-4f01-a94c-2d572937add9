<template>
  <div class="friend-list-page">
    <!-- 搜索和操作栏 -->
    <div class="toolbar">
      <div class="search-section">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索好友微信号、昵称..."
          size="large"
          class="search-input"
          @search="handleSearch"
          @change="handleSearchChange"
        >
          <template #prefix>
            <SearchOutlined class="search-icon" />
          </template>
        </a-input-search>
      </div>

      <div class="action-section">
        <a-space size="middle">
          <a-button
            @click="refreshData"
            :loading="loading"
            :icon="h(ReloadOutlined)"
            class="refresh-btn"
          >
            刷新
          </a-button>

          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-button type="primary" :icon="h(MoreOutlined)">
              批量操作 ({{ selectedRowKeys.length }})
              <DownOutlined />
            </a-button>
            <template #overlay>
              <div class="custom-dropdown-menu">
                <div 
                  class="custom-menu-item" 
                  @click="handleBatchAction({ key: 'export' })"
                >
                  <DownloadOutlined class="menu-icon" />
                  导出选中
                </div>
                <div class="menu-divider"></div>
                <div 
                  class="custom-menu-item danger" 
                  @click="handleBatchAction({ key: 'batch-delete' })"
                >
                  <DeleteOutlined class="menu-icon" />
                  批量删除
                </div>
              </div>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>

    <!-- 好友列表表格 -->
    <div class="friend-table-container">
      <a-table
        :columns="tableColumns"
        :data-source="displayFriendList"
        :loading="loading && !pageChanging"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        :scroll="{ x: 1200 }"
        row-key="key"
        class="friend-table"
        @change="handleTableChange"
        :locale="{
          emptyText: searchKeyword ? '没有找到匹配的好友' : '还没有添加任何好友'
        }"
        :size="'middle'"
        :bordered="false"
        :show-header="true"
        :table-layout="'fixed'"
      >
        <!-- 好友信息列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'friendInfo'">
            <div class="friend-info">
              <!-- 骨架屏状态 -->
              <template v-if="record.loading">
                <div class="skeleton-avatar"></div>
                <div class="friend-details">
                  <div class="skeleton-text skeleton-name"></div>
                  <div class="skeleton-text skeleton-wechat"></div>
                </div>
              </template>
              <!-- 正常显示 -->
              <template v-else>
                <a-avatar
                  :src="record.头像 || ''"
                  :style="{ backgroundColor: getAvatarDetails(record).color }"
                  :size="40"
                  class="friend-avatar"
                >
                  {{ getAvatarDetails(record).text }}
                </a-avatar>
                <div class="friend-details">
                  <div class="friend-name">
                    {{ record.昵称 || record.对方微信号 || '未知好友' }}
                  </div>
                  <div class="friend-wechat">
                    <WechatOutlined class="wechat-icon" />
                    {{ record.对方微信号 }}
                  </div>
                  <!-- 显示好友关系的唯一标识，帮助用户理解多微信号同一好友的情况 -->
                  <div class="friend-relation" v-if="record.识别id">
                    <span class="relation-id">关系ID: {{ record.识别id }}</span>
                  </div>
                </div>
              </template>
            </div>
          </template>

          <!-- 微信账号列 -->
          <template v-else-if="column.key === '我方微信号'">
            <div class="my-wechat">
              <!-- 骨架屏状态 -->
              <template v-if="record.loading">
                <div class="skeleton-text skeleton-wechat-id"></div>
              </template>
              <!-- 正常显示 -->
              <template v-else>
                <div class="wechat-id">{{ record.我方微信号 }}</div>
                <div class="account-note" v-if="record.微信备注">{{ record.微信备注 }}</div>
              </template>
            </div>
          </template>

          <!-- 时间列统一处理 -->
          <template v-else-if="isTimeColumn(column.key)">
            <div class="time-display">
              <!-- 骨架屏状态 -->
              <template v-if="record.loading">
                <div class="skeleton-text skeleton-time-relative"></div>
                <div class="skeleton-text skeleton-time-absolute"></div>
              </template>
              <!-- 正常显示 -->
              <template v-else>
                <div class="time-relative">
                  {{ getRelativeTime(record[column.key]) }}
                </div>
                <div class="time-absolute">
                  {{ formatDate(record[column.key]) }}
                </div>
              </template>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <div class="action-buttons">
              <!-- 骨架屏状态 -->
              <template v-if="record.loading">
                <div class="skeleton-action-buttons">
                  <div class="skeleton-button"></div>
                  <div class="skeleton-button"></div>
                  <div class="skeleton-button"></div>
                </div>
              </template>
              <!-- 正常显示 -->
              <template v-else>
                <a-tooltip title="查看详情">
                  <a-button
                    type="text"
                    size="small"
                    @click="viewFriend(record)"
                    :icon="h(EyeOutlined)"
                    class="action-btn"
                  />
                </a-tooltip>

                <a-tooltip title="编辑备注">
                  <a-button
                    type="text"
                    size="small"
                    @click="editFriend(record)"
                    :icon="h(EditOutlined)"
                    class="action-btn"
                  />
                </a-tooltip>

                <a-dropdown placement="bottomRight">
                  <a-button
                    type="text"
                    size="small"
                    :icon="h(MoreOutlined)"
                    class="action-btn"
                  />
                  <template #overlay>
                    <div class="custom-dropdown-menu">
                      <div 
                        class="custom-menu-item" 
                        @click="handleFriendAction('progress', record)"
                      >
                        <BarChartOutlined class="menu-icon" />
                        对接进度
                      </div>
                      <div class="menu-divider"></div>
                      <div 
                        v-if="canDeleteFriend(record)"
                        class="custom-menu-item danger" 
                        @click="handleFriendAction('delete', record)"
                      >
                        <DeleteOutlined class="menu-icon" />
                        删除好友
                      </div>
                    </div>
                  </template>
                </a-dropdown>
              </template>
            </div>
          </template>
        </template>

        <!-- 空状态 -->
        <template #emptyText>
          <div class="empty-state">
            <UserOutlined class="empty-icon" />
            <div class="empty-text">暂无好友数据</div>
            <div class="empty-description">
              {{ searchKeyword ? '没有找到匹配的好友' : '还没有添加任何好友' }}
            </div>
          </div>
        </template>
      </a-table>
    </div>



    <!-- 编辑好友弹窗 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑好友信息"
      :width="600"
      @ok="handleUpdateFriend"
      :confirmLoading="updateLoading"
    >
      <a-form ref="editFormRef" :model="editForm" :rules="editFormRules" layout="vertical">
        <a-form-item label="好友微信号" name="好友微信号">
          <a-input v-model:value="editForm.好友微信号" disabled />
          <div class="form-tip">好友微信号不可修改</div>
        </a-form-item>

        <a-form-item label="好友昵称" name="昵称">
          <a-input
            v-model:value="editForm.昵称"
            disabled
            placeholder="好友昵称（只读）"
          />
          <div class="form-tip">好友昵称暂不支持修改</div>
        </a-form-item>

        <a-form-item label="备注" name="备注">
          <a-textarea
            v-model:value="editForm.备注"
            placeholder="添加备注信息（可选）"
            :rows="4"
            :maxlength="500"
            show-count
          />
          <div class="form-tip">可以为这个好友添加备注信息，最多500个字符</div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 好友详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="好友详情"
      :width="700"
      :footer="null"
    >
      <div v-if="currentFriend" class="friend-detail">
        <div class="detail-header">
          <a-avatar 
            :size="80" 
            :src="currentFriend.头像"
            :style="{ backgroundColor: getAvatarColor(currentFriend.昵称) }"
          >
            {{ currentFriend.昵称 ? currentFriend.昵称.charAt(0) : currentFriend.对方微信号.charAt(0) }}
          </a-avatar>
          <div class="detail-info">
            <h3>{{ currentFriend.昵称 || currentFriend.对方微信号 }}</h3>
            <p>{{ currentFriend.对方微信号 }}</p>
          </div>
        </div>

        <a-divider />

        <a-descriptions title="基本信息" :column="2">
          <a-descriptions-item label="我方微信号">
            {{ currentFriend.我方微信号 }}
          </a-descriptions-item>
          <a-descriptions-item label="好友入库时间">
            {{ formatDate(currentFriend.好友入库时间) || '暂无' }}
          </a-descriptions-item>
          <a-descriptions-item label="对方最后发消息时间">
            {{ formatDate(currentFriend.对方最后一条消息发送时间) || '暂无' }}
          </a-descriptions-item>
          <a-descriptions-item label="我方最后发消息时间">
            {{ formatDate(currentFriend.我方最后一条消息发送时间) || '暂无' }}
          </a-descriptions-item>
          <a-descriptions-item label="备注信息" :span="2">
            {{ currentFriend.备注 || '暂无备注' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, h } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  ReloadOutlined,
  MoreOutlined,
  DownOutlined,
  DownloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  BarChartOutlined,
  UserOutlined,
  SearchOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'

// 导入服务
import { friendService } from '@/services/friend'

defineOptions({
  name: 'FriendList'
})

const router = useRouter()

// 组件挂载状态管理
const isMounted = ref(false)
const isDestroyed = ref(false)

// 响应式数据
const loading = ref(false)
const friendList = ref([])
const searchKeyword = ref('')

// 排序状态管理
const sortConfig = ref({
  排序字段: null,
  排序方向: 'DESC'
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 弹窗状态
const editModalVisible = ref(false)
const detailModalVisible = ref(false)
const updateLoading = ref(false)

// 当前操作的好友
const currentFriend = ref(null)

// 表格选择
const selectedRowKeys = ref([])

// 新增状态管理 - 优化分页切换体验
const pageChanging = ref(false) // 页面切换中状态
const tableMinHeight = ref('500px') // 表格最小高度，防止布局抖动
const previousDataCache = ref([]) // 缓存上一页数据，平滑过渡

// 表单引用
const editFormRef = ref()

// 表单数据
const editForm = reactive({
  好友ID: null,
  好友微信号: '',
  昵称: '',
  备注: ''
})

// 表单验证规则
const editFormRules = {
  备注: [{ max: 500, message: '备注不能超过500个字符' }]
}

// 字段映射：表格列key -> 后端排序字段名
const sortFieldMapping = {
  '对方最后一条消息发送时间': '对方最后发消息时间',
  '我方最后一条消息发送时间': '我方最后发消息时间',
  '好友入库时间': '入库时间'
}

// 获取排序状态（适配Ant Design）
const getSortOrder = (columnKey) => {
  const backendField = sortFieldMapping[columnKey]
  if (sortConfig.value.排序字段 === backendField) {
    return sortConfig.value.排序方向 === 'ASC' ? 'ascend' : 'descend'
  }
  return null
}

// 注：使用Ant Design内置排序，不需要自定义排序函数

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '好友信息',
    key: 'friendInfo',
    width: 220,
    fixed: 'left',
    ellipsis: true
  },
  {
    title: '我方微信号',
    key: '我方微信号',
    width: 140,
    ellipsis: true
  },
  {
    title: '对方最后发消息时间',
    key: '对方最后一条消息发送时间',
    width: 160,
    sorter: true,
    sortOrder: getSortOrder('对方最后一条消息发送时间'),
    ellipsis: true
  },
  {
    title: '我方最后发消息时间',
    key: '我方最后一条消息发送时间',
    width: 160,
    sorter: true,
    sortOrder: getSortOrder('我方最后一条消息发送时间'),
    ellipsis: true
  },
  {
    title: '入库时间',
    key: '好友入库时间',
    width: 140,
    sorter: true,
    sortOrder: getSortOrder('好友入库时间'),
    ellipsis: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
])

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'default',
  // 优化分页切换体验
  showLessItems: true, // 减少分页按钮数量，避免布局抖动
  simple: false, // 保持完整分页器
  position: ['bottomCenter'] // 固定分页器位置
}))

// 判断是否为时间列
const isTimeColumn = (key) => {
  return ['对方最后一条消息发送时间', '我方最后一条消息发送时间', '好友入库时间'].includes(key)
}

// 骨架屏数据 - 用于页面切换时的占位显示
const skeletonData = computed(() => {
  return Array.from({ length: pagination.pageSize }, (_, index) => ({
    key: `skeleton-${index}`,
    loading: true,
    昵称: '',
    对方微信号: '',
    我方微信号: '',
    对方最后一条消息发送时间: null,
    我方最后一条消息发送时间: null,
    好友入库时间: null
  }))
})

// 计算属性：处理显示的好友列表数据
const displayFriendList = computed(() => {
  // 如果正在切换页面且没有新数据，显示骨架屏
  if (pageChanging.value && friendList.value.length === 0) {
    return skeletonData.value
  }

  if (!Array.isArray(friendList.value)) {
    return []
  }

  return friendList.value.map((friend, index) => {
    // 确保数据结构安全
    // 使用我方微信号id + 对方微信号id + 识别id 组合作为唯一key，确保每条好友关系记录都有唯一标识
    const uniqueKey = `${friend.我方微信号id || 'unknown'}_${friend.对方微信号id || 'unknown'}_${friend.识别id || index}`

    const safeFriend = {
      ...friend,
      key: uniqueKey,
      friendInfo: friend.对方微信号 || '未知好友',
      // 添加显示用的组合信息，便于前端展示
      relationshipInfo: `${friend.我方微信号 || '未知'} → ${friend.对方微信号 || '未知好友'}`
    }

    return safeFriend
  })
})

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record) => ({
    disabled: record.状态 === '已删除'
  })
}))

// 组件挂载时初始化
onMounted(async () => {
  try {
    isMounted.value = true

    // 检查组件是否在挂载过程中被销毁
    if (isDestroyed.value) return

    await loadFriendList()

    // 再次检查组件状态
    if (isDestroyed.value) return

    // 监听全局事件
    window.addEventListener('refresh-data', refreshData)
    window.addEventListener('friend-search', handleGlobalSearch)
  } catch (error) {
    console.error('组件初始化失败:', error)
    if (!isDestroyed.value) {
      message.error('页面加载失败，请刷新重试')
    }
  }
})

// 组件卸载时清理
onBeforeUnmount(() => {
  // 首先设置销毁标志
  isDestroyed.value = true
  isMounted.value = false

  // 清理全局事件监听
  try {
    window.removeEventListener('refresh-data', refreshData)
    window.removeEventListener('friend-search', handleGlobalSearch)
  } catch (error) {
    console.warn('清理事件监听器时出现警告:', error)
  }

  // 安全地清理响应式数据，避免内存泄漏
  try {
    friendList.value = []
    selectedRowKeys.value = []
    currentFriend.value = null
  } catch (error) {
    console.warn('清理响应式数据时出现警告:', error)
  }
})

// 方法定义



/**
 * 加载好友列表
 */
const loadFriendList = async () => {
  // 检查组件状态
  if (isDestroyed.value) return
  
  try {
    // 缓存当前数据，用于平滑过渡
    if (friendList.value.length > 0) {
      previousDataCache.value = [...friendList.value]
    }
    
    loading.value = true
    
    const params = {
      页码: pagination.current,
      每页条数: pagination.pageSize,
      关键词: searchKeyword.value || null,
      好友类型: null, // filters.类型 || null, // 移除未使用的filters变量
      排序字段: sortConfig.value.排序字段,
      排序方向: sortConfig.value.排序方向
    }
    
    const response = await friendService.getUserFriends(params)

    // 检查组件是否已销毁
    if (isDestroyed.value) return

    // 使用nextTick确保DOM更新安全
    await nextTick(() => {
      // 再次检查组件状态，避免组件已卸载时更新数据
      if (!isDestroyed.value && isMounted.value) {
        const processedList = (response.好友列表 || []).map(item => ({
          ...item,
          // 映射后端字段到前端期望的字段
          昵称: item.昵称 || item.对方昵称 || item.对方微信号 || '',
          好友微信号: item.好友微信号 || item.对方微信号 || '',
          头像: item.头像 || item.对方头像 || '',
          // 确保必要的字段存在
          我方微信号id: item.我方微信号id,
          对方微信号id: item.对方微信号id,
          备注: item.备注 || ''
        }));

        // 平滑更新数据
        friendList.value = processedList
        pagination.total = response.总数 || 0
        
        // 计算表格最小高度，基于当前页面数据量
        const minRows = Math.min(pagination.pageSize, processedList.length)
        tableMinHeight.value = `${Math.max(500, minRows * 65 + 100)}px`
        
        // 重置页面切换状态
        pageChanging.value = false
      }
    })
    
  } catch (error) {
    console.error('加载好友列表失败:', error)
    if (!isDestroyed.value) {
      // 发生错误时恢复缓存数据，避免空白页面
      if (previousDataCache.value.length > 0) {
        friendList.value = previousDataCache.value
      }
      pageChanging.value = false
      message.error(`加载数据失败: ${error.message || '请重试'}`)
    }
  } finally {
    if (!isDestroyed.value) {
      loading.value = false
    }
  }
}

/**
 * 刷新数据
 */
const refreshData = async () => {
  if (isDestroyed.value) return
  
  try {
    await loadFriendList()
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

/**
 * 处理搜索
 */
const handleSearch = async (value) => {
  if (isDestroyed.value) return
  
  searchKeyword.value = value
  pagination.current = 1
  await loadFriendList()
}

/**
 * 处理搜索输入变化
 */
const handleSearchChange = async (e) => {
  if (isDestroyed.value) return
  
  const value = e.target.value
  if (value.length === 0) {
    searchKeyword.value = ''
    await loadFriendList()
  }
}

/**
 * 处理全局搜索事件
 */
const handleGlobalSearch = (event) => {
  const keyword = event.detail.keyword
  handleSearch(keyword)
}





/**
 * 处理表格变化（分页和排序）
 */
const handleTableChange = async (pag, filters, sorter) => {
  if (isDestroyed.value) return

  // 标记页面切换状态开始
  const isPageChange = pagination.current !== pag.current || pagination.pageSize !== pag.pageSize
  if (isPageChange) {
    pageChanging.value = true
  }

  // 处理分页变化
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize

  // 处理排序变化
  if (sorter && sorter.columnKey && sortFieldMapping[sorter.columnKey]) {
    const backendField = sortFieldMapping[sorter.columnKey]
    if (sorter.order) {
      // 有排序方向：ascend 或 descend
      sortConfig.value.排序字段 = backendField
      sortConfig.value.排序方向 = sorter.order === 'ascend' ? 'ASC' : 'DESC'
    } else {
      // 取消排序：order 为 null
      sortConfig.value.排序字段 = null
      sortConfig.value.排序方向 = 'DESC'
    }
  }

  try {
    await loadFriendList()
  } catch (error) {
    // 加载失败时重置页面切换状态
    pageChanging.value = false
    console.error('表格数据加载失败:', error)
  }
}



/**
 * 查看好友详情
 */
const viewFriend = (friend) => {
  currentFriend.value = friend
  detailModalVisible.value = true
}

/**
 * 编辑好友
 */
const editFriend = (friend) => {
  // 存储原始数据用于API调用
  currentFriend.value = friend

  // 填充表单数据
  editForm.好友ID = friend.好友ID || friend.识别id
  editForm.好友微信号 = friend.对方微信号 || friend.好友微信号 || ''
  editForm.昵称 = friend.昵称 || friend.对方昵称 || friend.对方微信号 || ''
  editForm.备注 = friend.备注 || ''

  editModalVisible.value = true
}

/**
 * 处理更新好友
 */
const handleUpdateFriend = async () => {
  if (isDestroyed.value) return

  try {
    await editFormRef.value?.validate()

    if (isDestroyed.value) return

    updateLoading.value = true

    // 构建符合后端API要求的数据格式
    const updateData = {
      我方微信号id: currentFriend.value.我方微信号id,
      对方微信号id: currentFriend.value.对方微信号id,
      备注: editForm.备注
    }

    await friendService.updateFriend(updateData)

    if (isDestroyed.value) return

    message.success('好友信息更新成功')
    editModalVisible.value = false
    loadFriendList()

  } catch (error) {
    console.error('更新好友失败:', error)
    if (error.errors) return
    if (!isDestroyed.value) {
      message.error(error.message || '更新失败，请重试')
    }
  } finally {
    if (!isDestroyed.value) {
      updateLoading.value = false
    }
  }
}

/**
 * 处理好友操作
 */
const handleFriendAction = (action, friend) => {
  switch (action) {
    case 'progress':
      router.push({
        name: 'FriendProgressManagement',
        query: { 好友ID: friend.好友ID }
      })
      break
    case 'delete':
      deleteFriend(friend)
      break
  }
}

/**
 * 检查是否可以删除好友
 * 用户只能删除与自己绑定的微信好友记录
 */
const canDeleteFriend = () => {
  // 这里可以根据好友数据中的权限字段来判断
  // 由于后端会进行权限验证，前端暂时允许显示删除按钮
  // 如果后端返回的数据中包含权限信息，可以在这里进行判断
  return true // 暂时允许所有用户看到删除按钮，权限由后端控制
}

/**
 * 删除好友 - 包含权限验证和确认弹窗
 */
const deleteFriend = (friend) => {
  Modal.confirm({
    title: '确认删除好友',
    content: `您确定要删除好友 "${friend.昵称 || friend.好友微信号}" 吗？删除后将无法恢复，请谨慎操作。`,
    okText: '确定删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      if (isDestroyed.value) return

      try {
        // 调用删除好友API，后端会验证权限
        await friendService.deleteFriend(friend.好友ID)

        if (isDestroyed.value) return

        message.success('好友删除成功')
        // 刷新好友列表
        await loadFriendList()
      } catch (error) {
        console.error('删除好友失败:', error)
        // 显示具体的错误信息
        if (error.message && error.message.includes('权限')) {
          message.error('删除失败：您没有权限删除该好友')
        } else {
          message.error(error.message || '删除失败，请重试')
        }
      }
    }
  })
}

/**
 * 处理批量操作
 */
const handleBatchAction = ({ key }) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的好友')
    return
  }
  
  switch (key) {
    case 'export':
      exportFriends()
      break
    case 'batch-update':
      message.info('批量更新功能开发中...')
      break
    case 'batch-delete':
      batchDeleteFriends()
      break
  }
}

/**
 * 导出好友数据
 */
const exportFriends = () => {
  message.info('导出功能开发中...')
}

/**
 * 批量删除好友
 */
const batchDeleteFriends = () => {
  Modal.confirm({
    title: '批量删除确认',
    content: `您确定要删除选中的 ${selectedRowKeys.value.length} 个好友吗？`,
    okText: '确定删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      if (isDestroyed.value) return

      try {
        await friendService.batchDeleteFriends(selectedRowKeys.value)

        if (isDestroyed.value) return

        message.success('批量删除成功')
        selectedRowKeys.value = []
        loadFriendList()
      } catch (error) {
        console.error('批量删除失败:', error)
        message.error(error.message || '删除失败，请重试')
      }
    }
  })
}

// 工具函数

/**
 * 格式化日期
 */
const formatDate = (date) => {
  try {
    if (!date) return '暂无'
    // 安全的日期格式化
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) return '暂无'
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.warn('日期格式化失败:', error)
    return '暂无'
  }
}

/**
 * 获取相对时间显示
 */
const getRelativeTime = (date) => {
  try {
    if (!date) return '暂无'

    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) return '暂无'

    const now = new Date()
    const diffMs = now.getTime() - dateObj.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    // 如果是未来时间，显示为"刚刚"
    if (diffMs < 0) return '刚刚'

    if (diffMinutes < 1) {
      return '刚刚'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7)
      return `${weeks}周前`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return `${months}个月前`
    } else {
      const years = Math.floor(diffDays / 365)
      return `${years}年前`
    }
  } catch (error) {
    console.warn('相对时间计算失败:', error)
    return '暂无'
  }
}

/**
 * 根据昵称或微信号生成头像颜色
 * @param {string} name - 名称字符串
 */
const getAvatarColor = (name) => {
  if (!name) return '#cccccc';
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae'];
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
};

/**
 * 获取头像显示的文字和颜色
 * @param {object} record - 好友记录
 */
const getAvatarDetails = (record) => {
  const name = record.昵称 || record.好友微信号 || '';
  return {
    text: name ? name.charAt(0).toUpperCase() : '?',
    color: getAvatarColor(name)
  };
};


</script>

<style scoped>
/* 页面整体布局 */
.friend-list-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
  /* 优化布局稳定性 */
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  gap: 24px;
  /* 固定工具栏位置和大小 */
  flex-shrink: 0;
  min-height: 80px; /* 固定最小高度 */
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.search-input {
  border-radius: 12px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.search-input:hover {
  border-color: #d9d9d9;
}

.search-input:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-icon {
  color: #8c8c8c;
}

.action-section {
  display: flex;
  align-items: center;
}

.refresh-btn {
  border-radius: 8px;
}

/* 表格容器 */
.friend-table-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  /* 优化分页切换体验 */
  min-height: v-bind(tableMinHeight);
  transition: min-height 0.3s ease-in-out;
  position: relative;
  /* 确保表格容器占据剩余空间 */
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出导致布局变化 */
}

.friend-table {
  border-radius: 12px;
  overflow: hidden;
  /* 确保表格在数据加载时保持稳定 */
  width: 100%;
  table-layout: fixed;
  /* 占据容器剩余空间 */
  flex: 1;
  height: 0; /* 配合flex: 1使用，确保正确的高度计算 */
}

.friend-table :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border-bottom: 2px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
  padding: 16px 12px;
  /* 固定表头，防止抖动 */
  position: sticky;
  top: 0;
  z-index: 10;
}

.friend-table :deep(.ant-table-tbody > tr > td) {
  padding: 16px 12px;
  border-bottom: 1px solid #f5f5f5;
  /* 添加过渡效果 */
  transition: background-color 0.2s ease;
}

.friend-table :deep(.ant-table-tbody > tr:hover > td) {
  background: #f8f9ff;
}

/* 分页切换加载状态 */
.friend-table :deep(.ant-table-tbody) {
  /* 在数据加载时保持最小高度 */
  min-height: 400px;
  transition: opacity 0.3s ease-in-out;
}

/* 骨架屏行样式 */
.friend-table :deep(.ant-table-tbody > tr[data-row-key^="skeleton"]) {
  background: #fafafa;
  opacity: 0.7;
}

.friend-table :deep(.ant-table-tbody > tr[data-row-key^="skeleton"]:hover > td) {
  background: #fafafa;
}

/* 好友信息样式 */
.friend-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.friend-avatar {
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.friend-details {
  flex: 1;
  min-width: 0;
}

.friend-name {
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.friend-wechat {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.wechat-icon {
  color: #52c41a;
  font-size: 12px;
}

/* 好友关系ID样式 */
.friend-relation {
  margin-top: 4px;
}

.relation-id {
  font-size: 10px;
  color: #bfbfbf;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 我方微信号样式 */
.my-wechat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.wechat-id {
  display: inline-block;
  padding: 6px 12px;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 1px solid #91d5ff;
  border-radius: 8px;
  color: #1890ff;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  width: fit-content;
}

.account-note {
  font-size: 11px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* 时间显示样式 */
.time-display {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.time-relative {
  font-weight: 600;
  color: #262626;
  font-size: 13px;
  line-height: 1.2;
}

.time-absolute {
  font-size: 11px;
  color: #8c8c8c;
  line-height: 1.2;
  padding: 2px 8px;
  background: #f5f5f5;
  border-radius: 6px;
  display: inline-block;
  width: fit-content;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f0f0f0;
  transform: translateY(-1px);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #8c8c8c;
}

.empty-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #595959;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: #8c8c8c;
}

/* 自定义下拉菜单样式 - 替代a-menu避免DOM Mutation Event警告 */
.custom-dropdown-menu {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 120px;
}

.custom-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #262626;
  gap: 8px;
  user-select: none;
}

.custom-menu-item:hover {
  background-color: #f5f5f5;
}

.custom-menu-item.danger {
  color: #ff4d4f;
}

.custom-menu-item.danger:hover {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.custom-menu-item .menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  font-size: 14px;
}

.menu-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 4px 0;
}

/* 骨架屏样式 */
.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-text {
  height: 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-name {
  width: 120px;
  margin-bottom: 4px;
}

.skeleton-wechat {
  width: 80px;
}

.skeleton-wechat-id {
  width: 100px;
}

.skeleton-time-relative {
  width: 100px;
  height: 16px;
  margin-bottom: 4px;
}

.skeleton-time-absolute {
  width: 120px;
  height: 16px;
}

.skeleton-action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.skeleton-button {
  width: 24px;
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    background-color: #f0f0f0;
  }
  50% {
    background-color: #e0e0e0;
  }
  100% {
    background-color: #f0f0f0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .friend-list-page {
    padding: 16px;
    /* 保持flex布局 */
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    /* 保持固定最小高度 */
    min-height: 120px; /* 移动端需要更多空间 */
  }

  .search-section {
    max-width: none;
    width: 100%;
  }

  .action-section {
    width: 100%;
    justify-content: flex-end;
  }

  .friend-table-container {
    padding: 16px;
    /* 保持flex布局 */
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .friend-info {
    gap: 8px;
  }

  .friend-details {
    min-width: 0;
  }

  .friend-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .wechat-id {
    font-size: 11px;
    padding: 4px 8px;
  }

  .time-display {
    gap: 2px;
  }

  .time-relative {
    font-size: 12px;
  }

  .time-absolute {
    font-size: 10px;
    padding: 1px 6px;
  }
}

@media (max-width: 480px) {
  .friend-list-page {
    padding: 12px;
    /* 保持flex布局 */
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .toolbar {
    padding: 12px;
    /* 保持固定最小高度 */
    min-height: 100px;
  }

  .friend-table-container {
    padding: 12px;
    /* 保持flex布局 */
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .friend-table :deep(.ant-table-thead > tr > th),
  .friend-table :deep(.ant-table-tbody > tr > td) {
    padding: 12px 8px;
  }
}
</style>