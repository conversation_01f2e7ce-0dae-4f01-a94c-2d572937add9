<template>
  <div class="invitation-list">
    <!-- 邀请列表表格 -->
    <a-table
      :columns="columns"
      :data-source="invitationList"
      :loading="loading"
      :pagination="paginationConfig"
      @change="handleTableChange"
      :scroll="{ x: 800 }"
      size="middle"
    >
      <!-- 使用新的 v-slot:bodyCell 语法 -->
      <template #bodyCell="{ column, record }">
        <!-- 被邀请人信息 -->
        <template v-if="column.key === 'inviteeInfo'">
          <div class="invitee-info">
            <div class="invitee-name">
              <a-avatar
                :size="32"
                :src="record.头像URL"
                :style="{ backgroundColor: getAvatarColor(record.被邀请人姓名 || record.手机号) }"
              >
                {{ getAvatarText(record.被邀请人姓名 || record.手机号) }}
              </a-avatar>
              <div class="invitee-details">
                <div class="name">{{ record.被邀请人姓名 || '未注册用户' }}</div>
                <div class="phone">{{ record.手机号 }}</div>
              </div>
            </div>
          </div>
        </template>

        <!-- 邀请状态 -->
        <template v-else-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.状态)">
            {{ getStatusText(record.状态) }}
          </a-tag>
        </template>

        <!-- 邀请角色 -->
        <template v-else-if="column.key === 'role'">
          <a-tag color="blue">{{ record.职位 || '成员' }}</a-tag>
        </template>

        <!-- 邀请时间 -->
        <template v-else-if="column.key === 'inviteTime'">
          <div class="time-info">
            <div>{{ formatDate(record.更新时间 || record.创建时间) }}</div>
            <div class="time-ago">{{ getTimeAgo(record.更新时间 || record.创建时间) }}</div>
          </div>
        </template>

        <!-- 邀请人 -->
        <template v-else-if="column.key === 'inviter'">
          <div class="inviter-info">
            <a-avatar :size="24" :style="{ backgroundColor: getAvatarColor(record.邀请人姓名) }">
              {{ getAvatarText(record.邀请人姓名) }}
            </a-avatar>
            <span class="inviter-name">{{ record.邀请人姓名 }}</span>
          </div>
        </template>

        <!-- 操作按钮 -->
        <template v-else-if="column.key === 'actions'">
          <InvitationActions
            :invitation="record"
            :resend-loading="actionLoading[record.id]?.resend"
            :revoke-loading="actionLoading[record.id]?.revoke"
            @view-link="handleViewInviteLink"
            @resend="handleResendInvitation"
            @view-detail="handleViewDetail"
            @action-success="handleActionSuccess"
          />
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import { computed } from 'vue'
import { formatDate } from '../../utils/teamUtils'
import InvitationActions from './InvitationActions.vue'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

defineOptions({
  name: 'InvitationList'
})

const props = defineProps({
  // 邀请列表数据
  invitationList: {
    type: Array,
    default: () => []
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 分页配置
  pagination: {
    type: Object,
    default: () => ({
      current: 1,
      pageSize: 20,
      total: 0
    })
  },
  // 操作加载状态
  actionLoading: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits([
  'table-change',
  'view-link',
  'resend',
  'view-detail',
  'action-success'
])

// 表格列配置 - 移除已废弃的 slots 属性
const columns = [
  {
    title: '被邀请人',
    key: 'inviteeInfo',
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '角色',
    key: 'role',
    width: 100
  },
  {
    title: '最后操作时间',
    key: 'inviteTime',
    width: 150
  },
  {
    title: '邀请人',
    key: 'inviter',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 160,
    fixed: 'right'
  }
]

// 计算属性
const paginationConfig = computed(() => ({
  current: props.pagination.current,
  pageSize: props.pagination.pageSize,
  total: props.pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

// 工具方法

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  const colorMap = {
    '邀请待处理': 'orange',
    '正常': 'green',
    '已拒绝邀请': 'red',
    '已移除': 'gray'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const textMap = {
    '邀请待处理': '待处理',
    '正常': '已接受',
    '已拒绝邀请': '已拒绝',
    '已移除': '已撤销'
  }
  return textMap[status] || status
}

/**
 * 获取头像颜色
 */
const getAvatarColor = (name) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  return colors[hash % colors.length]
}

/**
 * 获取头像文本
 */
const getAvatarText = (name) => {
  if (!name) return '?'
  return name.length > 1 ? name.slice(-2) : name.slice(-1)
}

/**
 * 获取相对时间
 */
const getTimeAgo = (dateString) => {
  if (!dateString) return ''
  try {
    return dayjs(dateString).fromNow()
  } catch (error) {
    console.warn('相对时间计算失败:', dateString, error)
    return ''
  }
}

// 事件处理方法

/**
 * 表格变化处理
 */
const handleTableChange = (paginationParams) => {
  emit('table-change', paginationParams)
}

/**
 * 查看邀请链接
 */
const handleViewInviteLink = (invitation) => {
  emit('view-link', invitation)
}

/**
 * 重发邀请
 */
const handleResendInvitation = (invitation) => {
  emit('resend', invitation)
}

/**
 * 查看详情
 */
const handleViewDetail = (invitation) => {
  emit('view-detail', invitation)
}

/**
 * 操作成功处理
 */
const handleActionSuccess = (result) => {
  emit('action-success', result)
}
</script>

<style scoped>
.invitation-list {
  /* 列表容器样式 */
}

.invitee-info {
  display: flex;
  align-items: center;
}

.invitee-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.invitee-details {
  display: flex;
  flex-direction: column;
}

.invitee-details .name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.invitee-details .phone {
  font-size: 12px;
  color: #8c8c8c;
}

.time-info {
  display: flex;
  flex-direction: column;
}

.time-info .time-ago {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.inviter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.inviter-name {
  font-size: 13px;
  color: #595959;
}
</style> 