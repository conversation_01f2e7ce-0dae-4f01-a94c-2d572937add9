<template>
  <div class="schema-builder">
    <div class="builder-header">
      <h3>JSON Schema 可视化构建器</h3>
      <p>通过可视化界面构建JSON Schema</p>
    </div>

    <div class="builder-content">
      <a-row :gutter="16">
        <!-- 左侧字段列表 -->
        <a-col :span="12">
          <a-card title="字段定义" size="small">
            <div class="fields-list">
              <div 
                v-for="(field, index) in fields" 
                :key="index"
                class="field-item"
              >
                <a-card size="small" class="field-card">
                  <template #title>
                    <div class="field-title">
                      <span>{{ field.name || `字段${index + 1}` }}</span>
                      <a-button 
                        type="text" 
                        size="small" 
                        danger
                        @click="removeField(index)"
                      >
                        <DeleteOutlined />
                      </a-button>
                    </div>
                  </template>

                  <a-form layout="vertical" size="small">
                    <a-row :gutter="8">
                      <a-col :span="12">
                        <a-form-item label="字段名">
                          <a-input
                            v-model:value="field.name"
                            placeholder="字段名"
                            @change="updateSchema"
                          />
                        </a-form-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-item label="类型">
                          <a-select
                            v-model:value="field.type"
                            @change="updateSchema"
                          >
                            <a-select-option value="string">字符串</a-select-option>
                            <a-select-option value="number">数字</a-select-option>
                            <a-select-option value="integer">整数</a-select-option>
                            <a-select-option value="boolean">布尔值</a-select-option>
                            <a-select-option value="array">数组</a-select-option>
                            <a-select-option value="object">对象</a-select-option>
                          </a-select>
                        </a-form-item>
                      </a-col>
                    </a-row>

                    <a-form-item label="描述">
                      <a-input
                        v-model:value="field.description"
                        placeholder="字段描述"
                        @change="updateSchema"
                      />
                    </a-form-item>

                    <a-form-item label="示例值">
                      <a-input
                        v-model:value="field.example"
                        placeholder="示例值"
                        @change="updateSchema"
                      />
                    </a-form-item>

                    <a-checkbox
                      v-model:checked="field.required"
                      @change="updateSchema"
                    >
                      必需字段
                    </a-checkbox>
                  </a-form>
                </a-card>
              </div>

              <a-button type="dashed" block @click="addField">
                <PlusOutlined /> 添加字段
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧预览 -->
        <a-col :span="12">
          <a-card title="Schema预览" size="small">
            <div class="schema-preview">
              <a-tabs type="card" size="small">
                <a-tab-pane key="json" tab="JSON Schema">
                  <pre class="schema-code">{{ formattedSchema }}</pre>
                </a-tab-pane>
                <a-tab-pane key="example" tab="示例数据">
                  <pre class="example-code">{{ exampleData }}</pre>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <div class="builder-footer">
      <a-space>
        <a-button @click="$emit('close')">取消</a-button>
        <a-button type="primary" @click="applySchema">应用Schema</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  initialSchema: {
    type: Object,
    default: () => null
  }
})

// Emits
const emit = defineEmits(['schema-change', 'close'])

// 字段列表
const fields = ref([])

// 计算生成的Schema
const generatedSchema = computed(() => {
  const schema = {
    type: 'object',
    properties: {},
    required: []
  }

  fields.value.forEach(field => {
    if (field.name) {
      schema.properties[field.name] = {
        type: field.type,
        description: field.description || undefined,
        example: field.example || undefined
      }

      if (field.required) {
        schema.required.push(field.name)
      }
    }
  })

  if (schema.required.length === 0) {
    delete schema.required
  }

  return schema
})

// 格式化的Schema
const formattedSchema = computed(() => {
  return JSON.stringify(generatedSchema.value, null, 2)
})

// 示例数据
const exampleData = computed(() => {
  const example = {}
  
  fields.value.forEach(field => {
    if (field.name) {
      if (field.example) {
        try {
          // 尝试解析JSON格式的示例值
          example[field.name] = JSON.parse(field.example)
        } catch {
          example[field.name] = field.example
        }
      } else {
        // 生成默认示例值
        switch (field.type) {
          case 'string':
            example[field.name] = '示例文本'
            break
          case 'number':
            example[field.name] = 123
            break
          case 'integer':
            example[field.name] = 42
            break
          case 'boolean':
            example[field.name] = true
            break
          case 'array':
            example[field.name] = ['示例项目']
            break
          case 'object':
            example[field.name] = { key: 'value' }
            break
        }
      }
    }
  })
  
  return JSON.stringify(example, null, 2)
})

// 添加字段
const addField = () => {
  fields.value.push({
    name: '',
    type: 'string',
    description: '',
    example: '',
    required: false
  })
}

// 移除字段
const removeField = (index) => {
  fields.value.splice(index, 1)
  updateSchema()
}

// 更新Schema
const updateSchema = () => {
  emit('schema-change', generatedSchema.value)
}

// 应用Schema
const applySchema = () => {
  emit('schema-change', generatedSchema.value)
  emit('close')
}

// 初始化字段
const initializeFields = () => {
  if (props.initialSchema && props.initialSchema.properties) {
    fields.value = Object.entries(props.initialSchema.properties).map(([name, prop]) => ({
      name,
      type: prop.type || 'string',
      description: prop.description || '',
      example: prop.example ? JSON.stringify(prop.example) : '',
      required: props.initialSchema.required?.includes(name) || false
    }))
  } else {
    // 默认添加一个字段
    addField()
  }
}

// 监听初始Schema变化
watch(() => props.initialSchema, initializeFields, { immediate: true })
</script>

<style scoped>
.schema-builder {
  padding: 16px;
}

.builder-header {
  margin-bottom: 16px;
}

.builder-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.builder-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 12px;
}

.builder-content {
  margin-bottom: 16px;
}

.fields-list {
  max-height: 400px;
  overflow-y: auto;
}

.field-item {
  margin-bottom: 12px;
}

.field-card {
  border: 1px solid #f0f0f0;
}

.field-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schema-preview {
  height: 400px;
  overflow: hidden;
}

.schema-code,
.example-code {
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 320px;
  overflow-y: auto;
}

.builder-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-card-head) {
  min-height: auto;
  padding: 8px 12px;
}

:deep(.ant-card-body) {
  padding: 12px;
}

:deep(.ant-tabs-content-holder) {
  padding: 8px 0;
}
</style>
