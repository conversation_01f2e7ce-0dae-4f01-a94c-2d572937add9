/**
 * 模型供应商管理API服务
 * 用于管理AI模型的供应商配置和API密钥
 */

import apiClient from './apiClient'

export const modelProviderService = {


  /**
   * 获取模型配置详情
   * @param {number} modelId - 模型id
   * @returns {Promise} API响应
   */
  async getModelDetail(模型id) {
    try {
      const 响应 = await apiClient.post('/admin/langchain/model-providers/models/detail', { 模型id })

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '获取模型详情失败'
        }
      }
    } catch (错误) {
      console.error('获取模型详情失败:', 错误)
      return {
        success: false,
        error: 错误.userFriendlyMessage || 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 获取模型列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  async getModelList(params = {}) {
    try {
      const requestData = {
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        模型类型: params.模型类型 || null
      }

      const response = await apiClient.post('/admin/langchain/model-providers/models/list', requestData)

      // 适配响应拦截器重构后的格式
      const 状态码 = response.status || response.data?.status
      const 数据 = response.data || response.originalData?.data
      const 消息 = response.message || response.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '获取模型列表失败'
        }
      }
    } catch (error) {
      console.error('获取模型列表失败:', error)
      return {
        success: false,
        error: error.userFriendlyMessage || error.response?.data?.message || error.message || '网络错误'
      }
    }
  },

  /**
   * 获取供应商统计信息 - 适配现有模型管理器格式
   * @returns {Object} 供应商统计
   */
  getProviderStats(modelList) {
    const stats = {}
    
    modelList.forEach(model => {
      const provider = model.类型 || '未知'
      if (!stats[provider]) {
        stats[provider] = {
          总数: 0,
          可用数: 0,
          不可用数: 0
        }
      }
      
      stats[provider].总数++
      if (model.状态 === '已加载') {
        stats[provider].可用数++
      } else {
        stats[provider].不可用数++
      }
    })
    
    return stats
  },



  /**
   * 刷新模型配置
   * @returns {Promise} API响应
   */
  async refreshModelConfig() {
    try {
      const 响应 = await apiClient.post('/admin/langchain/model-providers/refresh')

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '刷新模型配置失败'
        }
      }
    } catch (错误) {
      console.error('刷新模型配置失败:', 错误)
      return {
        success: false,
        error: 错误.userFriendlyMessage || 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 测试文本生成模型连接
   * @param {Object} testData - 测试数据
   * @param {number} testData.模型id - 模型id
   * @param {string} testData.测试消息 - 测试消息
   * @returns {Promise} API响应
   */
  async testModelConnection(测试数据) {
    try {
      const 请求数据 = {
        模型id: 测试数据.模型id,
        测试消息: 测试数据.测试消息 || '你好'
      }

      const 响应 = await apiClient.post('/admin/langchain/model-providers/test-chat', 请求数据)

      console.log('文本生成模型测试响应:', 响应)

      // 简化响应处理，直接返回后端数据
      return 响应
    } catch (错误) {
      console.error('测试模型连接失败:', 错误)
      return {
        success: false,
        error: 错误.userFriendlyMessage || 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 设置默认模型
   * @param {string} 模型名称 - 模型名称
   * @returns {Promise} API响应
   */
  async setDefaultModel(模型名称) {
    try {
      const 请求数据 = {
        模型名称: 模型名称
      }

      const 响应 = await apiClient.post('/admin/langchain/model-providers/default', 请求数据)

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '设置默认模型失败'
        }
      }
    } catch (错误) {
      console.error('设置默认模型失败:', 错误)
      return {
        success: false,
        error: 错误.userFriendlyMessage || 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 更新阿里云配置
   * @param {Object} 配置数据 - 阿里云配置数据
   * @returns {Promise} API响应
   */
  async updateAliyunConfig(配置数据) {
    try {
      const 响应 = await apiClient.post('/admin/langchain/model-providers/config/update', 配置数据)

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '更新阿里云配置失败'
        }
      }
    } catch (错误) {
      console.error('更新阿里云配置失败:', 错误)
      return {
        success: false,
        error: 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 创建模型配置
   * @param {Object} 模型数据 - 模型配置数据
   * @returns {Promise} API响应
   */
  async createModel(模型数据) {
    try {
      const 请求数据 = {
        供应商名称: 模型数据.供应商名称,
        模型名称: 模型数据.模型名称,
        模型类型: 模型数据.模型类型,
        API密钥: 模型数据.API密钥,
        API基础URL: 模型数据.API基础URL || null,
        模型参数: 模型数据.模型参数 || {},
        是否启用: 模型数据.是否启用 !== false,
        备注: 模型数据.备注 || null
      }

      const 响应 = await apiClient.post('/admin/langchain/model-providers/create', 请求数据)

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '创建模型配置失败'
        }
      }
    } catch (错误) {
      console.error('创建模型配置失败:', 错误)
      return {
        success: false,
        error: 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 更新模型配置
   * @param {number} 模型id - 模型数据库ID
   * @param {Object} 更新数据 - 要更新的模型配置数据
   * @returns {Promise} API响应
   */
  async updateModel(模型id, 更新数据) {
    try {
      const 请求数据 = {
        供应商名称: 更新数据.供应商名称,
        模型名称: 更新数据.模型名称,
        模型类型: 更新数据.模型类型,
        API密钥: 更新数据.API密钥,
        API基础URL: 更新数据.API基础URL,
        模型参数: 更新数据.模型参数,
        是否启用: 更新数据.是否启用,
        备注: 更新数据.备注
      }

      // 移除未定义的字段
      Object.keys(请求数据).forEach(key => {
        if (请求数据[key] === undefined) {
          delete 请求数据[key]
        }
      })

      const 响应 = await apiClient.post(`/admin/langchain/model-providers/models/${模型id}/update`, 请求数据)

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '更新模型配置失败'
        }
      }
    } catch (错误) {
      console.error('更新模型配置失败:', 错误)
      return {
        success: false,
        error: 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 删除模型配置
   * @param {number} 模型id - 要删除的模型id
   * @returns {Promise} API响应
   */
  async deleteModel(模型id) {
    try {
      const 响应 = await apiClient.post(`/admin/langchain/model-providers/models/${模型id}/delete`)

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '删除模型配置失败'
        }
      }
    } catch (错误) {
      console.error('删除模型配置失败:', 错误)
      return {
        success: false,
        error: 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 测试向量模型
   * @param {Object} 测试数据 - 向量模型测试数据
   * @returns {Promise} API响应
   */
  async testEmbeddingModel(测试数据) {
    try {
      // 构建测试请求数据
      const 请求数据 = {
        模型id: 测试数据.模型id,
        测试消息: 测试数据.测试文本列表?.[0] || '你好',
        测试文本列表: 测试数据.测试文本列表
      }

      // 使用专门的向量模型测试接口
      const 响应 = await apiClient.post('/admin/langchain/model-providers/test-embedding', 请求数据)

      console.log('向量模型测试响应:', 响应)

      // 简化响应处理，直接返回后端数据
      return 响应
    } catch (错误) {
      console.error('测试向量模型失败:', 错误)
      return {
        success: false,
        error: 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  },

  /**
   * 获取向量模型列表
   * @returns {Promise} API响应
   */
  async getEmbeddingModelList() {
    try {
      const 响应 = await apiClient.get('/admin/langchain/model-providers/embedding-models')

      // 适配响应拦截器重构后的格式
      const 状态码 = 响应.status || 响应.data?.status
      const 数据 = 响应.data || 响应.originalData?.data
      const 消息 = 响应.message || 响应.data?.message

      if (状态码 === 100) {
        return {
          success: true,
          data: 数据,
          message: 消息
        }
      } else {
        return {
          success: false,
          error: 消息 || '获取向量模型列表失败'
        }
      }
    } catch (错误) {
      console.error('获取向量模型列表失败:', 错误)
      return {
        success: false,
        error: 错误.response?.data?.message || 错误.message || '网络错误'
      }
    }
  }
}

export default modelProviderService 