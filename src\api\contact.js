import request from '@/utils/request'

/**
 * 联系方式相关API
 */
export const contactApi = {
  /**
   * 获取联系方式列表
   * 注意：此接口只返回有联系方式的达人记录
   * 抖音达人会额外返回uid_number字段
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码
   * @param {number} params.每页数量 - 每页数量
   * @param {string} params.关键词 - 搜索关键词
   * @param {string} params.平台 - 平台筛选
   * @returns {Promise} API响应
   */
  getContactList(params) {
    return request({
      url: '/kol/contact-list',
      method: 'post',
      data: params
    })
  },

  /**
   * 更新联系方式
   * 注意：联系方式和联系方式类型一旦创建就不能修改，只能更新备注、标签、补充信息
   * @param {Object} params - 更新参数
   * @param {number} params.补充信息id - 补充信息id
   * @param {string} params.个人备注 - 个人备注
   * @param {Array} params.个人标签 - 个人标签
   * @param {Object} params.补充信息 - 补充信息
   * @returns {Promise} API响应
   */
  updateContact(params) {
    return request({
      url: '/kol/update-contact',
      method: 'post',
      data: params
    })
  },

  /**
   * 删除联系方式
   * @param {Object} params - 删除参数
   * @param {number} params.达人id - 达人id
   * @returns {Promise} API响应
   */
  deleteContact(params) {
    return request({
      url: '/kol/delete-contact',
      method: 'post',
      data: params
    })
  },

  /**
   * 批量导出联系方式
   * @param {Object} params - 导出参数
   * @returns {Promise} API响应
   */
  exportContacts(params) {
    return request({
      url: '/kol/export-contacts',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  },



  /**
   * 导入联系方式
   * @param {Object} params - 导入参数
   * @param {string} params.平台类型 - 平台类型（抖音/微信）
   * @param {Array} params.导入数据 - 导入的数据列表
   * @returns {Promise} API响应
   */
  importContacts(params) {
    return request({
      url: '/kol/import-contacts',
      method: 'post',
      data: params
    })
  },

  /**
   * 搜索达人
   * @param {Object} params - 搜索参数
   * @param {string} params.平台账号 - 平台账号
   * @returns {Promise} API响应
   */
  searchTalent(params) {
    return request({
      url: '/kol/search-talent',
      method: 'post',
      data: params
    })
  },

  /**
   * 关联达人
   * @param {Object} params - 关联参数
   * @param {number} params.补充信息id - 用户达人补充信息表id
   * @param {string} params.平台账号 - 平台账号
   * @param {string} params.达人UID - 达人UID，写入kol.达人表的uid_number字段
   * @returns {Promise} API响应
   */
  bindTalent(params) {
    return request({
      url: '/kol/bind-talent',
      method: 'post',
      data: params
    })
  },

  /**
   * 搜索达人
   * @param {Object} params - 搜索参数
   * @param {string} params.平台账号 - 平台账号
   * @returns {Promise} API响应
   */
  searchTalent(params) {
    return request({
      url: '/kol/search-talent',
      method: 'post',
      data: params
    })
  }

}

/**
 * 用户联系人相关API
 */
export const userContactApi = {


  /**
   * 关联已存在的联系人到达人补充信息
   * @param {Object} params - 关联参数
   * @param {number} params.补充信息id - 用户达人补充信息表id
   * @param {string} params.用户联系人id - 用户联系人UUID
   * @returns {Promise} API响应
   */
  associateContact(params) {
    return request({
      url: '/user/user-contact/associate',
      method: 'post',
      data: params
    })
  },

  /**
   * 创建联系人并关联到达人补充信息（事务操作）
   * @param {Object} params - 创建并关联参数
   * @param {string} params.姓名 - 联系人姓名
   * @param {number} params.补充信息id - 用户达人补充信息表id
   * @returns {Promise} API响应
   */
  createAndAssociateContact(params) {
    return request({
      url: '/user/user-contact/create-and-associate',
      method: 'post',
      data: params
    })
  },

  /**
   * 获取用户联系人列表
   * @param {Object} params - 查询参数
   * @param {string} params.关键词 - 搜索关键词（姓名或联系方式）
   * @returns {Promise} API响应
   */
  getContactList(params = {}) {
    return request({
      url: '/user/user-contact/list',
      method: 'post',
      data: params
    })
  }
}

export default contactApi
