import api from './api'

/**
 * 订单管理API服务
 * 统一管理所有订单相关的API调用
 */
class OrderService {
  
  /**
   * 获取会员套餐列表
   * @returns {Promise} 套餐列表数据
   */
  async getMembershipPlans() {
    return await api.post('/order/membership_plans', {})
  }

  /**
   * 创建支付订单
   * @param {number} membershipId - 会员套餐ID
   * @param {string} billingCycle - 付费周期：monthly/yearly
   * @param {string} paymentType - 支付类型：NATIVE/JSAPI，默认NATIVE
   * @returns {Promise} 订单创建结果
   */
  async createOrder(membershipId, billingCycle, paymentType = 'NATIVE') {
    try {
      console.log('创建支付订单:', { membershipId, billingCycle, paymentType })

      const response = await api.post('/order/create', {
        会员id: membershipId,
        付费周期: billingCycle,
        支付类型: paymentType
      })

      console.log('订单创建响应:', response)
      return response
    } catch (error) {
      console.error('创建订单失败:', error)
      throw error
    }
  }

  /**
   * 查询订单状态
   * @param {string} orderNumber - 订单号
   * @returns {Promise} 订单状态信息
   */
  async queryOrder(orderNumber) {
    try {
      console.log('查询订单状态:', orderNumber)

      const response = await api.post('/order/query', {
        订单号: orderNumber
      })

      console.log('订单状态查询响应:', response)
      return response
    } catch (error) {
      console.error('查询订单状态失败:', error)
      throw error
    }
  }

  /**
   * 取消订单
   * @param {string} orderNumber - 订单号
   * @returns {Promise} 取消结果
   */
  async cancelOrder(orderNumber) {
    try {
      console.log('取消订单:', orderNumber)

      const response = await api.post('/order/cancel', {
        订单号: orderNumber
      })

      console.log('取消订单响应:', response)
      return response
    } catch (error) {
      console.error('取消订单失败:', error)
      throw error
    }
  }

  /**
   * 获取用户订单列表
   * @returns {Promise} 用户订单列表
   */
  async getUserOrders() {
    try {
      console.log('获取用户订单列表')

      const response = await api.get('/order/user_orders')

      console.log('用户订单列表响应:', response)
      return response
    } catch (error) {
      console.error('获取用户订单列表失败:', error)
      throw error
    }
  }

  /**
   * 获取用户会员信息
   * @returns {Promise} 用户会员信息
   */
  async getUserMembership() {
    try {
      console.log('获取用户会员信息')

      const response = await api.get('/order/user_membership')

      console.log('用户会员信息响应:', response)
      return response
    } catch (error) {
      console.error('获取用户会员信息失败:', error)
      throw error
    }
  }

  /**
   * 检查支付功能状态
   * @returns {Promise} 支付功能状态
   */
  async checkPaymentStatus() {
    try {
      console.log('检查支付功能状态')

      const response = await api.get('/order/payment_status')

      console.log('支付功能状态响应:', response)
      return response
    } catch (error) {
      console.error('检查支付功能状态失败:', error)
      throw error
    }
  }
}

// 创建订单服务实例
const orderService = new OrderService()

export default orderService 