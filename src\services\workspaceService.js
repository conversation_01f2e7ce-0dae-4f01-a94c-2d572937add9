/**
 * Workspace Service
 * 工作台服务 - 基于真实业务数据的工作台接口
 */
import api from './api.js'

/**
 * Workspace service class for handling all workspace-related API calls
 * 工作台服务类，处理所有工作台相关的API调用
 */
class WorkspaceService {
  constructor() {
    this.baseUrl = '/workspace'
  }

  /**
   * Get WeChat operations data
   * 获取个人微信运营数据
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {string} params.timeRange - Time range ('1d'=今日, 'yesterday'=昨日, '7d'=7天, '30d'=30天, 'custom'=自定义)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} WeChat operations data
   */
  async getWechatOperations(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      const response = await api.post(`${this.baseUrl}/wechat-operations`, {
        用户id: params.userId,
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })
      return response
    } catch (error) {
      console.error('Failed to fetch WeChat operations data:', error)
      throw error
    }
  }

  /**
   * 时间范围参数处理工具
   * 直接传递中文时间范围参数给后端，由后端统一处理
   */
  processTimeRangeParam(timeRange) {
    // 直接返回时间范围参数，由后端进行验证和处理
    return timeRange || '今日'
  }

  /**
   * Get WeChat core metrics data
   * 获取微信运营核心指标数据 - 7个关键指标
   * @param {Object} params - Request parameters
   * @param {string} params.timeRange - Time range (支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} WeChat core metrics data
   */
  async getWechatCoreMetrics(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '今日')

      // 构建完整的API路径
      const apiPath = `${this.baseUrl}/dashboard/wechat-metrics`
      console.log('🔍 微信运营核心指标API调用:', {
        baseUrl: this.baseUrl,
        fullPath: apiPath,
        timeRange: params.timeRange,
        processedTimeRange: timeRange
      })

      // 调用后端API，直接使用中文时间范围参数
      const response = await api.post('/workspace/dashboard/wechat-metrics', {
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })
      return response
    } catch (error) {
      console.error('Failed to fetch WeChat core metrics data:', error)
      throw error
    }
  }

  /**
   * Get invitation business data
   * 获取邀约业务数据
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {string} params.timeRange - Time range (支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} Invitation business data
   */
  async getInvitationBusiness(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      const response = await api.post(`${this.baseUrl}/invitation-business`, {
        用户id: params.userId,
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })
      return response
    } catch (error) {
      console.error('Failed to fetch invitation business data:', error)
      throw error
    }
  }

  /**
   * Get talent management data
   * 获取达人管理数据
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {string} params.timeRange - Time range (支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} Talent management data
   */
  async getTalentManagement(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      const response = await api.post(`${this.baseUrl}/talent-management`, {
        用户id: params.userId,
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })
      return response
    } catch (error) {
      console.error('Failed to fetch talent management data:', error)
      throw error
    }
  }

  /**
   * Get talent management statistics by platform
   * 获取分平台达人管理统计数据
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {string} params.timeRange - Time range (支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} Platform-specific talent management statistics
   */
  async getTalentManagementByPlatform(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      console.log('🎯 调用分平台达人管理统计接口，参数:', {
        用户id: params.userId,
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })

      const response = await api.post(`${this.baseUrl}/talent-management-by-platform`, {
        用户id: params.userId,
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })

      console.log('✅ 分平台达人管理统计接口响应:', response)
      return response
    } catch (error) {
      console.error('❌ 分平台达人管理统计接口调用失败:', error)
      throw error
    }
  }

  // 移除合作项目数据获取方法
  // /**
  //  * Get cooperation projects data
  //  * 获取合作项目数据
  //  * @param {Object} params - Request parameters
  //  * @param {number} params.userId - User ID (optional)
  //  * @param {string} params.timeRange - Time range ('1d'=今日, 'yesterday'=昨日, '7d'=7天, '30d'=30天, 'custom'=自定义)
  //  * @param {string} params.startDate - Start date (for custom time range)
  //  * @param {string} params.endDate - End date (for custom time range)
  //  * @returns {Promise<Object>} Cooperation projects data
  //  */
  // async getCooperationProjects(params = {}) {
  //   try {
  //     const response = await api.post(`${this.baseUrl}/cooperation-projects`, {
  //       用户id: params.userId,
  //       时间范围: params.timeRange || '7d',
  //       开始日期: params.startDate,
  //       结束日期: params.endDate
  //     })
  //     return response
  //   } catch (error) {
  //     console.error('Failed to fetch cooperation projects data:', error)
  //     throw error
  //   }
  // }

  /**
   * Get team overview data
   * 获取团队数据概览
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {number} params.teamId - Team ID (optional)
   * @param {string} params.timeRange - Time range (支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} Team overview data
   */
  async getTeamOverview(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      const response = await api.post(`${this.baseUrl}/team-overview`, {
        用户id: params.userId,
        团队id: params.teamId,
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })
      return response
    } catch (error) {
      console.error('Failed to fetch team overview data:', error)
      throw error
    }
  }

  /**
   * Get trends data
   * 获取趋势数据
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {string} params.dataType - Data type ('invitation', 'cooperation', 'sales')
   * @param {string} params.timeRange - Time range (支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} Trends data
   */
  async getTrends(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      const response = await api.post(`${this.baseUrl}/trends`, {
        用户id: params.userId,
        数据类型: params.dataType || 'invitation',
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })
      return response
    } catch (error) {
      console.error('Failed to fetch trends data:', error)
      throw error
    }
  }

  /**
   * Get multi-metric trends data
   * 获取多指标趋势数据
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {string} params.businessModule - Business module ('wechat', 'invitation', 'talent', 'sample')
   * @param {Array} params.metricList - List of metrics to query
   * @param {string} params.timeDimension - Time dimension ('day', 'week', 'month', 'quarter')
   * @param {string} params.timeRange - Time range (支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度)
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} Multi-metric trends data
   */
  async getMultiMetricTrends(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      const response = await api.post(`${this.baseUrl}/multi-metric-trends`, {
        用户id: params.userId,
        业务模块: params.businessModule || 'wechat',
        指标列表: params.metricList || ['wechat_accounts'],
        时间维度: params.timeDimension || 'week',
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })
      return response
    } catch (error) {
      console.error('Failed to fetch multi-metric trends data:', error)
      throw error
    }
  }

  /**
   * Get todos list
   * 获取待办事项
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @returns {Promise<Object>} Todos list
   */
  async getTodos(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/todos`, {
        用户id: params.userId
      })
      return response
    } catch (error) {
      console.error('Failed to fetch todos:', error)
      throw error
    }
  }

  /**
   * Get sample metrics data
   * 获取寄样统计数据
   * @param {Object} params - Request parameters
   * @param {string} params.timeRange - Time range
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @returns {Promise<Object>} Sample metrics data
   */
  async getSampleMetrics(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本月')

      console.log('🔍 寄样统计数据API调用:', {
        timeRange: params.timeRange,
        processedTimeRange: timeRange,
        startDate: params.startDate,
        endDate: params.endDate
      })

      const response = await api.post('/dashboard/sample-metrics', {
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate
      })

      console.log('✅ 寄样统计数据获取成功:', response)
      return response
    } catch (error) {
      console.error('❌ 获取寄样统计数据失败:', error)
      throw error
    }
  }

  /**
   * Get complete workspace dashboard data
   * 获取工作台完整数据（聚合接口）
   * @param {Object} params - Request parameters
   * @param {number} params.userId - User ID (optional)
   * @param {string} params.timeRange - Time range
   * @param {string} params.startDate - Start date (for custom time range)
   * @param {string} params.endDate - End date (for custom time range)
   * @param {Array<string>} params.modules - Modules to include
   * @returns {Promise<Object>} Complete workspace data
   */
  async getDashboard(params = {}) {
    try {
      // 处理时间范围参数，直接传递给后端
      const timeRange = this.processTimeRangeParam(params.timeRange || '本周')

      const response = await api.post(`${this.baseUrl}/dashboard`, {
        用户id: params.userId,
        时间范围: timeRange,
        开始日期: params.startDate,
        结束日期: params.endDate,
        包含模块: params.modules || ['personal', 'team', 'trends', 'todos']
      })

      console.log('🔍 工作台服务响应:', response)

      // API拦截器已经处理了响应格式，直接返回response
      return response
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
      throw error
    }
  }


}

// Create and export singleton instance
// 创建并导出单例实例
export const workspaceService = new WorkspaceService()
export default workspaceService
