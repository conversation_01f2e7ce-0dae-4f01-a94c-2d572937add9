import api from '../api'
import { isApiSuccess, getApiData, debugApiResponse, processPaginationResponse, processFormResponse } from '../../utils/apiUtils'
import { TEAM_STATUS } from '../../constants/businessStatus'

/**
 * 团队基础服务
 * 处理团队的基本操作：创建、获取详情、列表等
 */
export const teamBasicService = {
  /**
   * 获取用户团队列表
   * @param {Object} params - 查询参数
   * @param {number} params.用户id - 用户id，不传则获取当前用户
   * @param {number} params.页码 - 页码，默认1
   * @param {number} params.每页数量 - 每页数量，默认10
   * @param {string} params.团队关系类型 - 关系类型：all-全部，created-我创建的，managed-我管理的，joined-我所在的
   * @param {string} params.搜索关键词 - 搜索关键词
   * @param {number} params.公司ID - 公司ID筛选
   * @returns {Promise} API响应
   */
  async getUserTeams(params = {}) {
    try {
      const response = await api.post('/team/user/teams', {
        用户id: params.用户id,
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10,
        团队关系类型: params.团队关系类型,
        搜索关键词: params.搜索关键词,
        公司ID: params.公司ID
      })
      
      debugApiResponse(response, '获取用户团队列表')
      
      // 使用统一的分页响应处理
      const result = processPaginationResponse(response, '团队列表')
      
      if (result.success) {
        // 确保数据结构完整，优化团队列表数据
        result.list = result.list.map(team => ({
          ...team,
          // 统一字段处理，确保前端使用的字段名一致
          团队id: team.团队id,
          团队名称: team.团队名称,
          公司名称: team.公司名称,
          用户角色: team.用户角色,
          是否创建者: team.是否创建者,
          可否管理: team.可否管理,
          // 添加格式化字段
          创建时间格式化: team.创建时间 ? new Date(team.创建时间).toLocaleString('zh-CN') : '',
          更新时间格式化: team.更新时间 ? new Date(team.更新时间).toLocaleString('zh-CN') : ''
        }))
        
        return {
          status: 100,
          data: {
            团队列表: result.list,
            总数: result.total
          }
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('获取团队列表失败:', error)
      throw new Error('获取用户团队列表失败: ' + error.message)
    }
  },

  /**
   * 获取用户团队统计
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.用户id - 用户id，不传则获取当前用户
   * @returns {Promise<Object>} 包含用户团队统计数据的响应对象
   */
  async getUserTeamStats(params = {}) {
    try {
      // 调用后端API获取用户团队统计
      const response = await api.post('/team/user/stats', {
        用户id: params.用户id
      })
      
      // 记录API响应信息用于调试
      debugApiResponse(response, '获取用户团队统计')
      
      // 处理成功的API响应
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        // 直接返回后端数据，不做字段转换
        return {
          status: 100,
          data: data
        }

      } else {
        // 处理业务失败响应
        throw new Error(response.message || '获取团队统计失败')
      }
    } catch (error) {
      // 记录详细错误信息
      console.error('获取团队统计失败:', error)
      // 将错误向上传递，由调用者处理
      throw new Error('获取团队统计失败: ' + error.message)
    }
  },

  /**
   * 获取团队详情
   * @param {number|string} teamId - 团队id
   * @param {Object} options - 可选参数
   * @param {boolean} options.包含成员统计 - 是否包含成员统计
   * @param {boolean} options.包含权限信息 - 是否包含权限信息
   * @returns {Promise} API响应
   */
  async getTeamDetail(teamId, options = {}) {
    try {
      // 确保teamId是数字类型
      const numericTeamId = parseInt(teamId)
      if (isNaN(numericTeamId) || numericTeamId <= 0) {
        throw new Error('团队id必须是有效的数字')
      }

      // 记录详细的调试信息
      if (import.meta.env.DEV) {
        console.log('获取团队详情请求:', {
          teamId: numericTeamId,
          options: options,
          timestamp: new Date().toISOString()
        })
      }

      const response = await api.post('/team/detail', {
        团队id: numericTeamId,
        包含成员统计: options.包含成员统计 ?? true,
        包含权限信息: options.包含权限信息 ?? true
      })
      
      debugApiResponse(response, '获取团队详情')
      
      if (isApiSuccess(response)) {
        const data = getApiData(response)
        
        // 优化团队详情数据，确保字段完整性
        const teamDetail = {
          ...data,
          // 格式化时间字段
          创建时间格式化: data?.创建时间 ? new Date(data.创建时间).toLocaleString('zh-CN') : '',
          更新时间格式化: data?.更新时间 ? new Date(data.更新时间).toLocaleString('zh-CN') : '',
          // 确保关键字段存在
          团队id: data?.团队id || numericTeamId,
          团队名称: data?.团队名称 || '',
          公司名称: data?.公司名称 || '',
          用户角色: data?.用户角色 || '访客',
          是否创建者: Boolean(data?.是否创建者),
          可否管理: Boolean(data?.可否管理),
          在团队中: Boolean(data?.在团队中),
          是否团队成员: Boolean(data?.是否团队成员),
          权限列表: Array.isArray(data?.权限列表) ? data.权限列表 : [],
          当前成员数: data?.当前成员数 || data?.实际成员数 || 0,
          最大成员数: data?.最大成员数 || 100
        }
        
        // 记录成功获取的详细信息
        if (import.meta.env.DEV) {
          console.log('团队详情获取成功:', {
            teamId: numericTeamId,
            teamName: teamDetail.团队名称,
            userRole: teamDetail.用户角色,
            memberCount: teamDetail.当前成员数,
            permissions: teamDetail.权限列表
          })
        }
        
        return {
          status: 100,
          data: teamDetail
        }
      } else {
        // 处理各种错误情况
        const errorMessage = response.message || '获取团队详情失败'
        
        // 特殊处理团队不存在的情况
        if (response.status === 404 || errorMessage.includes('团队不存在')) {
          return { 
            error: 'TEAM_NOT_FOUND', 
            message: '团队不存在或已被删除',
            status: 404
          }
        }
        
        // 特殊处理权限问题
        if (response.status === 403 || errorMessage.includes('权限')) {
          return { 
            error: 'PERMISSION_DENIED', 
            message: '无权限访问该团队',
            status: 403
          }
        }
        
        // 特殊处理团队已解散
        if (response.status === 410 || errorMessage.includes('解散')) {
          return { 
            error: 'TEAM_DISSOLVED', 
            message: '团队已解散',
            status: 410
          }
        }
        
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('获取团队详情失败:', {
        teamId: teamId,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
      
      // 如果是网络错误，提供更友好的错误信息
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new Error('请求超时，请检查网络连接后重试')
      }
      
      if (error.code === 'ERR_NETWORK' || error.message.includes('Network Error')) {
        throw new Error('网络连接失败，请检查网络设置')
      }
      
      // 特殊处理团队不存在的情况
      if (error.message && error.message.includes('团队不存在')) {
        return { error: 'TEAM_NOT_FOUND', message: '团队不存在或已被删除' }
      }
      
      throw new Error('获取团队详情失败: ' + error.message)
    }
  },

  /**
   * 创建团队
   * @param {Object} params - 团队信息
   * @param {string} params.团队名称 - 团队名称
   * @param {number} params.公司ID - 公司ID
   * @param {string} params.团队描述 - 团队描述
   * @returns {Promise} API响应
   * @note 最大成员数由后端根据用户会员权限自动设置，无需前端传递
   */
  async createTeam(params) {
    try {
      // 构建请求数据，不包含最大成员数（由后端根据用户会员权限自动设置）
      const requestData = {
        团队名称: params.团队名称,
        公司ID: params.公司ID,
        团队描述: params.团队描述,
      }
      
      const response = await api.post('/team/create', requestData)

      debugApiResponse(response, '创建团队')

      // 检查是否成功
      if (isApiSuccess(response)) {
        return {
          status: 100,
          data: getApiData(response),
          message: response.message || '团队创建成功'
        }
      } else {
        // 创建业务错误对象，包含状态码信息
        const error = new Error(response.message || '创建团队失败')
        error.statusCode = response.status
        error.isLimitReached = response.status === TEAM_STATUS.CREATE_LIMIT_REACHED
        throw error
      }
    } catch (error) {
      // 如果已经是业务错误，直接抛出
      if (error.statusCode) {
        throw error
      }
      // 网络错误等其他错误
      throw new Error('创建团队失败: ' + error.message)
    }
  },

  /**
   * 解散团队
   * @param {number} teamId - 团队id
   * @returns {Promise} API响应
   */
  async dissolveTeam(teamId) {
    try {
      const response = await api.post('/team/dissolve', {
        团队id: parseInt(teamId)
      })

      debugApiResponse(response, '解散团队')

      const result = processFormResponse(response, '团队已成功解散', '解散团队失败')

      if (result.success) {
        return {
          status: 100,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('解散团队失败:', error)
      throw new Error('解散团队失败: ' + error.message)
    }
  },

  /**
   * 获取用户最近活动
   * @param {Object} params - 查询参数
   * @param {number} params.限制数量 - 限制返回的活动数量，默认10
   * @param {number} params.天数 - 查询天数，默认7天
   * @returns {Promise} API响应
   */
  async getUserRecentActivities(params = {}) {
    try {
      const response = await api.post('/team/user/recent-activities', {
        限制数量: params.限制数量 || 10,
        天数: params.天数 || 7
      })

      debugApiResponse(response, '获取用户最近活动')

      if (isApiSuccess(response)) {
        const data = getApiData(response)
        return {
          status: 100,
          data: data?.活动列表 || []
        }
      } else {
        console.warn('获取用户最近活动失败:', response.message)
        return {
          status: response.status || 500,
          data: [],
          message: response.message || '获取用户最近活动失败'
        }
      }
    } catch (error) {
      console.error('获取用户最近活动失败:', error)
      return {
        status: 500,
        data: [],
        message: '获取用户最近活动失败: ' + error.message
      }
    }
  },

  /**
   * 退出团队
   * @param {Object} params - 参数
   * @param {number} params.团队id - 团队id
   * @returns {Promise} API响应
   */
  async leaveTeam(params) {
    try {
      const response = await api.post('/team/leave', {
        团队id: params.团队id
      })
      
      debugApiResponse(response, '退出团队')
      
      const result = processFormResponse(response, '已成功退出团队', '退出团队失败')
      
      if (result.success) {
        return {
          status: 100,
          message: result.message
        }
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('退出团队失败:', error)
      throw new Error('退出团队失败: ' + error.message)
    }
  },

  /**
   * 获取公司列表
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码
   * @param {number} params.每页数量 - 每页数量
   * @param {string} params.搜索关键词 - 搜索关键词
   * @returns {Promise<Object>} 包含公司列表和分页信息的响应对象
   */
  async getCompanyList(params = {}) {
    try {
      // 调用后端API获取公司列表
      // 根据后端路由定义，正确的路径是'/team/company/list'
      const response = await api.post('/team/company/list', {
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 50,
        搜索关键词: params.搜索关键词,
        公司状态: params.公司状态,       // 可选参数
        审核状态: params.审核状态        // 可选参数
      })
      
      // 记录API响应信息用于调试
      debugApiResponse(response, '获取公司列表')
      
      // 处理响应数据，转换为标准格式
      const result = processPaginationResponse(response, '公司列表')
      
      // 处理成功的API响应
      if (result.success) {
        return {
          status: 100,
          data: {
            公司列表: result.list,
            总数: result.total
          }
        }
      } else {
        // 处理业务失败响应
        throw new Error(result.message || '获取公司列表失败')
      }
    } catch (error) {
      // 记录详细错误信息
      console.error('获取公司列表失败:', error)
      // 将错误向上传递，由调用者处理
      throw new Error('获取公司列表失败: ' + error.message)
    }
  },

  /**
   * 创建公司
   * 
   * @param {Object} params - 公司信息
   * @param {string} params.公司名称 - 公司名称（必填）
   * @param {string} params.公司简称 - 公司简称（可选）
   * @param {string} params.公司代码 - 公司代码（可选）
   * @param {string} params.公司地址 - 公司地址（可选）
   * @param {string} params.联系电话 - 联系电话（可选）
   * @param {string} params.邮箱 - 公司邮箱（可选）
   * @param {string} params.法人代表 - 法人代表（可选）
   * @param {string} params.营业执照号 - 营业执照号（可选）
   * @param {string} params.备注 - 备注信息（可选）
   * @returns {Promise<Object>} 创建结果响应对象
   */
  async createCompany(params = {}) {
    try {
      // 调用后端API创建公司
      // 根据后端路由定义，正确的路径是'/team/company/create'
      const response = await api.post('/team/company/create', {
        公司名称: params.公司名称,
        公司简称: params.公司简称,
        公司代码: params.公司代码,
        公司地址: params.公司地址,
        联系电话: params.联系电话,
        邮箱: params.邮箱,
        法人代表: params.法人代表,
        营业执照号: params.营业执照号,
        备注: params.备注
      })
      
      // 直接返回后端响应，保持业务状态码
      return {
        status: response?.data?.status || response?.status || 100,
        data: response?.data?.data || response?.data,
        message: response?.data?.message || response?.message || '操作完成'
      }
    } catch (error) {
      console.error('创建公司失败:', error)
      return {
        status: 5002,
        data: null,
        message: error.message || '创建公司失败'
      }
    }
  },

  /**
   * 智能退出团队
   * @param {Object} params - 参数
   * @param {number} params.团队id - 团队id
   * @param {boolean} params.确认解散 - 如果是单人团队，是否确认解散
   * @returns {Promise} API响应
   */
  async smartLeaveTeam(params) {
    try {
      const response = await api.post('/team/smart-leave', {
        团队id: params.团队id,
        确认解散: params.确认解散 || false
      })
      
      debugApiResponse(response, '智能退出团队')
      
      if (isApiSuccess(response)) {
        return {
          status: 100,
          data: getApiData(response),
          message: response.message || '操作成功'
        }
      } else {
        throw new Error(response.message || '智能退出团队失败')
      }
    } catch (error) {
      console.error('智能退出团队失败:', error)
      throw new Error('智能退出团队失败: ' + error.message)
    }
  },

  /**
   * 转移团队所有权
   * @param {Object} params - 参数  
   * @param {number} params.团队id - 团队id
   * @param {number} params.新所有者用户id - 新所有者的用户id
   * @param {boolean} params.转移后退出 - 转移后是否立即退出团队
   * @returns {Promise} API响应
   */
  async transferOwnership(params) {
    try {
      const response = await api.post('/team/transfer-ownership', {
        团队id: params.团队id,
        新所有者用户id: params.新所有者用户id,
        转移后退出: params.转移后退出 || false
      })
      
      debugApiResponse(response, '转移团队所有权')
      
      if (isApiSuccess(response)) {
        return {
          status: 100,
          data: getApiData(response),
          message: response.message || '转移成功'
        }
      } else {
        throw new Error(response.message || '转移所有权失败')
      }
    } catch (error) {
      console.error('转移团队所有权失败:', error)
      throw new Error('转移团队所有权失败: ' + error.message)
    }
  },

  /**
   * 更新公司信息
   * @param {Object} params - 公司信息
   * @param {number} params.公司ID - 公司ID
   * @param {string} params.公司名称 - 公司名称（必填）
   * @param {string} params.公司简称 - 公司简称（可选）
   * @param {string} params.公司代码 - 公司代码（可选）
   * @param {string} params.公司地址 - 公司地址（可选）
   * @param {string} params.联系电话 - 联系电话（可选）
   * @param {string} params.邮箱 - 公司邮箱（可选）
   * @param {string} params.法人代表 - 法人代表（可选）
   * @param {string} params.营业执照号 - 营业执照号（可选）
   * @param {string} params.备注 - 备注信息（可选）
   * @returns {Promise<Object>} 更新结果响应对象
   */
  async updateCompany(params = {}) {
    try {
      // 调用后端API更新公司
      const response = await api.put('/team/company/update', {
        公司ID: params.公司ID,
        公司名称: params.公司名称,
        公司简称: params.公司简称,
        公司代码: params.公司代码,
        公司地址: params.公司地址,
        联系电话: params.联系电话,
        邮箱: params.邮箱,
        法人代表: params.法人代表,
        营业执照号: params.营业执照号,
        备注: params.备注
      })

      // 直接返回后端响应，保持业务状态码
      return {
        status: response?.data?.status || response?.status || 100,
        data: response?.data?.data || response?.data,
        message: response?.data?.message || response?.message || '操作完成'
      }
    } catch (error) {
      console.error('更新公司失败:', error)
      return {
        status: 5002,
        data: null,
        message: error.message || '更新公司失败'
      }
    }
  }
}

// 导出服务对象
export default teamBasicService