/**
 * 日期工具函数
 * 提供统一的日期格式化和处理功能
 */

/**
 * 格式化日期为本地字符串
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (dateString, options = {}) => {
  if (!dateString) return '未知时间';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  
  const defaultOptions = {
    hour12: false,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    ...options
  };
  
  return date.toLocaleString('zh-CN', defaultOptions);
};

/**
 * 格式化日期为简短格式（仅日期）
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @returns {string} 格式化后的日期字符串
 */
export const formatDateShort = (dateString) => {
  return formatDate(dateString, {
    hour: undefined,
    minute: undefined,
    second: undefined
  });
};

/**
 * 格式化日期为相对时间（如：2小时前）
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @returns {string} 相对时间字符串
 */
export const formatRelativeTime = (dateString) => {
  if (!dateString) return '未知时间';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  
  const now = new Date();
  const diffMs = now - date;
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) return '刚刚';
  if (diffMinutes < 60) return `${diffMinutes}分钟前`;
  if (diffHours < 24) return `${diffHours}小时前`;
  if (diffDays < 7) return `${diffDays}天前`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`;
  return `${Math.floor(diffDays / 365)}年前`;
};

/**
 * 计算账户年龄
 * @param {string|Date} createdDate - 创建日期
 * @returns {string} 账户年龄描述
 */
export const getAccountAge = (createdDate) => {
  if (!createdDate) return '未知';
  
  const created = new Date(createdDate);
  const now = new Date();
  const diffTime = Math.abs(now - created);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 30) return `${diffDays} 天`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} 个月`;
  return `${Math.floor(diffDays / 365)} 年`;
};

/**
 * 检查日期是否有效
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @returns {boolean} 是否为有效日期
 */
export const isValidDate = (dateString) => {
  if (!dateString) return false;
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

/**
 * 格式化时间范围
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {string} 时间范围字符串
 */
export const formatDateRange = (startDate, endDate) => {
  const start = startDate ? formatDate(startDate) : '未设置';
  const end = endDate ? formatDate(endDate) : '未设置';
  return `${start} ~ ${end}`;
}; 