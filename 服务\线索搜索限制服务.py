from datetime import date
from typing import Any, Dict, Optional

from fastapi import HTTPException, status

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器, 错误日志器
from 状态 import 状态


class 线索搜索限制服务:
    """
    线索搜索和翻页限制服务
    负责处理用户搜索次数检查、翻页次数限制、记录更新和会员状态验证
    """

    # 搜索次数限制配置
    非会员每日搜索限制 = 10
    会员每日搜索限制 = 100

    # 翻页次数限制配置（适用于所有用户）
    每日翻页限制 = 100  # 修改为每日100页限制
    翻页警告阈值 = 80  # 80次时开始提醒
    翻页严重警告阈值 = 90  # 90次时严重警告

    def __init__(self):
        """初始化服务"""
        应用日志器.info("线索搜索限制服务初始化完成")

    async def 检查用户搜索权限(self, 用户id: int) -> Dict[str, Any]:
        """
        检查用户是否还能继续搜索

        Args:
            用户id: 用户id

        Returns:
            Dict包含检查结果：
            - can_search: bool 是否可以搜索
            - is_member: bool 是否为会员
            - used_count: int 今日已使用次数
            - limit_count: int 每日限制次数
            - remaining_count: int 剩余次数
            - message: str 提示信息
        """
        try:
            应用日志器.info(f"开始检查用户 {用户id} 的搜索权限")

            # 1. 检查用户会员状态
            会员状态 = await self._获取用户会员状态(用户id)
            是否为会员 = 会员状态["is_member"]

            # 2. 获取今日搜索次数
            今日搜索次数 = await self._获取今日搜索次数(用户id)

            # 3. 确定搜索限制
            搜索限制 = self.会员每日搜索限制 if 是否为会员 else self.非会员每日搜索限制

            # 4. 计算剩余次数
            剩余次数 = max(0, 搜索限制 - 今日搜索次数)
            可以搜索 = 剩余次数 > 0

            # 5. 生成提示信息
            if 可以搜索:
                if 是否为会员:
                    消息 = f"会员用户，今日还可搜索 {剩余次数} 次"
                else:
                    消息 = f"非会员用户，今日还可搜索 {剩余次数} 次"
            else:
                if 是否为会员:
                    消息 = f"会员用户今日搜索次数已达上限({搜索限制}次)，请明日再试"
                else:
                    消息 = f"非会员用户今日搜索次数已达上限({搜索限制}次)，升级会员可获得更多搜索次数"

            结果 = {
                "can_search": 可以搜索,
                "is_member": 是否为会员,
                "used_count": 今日搜索次数,
                "limit_count": 搜索限制,
                "remaining_count": 剩余次数,
                "message": 消息,
                "member_info": 会员状态,
            }

            应用日志器.info(f"用户 {用户id} 搜索权限检查完成: {结果}")
            return 结果

        except Exception as e:
            错误日志器.error(f"检查用户 {用户id} 搜索权限失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.线索搜索.搜索权限检查失败,
                    "message": "检查搜索权限时发生错误，请稍后重试",
                },
            )

    async def 记录搜索行为(self, 用户id: int) -> bool:
        """
        记录用户搜索行为，更新搜索次数

        Args:
            用户id: 用户id

        Returns:
            bool: 记录是否成功
        """
        try:
            应用日志器.info(f"开始记录用户 {用户id} 的搜索行为")

            今日日期 = date.today().strftime("%Y-%m-%d")

            # 检查今日是否已有记录
            现有记录 = await self._获取今日搜索记录(用户id, 今日日期)

            if 现有记录:
                # 更新现有记录
                新次数 = 现有记录["用户线索查询次数"] + 1
                更新sql = """
                UPDATE 用户_线索查询_记录表
                SET 用户线索查询次数 = $1
                WHERE 用户id = $2 AND 查询日期 = $3
                """
                await 异步连接池实例.执行更新(更新sql, (新次数, 用户id, 今日日期))
                应用日志器.info(f"更新用户 {用户id} 今日搜索次数为 {新次数}")
            else:
                # 创建新记录
                插入sql = """
                INSERT INTO 用户_线索查询_记录表 (用户id, 用户线索查询次数, 查询日期)
                VALUES ($1, 1, $2)
                """
                await 异步连接池实例.执行插入(插入sql, (用户id, 今日日期))
                应用日志器.info(f"为用户 {用户id} 创建今日首次搜索记录")

            return True

        except Exception as e:
            错误日志器.error(f"记录用户 {用户id} 搜索行为失败: {str(e)}", exc_info=True)
            return False

    async def 检查用户翻页权限(self, 用户id: int) -> Dict[str, Any]:
        """
        检查用户是否还能继续翻页

        Args:
            用户id: 用户id

        Returns:
            Dict包含检查结果：
            - can_paginate: bool 是否可以翻页
            - used_count: int 今日已翻页次数
            - limit_count: int 每日翻页限制
            - remaining_count: int 剩余翻页次数
            - warning_level: str 警告级别 (none/warning/severe)
            - message: str 提示信息
        """
        try:
            应用日志器.info(f"开始检查用户 {用户id} 的翻页权限")

            # 获取今日翻页次数
            今日翻页次数 = await self._获取今日翻页次数(用户id)

            # 计算剩余次数
            剩余次数 = max(0, self.每日翻页限制 - 今日翻页次数)
            可以翻页 = 剩余次数 > 0

            # 确定警告级别和提示信息
            if 今日翻页次数 >= self.翻页严重警告阈值:
                警告级别 = "severe"
                if 可以翻页:
                    消息 = f"翻页次数即将达到上限，今日还可翻页 {剩余次数} 次，建议使用搜索功能精确查找"
                else:
                    消息 = f"今日翻页次数已达上限({self.每日翻页限制}次)，请明日再试或使用搜索功能"
            elif 今日翻页次数 >= self.翻页警告阈值:
                警告级别 = "warning"
                消息 = f"翻页次数较多，今日还可翻页 {剩余次数} 次，建议使用搜索功能提高效率"
            else:
                警告级别 = "none"
                消息 = f"今日还可翻页 {剩余次数} 次"

            结果 = {
                "can_paginate": 可以翻页,
                "used_count": 今日翻页次数,
                "limit_count": self.每日翻页限制,
                "remaining_count": 剩余次数,
                "warning_level": 警告级别,
                "message": 消息,
            }

            应用日志器.info(f"用户 {用户id} 翻页权限检查完成: {结果}")
            return 结果

        except Exception as e:
            错误日志器.error(f"检查用户 {用户id} 翻页权限失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.线索搜索.翻页权限检查失败,
                    "message": "检查翻页权限时发生错误，请稍后重试",
                },
            )

    async def 记录翻页行为(self, 用户id: int) -> bool:
        """
        记录用户翻页行为，更新翻页次数
        与搜索行为记录逻辑保持一致：如果当天没有记录就新增，有记录就更新

        Args:
            用户id: 用户id

        Returns:
            bool: 记录是否成功
        """
        try:
            应用日志器.info(f"开始记录用户 {用户id} 的翻页行为")

            今日日期 = date.today().strftime("%Y-%m-%d")

            # 检查今日是否已有记录（使用与搜索记录相同的方法）
            现有记录 = await self._获取今日搜索记录(用户id, 今日日期)

            if 现有记录:
                # 更新现有记录的翻页次数（保持搜索次数不变）
                新翻页次数 = (现有记录.get("用户翻页次数") or 0) + 1
                更新sql = """
                UPDATE 用户_线索查询_记录表
                SET 用户翻页次数 = $1
                WHERE 用户id = $2 AND 查询日期 = $3
                """
                await 异步连接池实例.执行更新(
                    更新sql, (新翻页次数, 用户id, 今日日期)
                )
                应用日志器.info(f"更新用户 {用户id} 今日翻页次数为 {新翻页次数}")
            else:
                # 创建新记录，初始化翻页次数为1，搜索次数为0（与搜索记录逻辑一致）
                插入sql = """
                INSERT INTO 用户_线索查询_记录表 (用户id, 用户翻页次数, 用户线索查询次数, 查询日期)
                VALUES ($1, 1, 0, $2)
                """
                await 异步连接池实例.执行插入(插入sql, (用户id, 今日日期))
                应用日志器.info(f"为用户 {用户id} 创建今日首次翻页记录")

            return True

        except Exception as e:
            错误日志器.error(f"记录用户 {用户id} 翻页行为失败: {str(e)}", exc_info=True)
            return False

    async def _获取用户会员状态(self, 用户id: int) -> Dict[str, Any]:
        """
        获取用户会员状态

        Args:
            用户id: 用户id

        Returns:
            Dict包含会员信息
        """
        try:
            # 查询用户当前有效的会员信息
            会员查询sql = """
            SELECT um.id, um.用户id, um.会员id, um.开通时间, um.到期时间,
                   m.名称 as 会员名称, m.每月费用, m.每年费用, m.每月算力点
            FROM 用户_会员_关联表 um
            LEFT JOIN 会员表 m ON um.会员id = m.id
            WHERE um.用户id = $1
            AND TO_TIMESTAMP(um.到期时间, 'YYYY-MM-DD HH24:MI:SS') > NOW()
            ORDER BY TO_TIMESTAMP(um.到期时间, 'YYYY-MM-DD HH24:MI:SS') DESC
            LIMIT 1
            """

            会员记录 = await 异步连接池实例.执行查询(会员查询sql, (用户id,))

            if 会员记录:
                会员信息 = 会员记录[0]
                return {
                    "is_member": True,
                    "member_name": 会员信息.get("会员名称"),
                    "expire_time": 会员信息.get("到期时间"),
                    "member_id": 会员信息.get("会员id"),
                }
            else:
                return {
                    "is_member": False,
                    "member_name": None,
                    "expire_time": None,
                    "member_id": None,
                }

        except Exception as e:
            错误日志器.error(f"获取用户 {用户id} 会员状态失败: {str(e)}", exc_info=True)
            # 出错时默认为非会员，确保系统可用性
            return {
                "is_member": False,
                "member_name": None,
                "expire_time": None,
                "member_id": None,
            }

    async def _获取今日搜索次数(self, 用户id: int) -> int:
        """
        获取用户今日搜索次数

        Args:
            用户id: 用户id

        Returns:
            int: 今日搜索次数
        """
        try:
            今日日期 = date.today().strftime("%Y-%m-%d")

            查询sql = """
            SELECT 用户线索查询次数
            FROM 用户_线索查询_记录表
            WHERE 用户id = $1 AND 查询日期 = $2
            """

            结果 = await 异步连接池实例.执行查询(查询sql, (用户id, 今日日期))

            if 结果:
                return 结果[0]["用户线索查询次数"] or 0
            else:
                return 0

        except Exception as e:
            错误日志器.error(
                f"获取用户 {用户id} 今日搜索次数失败: {str(e)}", exc_info=True
            )
            return 0  # 出错时返回0，确保系统可用性

    async def _获取今日翻页次数(self, 用户id: int) -> int:
        """
        获取用户今日翻页次数

        Args:
            用户id: 用户id

        Returns:
            int: 今日翻页次数
        """
        try:
            今日日期 = date.today().strftime("%Y-%m-%d")

            查询sql = """
            SELECT 用户翻页次数
            FROM 用户_线索查询_记录表
            WHERE 用户id = $1 AND 查询日期 = $2
            """

            结果 = await 异步连接池实例.执行查询(查询sql, (用户id, 今日日期))

            if 结果:
                return 结果[0]["用户翻页次数"] or 0
            else:
                return 0

        except Exception as e:
            错误日志器.error(
                f"获取用户 {用户id} 今日翻页次数失败: {str(e)}", exc_info=True
            )
            return 0  # 出错时返回0，确保系统可用性

    async def _获取今日搜索记录(
        self, 用户id: int, 日期: str
    ) -> Optional[Dict[str, Any]]:
        """
        获取用户指定日期的搜索记录

        Args:
            用户id: 用户id
            日期: 查询日期 (YYYY-MM-DD格式)

        Returns:
            Optional[Dict]: 搜索记录或None
        """
        try:
            查询sql = """
            SELECT id, 用户id, 用户线索查询次数, 用户翻页次数, 查询日期
            FROM 用户_线索查询_记录表
            WHERE 用户id = $1 AND 查询日期 = $2
            """

            结果 = await 异步连接池实例.执行查询(查询sql, (用户id, 日期))

            return 结果[0] if 结果 else None

        except Exception as e:
            错误日志器.error(
                f"获取用户 {用户id} 日期 {日期} 搜索记录失败: {str(e)}", exc_info=True
            )
            return None
