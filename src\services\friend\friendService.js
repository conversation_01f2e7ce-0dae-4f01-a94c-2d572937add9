import api from '../api'

/**
 * 微信好友管理服务
 * 提供微信好友的CRUD操作和管理功能
 */
class FriendService {
  /**
   * 获取用户所有微信好友列表
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码
   * @param {number} params.每页条数 - 每页条数
   * @param {string} params.关键词 - 搜索关键词
   * @param {string} params.好友类型 - 好友类型筛选
   * @param {string} params.排序字段 - 排序字段
   * @param {string} params.排序方向 - 排序方向 ASC/DESC
   * @returns {Promise<Object>} 好友列表数据
   */
  async getUserFriends(params = {}) {
    try {
      const queryParams = {
        页码: params.页码 || 1,
        每页条数: params.每页条数 || 20,
        关键词: params.关键词 || null,
        好友类型: params.好友类型 || null,
        排序字段: params.排序字段 || null,
        排序方向: params.排序方向 || 'DESC'
      }

      const response = await api.post('/wechat/user-friends', queryParams)
      
      if (response.status === 100) {
        // 确保数据结构安全，处理后端返回的数据
        const responseData = response.data || {}
        const friendList = responseData.好友列表 || []
        const total = responseData.总数 || 0
        
        // 数据后处理：确保每个好友记录都有必要的字段
        const processedFriendList = friendList.map(friend => ({
          ...friend,
          好友ID: friend.对方微信号id || friend.好友ID, // 使用对方微信号id作为好友ID，用于删除操作
          好友类型: friend.好友类型 || '普通',
          状态: friend.状态 !== undefined ? friend.状态 : 1,
          互动次数: friend.互动次数 || 0
        }))
        
        return {
          好友列表: processedFriendList,
          总数: total,
          总页数: Math.ceil(total / queryParams.每页条数)
        }
      } else {
        throw new Error(response.message || '获取好友列表失败')
      }
    } catch (error) {
      console.error('获取微信好友列表失败:', error)
      throw new Error(`获取好友列表失败: ${error.message}`)
    }
  }

  /**
   * 获取微信好友列表（支持分页和搜索）
   * @param {Object} params - 查询参数
   * @param {number} params.微信账号id - 微信账号id
   * @param {number} params.页码 - 页码
   * @param {number} params.每页条数 - 每页条数
   * @param {string} params.好友类型 - 好友类型筛选
   * @param {string} params.关键词 - 搜索关键词
   * @returns {Promise<Object>} 好友列表数据
   */
  async getWeChatFriends(params) {
    try {
      const queryParams = {
        微信账号id: params.微信账号id || null,
        页码: params.页码 || 1,
        每页条数: params.每页条数 || 20,
        好友类型: params.好友类型 || null,
        关键词: params.关键词 || null
      }

      const response = await api.post('/wechat/friends', queryParams)
      
      console.log('微信好友列表API响应:', response) // 调试日志
      
      if (response.status === 100) {
        // 直接返回数据结构，确保前端能正确访问
        return {
          好友列表: response.data?.好友列表 || response.data?.列表 || [],
          总数: response.data?.总数 || 0,
          总页数: response.data?.总页数 || Math.ceil((response.data?.总数 || 0) / queryParams.每页条数)
        }
      } else {
        throw new Error(response.message || '获取好友列表失败')
      }
    } catch (error) {
      console.error('获取微信好友列表失败:', error)
      throw new Error(`获取好友列表失败: ${error.message}`)
    }
  }

  /**
   * 获取指定微信账号的好友列表
   * @param {Object} params - 查询参数
   * @param {number} params.微信id - 微信账号id
   * @param {number} params.页码 - 页码
   * @param {number} params.每页条数 - 每页条数
   * @param {string} params.关键词 - 搜索关键词
   * @returns {Promise<Object>} 好友列表数据
   */
  async getAccountFriends(params) {
    try {
      const queryParams = {
        微信id: params.微信id,
        页码: params.页码 || 1,
        每页条数: params.每页条数 || 20,
        关键词: params.关键词 || null
      }

      const response = await api.post('/wechat/account-friends', queryParams)
      
      console.log('指定微信账号好友列表API响应:', response) // 调试日志
      
      if (response.status === 100) {
        // 直接返回数据结构，确保前端能正确访问
        return {
          好友列表: response.data?.好友列表 || response.data?.列表 || [],
          总数: response.data?.总数 || 0,
          总页数: response.data?.总页数 || Math.ceil((response.data?.总数 || 0) / queryParams.每页条数),
          微信账号信息: response.data?.微信账号信息 || {}
        }
      } else {
        throw new Error(response.message || '获取好友列表失败')
      }
    } catch (error) {
      console.error('获取指定微信好友列表失败:', error)
      throw new Error(`获取好友列表失败: ${error.message}`)
    }
  }

  /**
   * 添加微信好友
   * @param {Object} friendData - 好友数据
   * @param {string} friendData.我方微信号 - 我方微信号
   * @param {string} friendData.对方微信号 - 对方微信号
   * @returns {Promise<Object>} 添加结果
   */
  async addWeChatFriend(friendData) {
    try {
      const response = await api.post('/wechat/add_wechat_friend', friendData)
      
      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信好友添加成功',
          data: response.data
        }
      } else {
        throw new Error(response.message || '添加好友失败')
      }
    } catch (error) {
      console.error('添加微信好友失败:', error)
      throw new Error(`添加好友失败: ${error.message}`)
    }
  }

  /**
   * 添加微信好友（新版本）
   * @param {Object} friendData - 好友数据
   * @param {number} friendData.微信账号id - 微信账号id
   * @param {string} friendData.好友微信号 - 好友微信号
   * @param {string} friendData.好友昵称 - 好友昵称
   * @param {string} friendData.好友头像 - 好友头像
   * @param {string} friendData.好友类型 - 好友类型
   * @param {string} friendData.备注 - 备注信息
   * @returns {Promise<Object>} 添加结果
   */
  async addFriend(friendData) {
    try {
      const response = await api.post('/wechat/friends/add', friendData)
      
      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信好友添加成功',
          data: response.data
        }
      } else {
        throw new Error(response.message || '添加好友失败')
      }
    } catch (error) {
      console.error('添加微信好友失败:', error)
      throw new Error(`添加好友失败: ${error.message}`)
    }
  }

  /**
   * 更新微信好友
   * @param {Object} friendData - 好友数据
   * @param {number} friendData.我方微信号id - 我方微信号id（必需）
   * @param {number} friendData.对方微信号id - 对方微信号id（必需）
   * @param {number} friendData.识别id - 识别id（可选）
   * @param {number} friendData.是否失效 - 是否失效状态（可选）
   * @param {string} friendData.添加好友时间 - 添加好友时间（可选）
   * @param {string} friendData.好友入库时间 - 好友入库时间（可选）
   * @param {string} friendData.我方最后一条消息发送时间 - 我方最后一条消息发送时间（可选）
   * @param {string} friendData.对方最后一条消息发送时间 - 对方最后一条消息发送时间（可选）
   * @param {string} friendData.备注 - 备注信息，最多500字符（可选）
   * @returns {Promise<Object>} 更新结果
   */
  async updateFriend(friendData) {
    try {
      const response = await api.post('/wechat/friends/update', friendData)
      
      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信好友更新成功',
          data: response.data
        }
      } else {
        throw new Error(response.message || '更新好友失败')
      }
    } catch (error) {
      console.error('更新微信好友失败:', error)
      throw new Error(`更新好友失败: ${error.message}`)
    }
  }

  /**
   * 删除微信好友
   * @param {number} friendId - 好友ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteFriend(friendId) {
    try {
      const response = await api.post('/wechat/friends/delete', {
        好友ID: friendId
      })

      if (response.status === 100) {
        return {
          status: 100,
          message: response.message || '微信好友删除成功'
        }
      } else {
        throw new Error(response.message || '删除好友失败')
      }
    } catch (error) {
      console.error('删除微信好友失败:', error)
      throw new Error(`删除好友失败: ${error.message}`)
    }
  }

  /**
   * 批量删除微信好友
   * @param {Array} friendIds - 好友ID列表
   * @returns {Promise<Object>} 批量删除结果
   */
  async batchDeleteFriends(friendIds) {
    try {
      // 由于后端没有批量删除接口，使用循环调用单个删除接口
      const results = []
      let successCount = 0
      let failCount = 0

      for (const friendId of friendIds) {
        try {
          await this.deleteFriend(friendId)
          results.push({ 好友ID: friendId, success: true })
          successCount++
        } catch (error) {
          results.push({ 好友ID: friendId, success: false, error: error.message })
          failCount++
        }
      }

      if (failCount === 0) {
        return {
          status: 100,
          message: `成功删除 ${successCount} 个好友`
        }
      } else if (successCount > 0) {
        return {
          status: 100,
          message: `删除完成：成功 ${successCount} 个，失败 ${failCount} 个`
        }
      } else {
        throw new Error(`批量删除失败：${failCount} 个好友删除失败`)
      }
    } catch (error) {
      console.error('批量删除微信好友失败:', error)
      throw new Error(`批量删除失败: ${error.message}`)
    }
  }

  /**
   * 通过ID获取微信好友信息
   * @param {Object} params - 查询参数
   * @param {number} params.我方微信号id - 我方微信号id
   * @param {number} params.好友识别id - 好友识别id
   * @returns {Promise<Object>} 好友详情
   */
  async getFriendById(params) {
    try {
      const response = await api.post('/wechat/get_wechat_friend_by_id', params)
      
      if (response.status === 100) {
        return {
          status: 100,
          data: response.data || {}
        }
      } else {
        throw new Error(response.message || '获取好友详情失败')
      }
    } catch (error) {
      console.error('获取微信好友详情失败:', error)
      throw new Error(`获取好友详情失败: ${error.message}`)
    }
  }

  /**
   * 获取微信好友统计数据
   * @param {Object} params - 统计参数
   * @param {number} params.微信账号id - 微信账号id
   * @param {string} params.时间范围 - 时间范围
   * @returns {Promise<Object>} 统计数据
   */
  async getFriendStats(params) {
    try {
      const queryParams = {
        微信账号id: params.微信账号id,
        时间范围: params.时间范围 || '30d'
      }

      const response = await api.post('/wechat/friends/stats', queryParams)
      
      if (response.status === 100) {
        return {
          status: 100,
          data: response.data || {}
        }
      } else {
        throw new Error(response.message || '获取好友统计失败')
      }
    } catch (error) {
      console.error('获取微信好友统计失败:', error)
      throw new Error(`获取好友统计失败: ${error.message}`)
    }
  }

  /**
   * 搜索微信好友
   * @param {string} keyword - 搜索关键词
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 搜索结果
   */
  async searchFriends(keyword, filters = {}) {
    return this.getUserFriends({
      关键词: keyword,
      ...filters,
      页码: 1,
      每页条数: 50
    })
  }

  /**
   * 格式化好友数据
   * @param {Object} friend - 原始好友数据
   * @returns {Object} 格式化后的数据
   */
  formatFriendData(friend) {
    return {
      id: friend.好友ID || friend.id,
      我方微信号: friend.我方微信号,
      我方微信号id: friend.我方微信号id,
      对方微信号: friend.对方微信号,
      对方微信号id: friend.对方微信号id,
      好友识别id: friend.好友识别id || friend.识别id,
      好友昵称: friend.好友昵称 || '',
      好友头像: friend.好友头像 || '',
      好友类型: friend.好友类型 || '普通',
      添加时间: friend.好友通过时间 || friend.发送请求时间 || friend.好友入库时间 || friend.创建时间,
      最后联系时间: friend.最后联系时间,
      备注: friend.备注 || '',
      状态: friend.状态 || 1,
      对接进度: friend.对接进度 || {}
    }
  }

  /**
   * 获取好友类型选项
   * @returns {Array} 好友类型列表
   */
  getFriendTypeOptions() {
    return [
      { label: '全部', value: '' },
      { label: '普通好友', value: '普通' },
      { label: '意向客户', value: '意向' },
      { label: '合作伙伴', value: '合作' },
      { label: '潜在客户', value: '潜在' },
      { label: '重要客户', value: '重要' },
      { label: '已成交', value: '成交' }
    ]
  }

  /**
   * 获取好友状态选项
   * @returns {Array} 好友状态列表
   */
  getFriendStatusOptions() {
    return [
      { label: '全部', value: '' },
      { label: '正常', value: 1 },
      { label: '已删除', value: 0 },
      { label: '拉黑', value: -1 }
    ]
  }

  /**
   * 获取好友列表（通用函数）
   * 根据参数自动选择合适的查询方法
   * @param {Object} params - 查询参数
   * @param {string} params.搜索关键词 - 搜索关键词
   * @param {number} params.页码 - 页码
   * @param {number} params.每页数量 - 每页数量
   * @param {string} params.微信id - 微信账号id（可选）
   * @param {string} params.类型 - 好友类型筛选（可选）
   * @param {string} params.状态 - 好友状态筛选（可选）
   * @returns {Promise<Object>} 好友列表数据
   */
  async getFriendList(params = {}) {
    try {
      // 统一参数格式
      const queryParams = {
        页码: params.页码 || 1,
        每页条数: params.每页数量 || params.每页条数 || 20,
        关键词: params.搜索关键词 || params.关键词 || null,
        好友类型: params.类型 || params.好友类型 || null,
        状态: params.状态 || null
      }

      // 如果指定了微信id，使用指定账号查询
      if (params.微信id) {
        queryParams.微信id = params.微信id
        return await this.getAccountFriends(queryParams)
      }
      
      // 否则查询用户所有好友
      return await this.getUserFriends(queryParams)
    } catch (error) {
      console.error('获取好友列表失败:', error)
      throw new Error(`获取好友列表失败: ${error.message}`)
    }
  }

  /**
   * 验证微信号格式
   * @param {string} wechatId - 微信号
   * @returns {boolean} 是否有效
   */
  validateWeChatId(wechatId) {
    if (!wechatId) return false
    
    // 微信号规则：6-20位，字母开头，允许字母、数字、下划线、减号
    const wechatRegex = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/
    return wechatRegex.test(wechatId)
  }
}

// 创建并导出服务实例
const friendService = new FriendService()
export default friendService 