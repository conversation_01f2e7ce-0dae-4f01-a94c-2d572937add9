from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import JSONResponse
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field

# 导入依赖
from 依赖项.认证 import 获取当前用户
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 状态 import 状态
from 数据模型.响应模型 import 统一响应模型

# 导入日志系统
from 日志 import 接口日志器, 错误日志器

# 创建路由器
用户达人邀约路由 = APIRouter()

# 数据模型
class 达人邀约请求模型(BaseModel):
    """达人邀约请求模型"""
    uid_number: str = Field(..., description="达人UID编号")
    account_douyin: str = Field(..., description="抖音账号")
    昵称: str = Field(..., description="达人昵称")

class 达人邀约响应模型(BaseModel):
    """达人邀约响应模型"""
    今日已用邀约次数: int = Field(..., description="今日已使用的邀约次数")
    每日邀约上限: int = Field(..., description="每日邀约上限，-1表示无限制")
    剩余邀约次数: int = Field(..., description="剩余邀约次数，-1表示无限制")

# 核心邀约接口
@用户达人邀约路由.post("/talent", summary="邀请达人（新版）", description="用户发送邀约请求给指定达人，使用新的会员和权限体系")
async def 邀请达人(请求数据: 达人邀约请求模型, 用户: dict = Depends(获取当前用户)):
    """
    用户发送邀约请求给指定达人
    
    新版本特性：
    1. 基于新的会员表和权限表架构
    2. 支持多种会员类型的权限验证
    3. 根据用户会员等级确定邀约次数限制
    4. 详细的邀约日志记录
    
    Args:
        请求数据: 包含达人信息的请求数据
        用户: 当前登录用户的认证信息
        
    Returns:
        邀约结果和用户邀约次数信息
    """
    try:
        # 验证用户是否存在
        if not 用户 or "id" not in 用户:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(状态.用户.令牌无效, "请先登录，登录后才能发送邀约").转字典()
            )

        用户id = 用户["id"]
        接口日志器.info(f"用户id {用户id} 开始邀约达人 {请求数据.uid_number} ({请求数据.昵称})")

        # 验证输入参数
        if not 请求数据.uid_number or not 请求数据.uid_number.strip():
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(状态.通用.参数错误, "达人UID不能为空").转字典()
            )
        
        if not 请求数据.account_douyin or not 请求数据.account_douyin.strip():
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(状态.通用.参数错误, "抖音账号不能为空").转字典()
            )
            
        if not 请求数据.昵称 or not 请求数据.昵称.strip():
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(状态.通用.参数错误, "达人昵称不能为空").转字典()
            )

        # 调用服务层处理邀约逻辑
        邀约结果 = await 处理用户达人邀约(
            用户id=用户id,
            uid_number=请求数据.uid_number.strip(),
            account_douyin=请求数据.account_douyin.strip(),
            昵称=请求数据.昵称.strip()
        )
        
        return 统一响应模型.成功(邀约结果, "邀约请求已成功发送")

    except HTTPException as he:
        错误日志器.warning(f"用户id {用户.get('id', 'unknown')} 邀约达人时发生HTTP异常: {str(he.detail)}")
        if hasattr(he, 'detail') and isinstance(he.detail, dict) and 'status' in he.detail:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(he.detail['status'], he.detail.get('message', '邀约失败')).转字典()
            )
        else:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(状态.通用.失败, "邀约请求处理失败").转字典()
            )
    
    except Exception as e:
        错误日志器.error(f"用户id {用户.get('id', 'unknown')} 邀约达人时发生未知错误: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.服务器错误, "系统处理邀约请求时发生内部错误，请稍后重试").转字典()
        )


# 服务层函数
async def 处理用户达人邀约(用户id: int, uid_number: str, account_douyin: str, 昵称: str) -> dict:
    """
    处理用户达人邀约的核心逻辑
    
    Args:
        用户id: 用户id
        uid_number: 达人UID
        account_douyin: 抖音账号
        昵称: 达人昵称
        
    Returns:
        邀约处理结果
    """
    邀约发起时间 = datetime.now()
    拥有无限制权限 = False
    每日邀约上限 = 0
    
    try:
        # 检查用户是否存在
        用户信息 = await 获取用户基本信息(用户id)
        if not 用户信息:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"status": 状态.用户.用户不存在, "message": "用户不存在或已被删除"}
            )

        # 检查用户会员权限
        会员权限信息 = await 检查用户会员权限(用户id)
        拥有无限制权限 = 会员权限信息["拥有无限制权限"]
        会员类型 = 会员权限信息["会员类型"]
        
        接口日志器.info(f"用户id {用户id} 会员类型: {会员类型}, 无限制权限: {拥有无限制权限}")

        # 如果没有无限制权限，检查每日邀约次数限制
        if not 拥有无限制权限:
            每日邀约上限 = await 获取用户每日邀约上限(用户id)
            今日已用次数 = await 获取用户今日邀约次数(用户id)
            
            接口日志器.info(f"用户id {用户id} 每日邀约上限: {每日邀约上限}, 今日已用: {今日已用次数}")
            
            if 今日已用次数 >= 每日邀约上限:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail={
                        "status": 状态.邀约.免费次数超限,
                        "message": f"您今天的邀约次数已达上限（{每日邀约上限}次），请升级会员获得更多邀约次数"
                    }
                )

        # 记录邀约日志
        await 记录邀约日志(
            用户id=用户id,
            uid_number=uid_number,
            account_douyin=account_douyin,
            昵称=昵称,
            邀约时间=邀约发起时间,
            会员类型=会员类型
        )

        # 获取最新的邀约次数信息用于返回
        if 拥有无限制权限:
            每日邀约上限 = -1  # 表示无限制
            剩余邀约次数 = -1  # 表示无限制
            今日已用次数 = await 获取用户今日邀约次数(用户id)
        else:
            # 重新获取最新的已用次数（包含本次邀约）
            每日邀约上限 = await 获取用户每日邀约上限(用户id)
            今日已用次数 = await 获取用户今日邀约次数(用户id)
            剩余邀约次数 = 每日邀约上限 - 今日已用次数

        接口日志器.info(f"用户id {用户id} 成功邀约达人 {uid_number}, 邀约次数统计 - 已用: {今日已用次数}, 上限: {每日邀约上限}, 剩余: {剩余邀约次数}")

        return {
            "今日已用邀约次数": 今日已用次数,
            "每日邀约上限": 每日邀约上限,
            "剩余邀约次数": 剩余邀约次数,
            "会员类型": 会员类型
        }

    except HTTPException:
        raise
    except Exception as e:
        错误日志器.error(f"处理用户达人邀约时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"status": 状态.通用.服务器错误, "message": "系统处理邀约请求时发生内部错误"}
        )


# 辅助函数
async def 获取用户基本信息(用户id: int) -> Optional[dict]:
    """获取用户基本信息"""
    try:
        查询SQL = "SELECT id, 手机号, 昵称 FROM 用户表 WHERE id = $1"
        结果 = await 异步连接池实例.执行查询(查询SQL, [用户id])
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取用户基本信息失败: {str(e)}")
        return None


async def 检查用户会员权限(用户id: int) -> dict:
    """
    检查用户会员权限
    
    Returns:
        dict: 包含权限信息的字典
    """
    try:
        当前时间 = datetime.now()
        
        # 查询用户当前有效的会员信息
        查询SQL = """
        SELECT 
            m.名称 as 会员名称,
            m.id as 会员id,
            um.到期时间,
            mp.权限id
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        JOIN 会员_权限_关联表 mp ON m.id = mp.会员id
        JOIN 权限表 p ON mp.权限id = p.id
        WHERE um.用户id = $1
        AND um.到期时间 > $2
        AND p.名称 = '抖音自动邀约'
        ORDER BY um.到期时间 DESC
        LIMIT 1
        """
        
        结果 = await 异步连接池实例.执行查询(查询SQL, (用户id, 当前时间))
        
        if 结果:
            会员信息 = 结果[0]
            return {
                "拥有无限制权限": True,
                "会员类型": 会员信息["会员名称"],
                "会员id": 会员信息["会员id"],
                "到期时间": 会员信息["到期时间"]
            }
        else:
            return {
                "拥有无限制权限": False,
                "会员类型": "普通用户",
                "会员id": None,
                "到期时间": None
            }
            
    except Exception as e:
        错误日志器.error(f"检查用户会员权限失败: {str(e)}")
        return {
            "拥有无限制权限": False,
            "会员类型": "普通用户",
            "会员id": None,
            "到期时间": None
        }


async def 获取用户每日邀约上限(用户id: int) -> int:
    """
    获取用户每日邀约上限
    
    Args:
        用户id: 用户id
        
    Returns:
        int: 每日邀约上限，默认为5次
    """
    try:
        # 查询用户在用户表中的每日邀约次数配置
        查询SQL = "SELECT 每日邀约次数 FROM 用户表 WHERE id = $1"
        结果 = await 异步连接池实例.执行查询(查询SQL, [用户id])
        
        if 结果 and 结果[0]["每日邀约次数"] is not None:
            每日上限 = 结果[0]["每日邀约次数"]
            return max(0, int(每日上限)) if isinstance(每日上限, (int, str)) else 5
        else:
            # 默认每日邀约次数
            return 5
            
    except Exception as e:
        错误日志器.error(f"获取用户每日邀约上限失败: {str(e)}")
        return 5


async def 获取用户今日邀约次数(用户id: int) -> int:
    """
    获取用户今日已使用的邀约次数
    
    Args:
        用户id: 用户id
        
    Returns:
        int: 今日已使用的邀约次数
    """
    try:
        查询SQL = """
        SELECT COUNT(*) as 邀约次数
        FROM 用户抖音达人邀约记录表
        WHERE 用户id = $1
        AND DATE(邀约发起时间) = CURRENT_DATE
        AND 邀约状态 = 100
        AND 当日计次 = 1
        """
        
        结果 = await 异步连接池实例.执行查询(查询SQL, [用户id])
        return 结果[0]["邀约次数"] if 结果 else 0
        
    except Exception as e:
        错误日志器.error(f"获取用户今日邀约次数失败: {str(e)}")
        return 0


async def 记录邀约日志(用户id: int, uid_number: str, account_douyin: str, 昵称: str, 邀约时间: datetime, 会员类型: str):
    """
    记录邀约日志
    
    Args:
        用户id: 用户id
        uid_number: 达人UID
        account_douyin: 抖音账号
        昵称: 达人昵称
        邀约时间: 邀约时间
        会员类型: 会员类型
    """
    try:
        插入SQL = """
        INSERT INTO 用户抖音达人邀约记录表 (
            用户id, kol_uid_number, kol_account_douyin, kol_昵称,
            邀约发起时间, 邀约状态, 状态备注, 当日计次
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """
        
        参数 = [
            用户id,
            uid_number,
            account_douyin,
            昵称,
            邀约时间,
            状态.通用.成功,  # 邀约状态：成功
            f"邀约成功 - 会员类型: {会员类型}",
            1  # 当日计次：是
        ]
        
        await 异步连接池实例.执行插入(插入SQL, 参数)
        接口日志器.info(f"邀约日志记录成功 - 用户id: {用户id}, 达人UID: {uid_number}")
        
    except Exception as e:
        错误日志器.error(f"记录邀约日志失败: {str(e)}")
        # 邀约日志记录失败不应影响邀约流程，仅记录错误


# 查询用户邀约统计信息的接口
@用户达人邀约路由.post("/statistics", summary="获取邀约统计信息", description="获取用户的邀约次数统计信息")
async def 获取邀约统计信息(用户: dict = Depends(获取当前用户)):
    """
    获取用户的邀约统计信息
    
    Returns:
        用户当前的邀约次数统计
    """
    try:
        if not 用户 or "id" not in 用户:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.失败(状态.用户.令牌无效, "请先登录").转字典()
            )

        用户id = 用户["id"]
        
        # 检查用户会员权限
        会员权限信息 = await 检查用户会员权限(用户id)
        拥有无限制权限 = 会员权限信息["拥有无限制权限"]
        
        if 拥有无限制权限:
            每日邀约上限 = -1
            剩余邀约次数 = -1
            今日已用次数 = await 获取用户今日邀约次数(用户id)
        else:
            每日邀约上限 = await 获取用户每日邀约上限(用户id)
            今日已用次数 = await 获取用户今日邀约次数(用户id)
            剩余邀约次数 = 每日邀约上限 - 今日已用次数

        统计信息 = {
            "今日已用邀约次数": 今日已用次数,
            "每日邀约上限": 每日邀约上限,
            "剩余邀约次数": 剩余邀约次数,
            "会员类型": 会员权限信息["会员类型"],
            "拥有无限制权限": 拥有无限制权限
        }
        
        return 统一响应模型.成功(统计信息, "获取邀约统计信息成功")

    except Exception as e:
        错误日志器.error(f"获取用户邀约统计信息失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=统一响应模型.失败(状态.通用.服务器错误, "获取邀约统计信息失败").转字典()
        ) 