<template>
  <a-modal
    v-model:open="visible"
    title="权限管理"
    width="1200"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="permission-container">
      <!-- 头部统计信息 -->
      <div class="permission-header">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="团队成员" :value="members.length" prefix="👥" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="创始人" :value="getFounderCount()" prefix="👑" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="负责人" :value="getLeaderCount()" prefix="🎯" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="管理员" :value="getAdminCount()" prefix="⚙️" />
          </a-col>
        </a-row>
      </div>

      <!-- 权限管理标签页 -->
      <a-tabs v-model:activeKey="activeTab">
        <!-- 成员权限管理 -->
        <a-tab-pane key="members" tab="成员权限">
          <MemberPermissionTable
            :members="members"
            :loading="tableLoading"
            :selected-member-ids="selectedMemberIds"
            @member-selection-change="onMemberSelectionChange"
            @batch-set-role="showBatchRoleModal = true"
            @batch-set-permissions="showBatchPermissionModal = true"
            @show-permission-logs="showPermissionLogs = true"
            @view-member-permissions="viewMemberPermissions"
            @edit-member-role="editMemberRole"
            @edit-member-permissions="editMemberPermissions"
            @transfer-ownership="transferOwnership"
            @remove-member="removeMember"
          />
        </a-tab-pane>

        <!-- 岗位管理 -->
        <a-tab-pane key="roles" tab="岗位管理">
          <div class="role-management">
            <a-alert
              type="info"
              show-icon
              message="角色管理已简化"
              description="系统现在使用固定的三个角色：创建者、负责人、成员。不再支持自定义岗位功能。"
              style="margin-bottom: 16px;"
            />
            
            <div class="fixed-roles">
              <a-list
                :data-source="[
                  { name: '创建者', desc: '团队创建者，拥有所有权限包括解散团队', icon: '👑' },
                  { name: '负责人', desc: '团队负责人，拥有管理权限除了解散团队', icon: '🎯' },
                  { name: '成员', desc: '普通团队成员，拥有基础查看权限', icon: '👤' }
                ]"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>
                        <span>{{ item.icon }} {{ item.name }}</span>
                      </template>
                      <template #description>
                        {{ item.desc }}
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-tab-pane>


      </a-tabs>
    </div>

    <!-- 编辑成员角色弹窗 -->
    <a-modal
      v-model:open="showMemberRoleModal"
      title="设置成员角色"
      @ok="updateMemberRole"
      @cancel="showMemberRoleModal = false"
    >
      <a-form layout="vertical">
        <a-form-item label="成员信息">
          <div v-if="selectedMember" class="member-info">
            <a-avatar :src="selectedMember.头像" :size="32">
              {{ selectedMember.昵称?.charAt(0) || '用' }}
            </a-avatar>
            <span>{{ selectedMember.昵称 }} ({{ selectedMember.手机号 }})</span>
          </div>
        </a-form-item>
        
        <!-- 简化的角色选择 -->
        <a-form-item label="角色类型">
          <a-select 
            v-model:value="selectedRoleType" 
            placeholder="请选择角色"
            style="width: 100%"
            @change="handleMemberRoleChange"
          >
            <a-select-option value="负责人" v-if="canSetLeader()">
              <div style="display: flex; align-items: center; gap: 8px;">
                <span>🎯</span>
                <span>负责人</span>
              </div>
            </a-select-option>
            <a-select-option value="成员">
              <div style="display: flex; align-items: center; gap: 8px;">
                <span>👤</span>
                <span>成员</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑成员权限弹窗 -->
    <a-modal
      v-model:open="showMemberPermissionModal"
      title="编辑成员权限"
      width="800"
      @ok="updateMemberPermissions"
      @cancel="showMemberPermissionModal = false"
    >
      <a-form layout="vertical">
        <a-form-item label="成员信息" v-if="selectedMember">
          <div class="member-info">
            <a-avatar :src="selectedMember.头像" :size="32">
              {{ selectedMember.昵称?.charAt(0) || '用' }}
            </a-avatar>
            <span>{{ selectedMember.昵称 }} ({{ selectedMember.手机号 }})</span>
          </div>
        </a-form-item>
        
        <!-- 详细权限设置 -->
        <a-divider>权限设置</a-divider>
        
        <!-- 动态权限模板 -->
        <a-form-item label="权限模板">
          <a-radio-group v-model:value="selectedPermissionTemplate" @change="applyPermissionTemplate">
            <a-radio 
              v-for="(group, category) in dynamicPermissions" 
              :key="category" 
              :value="category"
            >
              {{ category }}
            </a-radio>
            <a-radio value="custom">自定义权限</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <!-- 动态详细权限选择 -->
        <a-form-item label="具体权限">
          <a-spin :spinning="permissionLoading">
            <div v-for="(group, category) in dynamicPermissions" :key="category" class="permission-group">
              <h4 class="permission-category-title">{{ category }}</h4>
              <a-checkbox-group v-model:value="selectedMemberPermissions" style="width: 100%;">
                <a-row>
                  <a-col :span="12" v-for="permission in group" :key="permission.权限名称">
                    <a-checkbox :value="permission.权限名称" style="margin-bottom: 8px;">
                      {{ permission.权限名称 }}
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </div>
             <a-empty v-if="!permissionLoading && Object.keys(dynamicPermissions).length === 0" description="暂无可用权限" />
          </a-spin>
        </a-form-item>
        
        <!-- 权限预览 -->
        <a-form-item label="权限预览">
          <div class="permission-preview">
            <a-tag 
              v-for="permissionName in selectedMemberPermissions" 
              :key="permissionName" 
              style="margin-bottom: 4px;"
            >
              {{ permissionName }}
            </a-tag>
            <span v-if="selectedMemberPermissions.length === 0" style="color: #999;">
              未选择任何权限
            </span>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>



    <!-- 权限日志弹窗 -->
    <a-modal
      v-model:open="showPermissionLogs"
      title="权限变更日志"
      width="1000"
      :footer="null"
    >
      <a-table
        :dataSource="permissionLogs"
        :columns="logColumns"
        :loading="logLoading"
        :pagination="{ pageSize: 10 }"
        row-key="日志ID"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '操作类型'">
            <a-tag :color="getLogTypeColor(record.操作类型)">
              {{ record.操作类型 }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  DownOutlined,
  PlusOutlined,
  CrownOutlined,
  UserOutlined,
  SafetyCertificateOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  UsergroupAddOutlined,
  FileProtectOutlined
} from '@ant-design/icons-vue'

import PermissionConfig from './PermissionConfig.vue'
import MemberPermissionTable from './MemberPermissionTable.vue'
import teamService from '../../services/team'
import { teamPermissionService } from '../../services/team/teamPermission.js'
import { usePermissions, PERMISSIONS } from '../../composables/usePermissions'
import { getRoleColor, ROLE_TYPES } from '../../utils/roleUtils'
import { formatDate } from '../../utils/teamUtils'

defineOptions({
  name: 'PermissionModal'
})

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  team: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:open', 'success'])

// 权限系统
const { hasPermission, checkPermissions } = usePermissions()

// 响应式数据
const visible = ref(false)
const activeTab = ref('members')
const tableLoading = ref(false)

// 成员数据
const members = ref([])
const searchKeyword = ref('')
const roleFilter = ref('')
const selectedMemberIds = ref([])

// 权限数据
const permissionCategories = ref([])
const permissionTree = ref([])
const selectedPermissionKeys = ref([])
const selectedPermission = ref(null)

// 弹窗状态
const showMemberRoleModal = ref(false)
const showMemberPermissionModal = ref(false)
const showBatchRoleModal = ref(false)
const showBatchPermissionModal = ref(false)

const showPermissionLogs = ref(false)

// 编辑数据
const selectedMember = ref(null)
const selectedRoleType = ref('')
const selectedMemberPermissions = ref([])
const selectedPermissionTemplate = ref('custom')

// 可用权限列表
// availablePermissions, permissionCategories, permissionTree 已被 dynamicPermissions 替代
// const availablePermissions = ref([...])
// const permissionCategories = ref([...])
// const permissionTree = ref([])

// 权限模板
// const permissionTemplates = {
//   full_access: ['查看团队信息', '编辑团队信息', '查看成员列表', '邀请成员', '移除成员', '管理成员角色', '管理成员权限', '查看团队统计', '导出数据', '查看操作日志'],
//   manager: ['查看团队信息', '编辑团队信息', '查看成员列表', '邀请成员', '移除成员', '管理成员角色', '查看团队统计'],
//   basic: ['查看团队信息', '查看成员列表']
// }

// 计算属性
const teamId = computed(() => props.team?.团队id || props.team?.id)

// 权限日志
const permissionLogs = ref([])

// 表格列配置
const memberColumns = [
  {
    title: '成员信息',
    dataIndex: '成员信息',
    width: 200,
    fixed: 'left'
  },
  {
    title: '角色',
    dataIndex: '角色',
    width: 120
  },
  {
    title: '权限概览',
    dataIndex: '权限概览',
    width: 120
  },
  {
    title: '状态',
    dataIndex: '状态',
    width: 80
  },
  {
    title: '加入时间',
    dataIndex: '加入时间',
    width: 120
  },
  {
    title: '操作',
    dataIndex: '操作',
    width: 120,
    fixed: 'right'
  }
]

const logColumns = [
  {
    title: '操作时间',
    dataIndex: '操作时间',
    width: 150
  },
  {
    title: '操作人',
    dataIndex: '操作人',
    width: 120
  },
  {
    title: '操作类型',
    dataIndex: '操作类型',
    width: 120
  },
  {
    title: '目标成员',
    dataIndex: '目标成员',
    width: 120
  },
  {
    title: '变更内容',
    dataIndex: '变更内容'
  },
  {
    title: '操作详情',
    dataIndex: '操作内容',
    key: '操作内容',
    width: 300
  }
]

// 计算属性
const filteredMembers = computed(() => {
  let result = members.value

  if (searchKeyword.value) {
    result = result.filter(member => 
      (member.昵称 || '').includes(searchKeyword.value) ||
      member.手机号.includes(searchKeyword.value)
    )
  }

  if (roleFilter.value) {
    result = result.filter(member => member.角色类型 === roleFilter.value)
  }

  return result
})

// 方法
const getFounderCount = () => {
  return members.value.filter(m => m.角色类型 === 'founder').length
}

const getLeaderCount = () => {
  return members.value.filter(m => m.角色类型 === 'leader').length
}

const getAdminCount = () => {
  return members.value.filter(m => m.角色类型 === 'admin').length
}

const hasSelectedMembers = () => {
  return selectedMemberIds.value.length > 0
}

const onMemberSelectionChange = (selectedKeys) => {
  selectedMemberIds.value = selectedKeys
}

const getRoleIcon = (roleType) => {
  const icons = {
    founder: CrownOutlined,
    leader: ThunderboltOutlined,
    admin: SafetyCertificateOutlined,
    member: UserOutlined,
    custom: SettingOutlined
  }
  return icons[roleType] || UserOutlined
}

const canSetFounder = async () => {
  // 只有当前创始人可以转移所有权
  return await hasPermission(props.team.团队id, PERMISSIONS.TRANSFER_OWNERSHIP)
}

const canSetLeader = async () => {
  // 创始人和负责人可以设置负责人
  return await hasPermission(props.team.团队id, PERMISSIONS.MANAGE_ROLES)
}

const canSetAdmin = async () => {
  // 创始人和负责人可以设置管理员
  return await hasPermission(props.team.团队id, PERMISSIONS.MANAGE_ROLES)
}

const handleMemberRoleChange = (roleType) => {
  selectedRoleType.value = roleType
}

// 应用权限模板
const applyPermissionTemplate = (e) => {
  const template = e.target.value
  selectedPermissionTemplate.value = template

  if (template === 'custom') {
    // 切换到自定义时，不改变当前选中的权限
    return;
  }

  if (dynamicPermissions.value[template]) {
    // 根据选择的分类（模板），自动勾选该分类下的所有权限名称
    selectedMemberPermissions.value = dynamicPermissions.value[template].map(p => p.权限名称);
  } else {
    // 如果模板不存在或选择其他，清空权限
    selectedMemberPermissions.value = [];
  }
}

const handleMemberPermissionsChange = (permissionData) => {
  selectedMemberPermissions.value = [...permissionData.permissions]
}

const canTransferOwnership = async (member) => {
  // 只能对非创始人进行所有权转移
  if (member.角色类型 === 'founder') return false
  return await hasPermission(props.team.团队id, PERMISSIONS.TRANSFER_OWNERSHIP)
}

const canRemoveMember = async (member) => {
  // 不能移除创始人
  if (member.角色类型 === 'founder') return false
  return await hasPermission(props.team.团队id, PERMISSIONS.REMOVE_MEMBERS)
}

const getLogTypeColor = (type) => {
  const colors = {
    '角色变更': 'blue',
    '权限变更': 'green',
    '所有权转移': 'red',
    '成员移除': 'orange'
  }
  return colors[type] || 'default'
}

// 加载数据
const loadTeamMembers = async () => {
  try {
    tableLoading.value = true
    const response = await teamService.getTeamMembers({
      团队id: props.team.团队id,
      页码: 1,
      每页数量: 100
    })
    if (response.status === 100) {
      members.value = response.data?.成员列表 || []
      console.log('✅ PermissionModal 成员列表加载成功，数量:', members.value.length)
    }
  } catch (error) {
    console.error('❌ PermissionModal 加载成员列表失败:', error)
    message.error('加载成员列表失败：' + error.message)
    members.value = []
  } finally {
    tableLoading.value = false
  }
}

const loadPermissionCategories = async () => {
  try {
    const response = await teamService.getPermissionCategories()
    if (response.status === 100) {
      permissionCategories.value = response.message || []
      buildPermissionTree()
    }
  } catch (error) {
    message.error('加载权限分类失败：' + error.message)
  }
}

const buildPermissionTree = () => {
  permissionTree.value = permissionCategories.value.map(category => ({
    title: category.分类名称,
    key: category.分类ID,
    icon: SettingOutlined,
    children: category.权限列表?.map(permission => ({
      title: permission.权限名称,
      key: permission.权限代码,
      isLeaf: true,
      permission: permission
    })) || []
  }))
}

// 事件处理
const handleCancel = () => {
  visible.value = false
}

const viewMemberPermissions = (member) => {
  // 此功能可以被 editMemberPermissions 覆盖，或用于只读预览
  editMemberPermissions(member);
}

const editMemberRole = (member) => {
  selectedMember.value = member
  selectedRoleType.value = member.角色类型
  showMemberRoleModal.value = true
}

const editMemberPermissions = async (member) => {
  if (!member || !member.用户id) {
    message.error('无效的成员信息');
    return;
  }
  
  selectedMember.value = member;
  tableLoading.value = true;
  
  try {
    const response = await teamService.getUserTeamPermissionStatus({
      团队id: teamId.value,
      用户id: member.用户id
    });
    
    if (response.status === 100 && response.message) {
      // 使用后端返回的该成员的当前权限列表，填充选择框
      selectedMemberPermissions.value = response.message.权限列表 || [];
      selectedPermissionTemplate.value = 'custom'; // 默认进入自定义模式
      showMemberPermissionModal.value = true; // 获取成功后再打开弹窗
    } else {
      message.error(response.message || '获取成员权限失败');
    }
  } catch (error) {
    console.error('获取成员权限时出错:', error);
    message.error('获取成员权限信息时出错');
  } finally {
    tableLoading.value = false;
  }
}

const updateMemberRole = async () => {
  try {
    await teamService.setMemberRole({
      团队id: props.team.团队id,
      用户id: selectedMember.value.用户id,
      角色类型: selectedRoleType.value
    })
    message.success('角色设置成功')
    showMemberRoleModal.value = false
    await loadTeamMembers()
  } catch (error) {
    message.error('角色设置失败：' + error.message)
  }
}

const updateMemberPermissions = async () => {
  if (!selectedMember.value) {
    message.error('未选择任何成员');
    return;
  }
  
  loading.value = true;
  try {
    const response = await teamPermissionService.updateMemberPermissions({
      teamId: teamId.value,
      userId: selectedMember.value.用户id,
      permissions: selectedMemberPermissions.value, // 直接传递权限名称列表
    });
    
    if (response.status === 100) {
      message.success('权限更新成功');
      showMemberPermissionModal.value = false;
      emit('permissions-updated'); // 通知父组件刷新
      fetchMembers(); // 刷新成员列表以更新状态
    } else {
      message.error(response.message || '权限更新失败');
    }
  } catch (error) {
    console.error('更新成员权限失败:', error);
    message.error('更新成员权限失败');
  } finally {
    loading.value = false;
  }
};

const onPermissionSelect = (keys, { selectedNodes }) => {
  if (selectedNodes.length > 0) {
    const node = selectedNodes[0]
    if (node.permission) {
      selectedPermission.value = node.permission
    }
  }
}

const transferOwnership = async (member) => {
  try {
    const response = await teamService.transferOwnership({
      团队id: props.team.团队id,
      新创始人用户id: member.用户id
    })
    if (response.status === 100) {
      message.success('所有权转移成功')
      await loadTeamMembers()
      emit('success')
    }
  } catch (error) {
    message.error('所有权转移失败：' + error.message)
  }
}

const removeMember = async (member) => {
  try {
    const response = await teamService.removeMember({
      团队id: props.team.团队id,
      用户id: member.用户id
    })
    if (response.status === 100) {
      message.success('成员移除成功')
      await loadTeamMembers()
      emit('success')
    }
  } catch (error) {
    message.error('成员移除失败：' + error.message)
  }
}

const loadPermissionLogs = async () => {
  try {
    logLoading.value = true
    const response = await teamService.getPermissionLogs({
      团队id: props.team.团队id,
      页码: 1,
      每页数量: 50
    })
    if (response.status === 100) {
      permissionLogs.value = response.message.日志列表 || []
    }
  } catch (error) {
    message.error('加载权限日志失败：' + error.message)
  } finally {
    logLoading.value = false
  }
}

// 动态权限数据
const dynamicPermissions = ref({}) // 用于存储从后端获取的权限
const permissionLoading = ref(false) // 权限数据加载状态

/**
 * 获取所有可用权限列表
 */
const fetchAvailablePermissions = async () => {
  permissionLoading.value = true
  try {
    const response = await teamPermissionService.getPermissionList()
    if (response.status === 100 && response.message) {
      dynamicPermissions.value = response.message
    } else {
      message.error('获取权限列表失败')
    }
  } catch (error) {
    console.error('获取权限列表时出错:', error)
    message.error('获取权限列表时出错，请检查网络连接')
  } finally {
    permissionLoading.value = false
  }
}

// 方法
const fetchMembers = async () => {
  if (visible.value) {
    loadTeamMembers()
    loadPermissionCategories()
    fetchAvailablePermissions()
  }
}

// 监听器
watch(visible, (newVal) => {
  if (newVal) {
    loadTeamMembers()
    loadPermissionCategories()
    fetchAvailablePermissions()
  }
})

watch(showPermissionLogs, (newVal) => {
  if (newVal) {
    loadPermissionLogs()
  }
})

// 组件加载时
onMounted(() => {
  if (visible.value) {
    loadTeamMembers()
    loadPermissionCategories()
    fetchAvailablePermissions()
  }
})
</script>

<style scoped>
.permission-container {
  padding: 16px 0;
}

.permission-header {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-details {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-weight: 500;
  color: #262626;
}

.member-phone {
  font-size: 12px;
  color: #8c8c8c;
}

.role-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permission-config {
  min-height: 400px;
}

.role-management, .template-management {
  padding: 16px 0;
}

.role-header, .template-header {
  margin-bottom: 16px;
}

.permission-container {
  max-height: 70vh;
  overflow-y: auto;
}

.permission-preview {
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 32px;
}

.permission-group {
  margin-bottom: 16px;
}

.permission-category-title {
  margin-bottom: 8px;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 4px;
}
</style> 