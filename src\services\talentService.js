/**
 * 达人管理API服务模块
 * 
 * 功能概述：
 * - 提供达人公海、个人达人、团队达人的完整管理功能
 * - 支持达人详情查看、认领、取消认领等核心操作
 * - 提供统一的错误处理和响应格式
 * - 支持流式分页和传统分页两种模式
 * 
 * 作者: CRM系统开发团队
 * 更新时间: 2024-06-20
 */

import request from './api'

/**
 * 达人服务类
 * 封装所有达人相关的API调用方法
 */
class TalentService {
  /**
   * 获取达人公海列表（支持流式分页）
   * 
   * 流式分页说明：
   * - 通过最后id参数实现无限滚动加载
   * - 性能优于传统分页，适合大数据量展示
   * - 前端需要记录并传递上一页的最后id
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 当前页码，默认1（主要用于兼容性）
   * @param {number} params.每页数量 - 每页显示数量，默认18（适合卡片布局）
   * @param {number} params.最后id - 上一页最后的达人id，流式分页关键参数
   * @param {Object} params.筛选条件 - 筛选条件，如粉丝数范围、直播销售额等
   * @param {boolean} params.有联系方式 - 是否筛选有联系方式的达人
   * @param {string} params.关键词 - 通过抖音号搜索达人
   * @returns {Promise<Object>} API响应，包含达人列表和下一页最后id
   * 
   * 响应数据结构:
   * {
   *   status: 100,
   *   message: "获取达人公海列表成功",
   *   data: {
   *     当前页: 1,
   *     每页数量: 18,
   *     最后id: 0,
   *     达人列表: [...],      // 达人数据数组
   *     下一页最后id: 19,     // 流式分页的关键字段，前端需要保存
   *     团队模式: true,       // 是否为团队模式
   *     当前团队id: 6         // 当前团队id（如果是团队模式）
   *   }
   * }
   */
  async getTalentPool(params = {}) {
    // 设置默认参数，确保请求的完整性
    const defaultParams = {
      页码: 1,
      每页数量: 18,    // 适合流式加载的默认数量，3列布局下每屏6个
      最后id: 0,       // 流式分页关键参数：上一页最后的达人id
      筛选条件: null,
      有联系方式: null,
      关键词: null
    }

    // 合并参数，用户传入的参数优先级更高
    const requestParams = { ...defaultParams, ...params }

    // 调试日志：记录流式分页请求参数
    console.log('TalentService.getTalentPool 请求参数:', requestParams)

    try {
      const response = await request.post('/kol/list', requestParams)

      // 调试日志：记录响应数据结构，便于前端调试
      if (response.status === 100 && response.data) {
        console.log('TalentService.getTalentPool 响应摘要:', {
          当前页: response.data.当前页,
          每页数量: response.data.每页数量,
          最后id: response.data.最后id,
          达人数量: response.data.达人列表?.length || 0,
          下一页最后id: response.data.下一页最后id,
          团队模式: response.data.团队模式
        })
      }

      return response
    } catch (error) {
      console.error('TalentService.getTalentPool 请求失败:', error)
      throw this._handleError(error, '获取达人公海列表')
    }
  }

  /**
   * 获取达人详情信息
   * 
   * 包含完整的达人信息：
   * - 基本信息（昵称、头像、抖音号等）
   * - 数据统计（粉丝数、关注数、获赞数等）
   * - 联系方式（微信、手机、邮箱等）
   * - 认领状态（当前用户是否已认领）
   * 
   * @param {number} talentId - 达人id
   * @returns {Promise<Object>} 达人详情数据
   * 
   * 响应数据结构:
   * {
   *   status: 100,
   *   message: "获取达人详情成功",
   *   data: {
   *     id: 123,
   *     昵称: "伊佳服饰贸易",
   *     头像: "https://...",
   *     account_douyin: "douyin123",
   *     粉丝数: 18700,
   *     关注数: 0,
   *     获赞数: 0,
   *     账号状态: 1,
   *     简介: "个人介绍...",
   *     联系方式列表: [...],
   *     当前用户认领状态: { 已认领: false }
   *   }
   * }
   */
  async getTalentDetail(talentId) {
    // 参数验证
    if (!talentId || talentId <= 0) {
      throw new Error('达人id无效')
    }

    try {
      const response = await request.post('/kol/detail', {
        达人id: talentId
      })

      // 记录成功日志
      if (response.status === 100) {
        console.log(`TalentService.getTalentDetail 获取达人 ${talentId} 详情成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.getTalentDetail 获取达人 ${talentId} 详情失败:`, error)
      throw this._handleError(error, '获取达人详情')
    }
  }

  /**
   * 认领达人
   * 
   * 将指定达人认领到当前用户名下
   * 认领后该达人会出现在"我的达人"列表中
   * 
   * @param {number} talentId - 要认领的达人id
   * @returns {Promise<Object>} 认领操作结果
   */
  async claimTalent(talentId) {
    if (!talentId || talentId <= 0) {
      throw new Error('达人id无效')
    }

    try {
      const response = await request.post('/kol/claim', {
        达人id: talentId
      })

      if (response.status === 100) {
        console.log(`TalentService.claimTalent 认领达人 ${talentId} 成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.claimTalent 认领达人 ${talentId} 失败:`, error)
      throw this._handleError(error, '认领达人')
    }
  }

  /**
   * 取消认领达人
   * 
   * 取消对指定达人的认领关系
   * 取消后该达人重新回到公海中
   * 
   * @param {number} talentId - 要取消认领的达人id
   * @returns {Promise<Object>} 取消认领操作结果
   */
  async unclaimTalent(talentId) {
    if (!talentId || talentId <= 0) {
      throw new Error('达人id无效')
    }

    try {
      const response = await request.post('/kol/unclaim', {
        达人id: talentId
      })

      if (response.status === 100) {
        console.log(`TalentService.unclaimTalent 取消认领达人 ${talentId} 成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.unclaimTalent 取消认领达人 ${talentId} 失败:`, error)
      throw this._handleError(error, '取消认领达人')
    }
  }

  /**
   * 获取我认领的达人列表
   * 
   * 支持多种排序和筛选条件
   * 使用传统分页模式
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 当前页码，默认1
   * @param {number} params.每页数量 - 每页显示数量，默认20
   * @param {string} params.排序字段 - 排序字段，默认"认领时间"
   * @param {string} params.排序方式 - 排序方式，默认"desc"
   * @param {Object} params.筛选条件 - 筛选条件
   * @param {string} params.关键词 - 搜索关键词（可搜索昵称、抖音号等）
   * @returns {Promise<Object>} 我的达人列表
   */
  async getMyTalents(params = {}) {
    const defaultParams = {
      页码: 1,
      每页数量: 20,
      排序字段: "认领时间",
      排序方式: "desc",
      筛选条件: null,
      关键词: null
    }

    const requestParams = { ...defaultParams, ...params }

    try {
      const response = await request.post('/kol/myclaims', requestParams)

      if (response.status === 100) {
        console.log('TalentService.getMyTalents 获取我的达人列表成功:', {
          总数: response.data?.总数 || 0,
          当前页: response.data?.当前页 || 1,
          达人数量: (response.data?.达人列表 || response.data?.列表 || []).length
        })
      }

      return response
    } catch (error) {
      console.error('TalentService.getMyTalents 获取我的达人列表失败:', error)
      throw this._handleError(error, '获取我的达人列表')
    }
  }



  /**
   * 通过uid_number查询或创建达人
   * 
   * 如果达人不存在，会自动创建并尝试从抖音获取最新信息
   * 主要用于导入新达人或更新达人信息
   * 
   * @param {string} uidNumber - 达人唯一标识符
   * @returns {Promise<Object>} 查询或创建结果
   */
  async getOrCreateTalent(uidNumber) {
    if (!uidNumber || !uidNumber.trim()) {
      throw new Error('UID不能为空')
    }

    try {
      const response = await request.post('/kol/get_id', {
        uid_number: uidNumber.trim()
      })

      if (response.status === 100) {
        console.log(`TalentService.getOrCreateTalent UID ${uidNumber} 处理成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.getOrCreateTalent UID ${uidNumber} 处理失败:`, error)
      throw this._handleError(error, '查询或创建达人')
    }
  }

  /**
   * 更新达人信息
   * 
   * 支持更新达人的各种信息字段
   * 包括基本信息、统计数据等
   * 
   * @param {Object} talentData - 要更新的达人数据
   * @param {number} talentData.达人id - 达人id（必需）
   * @param {string} talentData.nickname - 昵称（可选）
   * @param {string} talentData.account_douyin - 抖音号（可选）
   * @param {string} talentData.avatar - 头像URL（可选）
   * @param {string} talentData.introduction - 个人介绍（可选）
   * @param {number} talentData.粉丝数 - 粉丝数（可选）
   * @param {number} talentData.关注数 - 关注数（可选）
   * @param {number} talentData.账号状态 - 账号状态（可选）
   * @returns {Promise<Object>} 更新结果
   */
  async updateTalentInfo(talentData) {
    if (!talentData || !talentData.达人id) {
      throw new Error('达人id不能为空')
    }

    try {
      const response = await request.post('/kol/update', talentData)

      if (response.status === 100) {
        console.log(`TalentService.updateTalentInfo 更新达人 ${talentData.达人id} 信息成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.updateTalentInfo 更新达人 ${talentData.达人id} 信息失败:`, error)
      throw this._handleError(error, '更新达人信息')
    }
  }

  /**
   * 更新达人数据（从第三方API获取最新信息）
   * 
   * 功能说明：
   * - 调用后端接口，通过UID从第三方API获取达人最新信息
   * - 自动更新数据库中的达人数据
   * - 返回更新后的达人信息
   * 
   * @param {Object} updateData - 更新数据
   * @param {number} updateData.达人id - 达人id（必需）
   * @param {string} [updateData.UID] - 达人UID（可选，如果不传则从数据库中获取uid_number字段）
   * @returns {Promise<Object>} 更新结果
   * 
   * @example
   * const result = await talentService.updateTalentData({
   *   达人id: 123,
   *   UID: 'MS4wLjABAAAAxxxxx'  // 可选参数
   * })
   */
  async updateTalentData(updateData) {
         // 参数验证
     if (!updateData || !updateData.达人id) {
       throw new Error('达人id不能为空')
     }

    const startTime = Date.now()
    console.log(`TalentService.updateTalentData 开始更新达人数据:`, {
      达人id: updateData.达人id,
      UID: updateData.UID
    })

    try {
      const response = await request.post('/kol/update-talent-data', updateData)

      const duration = Date.now() - startTime
      if (response.status === 100) {
        console.log(`TalentService.updateTalentData 更新达人数据成功 (耗时: ${duration}ms):`, {
          达人id: updateData.达人id,
          处理状态: response.data?.处理状态,
          昵称: response.data?.昵称
        })
      }

      return response
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`TalentService.updateTalentData 更新达人数据失败 (耗时: ${duration}ms):`, {
        达人id: updateData.达人id,
        错误: error.message || error
      })
      throw this._handleError(error, '更新达人数据')
    }
  }

  /**
   * 绑定达人微信号
   *
   * 为已认领的达人绑定微信号，建立完整的关联关系
   * 包括微信信息记录、达人商务微信记录和用户关联记录
   *
   * @param {Object} bindData - 绑定数据
   * @param {number} bindData.达人id - 达人id（必需）
   * @param {string} bindData.微信号 - 微信号（必需）
   * @param {string} bindData.平台 - 平台类型，默认为"微信"（可选）
   * @returns {Promise<Object>} 绑定结果
   *
   * @example
   * const result = await talentService.bindWechatNumber({
   *   达人id: 123,
   *   微信号: 'example_wx',
   *   平台: '微信'
   * })
   */
  async bindWechatNumber(bindData) {
    // 参数验证
    if (!bindData || !bindData.达人id) {
      throw new Error('达人id不能为空')
    }

    if (!bindData.微信号 || !bindData.微信号.trim()) {
      throw new Error('微信号不能为空')
    }

    const startTime = Date.now()

    try {
      console.log('🔗 TalentService.bindWechatNumber 开始绑定微信号:', {
        达人id: bindData.达人id,
        微信号: bindData.微信号,
        平台: bindData.平台 || '微信',
        时间戳: new Date().toISOString()
      })

      // 构建请求参数
      const requestParams = {
        达人id: bindData.达人id,
        微信号: bindData.微信号.trim(),
        平台: bindData.平台 || '微信'
      }

      console.log('📤 发送绑定微信号请求参数:', requestParams)

      // 发送请求到后端
      const response = await request.post('/kol/bind-wechat', requestParams)

      const duration = Date.now() - startTime

      if (response.status === 100) {
        console.log('✅ TalentService.bindWechatNumber 绑定微信号成功:', {
          达人id: requestParams.达人id,
          微信号: requestParams.微信号,
          响应消息: response.message,
          耗时: duration + 'ms'
        })
      }

      return response

    } catch (error) {
      const duration = Date.now() - startTime
      console.error('❌ TalentService.bindWechatNumber 绑定微信号失败:', {
        达人id: bindData.达人id,
        微信号: bindData.微信号,
        错误类型: error.name,
        错误消息: error.message,
        耗时: duration + 'ms'
      })

      // 使用增强的错误处理
      throw this._handleError(error, '绑定微信号')
    }
  }

  /**
   * 数据验证方法
   * @param {Object} talentData - 待验证的达人数据
   * @returns {Object} 验证结果
   * @private
   */
  _validateTalentData(talentData) {
    const errors = []

    // 手机号格式验证
    if (talentData.手机号 && !/^1[3-9]\d{9}$/.test(talentData.手机号)) {
      errors.push('手机号格式不正确')
    }

    // 邮箱格式验证
    if (talentData.邮箱 && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(talentData.邮箱)) {
      errors.push('邮箱格式不正确')
    }

    // 个人备注长度验证
    if (talentData.个人备注 && talentData.个人备注.length > 500) {
      errors.push('个人备注不能超过500个字符')
    }

    // 个人标签数量验证
    if (talentData.个人标签 && talentData.个人标签.length > 10) {
      errors.push('个人标签最多10个')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 构建请求参数
   * @param {Object} talentData - 原始达人数据
   * @returns {Object} 处理后的请求参数
   * @private
   */
  _buildRequestParams(talentData) {
    const requestParams = {
      达人id: talentData.达人id
    }

    // 添加可选字段，过滤空值
    const optionalFields = ['微信号', '手机号', '邮箱', '合作状态', '个人备注']

    optionalFields.forEach(field => {
      if (talentData[field] && talentData[field].trim()) {
        requestParams[field] = talentData[field].trim()
      }
    })

    // 处理个人标签
    if (talentData.个人标签 && Array.isArray(talentData.个人标签) && talentData.个人标签.length > 0) {
      requestParams.个人标签 = talentData.个人标签.filter(tag => tag && tag.trim()).map(tag => tag.trim())
    }

    return requestParams
  }

  /**
   * 统一错误处理方法
   * 
   * 将各种类型的错误统一处理，提供友好的错误信息
   * 增强功能：
   * - 区分网络错误、超时错误、业务错误
   * - 提供用户友好的错误提示
   * - 记录详细的错误信息用于调试
   * 
   * @param {Error} error - 原始错误对象
   * @param {string} operation - 操作名称，用于错误提示
   * @returns {Error} 处理后的错误对象
   * @private
   */
  _handleError(error, operation) {
    // 超时错误 - 提供更具体的指导
    if (error.code === 'ECONNABORTED') {
      return new Error(`${operation}失败：操作超时，请检查网络连接后重试。如问题持续存在，请联系技术支持。`)
    }

    // 网络错误
    if (error.code === 'NETWORK_ERROR' || !error.response) {
      return new Error(`${operation}失败：网络连接异常，请检查网络后重试`)
    }

    // 服务器错误
    if (error.response?.status >= 500) {
      return new Error(`${operation}失败：服务器内部错误，请稍后重试或联系技术支持`)
    }

    // 权限错误
    if (error.response?.status === 401) {
      return new Error(`${operation}失败：用户未登录或登录已过期，请重新登录`)
    }

    if (error.response?.status === 403) {
      return new Error(`${operation}失败：没有权限执行此操作，请联系管理员`)
    }

    // 请求错误（400系列）
    if (error.response?.status >= 400 && error.response?.status < 500) {
      const message = error.response?.data?.message || error.message
      return new Error(`${operation}失败：${message}`)
    }

    // 业务逻辑错误
    if (error.response?.data?.message) {
      return new Error(`${operation}失败：${error.response.data.message}`)
    }

    // 其他错误
    return new Error(`${operation}失败：${error.message || '未知错误'}`)
  }

  /**
   * 获取微信达人公海列表（支持流式分页）
   *
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 当前页码，默认1
   * @param {number} params.每页数量 - 每页显示数量，默认18
   * @param {number} params.最后id - 上一页最后的达人id，流式分页关键参数
   * @param {Object} params.筛选条件 - 筛选条件，如地区、性别等
   * @param {boolean} params.有联系方式 - 是否筛选有联系方式的达人
   * @param {string} params.关键词 - 通过微信号、昵称搜索达人
   * @returns {Promise<Object>} API响应，包含微信达人列表和下一页最后id
   */
  async getWechatTalentPool(params = {}) {
    // 设置默认参数
    const defaultParams = {
      页码: 1,
      每页数量: 18,
      最后id: 0,
      筛选条件: null,
      有联系方式: null,
      关键词: null
    }

    const requestParams = { ...defaultParams, ...params }

    console.log('TalentService.getWechatTalentPool 请求参数:', requestParams)

    try {
      const response = await request.post('/wechat-kol/list', requestParams)

      if (response.status === 100 && response.data) {
        console.log('TalentService.getWechatTalentPool 响应摘要:', {
          当前页: response.data.当前页,
          每页数量: response.data.每页数量,
          最后id: response.data.最后id,
          达人数量: response.data.达人列表?.length || 0,
          下一页最后id: response.data.下一页最后id,
          团队模式: response.data.团队模式
        })
      }

      return response
    } catch (error) {
      console.error('TalentService.getWechatTalentPool 请求失败:', error)
      throw this._handleError(error, '获取微信达人公海列表')
    }
  }

  /**
   * 获取微信达人详情信息
   *
   * @param {number} talentId - 微信达人id
   * @returns {Promise<Object>} 微信达人详情数据
   */
  async getWechatTalentDetail(talentId) {
    if (!talentId || talentId <= 0) {
      throw new Error('微信达人id无效')
    }

    try {
      const response = await request.post('/wechat-kol/detail', {
        达人id: talentId
      })

      if (response.status === 100) {
        console.log(`TalentService.getWechatTalentDetail 获取微信达人 ${talentId} 详情成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.getWechatTalentDetail 获取微信达人 ${talentId} 详情失败:`, error)
      throw this._handleError(error, '获取微信达人详情')
    }
  }

  /**
   * 认领微信达人
   *
   * @param {number} talentId - 要认领的微信达人id
   * @returns {Promise<Object>} 认领操作结果
   */
  async claimWechatTalent(talentId) {
    if (!talentId || talentId <= 0) {
      throw new Error('微信达人id无效')
    }

    try {
      const response = await request.post('/wechat-kol/claim', {
        达人id: talentId
      })

      if (response.status === 100) {
        console.log(`TalentService.claimWechatTalent 认领微信达人 ${talentId} 成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.claimWechatTalent 认领微信达人 ${talentId} 失败:`, error)
      throw this._handleError(error, '认领微信达人')
    }
  }

  /**
   * 取消认领微信达人
   *
   * @param {number} talentId - 要取消认领的微信达人id
   * @returns {Promise<Object>} 取消认领操作结果
   */
  async unclaimWechatTalent(talentId) {
    if (!talentId || talentId <= 0) {
      throw new Error('微信达人id无效')
    }

    try {
      const response = await request.post('/wechat-kol/unclaim', {
        达人id: talentId
      })

      if (response.status === 100) {
        console.log(`TalentService.unclaimWechatTalent 取消认领微信达人 ${talentId} 成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.unclaimWechatTalent 取消认领微信达人 ${talentId} 失败:`, error)
      throw this._handleError(error, '取消认领微信达人')
    }
  }

  /**
   * 获取我认领的微信达人列表
   *
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 当前页码，默认1
   * @param {number} params.每页数量 - 每页显示数量，默认20
   * @param {string} params.排序字段 - 排序字段，默认"认领时间"
   * @param {string} params.排序方式 - 排序方式，默认"desc"
   * @param {Object} params.筛选条件 - 筛选条件
   * @param {string} params.关键词 - 搜索关键词
   * @returns {Promise<Object>} 我的微信达人列表
   */
  async getMyWechatTalents(params = {}) {
    const defaultParams = {
      页码: 1,
      每页数量: 20,
      排序字段: "认领时间",
      排序方式: "desc",
      筛选条件: null,
      关键词: null
    }

    const requestParams = { ...defaultParams, ...params }

    try {
      const response = await request.post('/wechat-kol/myclaims', requestParams)

      if (response.status === 100) {
        console.log('TalentService.getMyWechatTalents 获取我的微信达人列表成功:', {
          总数: response.data?.总数 || 0,
          当前页: response.data?.当前页 || 1,
          达人数量: (response.data?.达人列表 || response.data?.列表 || []).length
        })
      }

      return response
    } catch (error) {
      console.error('TalentService.getMyWechatTalents 获取我的微信达人列表失败:', error)
      throw this._handleError(error, '获取我的微信达人列表')
    }
  }

  /**
   * 获取微信达人统计数据
   *
   * @param {Object} params - 统计参数
   * @param {number} params.时间范围 - 统计时间范围（天数），默认7天
   * @returns {Promise<Object>} 微信达人统计数据
   */
  async getWechatTalentStats(params = {}) {
    const defaultParams = {
      时间范围: 7
    }

    try {
      const response = await request.post('/wechat-kol/stats', {
        ...defaultParams,
        ...params
      })

      if (response.status === 100) {
        console.log('TalentService.getWechatTalentStats 获取微信达人统计成功')
      }

      return response
    } catch (error) {
      console.error('TalentService.getWechatTalentStats 获取微信达人统计失败:', error)
      throw this._handleError(error, '获取微信达人统计')
    }
  }

  /**
   * 通过微信号查询或创建微信达人
   *
   * @param {string} wechatId - 微信号
   * @param {Object} talentData - 可选，达人数据用于创建或更新
   * @returns {Promise<Object>} 查询或创建结果
   */
  async getOrCreateWechatTalent(wechatId, talentData = null) {
    if (!wechatId || !wechatId.trim()) {
      throw new Error('微信号不能为空')
    }

    try {
      const response = await request.post('/wechat-kol/get_id', {
        微信号: wechatId.trim(),
        达人数据: talentData
      })

      if (response.status === 100) {
        console.log(`TalentService.getOrCreateWechatTalent 微信号 ${wechatId} 处理成功`)
      }

      return response
    } catch (error) {
      console.error(`TalentService.getOrCreateWechatTalent 微信号 ${wechatId} 处理失败:`, error)
      throw this._handleError(error, '查询或创建微信达人')
    }
  }

  /**
   * 搜索达人 - 第一步：调用第三方搜索API
   *
   * @param {Object} params - 搜索参数
   * @param {string} params.抖音号 - 达人抖音号
   * @returns {Promise<Object>} 搜索结果
   */
  async searchTalent(params) {
    try {
      console.log('TalentService.searchTalent 搜索达人:', params)

      const response = await request.post('/kol/search-talent', params)

      console.log('TalentService.searchTalent 搜索结果:', response)
      return response
    } catch (error) {
      console.error('TalentService.searchTalent 搜索达人失败:', error)
      throw this._handleError(error, '搜索达人')
    }
  }

  /**
   * 添加达人 - 简化版本，只需要UID
   *
   * @param {Object} params - 添加参数
   * @param {string} params.UID - 达人UID
   * @returns {Promise<Object>} 添加结果
   */
  async addTalent(params) {
    try {
      console.log('TalentService.addTalent 添加达人:', params)

      const response = await request.post('/kol/add-talent', params)

      console.log('TalentService.addTalent 添加结果:', response)
      return response
    } catch (error) {
      console.error('TalentService.addTalent 添加达人失败:', error)
      throw this._handleError(error, '添加达人')
    }
  }

  /**
   * 达人公海搜索 - 第三方API搜索达人
   *
   * @param {Object} params - 搜索参数
   * @param {string} params.抖音号 - 达人抖音号
   * @returns {Promise<Object>} 搜索结果
   */
  async searchTalentPool(params) {
    try {
      console.log('TalentService.searchTalentPool 搜索达人公海:', params)

      const response = await request.post('/kol/search-talent-pool', params)

      console.log('TalentService.searchTalentPool 搜索结果:', response)
      return response
    } catch (error) {
      console.error('TalentService.searchTalentPool 搜索达人公海失败:', error)
      throw this._handleError(error, '搜索达人公海')
    }
  }

  /**
   * 保存达人到公海 - 用户选择达人后保存
   *
   * @param {Object} params - 保存参数
   * @param {string} params.抖音号 - 达人抖音号
   * @param {number} params.选择的达人索引 - 用户选择的达人索引
   * @param {Array} params.第三方搜索结果 - 第三方搜索结果
   * @returns {Promise<Object>} 保存结果
   */
  async saveTalentToPool(params) {
    try {
      console.log('TalentService.saveTalentToPool 保存达人到公海:', params)

      const response = await request.post('/kol/save-talent-to-pool', params)

      console.log('TalentService.saveTalentToPool 保存结果:', response)
      return response
    } catch (error) {
      console.error('TalentService.saveTalentToPool 保存达人到公海失败:', error)
      throw this._handleError(error, '保存达人到公海')
    }
  }


}

// 创建并导出服务实例
const talentService = new TalentService()

export default talentService

// 同时提供具名导出，方便按需导入
export { TalentService }
