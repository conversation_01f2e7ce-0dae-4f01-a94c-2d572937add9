import {createApp} from 'vue'
import Antd from 'ant-design-vue';
import App from './App.vue'
import router from './router'
import {createPinia} from 'pinia'
import {useUserStore} from './store'
import 'ant-design-vue/dist/reset.css'; // Ant Design Vue 样式重置

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)

// 在应用启动时清理无效的localStorage数据，防止JSON解析错误
try {
  const userStore = useUserStore()
  userStore.cleanupInvalidStorage()
} catch (error) {
  console.error('初始化用户store失败:', error)
  // 清理所有localStorage数据作为安全措施
  try {
    localStorage.clear()
  } catch (e) {
    console.error('清理localStorage失败:', e)
  }
}

app.use(router)
app.use(Antd); // 全局注册 Ant Design Vue 组件

// 全局错误处理，捕获未处理的路由错误
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info)
}

app.mount('#app') 