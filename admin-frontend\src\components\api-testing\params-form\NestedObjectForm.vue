<template>
  <div class="nested-object-form">
    <a-form layout="vertical">
      <a-row :gutter="16">
        <template v-for="field in fields" :key="field.key">
          <a-col :span="getColSpan(field)">
            <a-form-item 
              :name="field.key"
              :required="field.required"
              :rules="generateAntdValidationRules(field)"
              :extra="field.description || field.placeholder"
            >
              <template #label>
                <a-tooltip placement="topLeft">
                  <template #title>
                    <div style="white-space: pre-line;">{{ formatParamDetailsForTooltip(field) }}</div>
                  </template>
                  <span>{{ field.name }} ({{ field.key }})</span>
                </a-tooltip>
              </template>

              <!-- 字符串类型 -->
              <a-input 
                v-if="['string', 'url', 'email'].includes(field.type)" 
                v-model:value="formData[field.key]" 
                :placeholder="field.placeholder || field.name"
                allow-clear
                @change="emitUpdate"
              />
              
              <!-- 数字类型 -->
              <a-input-number 
                v-else-if="['number', 'integer'].includes(field.type)" 
                v-model:value="formData[field.key]" 
                :placeholder="field.placeholder || field.name"
                :precision="field.oasType === 'integer' ? 0 : undefined"
                style="width: 100%;"
                @change="emitUpdate"
              />
              
              <!-- 布尔类型 -->
              <a-switch 
                v-else-if="field.type === 'boolean'" 
                v-model:checked="formData[field.key]" 
                @change="emitUpdate"
              />
              
              <!-- 嵌套对象 -->
              <div v-else-if="field.type === 'object' && field.children && field.children.length > 0" class="nested-object">
                <nested-object-form
                  :fields="field.children"
                  :value="formData[field.key] || {}"
                  @change="(val) => handleNestedChange(field.key, val)"
                />
              </div>
              
              <!-- 嵌套数组 -->
              <div v-else-if="field.type === 'array' && field.itemSchema" class="nested-array">
                <nested-array-form
                  :item-schema="field.itemSchema"
                  :array-validations="field.validations"
                  :value="formData[field.key] || []"
                  @change="(val) => handleNestedChange(field.key, val)"
                />
              </div>

              <!-- 日期选择 -->
              <a-date-picker
                v-else-if="field.type === 'date'"
                v-model:value="formData[field.key]" 
                style="width: 100%;"
                valueFormat="YYYY-MM-DD"
                @change="emitUpdate"
              />
              <!-- 日期时间选择 -->
              <a-date-picker
                v-else-if="field.type === 'datetime'"
                v-model:value="formData[field.key]" 
                show-time
                style="width: 100%;"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                @change="emitUpdate"
              />
              <!-- 下拉选择 (based on field.options) -->
              <a-select
                v-else-if="field.options && field.options.length > 0" 
                v-model:value="formData[field.key]" 
                :placeholder="field.placeholder || `请选择 ${field.name}`"
                style="width: 100%;"
                allow-clear
                @change="emitUpdate"
              >
                <a-select-option v-for="option in field.options" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
              
              <!-- 默认文本区域 (for text, or object/array without children/itemSchema) -->
              <a-textarea 
                v-else
                v-model:value="formData[field.key]"
                :placeholder="field.placeholder || `请输入 ${field.name}`"
                :rows="3"
                allow-clear
                @change="emitUpdate"
                @blur="validateJsonOnBlur(field, $event)"
              />
            </a-form-item>
          </a-col>
        </template>
      </a-row>
    </a-form>
  </div>
</template>

<script setup>
import {defineAsyncComponent, markRaw, reactive, watch} from 'vue';
import {
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  message,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
  Switch as ASwitch,
  Textarea as ATextarea,
  Tooltip as ATooltip
} from 'ant-design-vue';

// 递归引用嵌套组件
import NestedArrayForm from './NestedArrayForm.vue';
// 在<script setup>中自引用需要特殊处理
const NestedObjectForm = markRaw(defineAsyncComponent(() => import('./NestedObjectForm.vue')));

const props = defineProps({
  fields: {
    type: Array,
    required: true
  },
  value: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['change']);

// 创建响应式数据对象
const formData = reactive({});

// 初始化和更新formData
watch(() => props.value, (newValue) => {
  // fields may not be available on first props.value watch if fields are dynamic
  // So, initialize based on newValue keys primarily, and then ensure all defined fields have at least a null/undefined placeholder
  const newFormData = { ... (newValue || {}) };
  props.fields.forEach(field => {
    if (!(field.key in newFormData)) {
      // Ensure all fields are present in formData for reactivity, initialize with default or undefined
      newFormData[field.key] = field.defaultValue !== undefined ? field.defaultValue : undefined;
    }
  });
  Object.keys(formData).forEach(key => delete formData[key]);
  Object.assign(formData, newFormData);

}, { immediate: true, deep: true });

// 处理嵌套表单的变更
const handleNestedChange = (key, value) => {
  formData[key] = value;
  emitUpdate();
};

// 向父组件发送更新事件
const emitUpdate = () => {
  emit('change', { ...formData });
};

// 根据字段类型获取列布局宽度
const getColSpan = (field) => {
  if (field.type === 'object' || field.type === 'array' || field.type === 'text') {
    return 24; // 占据整行
  }
  return 12; // 默认占据半行
};

// Helper function to format parameter details for tooltip
const formatParamDetailsForTooltip = (param) => {
  if (!param) return '';
  const details = [];
  details.push(`名称: ${param.name} (${param.key})`);
  // No isQueryParam for nested fields
  if (param.oasType) {
    details.push(`类型: ${param.oasType}${param.oasFormat ? ` (${param.oasFormat})` : ''}`);
  }
  if (param.description) {
    details.push(`描述: ${param.description}`);
  }
  if (param.required) {
    details.push('是否必填: 是');
  }
  if (param.defaultValue !== undefined) {
    details.push(`默认值: ${JSON.stringify(param.defaultValue)}`);
  }
  if (param.validations) {
    const vRules = [];
    if (param.validations.pattern) vRules.push(`模式: ${param.validations.pattern}`);
    if (param.validations.minLength !== undefined) vRules.push(`最小长度: ${param.validations.minLength}`);
    if (param.validations.maxLength !== undefined) vRules.push(`最大长度: ${param.validations.maxLength}`);
    if (param.validations.minimum !== undefined) vRules.push(`最小值: ${param.validations.minimum}`);
    if (param.validations.maximum !== undefined) vRules.push(`最大值: ${param.validations.maximum}`);
    if (param.validations.exclusiveMinimum !== undefined) vRules.push(`排他最小值: ${param.validations.exclusiveMinimum}`);
    if (param.validations.exclusiveMaximum !== undefined) vRules.push(`排他最大值: ${param.validations.exclusiveMaximum}`);
    if (param.validations.multipleOf !== undefined) vRules.push(`倍数: ${param.validations.multipleOf}`);
    if (param.validations.enum) vRules.push(`可选值: ${param.validations.enum.join(', ')}`);
    if (param.type === 'array') { // Array specific validations for the array field itself
      if (param.validations.minItems !== undefined) vRules.push(`最少项目数: ${param.validations.minItems}`);
      if (param.validations.maxItems !== undefined) vRules.push(`最多项目数: ${param.validations.maxItems}`);
      if (param.validations.uniqueItems) vRules.push(`项目需唯一`);
    }
    if (vRules.length > 0) {
      details.push(`校验规则:\n  - ${vRules.join('\n  - ')}`);
    }
  }
  return details.join('\n\n');
};

// Helper function to generate Ant Design Vue validation rules (similar to RequestParamsForm)
const generateAntdValidationRules = (param) => {
  if (!param) return [];
  const rules = [];

  if (param.required) {
    rules.push({ required: true, message: `${param.name} 是必填项` });
  }

  if (param.type === 'email') {
    rules.push({ type: 'email', message: '请输入有效的邮箱地址' });
  }
  if (param.type === 'url') {
    rules.push({ type: 'url', message: '请输入有效的URL地址' });
  }

  if (param.validations) {
    const vals = param.validations;
    if (vals.minLength !== undefined) {
      rules.push({ min: vals.minLength, message: `长度不能少于 ${vals.minLength} 个字符`, trigger: 'blur' });
    }
    if (vals.maxLength !== undefined) {
      rules.push({ max: vals.maxLength, message: `长度不能超过 ${vals.maxLength} 个字符`, trigger: 'blur' });
    }
    if (vals.pattern) {
      try {
        const regex = new RegExp(vals.pattern);
        rules.push({ pattern: regex, message: `必须符合模式: ${vals.pattern}`, trigger: 'blur' });
      } catch (e) {
        console.warn(`Invalid regex pattern for ${param.key}: ${vals.pattern}`, e);
      }
    }
    if (vals.minimum !== undefined) {
      rules.push({ type: param.oasType === 'integer' ? 'integer' : 'number', min: vals.minimum, message: `数值不能小于 ${vals.minimum}`, trigger: 'blur' });
    }
    if (vals.maximum !== undefined) {
      rules.push({ type: param.oasType === 'integer' ? 'integer' : 'number', max: vals.maximum, message: `数值不能大于 ${vals.maximum}`, trigger: 'blur' });
    }
    if (vals.enum && Array.isArray(vals.enum)) {
      rules.push({
        type: param.oasType === 'array' ? 'array' : undefined,
        validator: (rule, value) => {
          if (value === undefined || value === null || value === '') return Promise.resolve();
          const enumValues = vals.enum;
          if (param.type === 'array') {
            if (!Array.isArray(value)) return Promise.reject('必须是一个数组');
            for (const item of value) {
              if (!enumValues.includes(item)) {
                return Promise.reject(`${item} 不在允许的值列表中: [${enumValues.join(', ')}]`);
              }
            }
          } else {
            if (!enumValues.includes(value)) {
              return Promise.reject(`${value} 不在允许的值列表中: [${enumValues.join(', ')}]`);
            }
          }
          return Promise.resolve();
        },
        trigger: 'change',
      });
    }
    if (param.type === 'array') {
      if (vals.minItems !== undefined) {
        rules.push({ type: 'array', min: vals.minItems, message: `至少需要 ${vals.minItems} 个项目`, trigger: 'change' });
      }
      if (vals.maxItems !== undefined) {
        rules.push({ type: 'array', max: vals.maxItems, message: `最多允许 ${vals.maxItems} 个项目`, trigger: 'change' });
      }
      if (vals.uniqueItems && param.itemSchema) {
        rules.push({
          type: 'array',
          validator: (rule, value) => {
            if (!Array.isArray(value) || value.length === 0) return Promise.resolve();
            const seen = new Set();
            for (const item of value) {
              const comparableItem = (typeof item === 'object' && item !== null) ? JSON.stringify(item) : item;
              if (seen.has(comparableItem)) {
                return Promise.reject('数组中的项目必须是唯一的');
              }
              seen.add(comparableItem);
            }
            return Promise.resolve();
          },
          trigger: 'change',
        });
      }
    }
  }
  return rules;
};

const validateJsonOnBlur = (field, event) => {
  if (!field || !(field.type === 'object' || field.type === 'array' || field.type === 'json_string')) {
      // Only validate if it's supposed to be JSON but doesn't have structured handling
      if ((field.type === 'object' && (!field.children || field.children.length === 0)) || 
          (field.type === 'array' && !field.itemSchema)) {
          // continue with validation
      } else {
          return; // Already handled by structured forms or not a JSON type meant for textarea
      }
  }
  const value = event.target.value;
  if (value) {
    try {
      JSON.parse(value);
    } catch (e) {
      message.error(`${field.name} (${field.key}) 的JSON格式无效: ${e.message}`);
    }
  }
};
</script>

<style scoped>
.nested-object-form {
  padding: 8px;
}
</style> 