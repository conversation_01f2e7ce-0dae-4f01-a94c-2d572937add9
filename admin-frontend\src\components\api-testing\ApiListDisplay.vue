<template>
  <div class="api-list-display">
    <a-input-search 
      v-model:value="searchTermValue"
      placeholder="搜索接口名称或路径..."
      style="margin-bottom: 16px;"
      @search="onSearch"
      @input="onSearchDebounced"
    />
    <a-list 
      item-layout="horizontal" 
      :data-source="filteredApis"
      :loading="isLoading"
      bordered
      size="small"
      class="api-list-container"
    >
      <template #renderItem="{ item }">
        <a-list-item 
          @click="() => selectApi(item.id)"
          :class="{ 'selected-api-item': item.id === selectedApiId }"
          class="api-list-item"
        >
          <a-list-item-meta :description="item.path">
            <template #title>
              <div class="api-title-container">
                <a :title="item.name">{{ item.name }}</a>
                <a-tooltip :title="item.requiresAuth ? '需要认证' : '无需认证'">
                  <lock-outlined v-if="item.requiresAuth" class="auth-icon auth-required" />
                  <unlock-outlined v-else class="auth-icon auth-not-required" />
                </a-tooltip>
              </div>
            </template>
            <template #avatar>
              <component :is="getAntIcon(item.categoryIcon || 'api')" v-if="item.categoryIcon" />
              <api-outlined v-else />
            </template>
          </a-list-item-meta>
        </a-list-item>
      </template>
      <template #empty v-if="!isLoading">
         <a-empty description="未找到匹配的接口" />
      </template>
    </a-list>
  </div>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {
  Empty as AEmpty,
  InputSearch as AInputSearch,
  List as AList,
  ListItem as AListItem,
  ListItemMeta as AListItemMeta,
  Tooltip as ATooltip,
} from 'ant-design-vue';
import {
  ApiOutlined,
  AppstoreOutlined,
  BookOutlined,
  EllipsisOutlined,
  LockOutlined,
  RobotOutlined,
  SettingOutlined,
  ShoppingCartOutlined,
  UnlockOutlined,
  UserOutlined,
  WechatOutlined,
} from '@ant-design/icons-vue';
import {useApiTestingModule} from '@/store/apiTestingModule';
import debounce from 'lodash/debounce';

const store = useApiTestingModule();

const searchTermValue = ref(store.searchTerm);

const filteredApis = computed(() => store.getFilteredEndpoints);
const selectedApiId = computed(() => store.selectedApiId);
const isLoading = computed(() => store.isLoading); // 可以用于接口列表加载状态，如果从服务器获取的话

const onSearch = (value) => {
  store.setSearchTerm(value);
};
// 使用防抖处理输入事件，避免过于频繁地更新store和触发计算属性
const onSearchDebounced = debounce((event) => {
    store.setSearchTerm(event.target.value);
}, 300);

const selectApi = (apiId) => {
  store.selectApi(apiId);
};

// 当store中的searchTerm变化时（例如从其他地方修改），同步到本地的searchTermValue
watch(() => store.searchTerm, (newValue) => {
  if (searchTermValue.value !== newValue) {
    searchTermValue.value = newValue;
  }
});

// 用于根据分类获取图标（可以从 apiDefinitions.js 中获取分类的图标信息）
const categoryIcons = {
  用户: UserOutlined,
  产品: AppstoreOutlined,
  订单: ShoppingCartOutlined,
  AI: RobotOutlined,
  系统: SettingOutlined,
  微信: WechatOutlined,
  知识库: BookOutlined,
  其他: EllipsisOutlined,
};

const getAntIcon = (categoryName) => {
  // 尝试从 apiCategories 中找到对应分类的图标名称
  const category = store.apiCategories.find(c => c.name === categoryName || c.key === categoryName);
  if (category && category.icon && categoryIcons[category.icon]) {
    return categoryIcons[category.icon];
  }
  return categoryIcons[categoryName] || ApiOutlined; // 默认图标
};

// 如果初始时filteredApis为空（可能因为没有默认分类或searchTerm），可以尝试加载
if (filteredApis.value.length === 0 && store.apiEndpoints.length > 0) {
    // 这个逻辑可能更多在 ApiTestPlatform.vue 的 onMounted 中处理，确保分类和接口已加载
    // console.log("ApiListDisplay: Initial filteredApis is empty, check store state.");
}

</script>

<style scoped>
.api-list-display {
  display: flex;
  flex-direction: column;
  height: 100%; /* 尝试让组件填满父容器高度 */
}

.api-list-container {
  flex-grow: 1;
  overflow-y: auto; /* 使列表内容可滚动 */
  background-color: #fff; /* 确保有背景色 */
}

.api-list-item {
  cursor: pointer;
  padding: 8px 12px; /* 调整列表项的内边距 */
  transition: background-color 0.3s;
}

.api-list-item:hover {
  background-color: #f0f2f5; /* 鼠标悬停效果 */
}

.selected-api-item {
  background-color: #e6f7ff; /* 选中项的背景色 */
  border-right: 3px solid #1890ff; /* 选中项的右边框高亮 */
}

.ant-list-item-meta-title a {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

.selected-api-item .ant-list-item-meta-title a {
  color: #1890ff;
}

.ant-list-item-meta-description {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.api-title-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-icon {
  font-size: 12px;
}

.auth-required {
  color: #1890ff;
}

.auth-not-required {
  color: #52c41a;
}
</style> 