/**
 * SuperAdmin 服务
 * 统一管理所有超级管理员相关的API接口调用
 * 
 * 设计原则：
 * 1. 直接返回后端数据，不做过多处理
 * 2. 统一错误处理和日志记录
 * 3. 支持标准化响应数据格式 {status: 100, message: '', data: {}}
 * 4. 自动处理认证和请求头
 */

import apiClient, { createHttpClient } from './apiClient.js'

class SuperAdminService {
  constructor() {
    this.baseURL = '/admin'
    // 创建不需要认证的客户端，用于登录等公开接口
    this.publicApiClient = createHttpClient({
      enableAuth: false,
      enableLogging: true,
      enableStandardization: true,
      clientType: 'main'
    })
  }

  /**
   * 通用请求处理器
   * @param {Function} requestFn - 请求函数
   * @param {string} operation - 操作描述
   * @returns {Promise} 直接返回后端数据
   */
  async _handleRequest(requestFn, operation) {
    try {
      console.log(`🚀 SuperAdmin API调用: ${operation}`)
      const response = await requestFn()

      // 直接返回后端统一响应格式
      console.log(`✅ ${operation} 响应:`, response)
      return response

    } catch (error) {
      console.error(`❌ ${operation} 失败:`, error)
      
      // 标准化错误响应
      const errorResponse = {
        status: error.response?.status || 500,
        message: error.userFriendlyMessage || error.message || `${operation}失败`,
        data: null
      }
      
      // 如果是认证错误，添加特殊标记
      if (error.response?.status === 401) {
        errorResponse.needLogin = true
      }
      
      return errorResponse
    }
  }

  // ==================== 认证管理 ====================

  /**
   * 管理员登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise<Object>} 登录结果
   */
  async login(credentials) {
    return this._handleRequest(
      () => this.publicApiClient.post(`${this.baseURL}/login`, credentials),
      '管理员登录'
    )
  }

  /**
   * 管理员登出
   * @returns {Promise<Object>} 登出结果
   */
  async logout() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/logout`),
      '管理员登出'
    )
  }

  /**
   * 获取管理员个人信息数据 - 中文命名方法
   * @returns {Promise<Object>} 管理员信息
   */
  async 获取管理员个人信息数据() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/profile`),
      '获取管理员信息'
    )
  }

  /**
   * 获取管理员信息 - 兼容性方法
   * @returns {Promise<Object>} 管理员信息
   */
  async getProfile() {
    return this.获取管理员个人信息数据();
  }

  // ==================== 仪表盘 ====================

  /**
   * 获取仪表盘统计数据 - 中文命名方法
   * @returns {Promise<Object>} 统计数据
   */
  async 获取仪表盘统计数据() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/dashboard/stats`),
      '获取仪表盘统计'
    )
  }

  /**
   * 获取统计概览数据 - 中文命名方法
   * @returns {Promise<Object>} 统计概览
   */
  async 获取统计概览数据() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/stats/overview`),
      '获取统计概览'
    )
  }

  /**
   * 获取最近活动数据 - 中文命名方法
   * @returns {Promise<Object>} 最近活动数据
   */
  async 获取最近活动数据() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/activity/recent`),
      '获取最近活动'
    )
  }

  /**
   * 获取仪表盘统计数据 - 兼容性方法
   * @returns {Promise<Object>} 统计数据
   */
  async getDashboardStats() {
    return this.获取仪表盘统计数据();
  }

  /**
   * 获取统计概览 - 兼容性方法
   * @returns {Promise<Object>} 统计概览
   */
  async getStatsOverview() {
    return this.获取统计概览数据();
  }

  /**
   * 获取最近活动 - 兼容性方法
   * @returns {Promise<Object>} 最近活动数据
   */
  async getRecentActivity() {
    return this.获取最近活动数据();
  }

  // ==================== 用户管理 ====================

  /**
   * 获取用户列表数据
   * @param {Object} 查询参数 - 查询参数对象
   * @param {number} 查询参数.页码 - 页码
   * @param {number} 查询参数.每页数量 - 每页数量
   * @param {string} 查询参数.搜索关键词 - 搜索关键词
   * @param {string} 查询参数.状态筛选 - 状态筛选
   * @param {string} 查询参数.时间范围 - 时间范围
   * @returns {Promise<Object>} 用户列表响应数据
   */
  // {{ AURA-X: Add - 添加排序参数支持，简洁高效直接对接. Approval: 寸止(ID:1721062800). }}
  async 获取用户列表数据(查询参数 = {}) {
    const 请求参数 = {
      页码: 查询参数.页码 || 1,
      每页数量: 查询参数.每页数量 || 20,
      搜索关键词: 查询参数.搜索关键词 || '',
      状态筛选: 查询参数.状态筛选 || null,
      时间范围: 查询参数.时间范围 || null,
      排序字段: 查询参数.排序字段 || null,
      排序顺序: 查询参数.排序顺序 || null
    }

    console.log('🔄 用户列表API请求参数:', 请求参数);

    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/list`, 请求参数),
      '获取用户列表'
    )
  }

  /**
   * 兼容性方法 - 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 用户列表响应数据
   */
  async getUserList(params = {}) {
    return this.获取用户列表数据(params);
  }

  /**
   * 获取用户详情数据 - 中文命名方法
   * @param {number} 用户id - 用户id
   * @returns {Promise<Object>} 用户详情
   */
  async 获取用户详情数据(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/detail`, { 用户id: 用户id }),
      `获取用户详情 (ID: ${用户id})`
    )
  }

  /**
   * 创建用户数据 - 中文命名方法
   * @param {Object} 用户数据 - 用户数据
   * @returns {Promise<Object>} 创建结果
   */
  async 创建用户数据(用户数据) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/create`, 用户数据),
      '创建用户'
    )
  }

  /**
   * 更新用户数据 - 中文命名方法
   * @param {Object} 用户数据 - 用户数据（包含用户id）
   * @returns {Promise<Object>} 更新结果
   */
  async 更新用户数据(用户数据) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/update`, 用户数据),
      `更新用户 (ID: ${用户数据.用户id})`
    )
  }

  /**
   * 删除用户数据 - 中文命名方法
   * @param {number} 用户id - 用户id
   * @returns {Promise<Object>} 删除结果
   */
  async 删除用户数据(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/delete`, { 用户id: 用户id }),
      `删除用户 (ID: ${用户id})`
    )
  }

  // ==================== 兼容性方法（向后兼容） ====================

  /**
   * 获取用户详情 - 兼容性方法
   * @param {number} userId - 用户id
   * @returns {Promise<Object>} 用户详情
   */
  async getUserDetail(userId) {
    return this.获取用户详情数据(userId);
  }

  /**
   * 创建用户 - 兼容性方法
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 创建结果
   */
  async createUser(userData) {
    return this.创建用户数据(userData);
  }

  /**
   * 更新用户 - 兼容性方法
   * @param {Object} userData - 用户数据（包含用户id）
   * @returns {Promise<Object>} 更新结果
   */
  async updateUser(userData) {
    return this.更新用户数据(userData);
  }

  /**
   * 删除用户 - 兼容性方法
   * @param {number} userId - 用户id
   * @returns {Promise<Object>} 删除结果
   */
  async deleteUser(userId) {
    return this.删除用户数据(userId);
  }



  /**
   * 获取用户店铺数据 - 中文命名方法
   * @param {number} 用户id - 用户id
   * @returns {Promise<Object>} 用户店铺列表
   */
  async 获取用户店铺数据(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/shops`, { 用户id: 用户id }),
      `获取用户店铺 (ID: ${用户id})`
    )
  }

  /**
   * 获取用户登录历史数据 - 中文命名方法
   * @param {number} 用户id - 用户id
   * @returns {Promise<Object>} 用户登录历史
   */
  async 获取用户登录历史数据(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/login-history`, { 用户id: 用户id }),
      `获取用户登录历史 (ID: ${用户id})`
    )
  }

  /**
   * 获取用户统计信息数据 - 中文命名方法
   * @param {number} 用户id - 用户id
   * @returns {Promise<Object>} 用户统计信息
   */
  async 获取用户统计信息数据(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/statistics`, { 用户id: 用户id }),
      `获取用户统计信息 (ID: ${用户id})`
    )
  }

  /**
   * 获取用户关联店铺 - 兼容性方法
   * @param {number} userId - 用户id
   * @returns {Promise<Object>} 用户店铺列表
   */
  async getUserShops(userId) {
    return this.获取用户店铺数据(userId);
  }

  /**
   * 获取用户登录历史 - 兼容性方法
   * @param {number} userId - 用户id
   * @returns {Promise<Object>} 用户登录历史
   */
  async getUserLoginHistory(userId) {
    return this.获取用户登录历史数据(userId);
  }

  /**
   * 获取用户详细统计信息 - 兼容性方法
   * @param {number} userId - 用户id
   * @returns {Promise<Object>} 用户统计信息
   */
  async getUserStatistics(userId) {
    return this.获取用户统计信息数据(userId);
  }

  /**
   * 获取用户登录历史分页数据 - 中文命名方法
   * @param {Object} 查询参数 - 查询参数
   * @param {number} 查询参数.用户id - 用户id
   * @param {number} 查询参数.页码 - 页码
   * @param {number} 查询参数.每页数量 - 每页数量
   * @returns {Promise<Object>} 用户登录历史
   */
  async 获取用户登录历史分页数据(查询参数) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/login-history`, 查询参数),
      `获取用户登录历史分页 (ID: ${查询参数.用户id})`
    )
  }

  /**
   * 获取用户接口调用历史数据 - 中文命名方法
   * @param {Object} 查询参数 - 查询参数
   * @param {number} 查询参数.用户id - 用户id
   * @param {number} 查询参数.页码 - 页码
   * @param {number} 查询参数.每页数量 - 每页数量
   * @returns {Promise<Object>} 用户接口调用历史
   */
  async 获取用户接口调用历史数据(查询参数) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/api-history`, 查询参数),
      `获取用户接口调用历史 (ID: ${查询参数.用户id})`
    )
  }

  /**
   * 获取用户登录历史（分页）- 兼容性方法
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 用户登录历史
   */
  async getUserLoginHistoryPaged(params) {
    return this.获取用户登录历史分页数据(params);
  }

  /**
   * 获取用户接口调用历史（分页）- 兼容性方法
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 用户接口调用历史
   */
  async getUserApiHistory(params) {
    return this.获取用户接口调用历史数据(params);
  }

  // ==================== 通告管理 ====================

  /**
   * 获取通告列表数据 - 中文命名方法
   * @param {Object} 查询参数 - 查询参数
   * @param {number} 查询参数.页码 - 页码
   * @param {number} 查询参数.页面大小 - 每页大小
   * @param {string} 查询参数.类型筛选 - 类型筛选
   * @param {string} 查询参数.状态筛选 - 状态筛选
   * @returns {Promise<Object>} 通告列表
   */
  async 获取通告列表数据(查询参数 = {}) {
    const 请求参数 = {
      页码: 查询参数.页码 || 1,
      页面大小: 查询参数.页面大小 || 10,
      类型筛选: 查询参数.类型筛选 || null,
      状态筛选: 查询参数.状态筛选 || null
    }

    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements/list`, 请求参数),
      '获取通告列表'
    )
  }

  /**
   * 获取通告详情数据 - 中文命名方法
   * @param {number} 通告id - 通告id
   * @returns {Promise<Object>} 通告详情
   */
  async 获取通告详情数据(通告id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements/detail`, { 通告id }),
      `获取通告详情 (ID: ${通告id})`
    )
  }

  /**
   * 创建通告数据 - 中文命名方法
   * @param {Object} 通告数据 - 通告数据
   * @returns {Promise<Object>} 创建结果
   */
  async 创建通告数据(通告数据) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements`, 通告数据),
      '创建通告'
    )
  }

  /**
   * 更新通告数据 - 中文命名方法
   * @param {number} 通告id - 通告id
   * @param {Object} 通告数据 - 通告数据
   * @returns {Promise<Object>} 更新结果
   */
  async 更新通告数据(通告id, 通告数据) {
    const 请求数据 = {
      通告id,
      ...通告数据
    }
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements/update`, 请求数据),
      `更新通告 (ID: ${通告id})`
    )
  }

  /**
   * 删除通告数据 - 中文命名方法
   * @param {number} 通告id - 通告id
   * @returns {Promise<Object>} 删除结果
   */
  async 删除通告数据(通告id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements/delete`, { 通告id }),
      `删除通告 (ID: ${通告id})`
    )
  }

  /**
   * 获取通告列表 - 兼容性方法
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 通告列表
   */
  async getAnnouncementList(params = {}) {
    return this.获取通告列表数据(params);
  }

  /**
   * 获取通告详情 - 兼容性方法
   * @param {number} announcementId - 通告id
   * @returns {Promise<Object>} 通告详情
   */
  async getAnnouncementDetail(announcementId) {
    return this.获取通告详情数据(announcementId);
  }

  /**
   * 创建通告 - 兼容性方法
   * @param {Object} announcementData - 通告数据
   * @returns {Promise<Object>} 创建结果
   */
  async createAnnouncement(announcementData) {
    return this.创建通告数据(announcementData);
  }

  /**
   * 更新通告 - 兼容性方法
   * @param {number} announcementId - 通告id
   * @param {Object} announcementData - 通告数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateAnnouncement(announcementId, announcementData) {
    return this.更新通告数据(announcementId, announcementData);
  }

  /**
   * 删除通告 - 兼容性方法
   * @param {number} announcementId - 通告id
   * @returns {Promise<Object>} 删除结果
   */
  async deleteAnnouncement(announcementId) {
    return this.删除通告数据(announcementId);
  }

  // ==================== 系统管理 ====================

  /**
   * 获取系统状态
   * @returns {Promise<Object>} 系统状态
   */
  async getSystemStatus() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/system/status`),
      '获取系统状态'
    )
  }

  /**
   * 获取系统配置
   * @returns {Promise<Object>} 系统配置
   */
  async getSystemConfig() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/system/config`),
      '获取系统配置'
    )
  }

  /**
   * 更新系统配置
   * @param {Object} configData - 配置数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateSystemConfig(configData) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/system/config/update`, configData),
      '更新系统配置'
    )
  }

  /**
   * 测试SMTP配置
   * @param {Object} smtpConfig - SMTP配置
   * @returns {Promise<Object>} 测试结果
   */
  async testSmtpConfig(smtpConfig) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/system/smtp/test`, smtpConfig),
      '测试SMTP配置'
    )
  }

  // ==================== 日志管理 ====================

  /**
   * 获取日志文件列表
   * @returns {Promise<Object>} 日志文件列表
   */
  async getLogFilesList() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/log-files/list`),
      '获取日志文件列表'
    )
  }

  /**
   * 读取日志文件内容
   * @param {Object} params - 读取参数
   * @param {string} params.文件名 - 日志文件名
   * @param {number} params.行数 - 读取行数
   * @returns {Promise<Object>} 日志内容
   */
  async getLogFileContent(params) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/log-files/content`, params),
      `读取日志文件: ${params.文件名}`
    )
  }

  // ==================== 激活码管理 ====================

  /**
   * 获取激活码类型列表
   * @returns {Promise<Object>} 激活码类型
   */
  async getCardTypes() {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/card-types`),
      '获取激活码类型'
    )
  }

  /**
   * 生成激活码
   * @param {Object} params - 生成参数
   * @param {number} params.数量 - 生成数量
   * @param {number} params.类型id - 激活码类型id
   * @param {string} params.备注 - 激活码备注
   * @returns {Promise<Object>} 生成结果
   */
  async generateCards(params) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/generate-cards`, params),
      `生成激活码: ${params.数量}个`
    )
  }

  // ==================== 接口统计 ====================

  /**
   * 获取接口调用统计
   * @param {Object} params - 统计参数
   * @param {string} params.时间段 - 时间段
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @returns {Promise<Object>} 接口统计
   */
  async getApiStats(params = {}) {
    const defaultParams = {
      时间段: 'all',
      page: 1,
      size: 20,
      ...params
    }
    
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/api-stats`, defaultParams),
      '获取接口统计'
    )
  }

  /**
   * 获取接口调用用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 用户列表
   */
  async getApiCallUsers(params) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/api-call-users`, params),
      '获取接口调用用户'
    )
  }

  /**
   * 获取接口调用详情
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 调用详情
   */
  async getStatisticsDetail(params) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/statistics/detail`, params),
      '获取接口调用详情'
    )
  }

  // ==================== 工具方法 ====================

  /**
   * 检查响应是否成功
   * @param {Object} response - API响应
   * @returns {boolean} 是否成功
   */
  isSuccess(response) {
    return response && response.status === 100
  }

  /**
   * 获取响应数据
   * @param {Object} response - API响应
   * @returns {any} 响应数据
   */
  getData(response) {
    return this.isSuccess(response) ? response.data : null
  }

  /**
   * 获取响应消息
   * @param {Object} response - API响应
   * @returns {string} 响应消息
   */
  getMessage(response) {
    return response?.message || '操作失败'
  }

  /**
   * 批量处理多个API请求
   * @param {Array} requests - 请求数组
   * @returns {Promise<Array>} 批量结果
   */
  async batchRequest(requests) {
    try {
      console.log(`🚀 SuperAdmin 批量请求: ${requests.length}个`)
      const results = await Promise.allSettled(requests)
      
      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          console.log(`✅ 批量请求 ${index + 1} 成功`)
          return result.value
        } else {
          console.error(`❌ 批量请求 ${index + 1} 失败:`, result.reason)
          return {
            status: 500,
            message: result.reason?.message || '批量请求失败',
            data: null
          }
        }
      })
    } catch (error) {
      console.error('❌ 批量请求执行失败:', error)
      throw error
    }
  }

  // ==================== 公司审核管理 ====================
  
  /**
   * 获取待审核公司列表
   * @param {Object} params 查询参数 { 页码, 每页数量, 搜索关键词 }
   * @returns {Promise<Object>} 待审核公司列表
   */
  async getPendingCompanies(params = {}) {
    const payload = {
      页码: params.页码 || params.page || 1,
      每页数量: params.每页数量 || params.pageSize || 10,
      搜索关键词: params.搜索关键词 || params.search || ''
    };
    
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/companies/pending`, payload),
      '获取待审核公司列表'
    )
  }

  /**
   * 获取已审核公司列表
   * @param {Object} params 查询参数 { 页码, 每页数量, 搜索关键词, 审核状态 }
   * @returns {Promise<Object>} 已审核公司列表
   */
  async getReviewedCompanies(params = {}) {
    const payload = {
      页码: params.页码 || params.page || 1,
      每页数量: params.每页数量 || params.pageSize || 10,
      搜索关键词: params.搜索关键词 || params.search || '',
      审核状态: params.审核状态 || params.status || ''
    };
    
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/companies/reviewed`, payload),
      '获取已审核公司列表'
    )
  }

  /**
   * 批准公司
   * @param {Object} payload 请求数据 { 公司ID, 审核备注 }
   * @returns {Promise<Object>} 批准结果
   */
  async approveCompany(payload) {
    const requestData = {
      公司ID: payload.公司ID,
      审核备注: payload.审核备注 || ''
    };

    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/companies/approve-admin`, requestData),
      `批准公司 (ID: ${payload.公司ID})`
    )
  }

  /**
   * 拒绝公司
   * @param {Object} payload 请求数据 { 公司ID, 审核备注 }
   * @returns {Promise<Object>} 拒绝结果
   */
  async rejectCompany(payload) {
    const requestData = {
      公司ID: payload.公司ID,
      审核备注: payload.审核备注 || ''
    };

    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/companies/reject-admin`, requestData),
      `拒绝公司 (ID: ${payload.companyId || payload.公司ID})`
    )
  }

  /**
   * 获取公司详情
   * @param {string|number} companyId 公司ID
   * @returns {Promise<Object>} 公司详情
   */
  async getCompanyDetail(companyId) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/companies/detail-admin`, { 公司ID: companyId }),
      `获取公司详情 (ID: ${companyId})`
    )
  }

  /**
   * 获取用户邀约统计数据
   * 产品经理视角：分析用户邀约行为和转化效果
   */
  async getUserInvitationStats(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/invitation-stats`, { 用户id }),
      '获取用户邀约统计数据'
    )
  }

  /**
   * 获取用户权限详细信息
   * 产品经理视角：全面了解用户权限状态和使用情况
   */
  async getUserPermissionDetails(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/permissions/details`, { 用户id }),
      '获取用户权限详细信息'
    )
  }

  /**
   * 获取用户安全审计信息
   * 产品经理视角：监控用户安全行为和风险指标
   */
  async getUserSecurityAudit(用户id) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/audit/security`, { 用户id }),
      '获取用户安全审计信息'
    )
  }

  /**
   * 更新用户状态
   * 产品经理视角：用户状态管理（激活、禁用、冻结等）
   */
  async updateUserStatus(用户id, 新状态, 备注 = '') {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/update-status`, { 用户id, 新状态, 备注 }),
      '更新用户状态'
    )
  }

  /**
   * 批量操作用户
   * 产品经理视角：批量用户管理功能
   */
  async batchOperateUsers(用户id列表, 操作类型, 参数 = {}) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/batch-operation`, { 用户id列表, 操作类型, 参数 }),
      '批量操作用户'
    )
  }

  /**
   * 导出用户数据
   * 产品经理视角：数据导出功能用于分析和报告
   */
  async exportUserData(导出类型 = '基础信息', 筛选条件 = {}) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/export-data`, { 导出类型, 筛选条件 }),
      '导出用户数据'
    )
  }

  /**
   * 用户行为分析
   * 产品经理视角：深度分析用户行为模式，为产品优化提供数据支持
   */
  async analyzeUserBehavior(用户id = null, 分析维度 = '活跃度') {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/users/behavior-analysis`, { 用户id, 分析维度 }),
      '用户行为分析'
    )
  }

  // ==================== 通告管理相关接口 ====================



  /**
   * 获取通告详情
   * @param {number} announcementId - 通告id
   * @returns {Promise} 通告详情
   */
  async getAnnouncementDetail(announcementId) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements/detail`, { 通告id: announcementId }),
      '获取通告详情'
    )
  }

  /**
   * 创建通告
   * @param {Object} announcementData - 通告数据
   * @returns {Promise} 创建结果
   */
  async createAnnouncement(announcementData) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements`, announcementData),
      '创建通告'
    )
  }

  /**
   * 更新通告
   * @param {number} announcementId - 通告id
   * @param {Object} announcementData - 通告数据
   * @returns {Promise} 更新结果
   */
  async updateAnnouncement(announcementId, announcementData) {
    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/announcements/update`, {
        通告id: announcementId,
        ...announcementData
      }),
      '更新通告'
    )
  }

  /**
   * 删除通告
   * @param {number} announcementId - 通告id
   * @returns {Promise} 删除结果
   */
  async deleteAnnouncement(announcementId) {
    return this._handleRequest(
      () => apiClient.delete(`${this.baseURL}/announcements/delete/${announcementId}`),
      '删除通告'
    )
  }

  // {{ AURA-X: Add - 添加用户注册统计API，简洁高效直接对接. Approval: 寸止(ID:1721062800). }}
  /**
   * 获取用户注册统计数据
   * @param {Object} 查询参数 - 查询参数
   * @returns {Promise<Object>} 用户注册统计数据
   */
  async getUserRegistrationStats(查询参数 = {}) {
    const 请求参数 = {
      时间范围: 查询参数.时间范围 || 'today'
    }

    console.log('🔄 用户注册统计API请求参数:', 请求参数);

    return this._handleRequest(
      () => apiClient.post(`${this.baseURL}/dashboard/stats/user-registration`, 请求参数),
      '获取用户注册统计'
    )
  }
}

// 创建并导出服务实例
const superAdminService = new SuperAdminService()

export default superAdminService

// 导出类以供继承或测试
export { SuperAdminService } 