"""
轻量级资源管理器 - 最小内存开销的资源清理机制
"""

import logging
import os
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

# 配置日志
资源管理日志器 = logging.getLogger("LangChain.资源管理器")


class 轻量级资源管理器:
    """轻量级资源管理器 - 内存友好的资源清理"""

    def __init__(self):
        # 使用简单列表存储资源，避免复杂对象
        self.管理的资源: List[Dict[str, Any]] = []
        self.清理策略: Dict[str, Callable] = {}
        self._初始化清理策略()

    def _初始化清理策略(self):
        """初始化默认清理策略"""
        self.清理策略.update(
            {
                "文件": self._清理文件,
                "文档记录": self._清理文档记录,
                "向量数据": self._清理向量数据,
                "临时目录": self._清理临时目录,
            }
        )

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口 - 异常时清理所有资源"""
        if exc_type:  # 发生异常时清理所有资源
            await self._清理所有资源()
            资源管理日志器.info(f"异常清理完成，清理了 {len(self.管理的资源)} 个资源")
        else:
            # 正常完成时清空资源列表（不执行清理）
            self.管理的资源.clear()

    def 注册资源(
        self, 资源类型: str, 资源标识: Any, 清理函数: Optional[Callable] = None
    ):
        """注册需要管理的资源"""
        资源信息 = {
            "类型": 资源类型,
            "标识": 资源标识,
            "清理函数": 清理函数 or self.清理策略.get(资源类型),
            "创建时间": datetime.now(),
        }

        self.管理的资源.append(资源信息)
        资源管理日志器.debug(f"注册资源: {资源类型}:{资源标识}")

    async def _清理所有资源(self):
        """清理所有注册的资源 - 按创建时间逆序"""
        清理成功数量 = 0
        清理失败数量 = 0

        # 按创建时间逆序清理
        for 资源 in reversed(self.管理的资源):
            try:
                清理函数 = 资源["清理函数"]
                if 清理函数 and callable(清理函数):
                    try:
                        await 清理函数(资源["标识"])
                    except TypeError:
                        # 如果不是异步函数，直接调用
                        清理函数(资源["标识"])
                    清理成功数量 += 1
                    资源管理日志器.debug(f"清理成功: {资源['类型']}:{资源['标识']}")
                else:
                    资源管理日志器.warning(f"无清理函数: {资源['类型']}:{资源['标识']}")

            except Exception as e:
                清理失败数量 += 1
                资源管理日志器.error(
                    f"清理资源失败: {资源['类型']}:{资源['标识']}, 错误: {e}"
                )

        资源管理日志器.info(f"资源清理完成: 成功 {清理成功数量}, 失败 {清理失败数量}")
        self.管理的资源.clear()

    # ==================== 默认清理策略 ====================

    async def _清理文件(self, 文件路径: str):
        """清理文件"""
        try:
            if os.path.exists(文件路径):
                os.remove(文件路径)
                资源管理日志器.debug(f"文件已删除: {文件路径}")
        except Exception as e:
            资源管理日志器.error(f"删除文件失败: {文件路径}, 错误: {e}")
            raise

    async def _清理文档记录(self, 文档id: int):
        """清理文档记录"""
        try:
            from 数据.LangChain_数据层 import LangChain数据层实例

            await LangChain数据层实例.通过ID删除知识库文档(文档id)
            资源管理日志器.debug(f"文档记录已删除: {文档id}")
        except Exception as e:
            资源管理日志器.error(f"删除文档记录失败: {文档id}, 错误: {e}")
            raise

    async def _清理向量数据(self, 向量ID列表: List[int]):
        """清理向量数据 - 支持批量清理和部分失败处理"""
        try:
            from 数据.LangChain_数据层 import LangChain数据层实例

            成功数量 = 0
            失败数量 = 0

            for 向量ID in 向量ID列表:
                try:
                    await LangChain数据层实例.删除文档向量(向量ID)
                    成功数量 += 1
                except Exception as item_error:
                    失败数量 += 1
                    资源管理日志器.warning(
                        f"删除单个向量失败: ID={向量ID}, 错误: {item_error}"
                    )

            资源管理日志器.debug(
                f"向量数据清理完成: 成功 {成功数量}, 失败 {失败数量}, 总计 {len(向量ID列表)} 个"
            )

            # 如果有部分失败，记录但不抛出异常
            if 失败数量 > 0:
                资源管理日志器.warning(
                    f"向量数据部分清理失败: {失败数量}/{len(向量ID列表)}"
                )

        except Exception as e:
            资源管理日志器.error(f"删除向量数据失败: {向量ID列表}, 错误: {e}")
            raise

    async def _清理临时目录(self, 目录路径: str):
        """清理临时目录"""
        try:
            import shutil

            if os.path.exists(目录路径):
                shutil.rmtree(目录路径)
                资源管理日志器.debug(f"临时目录已删除: {目录路径}")
        except Exception as e:
            资源管理日志器.error(f"删除临时目录失败: {目录路径}, 错误: {e}")
            raise

    def 获取资源统计(self) -> Dict[str, int]:
        """获取资源统计信息"""
        统计信息 = {}
        for 资源 in self.管理的资源:
            资源类型 = 资源["类型"]
            统计信息[资源类型] = 统计信息.get(资源类型, 0) + 1
        return 统计信息


# 创建全局实例（轻量级，内存开销小）
轻量级资源管理器实例 = 轻量级资源管理器()
