<template>
  <a-card 
    :title="account.微信号" 
    size="small" 
    class="wechat-account-card"
    :class="{ 'account-inactive': account.状态值 !== 1 }"
  >
    <!-- 卡片头部额外操作 -->
    <template #extra>
      <a-dropdown>
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <a-menu-item key="edit">
              <EditOutlined />
              编辑账号
            </a-menu-item>
            <a-menu-item key="refresh">
              <ReloadOutlined />
              刷新数据
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="unbind" danger>
              <DisconnectOutlined />
              解绑账号
            </a-menu-item>
          </a-menu>
        </template>
        <a-button type="text" size="small">
          <MoreOutlined />
        </a-button>
      </a-dropdown>
    </template>

    <!-- 账号基本信息 -->
    <div class="account-info">
      <div class="account-avatar">
        <a-avatar 
          :size="48" 
          :style="{ backgroundColor: getAvatarColor(account.微信号) }"
        >
          <template #icon><WechatOutlined /></template>
        </a-avatar>
        
        <!-- 状态指示器 -->
        <a-badge 
          :status="getStatusBadge(account.状态值)" 
          class="status-badge"
        />
      </div>

      <div class="account-details">
        <div class="account-name">
          <span class="nickname">{{ account.微信号 }}</span>
          <a-tag 
            :color="account.状态值 === 1 ? 'success' : 'default'"
            size="small"
          >
            {{ account.状态 }}
          </a-tag>
        </div>
        
        <div class="account-meta">
          <span class="meta-item">
            <UserOutlined />
            {{ account.好友数量 || 0 }} 位好友
          </span>
          <span class="meta-item">
            <ClockCircleOutlined />
            {{ formatTime(account.绑定时间) }}
          </span>
        </div>

        <div v-if="account.备注" class="account-remark">
          <CommentOutlined />
          {{ account.备注 }}
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="account-stats">
      <div class="stat-item">
        <div class="stat-value">{{ statistics.今日新增好友 || 0 }}</div>
        <div class="stat-label">今日新增</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ statistics.活跃对接数 || 0 }}</div>
        <div class="stat-label">进行中</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ statistics.成功对接数 || 0 }}</div>
        <div class="stat-label">已成交</div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="account-actions">
      <a-button 
        type="primary" 
        size="small" 
        @click="viewFriends"
        :loading="loading"
      >
        <TeamOutlined />
        查看好友
      </a-button>
      
      <a-button 
        size="small" 
        @click="viewProgress"
      >
        <BarChartOutlined />
        对接进度
      </a-button>
      

    </div>
  </a-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  EditOutlined,
  ReloadOutlined,
  DisconnectOutlined,
  MoreOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CommentOutlined,
  TeamOutlined,
  BarChartOutlined,
  PlusOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'
import { wechatService } from '@/services/friend'

// 定义组件属性
const props = defineProps({
  // 微信账号数据
  account: {
    type: Object,
    required: true
  },
  // 统计数据
  statistics: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits([
  'edit',      // 编辑账号
  'refresh',   // 刷新数据
  'unbind',    // 解绑账号
  'viewFriends',  // 查看好友
  'viewProgress'  // 查看进度
])

// 响应式数据
const loading = ref(false)

// 计算属性
const formattedAccount = computed(() => {
  return wechatService.formatAccountData(props.account)
})

// 方法定义

/**
 * 处理下拉菜单点击
 * @param {Object} menuInfo - 菜单信息
 */
const handleMenuClick = ({ key }) => {
  switch (key) {
    case 'edit':
      emit('edit', formattedAccount.value)
      break
    case 'refresh':
      emit('refresh', formattedAccount.value)
      break
    case 'unbind':
      handleUnbind()
      break
  }
}

/**
 * 处理解绑操作
 */
const handleUnbind = () => {
  emit('unbind', formattedAccount.value)
}

/**
 * 查看好友列表
 */
const viewFriends = () => {
  loading.value = true
  try {
    emit('viewFriends', formattedAccount.value)
  } finally {
    loading.value = false
  }
}

/**
 * 查看对接进度
 */
const viewProgress = () => {
  emit('viewProgress', formattedAccount.value)
}



/**
 * 获取头像颜色
 * @param {string} wechatId - 微信号
 * @returns {string} 颜色值
 */
const getAvatarColor = (wechatId) => {
  const colors = [
    '#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068',
    '#2db7f5', '#108ee9', '#722ed1', '#eb2f96', '#52c41a'
  ]
  
  if (!wechatId) return colors[0]
  
  const hash = wechatId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0)
    return a & a
  }, 0)
  
  return colors[Math.abs(hash) % colors.length]
}

/**
 * 获取状态徽章类型
 * @param {number} status - 状态值
 * @returns {string} 徽章类型
 */
const getStatusBadge = (status) => {
  const statusMap = {
    1: 'success',    // 正常
    0: 'default',    // 未激活
    '-1': 'error'    // 已停用
  }
  return statusMap[status] || 'default'
}

/**
 * 获取状态文本
 * @param {number} status - 状态值
 * @returns {string} 状态文本
 */
const getStatusText = (status) => {
  const statusMap = {
    1: '正常',
    0: '未激活',
    '-1': '已停用'
  }
  return statusMap[status] || '未知'
}

/**
 * 格式化时间显示
 * @param {string} time - 时间字符串
 * @returns {string} 格式化后的时间
 */
const formatTime = (time) => {
  if (!time) return '未知'
  
  const date = new Date(time)
  const now = new Date()
  const diffTime = now - date
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)}周前`
  } else {
    return date.toLocaleDateString()
  }
}
</script>

<style scoped>
.wechat-account-card {
  margin-bottom: 16px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.wechat-account-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border-color: #d9d9d9;
  background-color: #fafafa;
}

.account-inactive {
  opacity: 0.7;
}

.account-info {
  display: flex;
  margin-bottom: 16px;
}

.account-avatar {
  position: relative;
  margin-right: 12px;
}

.status-badge {
  position: absolute;
  bottom: 0;
  right: 0;
}

.account-details {
  flex: 1;
  min-width: 0;
}

.account-name {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.nickname {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.account-remark {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  font-style: italic;
}

.account-stats {
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

.account-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.account-actions .ant-btn {
  flex: 1;
  min-width: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .account-avatar {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .account-meta {
    align-items: center;
  }
  
  .account-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .account-actions {
    flex-direction: column;
  }
}
</style> 