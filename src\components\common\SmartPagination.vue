<template>
  <div class="smart-pagination">
    <!-- 分页控件 -->
    <div class="pagination-controls">
      <!-- 上一页按钮 -->
      <a-button 
        :disabled="currentPage <= 1 || loading"
        @click="handlePrevPage"
        class="page-btn"
      >
        <left-outlined />
        上一页
      </a-button>

      <!-- 页码按钮组 -->
      <div class="page-numbers">
        <template v-for="page in visiblePages" :key="page">
          <a-button
            v-if="page !== '...'"
            :type="page === currentPage ? 'primary' : 'default'"
            :disabled="loading"
            @click="handlePageChange(page)"
            class="page-number-btn"
          >
            {{ page }}
          </a-button>
          <span v-else class="page-ellipsis">...</span>
        </template>
      </div>

      <!-- 下一页按钮 -->
      <a-button 
        :disabled="!hasNextPage || loading"
        @click="handleNextPage"
        class="page-btn"
      >
        下一页
        <right-outlined />
      </a-button>
    </div>

    <!-- 分页信息和翻页提示 -->
    <div class="pagination-info">
      <div class="current-info">
        第 {{ startItem }}-{{ endItem }} 项
        <span v-if="paginationWarning.show" class="pagination-warning">
          <a-tag :color="paginationWarning.color">
            {{ paginationWarning.message }}
          </a-tag>
        </span>
      </div>
      
      <!-- 页面大小选择器 -->
      <div class="page-size-selector">
        <span>每页</span>
        <a-select 
          v-model:value="currentPageSize" 
          @change="handlePageSizeChange"
          :disabled="loading"
          size="small"
          style="width: 80px; margin: 0 8px;"
        >
          <a-select-option :value="20">20</a-select-option>
          <a-select-option :value="50">50</a-select-option>
          <a-select-option :value="100">100</a-select-option>
        </a-select>
        <span>条</span>
      </div>
    </div>

    <!-- 翻页限制提示 -->
    <div v-if="paginationLimit.show" class="pagination-limit-notice">
      <a-alert
        :type="paginationLimit.type"
        :message="paginationLimit.message"
        :description="paginationLimit.description"
        show-icon
        closable
        @close="handleLimitNoticeClose"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'

// Props定义
const props = defineProps({
  current: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 20
  },
  hasNextPage: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  // 翻页限制信息（每个用户每日限制翻页10页）
  paginationLimitInfo: {
    type: Object,
    default: () => ({
      used_count: 0,
      limit_count: 10,  // 修改为每日10页限制
      remaining_count: 10,
      warning_level: 'none',
      message: ''
    })
  }
})

// Emits定义
const emit = defineEmits(['change', 'showSizeChange'])

// 响应式数据
const currentPage = ref(props.current)
const currentPageSize = ref(props.pageSize)

// 监听props变化
watch(() => props.current, (newVal) => {
  currentPage.value = newVal
})

watch(() => props.pageSize, (newVal) => {
  currentPageSize.value = newVal
})

// 计算当前显示的数据范围
const startItem = computed(() => {
  return (currentPage.value - 1) * currentPageSize.value + 1
})

const endItem = computed(() => {
  return currentPage.value * currentPageSize.value
})

// 计算可见的页码
const visiblePages = computed(() => {
  const pages = []
  const current = currentPage.value
  
  // 总是显示第1页
  if (current > 3) {
    pages.push(1)
    if (current > 4) {
      pages.push('...')
    }
  }
  
  // 显示当前页附近的页码
  const start = Math.max(1, current - 2)
  const end = current + 2
  
  for (let i = start; i <= end; i++) {
    if (!pages.includes(i)) {
      pages.push(i)
    }
  }
  
  return pages
})

// 翻页警告信息
const paginationWarning = computed(() => {
  const info = props.paginationLimitInfo
  
  if (info.warning_level === 'severe') {
    return {
      show: true,
      color: 'red',
      message: `翻页次数即将达到上限 (${info.used_count}/${info.limit_count})`
    }
  } else if (info.warning_level === 'warning') {
    return {
      show: true,
      color: 'orange',
      message: `翻页次数较多 (${info.used_count}/${info.limit_count})`
    }
  }
  
  return { show: false }
})

// 翻页限制提示
const paginationLimit = computed(() => {
  const info = props.paginationLimitInfo
  
  if (!info.can_paginate) {
    return {
      show: true,
      type: 'error',
      message: '翻页次数已达今日上限',
      description: '您今日的翻页次数已达到100页上限，请明日再试或使用搜索功能精确查找内容。'
    }
  } else if (info.warning_level === 'severe') {
    return {
      show: true,
      type: 'warning',
      message: '翻页次数即将达到上限',
      description: `您今日还可翻页 ${info.remaining_count} 次，建议使用搜索功能提高查找效率。`
    }
  }
  
  return { show: false }
})

// 事件处理
const handlePageChange = (page) => {
  if (page !== currentPage.value && !props.loading) {
    currentPage.value = page
    emit('change', page, currentPageSize.value)
  }
}

const handlePrevPage = () => {
  if (currentPage.value > 1) {
    handlePageChange(currentPage.value - 1)
  }
}

const handleNextPage = () => {
  if (props.hasNextPage) {
    handlePageChange(currentPage.value + 1)
  }
}

const handlePageSizeChange = (size) => {
  currentPageSize.value = size
  currentPage.value = 1 // 重置到第一页
  emit('showSizeChange', 1, size)
}

const handleLimitNoticeClose = () => {
  // 可以在这里添加关闭提示的逻辑
}
</script>

<style scoped>
.smart-pagination {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-number-btn {
  min-width: 32px;
  height: 32px;
}

.page-ellipsis {
  padding: 0 8px;
  color: #999;
}

.pagination-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.current-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-warning {
  margin-left: 8px;
}

.page-size-selector {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.pagination-limit-notice {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-controls {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .pagination-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .page-size-selector {
    align-self: flex-end;
  }
}
</style>
