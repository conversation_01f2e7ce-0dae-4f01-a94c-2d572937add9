# 服务/会员服务.py
from datetime import datetime
from typing import Any, Dict, List

from fastapi import HTTPException

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器, 错误日志器
from 状态 import 状态


class 会员服务类:
    """
    会员服务类 - 处理会员套餐和用户会员信息管理
    """

    def __init__(self):
        """初始化会员服务"""
        pass

    async def 获取套餐列表(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的会员套餐列表

        Returns:
            会员套餐列表
        """
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接池. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
            sql = """
            SELECT id, 名称, 每月费用, 每年费用, 每月算力点
            FROM 会员表
            ORDER BY 每月费用 ASC
            """
            结果 = await 异步连接池实例.执行查询(sql)

            套餐列表 = []
            for 套餐 in 结果:
                # 计算年费优惠比例
                每月费用 = float(套餐["每月费用"])
                每年费用 = float(套餐["每年费用"])
                年费优惠比例 = 0

                if 每月费用 > 0:
                    年费优惠比例 = round(
                        (1 - (每年费用 / (每月费用 * 12))) * 100, 1
                    )

                套餐信息 = {
                    "套餐ID": 套餐["id"],
                    "套餐名称": 套餐["名称"],
                    "套餐描述": self._获取套餐描述(套餐["名称"]),
                    "月价格": 每月费用,
                    "年价格": 每年费用,
                    "每月算力点": 套餐["每月算力点"],
                    "年费优惠比例": 年费优惠比例,
                    "推荐": self._判断是否推荐套餐(套餐["名称"]),
                    "特色功能": self._获取套餐特色功能(套餐["名称"]),
                }
                套餐列表.append(套餐信息)

            应用日志器.info(f"获取套餐列表成功，共 {len(套餐列表)} 个套餐")
            return 套餐列表

        except Exception as e:
            错误日志器.error(f"获取套餐列表失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"获取套餐列表失败: {str(e)}",
                },
            )

    async def 获取用户会员信息(self, 用户id: int) -> Dict[str, Any]:
        """
        获取用户当前的会员信息 - 支持多种会员同时显示

        Args:
            用户id: 用户id

        Returns:
            用户会员信息，包含所有有效会员和最新的已过期会员
        """
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接池. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
            # 查询用户所有有效的会员信息
            有效会员sql = """
            SELECT um.id, um.用户id, um.会员id, um.开通时间, um.到期时间,
                   m.名称 as 会员名称, m.每月费用, m.每年费用, m.每月算力点,
                   '有效' as 会员状态
            FROM 用户_会员_关联表 um
            LEFT JOIN 会员表 m ON um.会员id = m.id
            WHERE um.用户id = $1 AND um.到期时间 > NOW()
            ORDER BY um.到期时间 DESC
            """
            有效会员记录列表 = await 异步连接池实例.执行查询(有效会员sql, (用户id,))

            # 如果没有有效会员，查询最新的已过期会员
            最新已过期会员 = None
            if not 有效会员记录列表:
                已过期会员sql = """
                SELECT um.id, um.用户id, um.会员id, um.开通时间, um.到期时间,
                       m.名称 as 会员名称, m.每月费用, m.每年费用, m.每月算力点,
                       '已过期' as 会员状态
                FROM 用户_会员_关联表 um
                LEFT JOIN 会员表 m ON um.会员id = m.id
                WHERE um.用户id = $1 AND um.到期时间 <= NOW()
                ORDER BY um.到期时间 DESC
                LIMIT 1
                """
                已过期会员结果 = await 异步连接池实例.执行查询(已过期会员sql, (用户id,))
                最新已过期会员 = 已过期会员结果[0] if 已过期会员结果 else None

            # 处理会员记录
            会员列表 = []
            处理记录列表 = (
                有效会员记录列表
                if 有效会员记录列表
                else ([最新已过期会员] if 最新已过期会员 else [])
            )

            for 会员记录 in 处理记录列表:
                if 会员记录:
                    # 处理到期时间
                    到期时间_原始 = 会员记录["到期时间"]
                    if isinstance(到期时间_原始, str):
                        到期时间 = datetime.strptime(
                            到期时间_原始, "%Y-%m-%d %H:%M:%S"
                        )
                    else:
                        到期时间 = 到期时间_原始  # 已经是datetime对象

                    # 计算剩余天数，向上取整确保未过期时不为0
                    时间差 = 到期时间 - datetime.now()
                    if 时间差.total_seconds() > 0:
                        # 未过期：至少显示1天，或实际剩余天数
                        剩余天数 = max(
                            1, 时间差.days + (1 if 时间差.seconds > 0 else 0)
                        )
                    else:
                        # 已过期
                        剩余天数 = 0
                    会员状态 = 会员记录["会员状态"]

                    会员信息 = {
                        "用户id": 会员记录["用户id"],
                        "会员id": 会员记录["会员id"],
                        "会员名称": 会员记录["会员名称"],
                        "开通时间": 会员记录["开通时间"],
                        "到期时间": 会员记录["到期时间"],
                        "剩余天数": max(0, 剩余天数),
                        "每月费用": float(会员记录["每月费用"])
                        if 会员记录["每月费用"]
                        else 0,
                        "每年费用": float(会员记录["每年费用"])
                        if 会员记录["每年费用"]
                        else 0,
                        "会员状态": 会员状态,
                    }
                    会员列表.append(会员信息)

            if 会员列表:
                # 确定整体会员状态
                有有效会员 = any(
                    会员["会员状态"] == "有效" for 会员 in 会员列表
                )
                整体状态 = "有效" if 有有效会员 else "已过期"

                return {
                    "会员列表": 会员列表,
                    "会员状态": 整体状态,
                    "是否会员": 有有效会员,
                    "会员数量": len(
                        [
                            会员
                            for 会员 in 会员列表
                            if 会员["会员状态"] == "有效"
                        ]
                    ),
                }
            else:
                # 用户从未开通过会员
                return {
                    "会员列表": [],
                    "会员状态": "未开通",
                    "是否会员": False,
                    "会员数量": 0,
                }

        except Exception as e:
            错误日志器.error(f"获取用户会员信息失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"获取会员信息失败: {str(e)}",
                },
            )

    async def 获取用户会员历史(self, 用户id: int) -> List[Dict[str, Any]]:
        """
        获取用户的会员开通历史

        Args:
            用户id: 用户id

        Returns:
            会员历史记录列表
        """
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接池. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
            sql = """
            SELECT um.开通时间, um.到期时间, m.名称 as 会员名称,
                   CASE
                       WHEN um.到期时间 > NOW() THEN '有效'
                       ELSE '已过期'
                   END as 状态
            FROM 用户_会员_关联表 um
            LEFT JOIN 会员表 m ON um.会员id = m.id
            WHERE um.用户id = $1
            ORDER BY um.开通时间 DESC
            """
            结果 = await 异步连接池实例.执行查询(sql, (用户id,))

            历史记录 = []
            for 记录 in 结果:
                历史记录.append(
                    {
                        "会员名称": 记录["会员名称"],
                        "开通时间": 记录["开通时间"],
                        "到期时间": 记录["到期时间"],
                        "状态": 记录["状态"],
                    }
                )

            return 历史记录

        except Exception as e:
            错误日志器.error(f"获取用户会员历史失败: {str(e)}")
            return []

    async def 检查会员权限(self, 用户id: int, 所需权限: str) -> bool:
        """
        检查用户是否具有指定权限

        Args:
            用户id: 用户id
            所需权限: 所需的权限标识

        Returns:
            是否具有权限
        """
        try:
            # 获取用户当前会员信息
            会员信息 = await self.获取用户会员信息(用户id)

            if not 会员信息.get("是否会员", False):
                return False

            # 这里可以根据具体的权限系统进行扩展
            # 暂时简单返回会员用户都有权限
            return True

        except Exception as e:
            错误日志器.error(f"检查会员权限失败: {str(e)}")
            return False

    async def 获取会员统计(self) -> Dict[str, Any]:
        """
        获取会员相关统计信息

        Returns:
            会员统计数据
        """
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接池. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
            # 统计有效会员数量
            sql_active = """
            SELECT COUNT(*) as 有效会员数
            FROM 用户_会员_关联表
            WHERE 到期时间 > NOW()
            """
            有效会员结果 = await 异步连接池实例.执行查询(sql_active)
            有效会员数据 = 有效会员结果[0] if 有效会员结果 else None

            # 统计总会员数
            sql_total = "SELECT COUNT(DISTINCT 用户id) as 总会员数 FROM 用户_会员_关联表"
            总会员结果 = await 异步连接池实例.执行查询(sql_total)
            总会员数据 = 总会员结果[0] if 总会员结果 else None

            # 统计本月新增会员
            sql_monthly = """
            SELECT COUNT(*) as 本月新增
            FROM 用户_会员_关联表
            WHERE TO_CHAR(TO_TIMESTAMP(开通时间, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM') = TO_CHAR(NOW(), 'YYYY-MM')
            """
            月度结果 = await 异步连接池实例.执行查询(sql_monthly)
            月度数据 = 月度结果[0] if 月度结果 else None

            # 统计各套餐占比
            sql_plans = """
            SELECT m.名称, COUNT(*) as 用户数
            FROM 用户_会员_关联表 um
            LEFT JOIN 会员表 m ON um.会员id = m.id
            WHERE TO_TIMESTAMP(um.到期时间, 'YYYY-MM-DD HH24:MI:SS') > NOW()
            GROUP BY m.名称
            ORDER BY 用户数 DESC
            """
            套餐分布 = await 异步连接池实例.执行查询(sql_plans)

            return {
                "有效会员数": 有效会员数据["有效会员数"] if 有效会员数据 else 0,
                "总会员数": 总会员数据["总会员数"] if 总会员数据 else 0,
                "本月新增": 月度数据["本月新增"] if 月度数据 else 0,
                "套餐分布": [
                    {"套餐名称": 记录["名称"], "用户数": 记录["用户数"]}
                    for 记录 in 套餐分布
                ],
            }

        except Exception as e:
            错误日志器.error(f"获取会员统计失败: {str(e)}")
            return {"有效会员数": 0, "总会员数": 0, "本月新增": 0, "套餐分布": []}

    # ========================= 私有方法 =========================

    def _判断是否推荐套餐(self, 套餐名称: str) -> bool:
        """判断套餐是否为推荐套餐"""
        推荐套餐 = ["AI业务助理", "AI成交经理"]
        return 套餐名称 in 推荐套餐

    def _获取套餐描述(self, 套餐名称: str) -> str:
        """获取套餐描述"""
        描述映射 = {
            "个人版会员": "适合个人用户，基础功能体验",
            "AI业务助理": "专业商务助手，提升工作效率",
            "AI成交经理": "智能成交系统，提升转化率",
            "AI决策总监": "全方位决策支持，企业级服务",
        }
        return 描述映射.get(套餐名称, "高品质会员服务")

    def _获取套餐特色功能(self, 套餐名称: str) -> List[str]:
        """获取套餐的特色功能列表"""
        功能映射 = {
            "个人版会员": ["基础CRM功能", "客户管理", "基础报表", "邮件支持"],
            "AI业务助理": [
                "AI智能分析",
                "自动化跟进",
                "智能推荐",
                "高级报表",
                "优先客服支持",
            ],
            "AI成交经理": [
                "全功能AI助手",
                "智能销售预测",
                "自动化营销",
                "深度数据分析",
                "专属客户经理",
                "API接口权限",
            ],
            "AI决策总监": [
                "企业级AI决策",
                "高级业务分析",
                "自定义工作流",
                "无限API调用",
                "7x24小时支持",
                "专属解决方案",
                "私有化部署",
            ],
        }
        return 功能映射.get(套餐名称, ["基础功能"])

    async def _获取推荐套餐(self) -> Dict[str, Any]:
        """获取推荐套餐信息"""
        try:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法错误，改为直接使用连接池. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL asyncpg不需要cursor，直接在连接上操作 }}
            # 推荐AI业务助理套餐
            sql = """
            SELECT id, 名称, 每月费用, 每年费用
            FROM 会员表
            WHERE 名称 = 'AI业务助理'
            LIMIT 1
            """
            推荐套餐结果 = await 异步连接池实例.执行查询(sql)
            推荐套餐 = 推荐套餐结果[0] if 推荐套餐结果 else None

            if 推荐套餐:
                return {
                    "会员id": 推荐套餐["id"],
                    "套餐名称": 推荐套餐["名称"],
                    "月费": float(推荐套餐["每月费用"]),
                    "年费": float(推荐套餐["每年费用"]),
                }
            else:
                return {}
        except Exception as e:
            错误日志器.error(f"获取推荐套餐失败: {str(e)}")
            return {}
