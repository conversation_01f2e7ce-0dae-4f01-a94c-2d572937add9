"""
管理数据操作模块 - PostgreSQL版本
基于asyncpg实现的系统管理相关数据库操作

功能：
1. 系统配置管理
2. 操作日志管理
3. 权限管理
4. 数据统计和监控
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器

# ==================== 系统配置管理 ====================


async def 获取系统配置(配置键: str) -> Optional[str]:
    """
    获取系统配置值（注意：系统配置表不存在，返回默认值）

    Args:
        配置键: 配置键名

    Returns:
        配置值，不存在返回None
    """
    try:
        # 由于系统配置表不存在，返回一些常用的默认配置
        默认配置 = {
            "系统名称": "灵邀系统",
            "系统版本": "1.0.0",
            "站点名称": "灵邀管理后台",
            "最大文件上传大小": "10MB",
            "会话超时时间": "3600",
        }

        配置值 = 默认配置.get(配置键)
        if 配置值:
            数据库日志器.debug(f"获取系统配置（默认值）: {配置键}={配置值}")
        else:
            数据库日志器.debug(f"系统配置不存在: {配置键}")

        return 配置值

    except Exception as e:
        错误日志器.error(f"获取系统配置异常: 配置键={配置键}, 错误={str(e)}")
        return None


async def 设置系统配置(
    配置键: str,
    配置值: str,
    配置描述: Optional[str] = None,
    操作人ID: Optional[int] = None,
) -> bool:
    """
    设置系统配置值（注意：系统配置表不存在，仅记录到日志）

    Args:
        配置键: 配置键名
        配置值: 配置值
        配置描述: 配置描述
        操作人ID: 操作人ID

    Returns:
        是否设置成功
    """
    try:
        # 由于系统配置表不存在，仅记录到日志
        数据库日志器.info(f"设置系统配置（仅记录）: {配置键}={配置值}, 操作人ID={操作人ID}, 描述={配置描述}")
        return True

    except Exception as e:
        错误日志器.error(f"设置系统配置异常: 配置键={配置键}, 错误={str(e)}")
        return False


async def 获取所有系统配置(
    页码: int = 1, 每页数量: int = 50
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取所有系统配置（注意：系统配置表不存在，返回默认配置）

    Args:
        页码: 页码（从1开始）
        每页数量: 每页记录数

    Returns:
        (配置列表, 总记录数)
    """
    try:
        # 由于系统配置表不存在，返回一些默认配置
        默认配置列表 = [
            {
                "配置键": "系统名称",
                "配置值": "灵邀系统",
                "配置描述": "系统名称",
                "操作人ID": None,
                "创建时间": datetime.now(),
                "更新时间": datetime.now(),
                "操作人姓名": "系统默认"
            },
            {
                "配置键": "系统版本",
                "配置值": "1.0.0",
                "配置描述": "系统版本号",
                "操作人ID": None,
                "创建时间": datetime.now(),
                "更新时间": datetime.now(),
                "操作人姓名": "系统默认"
            }
        ]

        数据库日志器.debug(f"获取所有系统配置（默认值），总数: {len(默认配置列表)}")
        return 默认配置列表, len(默认配置列表)

    except Exception as e:
        错误日志器.error(f"获取所有系统配置异常: {str(e)}")
        return [], 0


async def 删除系统配置(配置键: str, 操作人ID: int) -> bool:
    """
    删除系统配置（注意：系统配置表不存在，仅记录到日志）

    Args:
        配置键: 配置键名
        操作人ID: 操作人ID

    Returns:
        是否删除成功
    """
    try:
        # 由于系统配置表不存在，仅记录到日志
        数据库日志器.info(f"删除系统配置（仅记录）: {配置键}, 操作人ID={操作人ID}")
        return True

    except Exception as e:
        错误日志器.error(f"删除系统配置异常: 配置键={配置键}, 错误={str(e)}")
        return False


# ==================== 操作日志管理 ====================


async def 记录操作日志(
    操作人ID: int,
    操作类型: str,
    操作对象: str,
    操作描述: str,
    操作结果: str = "成功",
    详细信息: Optional[Dict[str, Any]] = None,
    IP地址: Optional[str] = None,
) -> bool:
    """
    记录操作日志（注意：操作日志表不存在，此函数仅记录到应用日志）

    Args:
        操作人ID: 操作人ID
        操作类型: 操作类型（增删改查等）
        操作对象: 操作对象（表名、模块名等）
        操作描述: 操作描述
        操作结果: 操作结果（成功/失败）
        详细信息: 详细信息字典
        IP地址: 操作IP地址

    Returns:
        是否记录成功
    """
    try:
        # 由于操作日志表不存在，改为记录到应用日志
        日志信息 = f"操作日志 - 操作人ID: {操作人ID}, 类型: {操作类型}, 对象: {操作对象}, 描述: {操作描述}, 结果: {操作结果}"
        if IP地址:
            日志信息 += f", IP: {IP地址}"
        if 详细信息:
            日志信息 += f", 详细信息: {详细信息}"

        数据库日志器.info(日志信息)
        return True

    except Exception as e:
        错误日志器.error(f"记录操作日志异常: {操作类型}-{操作对象}, 错误={str(e)}")
        return False


async def 查询操作日志(
    页码: int = 1,
    每页数量: int = 50,
    操作人ID筛选: Optional[int] = None,
    操作类型筛选: Optional[str] = None,
    操作对象筛选: Optional[str] = None,
    开始时间: Optional[datetime] = None,
    结束时间: Optional[datetime] = None,
) -> tuple[List[Dict[str, Any]], int]:
    """
    查询操作日志

    Args:
        页码: 页码（从1开始）
        每页数量: 每页记录数
        操作人ID筛选: 按操作人筛选
        操作类型筛选: 按操作类型筛选
        操作对象筛选: 按操作对象筛选
        开始时间: 开始时间
        结束时间: 结束时间

    Returns:
        (日志列表, 总记录数)
    """
    try:
        where_条件 = []
        参数列表 = []
        参数索引 = 1

        if 操作人ID筛选:
            where_条件.append(f"ol.操作人ID = $1")
            参数列表.append(操作人ID筛选)
            参数索引 += 1

        if 操作类型筛选:
            where_条件.append(f"ol.操作类型 = $1")
            参数列表.append(操作类型筛选)
            参数索引 += 1

        if 操作对象筛选:
            where_条件.append(f"ol.操作对象 ILIKE $1")
            参数列表.append(f"%{操作对象筛选}%")
            参数索引 += 1

        if 开始时间:
            where_条件.append(f"ol.操作时间 >= $1")
            参数列表.append(开始时间)
            参数索引 += 1

        if 结束时间:
            where_条件.append(f"ol.操作时间 <= $1")
            参数列表.append(结束时间)
            参数索引 += 1

        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""

        # 由于操作日志表不存在，返回空结果
        数据库日志器.warning("操作日志表不存在，返回空结果")
        return [], 0

    except Exception as e:
        错误日志器.error(f"查询操作日志异常: {str(e)}")
        return [], 0


# ==================== 权限管理 ====================


async def 检查用户权限(
    用户id: int, 权限代码: str, 资源ID: Optional[int] = None
) -> bool:
    """
    检查用户是否具有指定权限

    Args:
        用户id: 用户id
        权限代码: 权限代码
        资源ID: 资源ID（可选）

    Returns:
        是否具有权限
    """
    try:
        # 检查用户角色权限
        角色权限SQL = """
        SELECT COUNT(*) as count
        FROM 用户角色表 ur
        INNER JOIN 角色权限表 rp ON ur.角色ID = rp.角色ID
        INNER JOIN 权限表 p ON rp.权限ID = p.id
        WHERE ur.用户id = $1 AND p.权限代码 = $2
        AND ur.状态 = '正常' AND rp.状态 = '正常' AND p.状态 = '启用'
        """

        角色权限结果 = await 异步连接池实例.执行查询(角色权限SQL, (用户id, 权限代码))
        角色权限数 = 角色权限结果[0]["count"] if 角色权限结果 else 0

        if 角色权限数 > 0:
            数据库日志器.debug(f"用户{用户id}通过角色具有权限{权限代码}")
            return True

        # 检查用户直接权限
        直接权限SQL = """
        SELECT COUNT(*) as count
        FROM 用户权限表 up
        INNER JOIN 权限表 p ON up.权限ID = p.id
        WHERE up.用户id = $1 AND p.权限代码 = $2
        AND up.状态 = '正常' AND p.状态 = '启用'
        """

        if 资源ID:
            直接权限SQL += " AND (up.资源ID IS NULL OR up.资源ID = $3)"
            参数 = (用户id, 权限代码, 资源ID)
        else:
            参数 = (用户id, 权限代码)

        直接权限结果 = await 异步连接池实例.执行查询(直接权限SQL, 参数)
        直接权限数 = 直接权限结果[0]["count"] if 直接权限结果 else 0

        有权限 = 直接权限数 > 0

        if 有权限:
            数据库日志器.debug(f"用户{用户id}直接具有权限{权限代码}")
        else:
            数据库日志器.debug(f"用户{用户id}不具有权限{权限代码}")

        return 有权限

    except Exception as e:
        错误日志器.error(
            f"检查用户权限异常: 用户id={用户id}, 权限={权限代码}, 错误={str(e)}"
        )
        return False


async def 获取用户权限列表(用户id: int) -> List[Dict[str, Any]]:
    """
    获取用户所有权限列表

    Args:
        用户id: 用户id

    Returns:
        权限列表
    """
    try:
        # 获取角色权限
        角色权限SQL = """
        SELECT DISTINCT p.id, p.权限代码, p.权限名称, p.权限描述, '角色权限' as 权限来源,
               r.角色名称 as 来源详情
        FROM 用户角色表 ur
        INNER JOIN 角色权限表 rp ON ur.角色ID = rp.角色ID
        INNER JOIN 权限表 p ON rp.权限ID = p.id
        INNER JOIN 角色表 r ON ur.角色ID = r.id
        WHERE ur.用户id = $1
        AND ur.状态 = '正常' AND rp.状态 = '正常' AND p.状态 = '启用'
        """

        角色权限 = await 异步连接池实例.执行查询(角色权限SQL, (用户id,))

        # 获取直接权限
        直接权限SQL = """
        SELECT p.id, p.权限代码, p.权限名称, p.权限描述, '直接权限' as 权限来源,
               CASE WHEN up.资源ID IS NOT NULL THEN CONCAT('资源ID:', up.资源ID) ELSE '全局' END as 来源详情
        FROM 用户权限表 up
        INNER JOIN 权限表 p ON up.权限ID = p.id
        WHERE up.用户id = $1
        AND up.状态 = '正常' AND p.状态 = '启用'
        """

        直接权限 = await 异步连接池实例.执行查询(直接权限SQL, (用户id,))

        # 合并权限列表
        所有权限 = (角色权限 or []) + (直接权限 or [])

        数据库日志器.debug(
            f"获取用户权限列表成功: 用户id={用户id}, 权限数={len(所有权限)}"
        )
        return 所有权限

    except Exception as e:
        错误日志器.error(f"获取用户权限列表异常: 用户id={用户id}, 错误={str(e)}")
        return []


# ==================== 数据统计和监控 ====================


async def 获取系统统计信息() -> Dict[str, Any]:
    """
    获取系统统计信息

    Returns:
        统计信息字典
    """
    try:
        统计信息 = {}

        # 用户统计
        用户统计SQL = """
        SELECT
            COUNT(*) as 总用户数,
            COUNT(CASE WHEN 状态 = '正常' THEN 1 END) as 正常用户数,
            COUNT(CASE WHEN 状态 = '禁用' THEN 1 END) as 禁用用户数,
            COUNT(CASE WHEN DATE(创建时间) = CURRENT_DATE THEN 1 END) as 今日新增用户
        FROM 用户表
        """

        用户统计 = await 异步连接池实例.执行查询(用户统计SQL)
        统计信息["用户统计"] = 用户统计[0] if 用户统计 else {}

        # 团队统计
        团队统计SQL = """
        SELECT
            COUNT(*) as 总团队数,
            COUNT(CASE WHEN 团队状态 = '正常' THEN 1 END) as 正常团队数,
            SUM(当前成员数) as 总成员数,
            AVG(当前成员数) as 平均成员数
        FROM 团队表
        WHERE 团队状态 != '已删除'
        """

        团队统计 = await 异步连接池实例.执行查询(团队统计SQL)
        统计信息["团队统计"] = 团队统计[0] if 团队统计 else {}

        # 操作日志统计（表不存在，返回默认值）
        统计信息["操作统计"] = {
            "总操作数": 0,
            "今日操作数": 0,
            "近7日操作数": 0,
            "活跃用户数": 0
        }

        # 系统配置统计（表不存在，返回默认值）
        统计信息["配置统计"] = {
            "总配置数": 2,
            "启用配置数": 2,
            "禁用配置数": 0
        }

        数据库日志器.debug("获取系统统计信息成功")
        return 统计信息

    except Exception as e:
        错误日志器.error(f"获取系统统计信息异常: {str(e)}")
        return {}


async def 获取数据库性能指标() -> Dict[str, Any]:
    """
    获取数据库性能指标

    Returns:
        性能指标字典
    """
    try:
        性能指标 = {}

        # 连接池状态
        连接池状态 = {
            "最大连接数": 异步连接池实例._pool.get_max_size()
            if hasattr(异步连接池实例, "_pool")
            else 0,
            "当前连接数": 异步连接池实例._pool.get_size()
            if hasattr(异步连接池实例, "_pool")
            else 0,
            "空闲连接数": 异步连接池实例._pool.get_idle_size()
            if hasattr(异步连接池实例, "_pool")
            else 0,
        }
        性能指标["连接池状态"] = 连接池状态

        # 数据库大小统计
        数据库大小SQL = """
        SELECT
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as 表大小,
            pg_total_relation_size(schemaname||'.'||tablename) as 表大小字节
        FROM pg_tables
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10
        """

        表大小统计 = await 异步连接池实例.执行查询(数据库大小SQL)
        性能指标["表大小统计"] = 表大小统计 or []

        数据库日志器.debug("获取数据库性能指标成功")
        return 性能指标

    except Exception as e:
        错误日志器.error(f"获取数据库性能指标异常: {str(e)}")
        return {}
