import { ref } from 'vue'
import { message } from 'ant-design-vue'
import adminLangchainService from '@/services/adminLangchainService'
import knowledgeBaseService from '@/services/knowledgeBaseService'

/**
 * 智能体API调用逻辑
 */
export function useAgentAPI() {
  
  // 基础数据
  const 对话模型列表 = ref([])
  const 嵌入模型列表 = ref([])
  const 可用知识库列表 = ref([])
  const 可用工具列表 = ref([])
  
  // 加载状态
  const 加载状态 = ref({
    模型列表: false,
    知识库列表: false,
    工具列表: false,
    智能体数据: false
  })

  // 加载对话模型列表
  const 加载对话模型列表 = async () => {
    try {
      加载状态.value.模型列表 = true
      const 响应 = await adminLangchainService.getModelList('chat')

      if (响应.success) {
        对话模型列表.value = 响应.data.map(模型 => ({
          label: 模型.显示名称 || 模型.模型名称,
          value: 模型.id,
          ...模型
        }))
      } else {
        message.error(响应.error)
      }
    } catch (error) {
      console.error('加载对话模型列表失败:', error)
      message.error('加载对话模型列表失败')
    } finally {
      加载状态.value.模型列表 = false
    }
  }

  // 加载嵌入模型列表
  const 加载嵌入模型列表 = async () => {
    try {
      const 响应 = await adminLangchainService.getModelList('embedding')

      if (响应.success) {
        嵌入模型列表.value = 响应.data.map(模型 => ({
          label: 模型.显示名称 || 模型.模型名称,
          value: 模型.id,
          ...模型
        }))
      } else {
        message.error(响应.error)
      }
    } catch (error) {
      console.error('加载嵌入模型列表失败:', error)
      message.error('加载嵌入模型列表失败')
    }
  }

  // 加载知识库列表
  const 加载知识库列表 = async () => {
    try {
      加载状态.value.知识库列表 = true
      const 响应 = await knowledgeBaseService.getKnowledgeBaseList()

      if (响应.success) {
        可用知识库列表.value = (响应.data.知识库列表 || []).map(知识库 => ({
          label: 知识库.知识库名称,
          value: 知识库.id,
          ...知识库
        }))
      } else {
        message.error(响应.error)
      }
    } catch (error) {
      console.error('加载知识库列表失败:', error)
      message.error('加载知识库列表失败')
    } finally {
      加载状态.value.知识库列表 = false
    }
  }

  // 加载可用工具列表
  const 加载可用工具列表 = async () => {
    try {
      加载状态.value.工具列表 = true
      const 响应 = await adminLangchainService.获取工具列表()

      if (响应.success) {
        可用工具列表.value = 响应.data || []
      } else {
        message.error(响应.message)
      }
    } catch (error) {
      console.error('加载工具列表失败:', error)
      message.error('加载工具列表失败')
    } finally {
      加载状态.value.工具列表 = false
    }
  }

  // 加载智能体数据
  const 加载智能体数据 = async (智能体id) => {
    try {
      加载状态.value.智能体数据 = true
      const 智能体数据 = await adminLangchainService.getAgentDetail(智能体id)
      return 智能体数据
    } catch (error) {
      console.error('加载智能体数据失败:', error)
      message.error(error.message || '加载智能体数据失败')
      throw error
    } finally {
      加载状态.value.智能体数据 = false
    }
  }

  // 加载智能体关联知识库
  const 加载智能体关联知识库 = async (智能体id) => {
    try {
      加载状态.value.知识库列表 = true
      const 知识库数据 = await adminLangchainService.getAgentKnowledgeBases(智能体id)
      return 知识库数据
    } catch (error) {
      console.error('加载智能体关联知识库失败:', error)
      message.error(error.message || '加载智能体关联知识库失败')
      return { 知识库列表: [], 总数量: 0 }
    } finally {
      加载状态.value.知识库列表 = false
    }
  }

  // 加载智能体关联工具详情
  const 加载智能体关联工具详情 = async (智能体id) => {
    try {
      加载状态.value.工具列表 = true
      const 工具数据 = await adminLangchainService.getAgentToolsDetail(智能体id)
      return 工具数据
    } catch (error) {
      console.error('加载智能体关联工具详情失败:', error)
      message.error(error.message || '加载智能体关联工具详情失败')
      return { 工具列表: [], 总数量: 0 }
    } finally {
      加载状态.value.工具列表 = false
    }
  }

  // 保存智能体
  const 保存智能体 = async (智能体数据, 是否编辑模式 = false, 智能体id = null) => {
    try {
      // 直接使用智能体数据，确保数据类型正确
      const 后端数据 = {
        ...智能体数据,

        // 确保知识库列表是数字数组
        知识库列表: (智能体数据.知识库列表 || []).map(id => {
          const numId = typeof id === 'number' ? id : parseInt(id)
          return isNaN(numId) ? null : numId
        }).filter(id => id !== null),

        // 确保工具列表是字符串数组
        工具列表: (智能体数据.工具列表 || []).filter(tool => typeof tool === 'string' && tool.trim())
      }

      console.log('💾 保存智能体数据映射完成')

      let 响应

      if (是否编辑模式 && 智能体id) {
        响应 = await adminLangchainService.updateAgent(智能体id, 后端数据)
      } else {
        响应 = await adminLangchainService.createAgent(后端数据)
      }

      message.success(是否编辑模式 ? '智能体更新成功' : '智能体创建成功')
      return 响应
    } catch (error) {
      console.error('保存智能体失败:', error)
      message.error(error.message || '保存智能体失败')
      throw error
    }
  }

  // 测试智能体对话
  const 测试智能体对话 = async (智能体id, 消息, 选项 = {}) => {
    try {
      const 响应 = await adminLangchainService.chatWithAgent(智能体id, {
        消息: 消息,
        保留历史: 选项.保留历史 ?? true,
        调试模式: 选项.调试模式 ?? false
      })

      if (响应.success) {
        return 响应.data
      } else {
        throw new Error(响应.error || '对话测试失败')
      }
    } catch (error) {
      console.error('测试智能体对话失败:', error)
      message.error(error.message || '对话测试失败')
      throw error
    }
  }

  // 测试RAG检索
  const 测试RAG检索 = async (智能体id, 查询文本, 检索参数 = {}) => {
    try {
      const 响应 = await adminLangchainService.testAgentRetrieval(智能体id, {
        查询文本: 查询文本,
        检索参数: 检索参数
      })

      if (响应.success) {
        return 响应.data
      } else {
        throw new Error(响应.error || 'RAG检索测试失败')
      }
    } catch (error) {
      console.error('测试RAG检索失败:', error)
      message.error(error.message || 'RAG检索测试失败')
      throw error
    }
  }

  // 获取智能体工具配置
  const 获取智能体工具配置 = async (智能体id) => {
    try {
      const 响应 = await adminLangchainService.获取智能体工具关联(智能体id)

      if (响应.success) {
        return 响应.data
      } else {
        throw new Error(响应.message || '获取工具配置失败')
      }
    } catch (error) {
      console.error('获取智能体工具配置失败:', error)
      return { 工具列表: [], 总数量: 0 }
    }
  }

  // 更新智能体工具配置
  const 更新智能体工具配置 = async (智能体id, 工具配置) => {
    try {
      const 响应 = await adminLangchainService.保存智能体工具关联(智能体id, 工具配置)

      if (响应.success) {
        message.success('工具配置更新成功')
        return 响应.data
      } else {
        throw new Error(响应.message || '更新工具配置失败')
      }
    } catch (error) {
      console.error('更新智能体工具配置失败:', error)
      message.error(error.message || '更新工具配置失败')
      throw error
    }
  }

  // 初始化基础数据
  const 初始化基础数据 = async () => {
    try {
      await Promise.all([
        加载对话模型列表(),
        加载嵌入模型列表(),
        加载知识库列表(),
        加载可用工具列表()
      ])
    } catch (error) {
      console.error('初始化基础数据失败:', error)
      message.error('初始化基础数据失败，请刷新页面重试')
      throw error
    }
  }

  return {
    // 数据
    对话模型列表,
    嵌入模型列表,
    可用知识库列表,
    可用工具列表,
    加载状态,
    
    // 方法
    加载对话模型列表,
    加载嵌入模型列表,
    加载知识库列表,
    加载可用工具列表,
    加载智能体数据,
    加载智能体关联知识库,
    加载智能体关联工具详情,
    保存智能体,
    测试智能体对话,
    测试RAG检索,
    获取智能体工具配置,
    更新智能体工具配置,
    初始化基础数据
  }
}
