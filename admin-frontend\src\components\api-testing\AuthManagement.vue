<template>
  <div class="auth-management">
    <a-card title="API认证管理" :bordered="false">
      <!-- 当前认证状态 -->
      <div class="auth-status">
        <div class="status-header">
          <h3>认证状态</h3>
          <a-tag :color="hasToken ? 'success' : 'error'">{{ hasToken ? '已认证' : '未认证' }}</a-tag>
        </div>
        
        <div v-if="hasToken" class="token-display">
          <a-input-password
            v-model:value="displayToken"
            readonly
            placeholder="当前Token"
          >
            <template #addonAfter>
              <a-tooltip title="复制Token">
                <copy-outlined @click="copyToken" />
              </a-tooltip>
            </template>
          </a-input-password>
          
          <div class="token-expiry" v-if="tokenExpiry">
            <a-alert type="info" show-icon>
              <template #message>Token过期时间: {{ tokenExpiry }}</template>
            </a-alert>
          </div>
        </div>
        
        <div v-else class="no-token-notice">
          <a-alert type="warning" show-icon message="无认证Token，需要接口认证时将无法自动添加" />
        </div>
      </div>
      
      <!-- 认证方式配置 -->
      <a-divider />
      <div class="auth-settings">
        <h3>认证设置</h3>
        
        <a-form layout="vertical">
          <a-form-item label="认证方式">
            <a-radio-group v-model:value="authPreference" @change="handleAuthPreferenceChange">
              <a-radio-button value="header">仅使用Header</a-radio-button>
              <a-radio-button value="cookie">仅使用Cookie</a-radio-button>
              <a-radio-button value="both">两者都使用</a-radio-button>
            </a-radio-group>
            <div class="preference-hint">
              {{ preferenceExplanation }}
            </div>
          </a-form-item>
          
          <a-form-item label="从登录响应自动保存Token" v-if="hasLoginApi">
            <a-switch 
              v-model:checked="autoSaveToken" 
              checked-children="开启" 
              un-checked-children="关闭" 
            />
          </a-form-item>
          
          <a-form-item label="始终添加认证头">
            <a-switch 
              v-model:checked="forceAuth" 
              checked-children="开启" 
              un-checked-children="关闭" 
            />
            <div class="preference-hint">
              开启后，所有接口都会自动添加认证头，无论OpenAPI文档是否标记需要认证
            </div>
          </a-form-item>
        </a-form>
      </div>
      
      <!-- Token操作区域 -->
      <a-divider />
      <div class="token-operations">
        <h3>Token操作</h3>
        
        <div class="token-import">
          <a-input-password
            v-model:value="tokenToImport"
            placeholder="输入Token以手动导入"
            allow-clear
          />
          <div class="import-actions">
            <a-button type="primary" @click="handleImportToken" :disabled="!tokenToImport">
              导入Token
            </a-button>
            <a-button 
              type="primary" 
              danger 
              @click="handleClearToken" 
              :disabled="!hasToken"
            >
              清除Token
            </a-button>
          </div>
        </div>
        
        <a-collapse class="token-decoder" v-if="hasToken">
          <a-collapse-panel key="1" header="Token解析 (JWT)">
            <div v-if="tokenInfo">
              <pre>{{ tokenInfo }}</pre>
            </div>
            <div v-else>
              <a-alert type="info" message="非JWT格式Token或无法解析" />
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import {computed, onMounted, ref, watch} from 'vue';
import {useApiTestingModule} from '@/store/apiTestingModule';
import {
  Alert as AAlert,
  Button as AButton,
  Card as ACard,
  Collapse as ACollapse,
  CollapsePanel as ACollapsePanel,
  Divider as ADivider,
  Form as AForm,
  FormItem as AFormItem,
  InputPassword as AInputPassword,
  message,
  RadioButton as ARadioButton,
  RadioGroup as ARadioGroup,
  Switch as ASwitch,
  Tag as ATag,
  Tooltip as ATooltip
} from 'ant-design-vue';
import {CopyOutlined} from '@ant-design/icons-vue';

const store = useApiTestingModule();

// 状态
const hasToken = computed(() => !!store.mainAppToken);
const displayToken = computed(() => store.mainAppToken || '');
const tokenToImport = ref('');
const autoSaveToken = ref(localStorage.getItem('apiTestingAutoSaveToken') !== 'false');
const authPreference = computed(() => store.authPreference);
const forceAuth = ref(store.forceAuth);
const hasLoginApi = computed(() => store.apiEndpoints.some(api => 
  api.path.includes('login') || api.path.includes('登录')
));

// Token解析信息
const tokenInfo = computed(() => {
  if (!hasToken.value) return null;
  
  try {
    const token = store.mainAppToken;
    const parts = token.split('.');
    if (parts.length !== 3) return null; // 非JWT格式
    
    const base64Url = parts[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    return JSON.stringify(JSON.parse(jsonPayload), null, 2);
  } catch (e) {
    console.error('解析Token失败:', e);
    return null;
  }
});

// Token过期时间
const tokenExpiry = computed(() => {
  if (!hasToken.value) return null;
  
  try {
    const token = store.mainAppToken;
    const parts = token.split('.');
    if (parts.length !== 3) return null; // 非JWT格式
    
    const base64Url = parts[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    const payload = JSON.parse(jsonPayload);
    if (payload.exp) {
      // JWT的exp是Unix时间戳（秒）
      const expDate = new Date(payload.exp * 1000);
      return expDate.toLocaleString();
    }
    
    return null;
  } catch (e) {
    console.error('解析Token过期时间失败:', e);
    return null;
  }
});

// 认证设置解释
const preferenceExplanation = computed(() => {
  switch (authPreference.value) {
    case 'header':
      return '仅使用Authorization头部添加Bearer Token。最常见的API认证方式。';
    case 'cookie':
      return '仅使用Cookie中的token字段。适用于支持Cookie认证的API。';
    case 'both':
      return '同时使用Authorization头部和Cookie。最大兼容性，支持多种认证方式。';
    default:
      return '';
  }
});

// 方法
const handleAuthPreferenceChange = (e) => {
  store.setAuthPreference(e.target.value);
};

const handleImportToken = () => {
  if (!tokenToImport.value) {
    message.error('请输入有效的Token');
    return;
  }
  
  const success = store.importToken(tokenToImport.value);
  if (success) {
    message.success('Token导入成功');
    tokenToImport.value = '';
  } else {
    message.error('Token导入失败');
  }
};

const handleClearToken = () => {
  store.clearToken();
  message.success('Token已清除');
};

const copyToken = () => {
  if (!hasToken.value) return;
  
  navigator.clipboard.writeText(store.mainAppToken)
    .then(() => {
      message.success('Token已复制到剪贴板');
    })
    .catch((err) => {
      console.error('复制失败:', err);
      message.error('复制失败');
    });
};

// 自动保存Token设置变更
watch(autoSaveToken, (newVal) => {
  localStorage.setItem('apiTestingAutoSaveToken', newVal);
});

// 强制认证设置变更
watch(forceAuth, (newVal) => {
  store.setForceAuth(newVal);
});

// 组件挂载
onMounted(() => {
  // 可以在这里加载Token
  const savedToken = localStorage.getItem('apiTestingToken');
  if (savedToken && !store.mainAppToken) {
    store.importToken(savedToken);
  }
});
</script>

<style scoped>
.auth-management {
  padding: 8px;
}

.auth-status, .auth-settings, .token-operations {
  margin-bottom: 16px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.token-display {
  margin-bottom: 16px;
}

.token-expiry {
  margin-top: 8px;
}

.preference-hint {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 13px;
}

.token-import {
  margin-bottom: 16px;
}

.import-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.token-decoder {
  margin-top: 16px;
}

.token-decoder pre {
  white-space: pre-wrap;
  word-break: break-all;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style> 