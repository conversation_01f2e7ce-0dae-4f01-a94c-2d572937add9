import { message } from 'ant-design-vue'
import axios from 'axios'

/**
 * 统一API客户端工厂
 * 提供可配置的API客户端创建功能，消除重复配置
 * 
 * 功能特性：
 * - 统一的请求/响应拦截器
 * - 可配置的认证、日志、重试机制
 * - 标准化的错误处理
 * - 环境自适应配置
 */

/**
 * 获取Cookie值的辅助函数
 * @param {string} name - Cookie名称
 * @returns {string|null} Cookie值
 */
const 获取Cookie值 = (name) => {
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) return parts.pop().split(';').shift()
  return null
}

/**
 * 处理Token错误的辅助函数
 * @param {string} errorMessage - 错误消息
 */
const 处理Token错误 = (errorMessage) => {
  console.warn('Token错误:', errorMessage)

  // 清除本地存储的认证信息
  localStorage.removeItem('crm_token')
  localStorage.removeItem('crm_user_info')
  localStorage.removeItem('crm_permissions')
  sessionStorage.removeItem('crm_session_active')

  // 清除Cookie中的token
  document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;'

  // 检查当前页面路径
  const 当前页面路径 = window.location.pathname
  const 是邀请页面 = 当前页面路径.startsWith('/invitation/')
  const 是认证页面 = ['/login', '/register', '/forgot-password'].includes(当前页面路径)

  // 只在非邀请页面且非认证页面时才自动跳转到登录页
  if (!是邀请页面 && !是认证页面) {
    console.log('Token失效，跳转到登录页面')
    window.location.href = '/login'
  }
}

/**
 * 根据环境自动选择API基础URL
 * 使用统一配置管理
 * @returns {string} API基础URL
 */
const 获取API基础URL = () => {
  // 直接根据环境判断，避免循环依赖
  if (import.meta.env.MODE === 'development') {
    console.log('🔧 使用开发环境API地址: http://localhost:8000')
    return 'http://localhost:8000'
  } else {
    // 生产环境强制使用正确的后端API地址
    const prodUrl = 'https://invite.limob.cn'
    console.log('🔧 使用生产环境API地址:', prodUrl)
    console.log('🔧 当前模式:', import.meta.env.MODE)
    console.log('🔧 环境变量API地址:', import.meta.env.VITE_API_BASE_URL)
    return prodUrl
  }
}

/**
 * 统一响应处理函数
 * 标准化业务状态码，将 status = 1、0、100 统一为 status = 100
 * @param {Object} 响应数据 - 后端返回的原始响应数据
 * @returns {Object} 标准化后的响应数据
 */
const 标准化响应数据 = (响应数据) => {
  if (!响应数据 || typeof 响应数据 !== 'object') {
    return 响应数据
  }

  // 创建响应数据的副本，避免修改原始数据
  const 标准化响应 = { ...响应数据 }

  // 统一业务状态码：将 1、0、100 都标准化为 100（成功状态）
  if ([1, 0, 100].includes(响应数据.status)) {
    标准化响应.status = 100
  }

  return 标准化响应
}

/**
 * 重试配置
 */
const 重试配置 = {
  最大重试次数: 2,
  重试延迟: 1000, // 毫秒
  可重试错误类型: ['ECONNABORTED', 'NETWORK_ERROR', 'TIMEOUT']
}

/**
 * 重试处理函数
 * @param {Function} 请求函数 - 请求函数
 * @param {Object} 请求配置 - 请求配置
 * @returns {Promise} 请求结果
 */
const 执行重试请求 = async (请求函数, 请求配置) => {
  let 最后错误 = null

  for (let 尝试次数 = 0; 尝试次数 <= 重试配置.最大重试次数; 尝试次数++) {
    try {
      const 结果 = await 请求函数(请求配置)

      // 如果是重试成功，记录日志
      if (尝试次数 > 0) {
        console.log(`🔄 重试成功: ${请求配置.method?.toUpperCase()} ${请求配置.url} (第${尝试次数}次重试)`)
      }

      return 结果
    } catch (error) {
      最后错误 = error

      // 判断是否应该重试
      const 是业务错误 = error.response && error.response.status === 200
      const 应该重试 = (
        尝试次数 < 重试配置.最大重试次数 &&
        !是业务错误 && // 业务错误不重试
        (
          error.code === 'ECONNABORTED' || // 超时
          error.code === 'NETWORK_ERROR' || // 网络错误
          !error.response // 请求未到达服务器
        )
      )

      if (应该重试) {
        console.warn(`⚠️ 请求失败，准备重试: ${请求配置.method?.toUpperCase()} ${请求配置.url} (第${尝试次数 + 1}次重试，${重试配置.重试延迟}ms后重试)`)
        await new Promise(resolve => setTimeout(resolve, 重试配置.重试延迟))
        continue
      } else {
        break
      }
    }
  }

  // 所有重试都失败了，抛出最后一个错误
  throw 最后错误
}

/**
 * 创建API客户端工厂函数
 * @param {Object} options - 配置选项
 * @param {string} options.baseURL - 基础URL，默认使用环境配置
 * @param {number} options.timeout - 超时时间，默认30秒
 * @param {boolean} options.enableAuth - 是否启用认证，默认true
 * @param {boolean} options.enableLogging - 是否启用日志，默认true
 * @param {boolean} options.enableStandardization - 是否启用响应标准化，默认true
 * @param {boolean} options.enableRetry - 是否启用重试机制，默认true
 * @param {string} options.clientType - 客户端类型，用于日志标识
 * @returns {Object} 配置好的axios实例
 */
export const 创建API客户端 = (options = {}) => {
  const {
    baseURL = 获取API基础URL(),
    timeout = 30000,
    enableAuth = true,
    enableLogging = true,
    enableStandardization = true,
    enableRetry = true,
    clientType = 'main'
  } = options

  // 创建axios实例
  const instance = axios.create({
    baseURL,
    timeout,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 在开发环境下输出当前API配置
  if (import.meta.env.DEV && enableLogging) {
    console.log(`🔧 ${clientType}客户端配置:`, {
      模式: import.meta.env.MODE,
      基础URL: baseURL,
      超时时间: timeout,
      启用认证: enableAuth,
      启用日志: enableLogging,
      启用标准化: enableStandardization,
      启用重试: enableRetry
    })
  }

  // 请求拦截器
  if (enableAuth || enableLogging) {
    instance.interceptors.request.use(
      (config) => {
        // 认证处理
        if (enableAuth) {
          const tokenFromStorage = localStorage.getItem('crm_token')
          const tokenFromCookie = 获取Cookie值('token')
          const token = tokenFromStorage || tokenFromCookie

          if (enableLogging && import.meta.env.DEV) {
            console.log(`🔑 ${clientType}客户端Token状态:`, {
              url: config.url,
              method: config.method?.toUpperCase(),
              hasToken: !!token
            })
          }

          if (token) {
            config.headers.Authorization = `Bearer ${token}`
          }
        }

        // 为特定接口设置更长的超时时间
        const longTimeoutPaths = ['/kol/edit-my-talent', '/team/talent', '/user/upload', '/store/orders/import']
        if (longTimeoutPaths.some(path => config.url?.includes(path))) {
          config.timeout = 60000
          if (enableLogging) {
            console.log(`⏱️ 设置长超时: ${config.url} (60秒)`)
          }
        }

        // 记录请求信息
        if (enableLogging && import.meta.env.DEV) {
          console.log(`📤 ${clientType}客户端请求: ${config.method?.toUpperCase()} ${config.url}`)
        }

        return config
      },
      (error) => {
        if (enableLogging) {
          console.error(`❌ ${clientType}客户端请求拦截器错误:`, error)
        }
        return Promise.reject(error)
      }
    )
  }

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      if (enableLogging) {
        console.log(`✅ ${clientType}客户端响应成功: ${response.config.method?.toUpperCase()} ${response.config.url}`)
      }

      // 获取原始响应数据
      const 原始响应数据 = response.data

      // 统一业务状态码处理
      const 处理后响应数据 = enableStandardization ? 标准化响应数据(原始响应数据) : 原始响应数据

      // 处理token过期或无效的情况
      if (enableAuth && (原始响应数据.status === 502 || 原始响应数据.status === 503)) {
        处理Token错误(原始响应数据.message || '认证失败')
        return Promise.reject(new Error(原始响应数据.message || '认证失败'))
      }

      return 处理后响应数据
    },
    (error) => {
      // 只对非业务错误记录日志
      if (enableLogging && !(error.response?.status === 400)) {
        console.error(`❌ ${clientType}客户端响应错误:`, error)
      }

      // 处理HTTP状态码错误
      if (error.response) {
        const { status, data } = error.response

        switch (status) {
          case 401:
            if (enableAuth) {
              处理Token错误('登录已过期')
              message.error('登录已过期，请重新登录')
            }
            break
          case 403:
            message.error('权限不足，无法访问')
            break
          case 404:
            message.error('请求的资源不存在')
            break
          case 500:
            message.error('服务器内部错误，请稍后重试')
            break
          case 400:
            // 对于400错误，不自动显示提示，让组件自己处理
            // 特别是激活相关的业务错误
            break
          default:
            message.error(data?.message || `请求失败 (${status})`)
        }
      } else if (error.code === 'ECONNABORTED') {
        const isLongOperation = error.config?.timeout > 30000
        const isImportOperation = error.config?.url?.includes('/import')
        if (isImportOperation) {
          // 对于导入操作的超时，提供更友好的提示
          message.warning('导入请求超时，但文件可能仍在后台处理中，请稍后查看导入记录')
        } else if (isLongOperation) {
          message.error('操作超时，数据可能仍在处理中，请稍后刷新页面查看结果')
        } else {
          message.error('请求超时，请检查网络连接或稍后重试')
        }
      } else {
        message.error('网络连接异常，请检查网络设置')
      }

      return Promise.reject(error)
    }
  )

  // 如果启用重试机制，包装请求方法
  if (enableRetry) {
    const 原始POST = instance.post.bind(instance)
    const 原始GET = instance.get.bind(instance)

    instance.post = async (url, data, config = {}) => {
      return 执行重试请求(
        (cfg) => 原始POST(url, data, cfg),
        { ...config, url, method: 'POST' }
      )
    }

    instance.get = async (url, config = {}) => {
      return 执行重试请求(
        (cfg) => 原始GET(url, cfg),
        { ...config, url, method: 'GET' }
      )
    }
  }

  return instance
}

// 创建主应用API客户端
export const 主API客户端 = 创建API客户端({
  enableAuth: true,
  enableLogging: true,
  enableStandardization: true,
  enableRetry: true,
  clientType: 'main'
})

// 创建测试专用API客户端工厂函数
export const 创建测试API客户端 = (baseURL) => {
  return 创建API客户端({
    baseURL,
    enableAuth: false,
    enableLogging: true,
    enableStandardization: false,
    enableRetry: false,
    clientType: 'testing'
  })
}

// 导出辅助函数
export { 处理Token错误, 标准化响应数据, 获取API基础URL, 获取Cookie值 }

// 默认导出主API客户端
export default 主API客户端
