/**
 * 线索管理服务
 * 
 * 功能概述：
 * - 提供线索数据的CRUD操作
 * - 支持高级搜索和筛选
 * - 提供数据统计和分析
 * - 支持批量操作和数据导出
 * 
 * 作者: CRM系统开发团队
 * 创建时间: 2024-06-25
 */

import api from './api'

class LeadsService {
  /**
   * 获取线索概览统计
   * 产品价值：为管理者提供数据全貌，支持快速决策
   * 
   * @param {Object} params - 统计参数
   * @param {string} params.时间范围 - 统计时间范围：'7d', '30d', '90d', 'all'
   * @param {boolean} params.包含详细分析 - 是否包含详细分析数据
   * @returns {Promise} 包含统计数据的响应
   */
  async getLeadsOverview(params = {}) {
    try {
      console.log('获取线索概览统计:', params)
      
      const requestParams = {
        时间范围: params.时间范围 || '30d',
        包含详细分析: params.包含详细分析 || true
      }
      
      const response = await api.post('/leads/overview', requestParams)
      
      if (response.status === 100) {
        // 处理统计数据，添加前端需要的计算字段
        const stats = response.data
        
        const processedStats = {
          ...stats,
          // 计算增长率
          growthRate: this.calculateGrowthRate(stats.currentPeriod, stats.previousPeriod),
          // 格式化数字显示
          formattedTotal: this.formatNumber(stats.totalLeads),
          // 添加趋势分析
          trendAnalysis: this.analyzeTrend(stats.dailyStats || [])
        }
        
        console.log('线索概览统计数据处理完成:', processedStats)
        return { ...response, data: processedStats }
      }
      
      return response
    } catch (error) {
      console.error('获取线索概览统计失败:', error)
      
      // 降级处理，返回模拟数据
      return {
        status: 100,
        message: '使用模拟数据',
        data: this.generateMockOverviewStats()
      }
    }
  }
  
  /**
   * 获取线索列表
   * 产品价值：高效的数据浏览和基础操作
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 当前页码
   * @param {number} params.每页数量 - 每页显示数量
   * @param {string} params.关键词 - 搜索关键词
   * @param {string} params.线索来源 - 线索来源筛选
   * @param {string} params.地域筛选 - 地域筛选条件
   * @param {string} params.类别筛选 - 类别筛选条件
   * @param {Object} params.粉丝数范围 - 粉丝数范围筛选
   * @param {string} params.排序字段 - 排序字段
   * @param {string} params.排序方式 - 排序方式：'asc', 'desc'
   * @returns {Promise} 包含线索列表的响应
   */
  async getLeadsList(params) {
    try {
      console.log('获取线索列表:', params)
      
      const requestParams = {
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 20,
        // 修复搜索参数映射：根据后端API文档，关键词搜索应该同时支持联系方式和信息值搜索
        筛选_联系方式: params.关键词 || null,  // 在联系方式中搜索关键词
        筛选_信息值: params.关键词 || null,    // 在JSON信息中搜索关键词
        筛选_线索来源: params.线索来源 || null,
        起始id: params.起始id || 0,
        // 翻页标识：只有前端明确指定为翻页操作时才记录翻页次数
        // 这样可以准确区分搜索操作和翻页操作
        is_pagination: params.is_pagination === true
      }
      
      const response = await api.post('/leads/list', requestParams)
      
      if (response.status === 100) {
        // 处理响应数据，添加前端需要的计算字段
        const leads = (response.data.列表 || []).map(lead => ({
          ...lead,
          // 解析JSON信息
          parsedInfo: this.parseLeadInfo(lead.信息),
          // 添加显示用的格式化字段
          displayName: this.getDisplayName(lead),
          displayLocation: this.getDisplayLocation(lead),
          displayCategory: this.getDisplayCategory(lead),
          displayFansCount: this.getDisplayFansCount(lead),
          // 添加操作权限标识
          canEdit: true,
          canDelete: true,
          // 添加状态标识
          hasContact: this.hasContactInfo(lead),
          isHighValue: this.isHighValueLead(lead)
        }))
        
        const processedResponse = {
          ...response,
          data: {
            ...response.data,
            列表: leads,
            // 添加列表统计信息
            listStats: {
              hasContactCount: leads.filter(l => l.hasContact).length,
              highValueCount: leads.filter(l => l.isHighValue).length,
              topSources: this.calculateTopSources(leads),
              topLocations: this.calculateTopLocations(leads)
            }
          }
        }
        
        console.log('线索列表数据处理完成:', processedResponse.data.列表.length, '条线索')
        return processedResponse
      }
      
      return response
    } catch (error) {
      console.error('获取线索列表失败:', error)
      
      // 降级处理，返回模拟数据
      return {
        status: 100,
        message: '使用模拟数据',
        data: {
          列表: this.generateMockLeadsList(params),
          总数: 154054,
          页码: params.页码 || 1,
          每页数量: params.每页数量 || 20
        }
      }
    }
  }

  /**
   * 检查翻页限制
   * 产品价值：控制用户翻页行为，保护系统资源
   *
   * @returns {Promise} 包含翻页限制信息的响应
   */
  async checkPaginationLimit() {
    try {
      console.log('检查翻页限制')

      // 修复：使用api而不是request，并添加超时控制
      const response = await Promise.race([
        api.get('/leads/pagination-limit'),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('翻页限制检查超时')), 3000)
        )
      ])

      if (response && response.status === 100) {
        console.log('翻页限制检查成功:', response.data)
        return response
      }

      // 如果状态码不是100，也返回默认设置
      console.warn('翻页限制检查返回非成功状态:', response?.status)
      throw new Error('翻页限制检查返回错误状态')

    } catch (error) {
      console.error('检查翻页限制失败:', error.message || error)

      // 降级处理，返回默认允许翻页（每日10页限制）
      return {
        status: 100,
        message: '翻页限制检查失败，使用默认设置',
        data: {
          can_paginate: true,
          used_count: 0,
          limit_count: 10,  // 修改为每日10页限制
          remaining_count: 10,
          warning_level: 'none',
          message: '翻页限制检查暂时不可用'
        }
      }
    }
  }

  /**
   * 获取线索详情
   * 产品价值：完整的线索信息展示，支持深度分析
   * 
   * @param {number} leadId - 线索ID
   * @returns {Promise} 包含线索详情的响应
   */
  async getLeadDetail(leadId) {
    try {
      console.log('获取线索详情:', leadId)
      
      const response = await api.post('/leads/detail', { 线索id: leadId })
      
      if (response.status === 100) {
        const lead = response.data
        
        const processedLead = {
          ...lead,
          // 解析JSON信息
          parsedInfo: this.parseLeadInfo(lead.信息),
          // 添加格式化显示字段
          displayName: this.getDisplayName(lead),
          displayLocation: this.getDisplayLocation(lead),
          displayCategory: this.getDisplayCategory(lead),
          displayFansCount: this.getDisplayFansCount(lead),
          // 添加联系方式分析
          contactAnalysis: this.analyzeContactInfo(lead.联系方式详情),
          // 添加价值评估
          valueAssessment: this.assessLeadValue(lead)
        }
        
        console.log('线索详情数据处理完成:', processedLead)
        return { ...response, data: processedLead }
      }
      
      return response
    } catch (error) {
      console.error('获取线索详情失败:', error)
      throw error
    }
  }
  
  /**
   * 更新线索信息
   * 产品价值：支持线索信息的实时更新和维护
   * 
   * @param {Object} leadData - 线索数据
   * @returns {Promise} 更新结果
   */
  async updateLead(leadData) {
    try {
      console.log('更新线索信息:', leadData)
      
      const response = await api.post('/leads/update', leadData)
      
      if (response.status === 100) {
        console.log('线索更新成功:', response.data)
      }
      
      return response
    } catch (error) {
      console.error('更新线索失败:', error)
      throw error
    }
  }
  
  /**
   * 批量操作线索
   * 产品价值：提升大数据量操作效率
   * 
   * @param {Object} params - 批量操作参数
   * @param {Array} params.线索ID列表 - 要操作的线索ID列表
   * @param {string} params.操作类型 - 操作类型：'delete', 'update', 'export'
   * @param {Object} params.操作数据 - 操作相关数据
   * @returns {Promise} 批量操作结果
   */
  async batchOperateLeads(params) {
    try {
      console.log('批量操作线索:', params)
      
      const response = await api.post('/leads/batch-operate', params)
      
      if (response.status === 100) {
        console.log('批量操作成功:', response.data)
      }
      
      return response
    } catch (error) {
      console.error('批量操作失败:', error)
      throw error
    }
  }
  
  /**
   * 导出线索数据
   * 产品价值：支持线下分析和第三方系统集成
   * 
   * @param {Object} params - 导出参数
   * @param {string} params.导出格式 - 导出格式：'excel', 'csv'
   * @param {Array} params.导出字段 - 要导出的字段列表
   * @param {Object} params.筛选条件 - 导出数据的筛选条件
   * @returns {Promise} 导出结果
   */
  async exportLeads(params) {
    try {
      console.log('导出线索数据:', params)
      
      const response = await api.post('/leads/export', params, {
        responseType: 'blob' // 用于文件下载
      })
      
      return response
    } catch (error) {
      console.error('导出线索数据失败:', error)
      throw error
    }
  }
  
  // ==================== 辅助方法 ====================
  
  /**
   * 解析线索信息JSON
   * @param {string|Object} info - 线索信息
   * @returns {Object} 解析后的信息对象
   */
  parseLeadInfo(info) {
    if (!info) return {}
    
    if (typeof info === 'string') {
      try {
        return JSON.parse(info)
      } catch (e) {
        console.warn('解析线索信息JSON失败:', e)
        return {}
      }
    }
    
    return info
  }
  
  /**
   * 获取显示用的名称
   * @param {Object} lead - 线索对象
   * @returns {string} 显示名称
   */
  getDisplayName(lead) {
    const info = this.parseLeadInfo(lead.信息)
    return info.名称 || info.昵称 || info.name || '未知'
  }
  
  /**
   * 获取显示用的地域信息
   * @param {Object} lead - 线索对象
   * @returns {string} 地域信息
   */
  getDisplayLocation(lead) {
    const info = this.parseLeadInfo(lead.信息)
    const province = info.省份 || info.province || ''
    const city = info.城市 || info.city || ''
    
    if (province && city) {
      return `${province} ${city}`
    }
    return province || city || info.国家 || info.country || '未知'
  }
  
  /**
   * 获取显示用的类别信息
   * @param {Object} lead - 线索对象
   * @returns {string} 类别信息
   */
  getDisplayCategory(lead) {
    const info = this.parseLeadInfo(lead.信息)
    return info.类别 || info.分类 || info.category || '未分类'
  }
  
  /**
   * 获取显示用的粉丝数
   * @param {Object} lead - 线索对象
   * @returns {string} 格式化的粉丝数
   */
  getDisplayFansCount(lead) {
    const info = this.parseLeadInfo(lead.信息)
    const fansCount = info.抖音粉丝数 || info.粉丝数 || info.fans_count || 0
    
    return this.formatNumber(fansCount)
  }
  
  /**
   * 检查是否有联系方式
   * @param {Object} lead - 线索对象
   * @returns {boolean} 是否有联系方式
   */
  hasContactInfo(lead) {
    return !!(lead.联系方式id && lead.关联_联系方式内容)
  }
  
  /**
   * 判断是否为高价值线索
   * @param {Object} lead - 线索对象
   * @returns {boolean} 是否为高价值线索
   */
  isHighValueLead(lead) {
    const info = this.parseLeadInfo(lead.信息)
    const fansCount = info.抖音粉丝数 || info.粉丝数 || 0
    
    // 简单的高价值判断逻辑：粉丝数超过10万或有联系方式
    return fansCount > 100000 || this.hasContactInfo(lead)
  }
  
  /**
   * 格式化数字显示
   * @param {number} num - 数字
   * @returns {string} 格式化后的字符串
   */
  formatNumber(num) {
    if (!num || num === 0) return '0'
    
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    }
    
    return num.toLocaleString()
  }
  
  /**
   * 计算增长率
   * @param {number} current - 当前值
   * @param {number} previous - 之前值
   * @returns {number} 增长率百分比
   */
  calculateGrowthRate(current, previous) {
    if (!previous || previous === 0) return 0
    return ((current - previous) / previous * 100).toFixed(1)
  }
  
  /**
   * 分析趋势
   * @param {Array} dailyStats - 每日统计数据
   * @returns {string} 趋势描述
   */
  analyzeTrend(dailyStats) {
    if (!dailyStats || dailyStats.length < 2) return 'stable'
    
    const recent = dailyStats.slice(-7) // 最近7天
    const increasing = recent.filter((day, index) => 
      index > 0 && day.count > recent[index - 1].count
    ).length
    
    if (increasing >= 5) return 'increasing'
    if (increasing <= 2) return 'decreasing'
    return 'stable'
  }
  
  /**
   * 生成模拟概览统计数据
   * @returns {Object} 模拟统计数据
   */
  generateMockOverviewStats() {
    return {
      totalLeads: 154054,
      dailyNew: 156,
      weeklyNew: 1089,
      monthlyNew: 4567,
      hasContactCount: 45678,
      highValueCount: 12345,
      conversionRate: 23.5,
      growthRate: 12.3,
      formattedTotal: '15.4万',
      trendAnalysis: 'increasing',
      sourceDistribution: [
        { name: '全网达人Excel', value: 68900, percentage: 44.7 },
        { name: '主播数据', value: 20000, percentage: 13.0 },
        { name: '短视频达人', value: 15000, percentage: 9.7 },
        { name: '其他来源', value: 50154, percentage: 32.6 }
      ],
      locationDistribution: [
        { name: '北京', value: 15405 },
        { name: '上海', value: 12340 },
        { name: '广州', value: 9876 },
        { name: '深圳', value: 8765 },
        { name: '杭州', value: 6543 }
      ],
      categoryDistribution: [
        { name: '美妆', value: 23456 },
        { name: '时尚', value: 18765 },
        { name: '美食', value: 15432 },
        { name: '旅游', value: 12345 },
        { name: '科技', value: 9876 }
      ]
    }
  }
  
  /**
   * 生成模拟线索列表数据
   * @param {Object} params - 查询参数
   * @returns {Array} 模拟线索列表
   */
  generateMockLeadsList(params) {
    const mockData = []
    const names = ['周周周', '奶爸的书房', '电影生活', '美妆达人小雅', '时尚博主小丽']
    const cities = ['北京', '上海', '广州', '深圳', '杭州', '重庆', '成都', '西安']
    const categories = ['美妆', '时尚', '美食', '旅游', '科技', '娱乐', '文化', '社会']
    const sources = ['全网达人(1).xlsx', '一万个主播.xlsx', '短视频达人.xlsx']
    
    for (let i = 0; i < (params.每页数量 || 20); i++) {
      const fansCount = Math.floor(Math.random() * 1000000) + 10000
      
      mockData.push({
        id: 1000 + i,
        联系方式id: Math.random() > 0.3 ? 100 + i : null,
        信息: JSON.stringify({
          名称: names[i % names.length],
          城市: cities[Math.floor(Math.random() * cities.length)],
          类别: categories[Math.floor(Math.random() * categories.length)],
          抖音粉丝数: fansCount
        }),
        线索来源: sources[Math.floor(Math.random() * sources.length)],
        更新用户: 1,
        创建时间: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        更新时间: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        关联_联系方式内容: Math.random() > 0.3 ? `联系方式${i}` : null,
        关联_联系方式类型: Math.random() > 0.3 ? '微信' : null
      })
    }
    
    return mockData
  }
  
  /**
   * 计算热门来源
   * @param {Array} leads - 线索列表
   * @returns {Array} 热门来源列表
   */
  calculateTopSources(leads) {
    const sourceCount = {}
    
    leads.forEach(lead => {
      const source = lead.线索来源 || '未知来源'
      sourceCount[source] = (sourceCount[source] || 0) + 1
    })
    
    return Object.entries(sourceCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([source, count]) => ({ source, count }))
  }
  
  /**
   * 计算热门地域
   * @param {Array} leads - 线索列表
   * @returns {Array} 热门地域列表
   */
  calculateTopLocations(leads) {
    const locationCount = {}
    
    leads.forEach(lead => {
      const location = this.getDisplayLocation(lead)
      locationCount[location] = (locationCount[location] || 0) + 1
    })
    
    return Object.entries(locationCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([location, count]) => ({ location, count }))
  }
  
  /**
   * 分析联系方式信息
   * @param {Object} contactInfo - 联系方式信息
   * @returns {Object} 联系方式分析结果
   */
  analyzeContactInfo(contactInfo) {
    if (!contactInfo) {
      return { hasContact: false, quality: 'none' }
    }
    
    const content = contactInfo.联系方式 || contactInfo.内容 || ''
    const type = contactInfo.类型 || contactInfo.type || ''
    
    let quality = 'low'
    if (type === '微信' || type === '电话') {
      quality = 'high'
    } else if (content.length > 5) {
      quality = 'medium'
    }
    
    return {
      hasContact: true,
      quality,
      type,
      content: content.substring(0, 20) + (content.length > 20 ? '...' : '')
    }
  }
  
  /**
   * 评估线索价值
   * @param {Object} lead - 线索对象
   * @returns {Object} 价值评估结果
   */
  assessLeadValue(lead) {
    const info = this.parseLeadInfo(lead.信息)
    const fansCount = info.抖音粉丝数 || info.粉丝数 || 0
    const hasContact = this.hasContactInfo(lead)
    
    let score = 0
    let level = 'low'
    
    // 粉丝数评分
    if (fansCount > 1000000) score += 40
    else if (fansCount > 100000) score += 30
    else if (fansCount > 10000) score += 20
    else score += 10
    
    // 联系方式评分
    if (hasContact) score += 30
    
    // 类别评分（某些类别更有价值）
    const category = this.getDisplayCategory(lead)
    if (['美妆', '时尚', '美食'].includes(category)) score += 20
    
    // 地域评分（一线城市更有价值）
    const location = this.getDisplayLocation(lead)
    if (['北京', '上海', '广州', '深圳'].some(city => location.includes(city))) score += 10
    
    if (score >= 80) level = 'high'
    else if (score >= 60) level = 'medium'
    
    return { score, level, factors: { fansCount, hasContact, category, location } }
  }
}

// 创建线索服务实例
const leadsService = new LeadsService()

// 导出服务实例和主要方法
export default leadsService
export const {
  getLeadsOverview,
  getLeadsList,
  getLeadDetail,
  updateLead,
  batchOperateLeads,
  exportLeads,
  checkPaginationLimit
} = leadsService
