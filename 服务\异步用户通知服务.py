"""
用户通知服务层
处理用户通知相关的业务逻辑
"""

from typing import Any, Dict, List, Optional

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 保留部分MySQL函数（暂时兼容，后续逐步迁移）
from 数据.异步用户通知数据 import 异步批量标记通知已读 as 数据_异步批量标记通知已读
from 数据.异步用户通知数据 import 异步标记所有通知已读 as 数据_异步标记所有通知已读
from 数据.异步用户通知数据 import 异步标记通知已读 as 数据_异步标记通知已读
from 数据.异步用户通知数据 import (
    异步获取用户未读通知数量 as 数据_异步获取用户未读通知数量,
)
from 数据.异步用户通知数据 import 异步获取用户通知列表 as 数据_异步获取用户通知列表
from 数据.异步用户通知数据 import 异步获取通知详情 as 数据_异步获取通知详情

from 日志 import 应用日志器, 错误日志器


async def 异步获取用户通知列表(
    用户id: int,
    页码: int = 1,
    每页数量: int = 20,
    通知类型: Optional[str] = None,
    是否已读: Optional[bool] = None,
    排序字段: str = "创建时间",
    排序顺序: str = "DESC",
) -> Dict[str, Any]:
    """
    服务层：获取用户通知列表

    参数:
        用户id: 用户id
        页码: 页码，从1开始
        每页数量: 每页显示数量
        通知类型: 通知类型筛选
        是否已读: 已读状态筛选
        排序字段: 排序字段
        排序顺序: 排序顺序

    返回:
        包含通知列表和分页信息的字典
    """
    try:
        # 参数验证
        if 页码 < 1:
            页码 = 1
        if 每页数量 < 1 or 每页数量 > 100:
            每页数量 = 20

        # 调用数据层获取通知列表
        结果 = await 数据_异步获取用户通知列表(
            用户id=用户id,
            页码=页码,
            每页数量=每页数量,
            通知类型=通知类型,
            是否已读=是否已读,
            排序字段=排序字段,
            排序顺序=排序顺序,
        )

        应用日志器.info(
            f"获取用户通知列表成功: 用户id={用户id}, 页码={页码}, 总数={结果['总数']}"
        )
        return 结果

    except Exception as e:
        错误日志器.error(f"获取用户通知列表失败: {str(e)}", exc_info=True)
        return {"列表": [], "总数": 0, "总页数": 0, "当前页": 页码}


async def 异步获取用户未读通知数量(用户id: int) -> int:
    """
    服务层：获取用户未读通知数量

    参数:
        用户id: 用户id

    返回:
        未读通知数量
    """
    try:
        数量 = await 数据_异步获取用户未读通知数量(用户id)
        应用日志器.debug(f"用户 {用户id} 未读通知数量: {数量}")
        return 数量

    except Exception as e:
        错误日志器.error(f"获取用户未读通知数量失败: {str(e)}", exc_info=True)
        return 0


async def 异步标记通知已读(通知id, 用户id: int) -> Dict[str, Any]:
    """
    服务层：标记单个通知为已读

    参数:
        通知id: 通知id（支持字符串或整数格式）
        用户id: 用户id

    返回:
        操作结果字典
    """
    try:
        # 判断是通告还是普通通知
        if str(通知id).startswith('announcement_'):
            # 处理通告
            通告id = int(str(通知id).replace('announcement_', ''))
            from 数据.异步用户通知数据 import 异步标记通告已读
            结果 = await 异步标记通告已读(通告id, 用户id)
        else:
            # 处理普通通知
            try:
                普通通知id = int(通知id)
                结果 = await 数据_异步标记通知已读(普通通知id, 用户id)
            except ValueError:
                return {"成功": False, "消息": f"无效的通知ID格式: {通知id}"}

        if 结果["成功"]:
            应用日志器.info(f"用户 {用户id} 标记通知 {通知id} 为已读成功")
        else:
            应用日志器.warning(
                f"用户 {用户id} 标记通知 {通知id} 为已读失败: {结果['消息']}"
            )

        return 结果

    except Exception as e:
        错误日志器.error(f"标记通知已读失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"标记已读失败: {str(e)}"}


async def 异步批量标记通知已读(通知id列表: List[str], 用户id: int) -> Dict[str, Any]:
    """
    服务层：批量标记通知为已读

    参数:
        通知id列表: 通知id列表（支持字符串格式，内部会解析）
        用户id: 用户id

    返回:
        操作结果字典
    """
    try:
        if not 通知id列表:
            return {"成功": False, "消息": "通知id列表不能为空"}

        # 分离通告id和普通通知ID
        普通通知id列表 = []
        通告id列表 = []

        for 通知id in 通知id列表:
            if str(通知id).startswith('announcement_'):
                通告id = int(str(通知id).replace('announcement_', ''))
                通告id列表.append(通告id)
            else:
                try:
                    普通通知id列表.append(int(通知id))
                except ValueError:
                    应用日志器.warning(f"无效的通知ID格式: {通知id}")

        结果 = await 数据_异步批量标记通知已读(
            通知id列表=普通通知id列表 if 普通通知id列表 else None,
            通告id列表=通告id列表 if 通告id列表 else None,
            用户id=用户id
        )

        if 结果["成功"]:
            应用日志器.info(
                f"用户 {用户id} 批量标记通知已读成功: {结果.get('影响行数', 0)} 个"
            )
        else:
            应用日志器.warning(f"用户 {用户id} 批量标记通知已读失败: {结果['消息']}")

        return 结果

    except Exception as e:
        错误日志器.error(f"批量标记通知已读失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"批量标记已读失败: {str(e)}"}


async def 异步标记所有通知已读(用户id: int) -> Dict[str, Any]:
    """
    服务层：标记用户所有通知为已读

    参数:
        用户id: 用户id

    返回:
        操作结果字典
    """
    try:
        结果 = await 数据_异步标记所有通知已读(用户id)

        if 结果["成功"]:
            应用日志器.info(
                f"用户 {用户id} 标记所有通知已读成功: {结果.get('影响行数', 0)} 个"
            )
        else:
            应用日志器.warning(f"用户 {用户id} 标记所有通知已读失败: {结果['消息']}")

        return 结果

    except Exception as e:
        错误日志器.error(f"标记所有通知已读失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"标记所有已读失败: {str(e)}"}


async def 异步获取通知详情(通知id: int, 用户id: int) -> Optional[Dict[str, Any]]:
    """
    服务层：获取通知详情

    参数:
        通知id: 通知id
        用户id: 用户id

    返回:
        通知详情字典或None
    """
    try:
        通知详情 = await 数据_异步获取通知详情(通知id, 用户id)

        if 通知详情:
            应用日志器.info(f"用户 {用户id} 获取通知 {通知id} 详情成功")
        else:
            应用日志器.warning(
                f"用户 {用户id} 获取通知 {通知id} 详情失败: 通知不存在或无权限"
            )

        return 通知详情

    except Exception as e:
        错误日志器.error(f"获取通知详情失败: {str(e)}", exc_info=True)
        return None


async def 异步创建业务通知(
    用户id: int, 业务类型: str, 业务关联id: str, 模板变量: Dict[str, Any]
) -> Dict[str, Any]:
    """
    服务层：创建业务通知

    参数:
        用户id: 接收通知的用户id
        业务类型: 业务类型（如invitation_status、sample_result等）
        业务关联id: 业务关联id
        模板变量: 模板变量字典，用于替换模板中的占位符

    返回:
        创建结果字典
    """
    try:
        # 获取通知模板
        模板查询 = """
            SELECT 标题模板, 内容模板, 重要性 
            FROM 通知模板表 
            WHERE 业务类型 = $1 AND 是否启用 = 1
        """
        # {{ AURA-X: Modify - 修复PostgreSQL参数类型，使用tuple而非list. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数传递最佳实践 }}
        模板结果 = await 异步连接池实例.执行查询(模板查询, (业务类型,))

        if not 模板结果:
            return {"成功": False, "消息": f"未找到业务类型 {业务类型} 的通知模板"}

        模板 = 模板结果[0]

        # 替换模板变量
        标题 = 模板["标题模板"]
        内容模板 = 模板["内容模板"]
        重要性 = 模板["重要性"]

        # 简单的变量替换
        for 变量名, 变量值 in 模板变量.items():
            标题 = 标题.replace(f"{{{变量名}}}", str(变量值))
            内容模板 = 内容模板.replace(f"{{{变量名}}}", str(变量值))

        # 插入通知记录
        插入语句 = """
            INSERT INTO 用户通知表 
            (用户id, 通知类型, 标题, 内容, 重要性, 业务关联id, 业务类型, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
        """

        # {{ AURA-X: Modify - 修复PostgreSQL参数类型，使用tuple而非list. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数传递最佳实践 }}
        插入结果 = await 异步连接池实例.执行插入(
            插入语句, (用户id, "business", 标题, 内容模板, 重要性, 业务关联id, 业务类型)
        )

        if 插入结果 > 0:
            应用日志器.info(
                f"为用户 {用户id} 创建业务通知成功: 业务类型={业务类型}, 关联id={业务关联id}"
            )
            return {"成功": True, "消息": "通知创建成功", "通知id": 插入结果}
        else:
            return {"成功": False, "消息": "通知创建失败"}

    except Exception as e:
        错误日志器.error(f"创建业务通知失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"创建通知失败: {str(e)}"}


async def 异步创建系统通知(通告id: int) -> Dict[str, Any]:
    """
    服务层：基于系统通告为所有用户创建通知

    参数:
        通告id: 通告id

    返回:
        创建结果字典
    """
    try:
        # 获取通告信息
        通告查询 = """
            SELECT 标题, 内容, 重要性 
            FROM 通告 
            WHERE id = $1 AND 已发布 = 1
        """
        # {{ AURA-X: Modify - 修复PostgreSQL参数类型，使用tuple而非list. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数传递最佳实践 }}
        通告结果 = await 异步连接池实例.执行查询(通告查询, (通告id,))

        if not 通告结果:
            return {"成功": False, "消息": "通告不存在或未发布"}

        通告 = 通告结果[0]

        # 获取所有用户id
        用户查询 = "SELECT id FROM 用户表 WHERE 状态 != '禁用'"
        # {{ AURA-X: Modify - 修复PostgreSQL参数类型，无参数查询传递None. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL参数传递最佳实践 }}
        用户结果 = await 异步连接池实例.执行查询(用户查询, None)

        if not 用户结果:
            return {"成功": False, "消息": "没有可用用户"}

        # 批量插入通知
        插入语句 = """
            INSERT INTO 用户通知表 
            (用户id, 通知类型, 标题, 内容, 重要性, 来源通告id, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
        """

        插入数据 = []
        for 用户 in 用户结果:
            插入数据.append(
                [
                    用户["id"],
                    "system_update",
                    通告["标题"],
                    通告["内容"],
                    通告["重要性"],
                    通告id,
                ]
            )

        # 执行批量插入
        成功数量 = 0
        for 数据 in 插入数据:
            try:
                # {{ AURA-X: Modify - 修复PostgreSQL参数类型，使用tuple而非list. Approval: 寸止(ID:1735372800). }}
                # {{ Source: PostgreSQL参数传递最佳实践 }}
                await 异步连接池实例.执行插入(插入语句, tuple(数据))
                成功数量 += 1
            except Exception as e:
                错误日志器.warning(f"为用户 {数据[0]} 创建系统通知失败: {str(e)}")

        应用日志器.info(f"基于通告 {通告id} 创建系统通知完成: 成功 {成功数量} 个")
        return {
            "成功": True,
            "消息": f"成功为 {成功数量} 个用户创建通知",
            "成功数量": 成功数量,
        }

    except Exception as e:
        错误日志器.error(f"创建系统通知失败: {str(e)}", exc_info=True)
        return {"成功": False, "消息": f"创建系统通知失败: {str(e)}"}
