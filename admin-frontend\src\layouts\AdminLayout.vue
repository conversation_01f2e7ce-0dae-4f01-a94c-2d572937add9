<template>
  <a-layout style="min-height: 100vh">
    <a-layout-sider 
      v-model:collapsed="collapsed" 
      collapsible
      :width="256"
      :collapsed-width="64"
      class="admin-sider"
    >
      <div class="logo">
        <div class="logo-content">
          <h1 v-if="!collapsed" class="logo-text">管理后台</h1>
          <h1 v-else class="logo-text-collapsed">后台</h1>
        </div>
      </div>
      <a-menu 
        theme="dark" 
        v-model:selectedKeys="selectedKeys" 
        v-model:openKeys="openKeys"
        mode="inline" 
        @click="handleMenuClick"
        class="admin-menu"
      >
        <template v-for="routeItem in menuRoutes" :key="routeItem.path">
          <template v-if="!routeItem.children || routeItem.children.length === 0">
            <a-menu-item :key="routeItem.path" class="menu-item">
              <template #icon>
                <component :is="routeItem.meta.icon" v-if="routeItem.meta && routeItem.meta.icon" />
              </template>
              <span>{{ routeItem.meta.title }}</span>
            </a-menu-item>
          </template>
          <template v-else>
            <a-sub-menu :key="routeItem.path" class="submenu">
              <template #icon>
                <component :is="routeItem.meta.icon" v-if="routeItem.meta && routeItem.meta.icon" />
              </template>
              <template #title>
                <span>{{ routeItem.meta.title }}</span>
              </template>
              <a-menu-item v-for="child in routeItem.children" :key="child.path.startsWith('/') ? child.path : routeItem.path + '/' + child.path.replace(/^\/+/, '')" class="submenu-item">
                <template #icon>
                  <component :is="child.meta.icon" v-if="child.meta && child.meta.icon" />
                </template>
                <span>{{ child.meta.title }}</span>
              </a-menu-item>
            </a-sub-menu>
          </template>
        </template>
      </a-menu>
    </a-layout-sider>
    
    <a-layout class="main-layout" :class="{ collapsed: collapsed }">
      <a-layout-header class="admin-header">
        <div class="header-left">
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="(crumb, index) in breadcrumbs" :key="index">
              <router-link v-if="index < breadcrumbs.length - 1 && crumb.path" :to="crumb.path">
                <component :is="crumb.icon" v-if="crumb.icon" class="breadcrumb-icon" />
                {{ crumb.title }}
              </router-link>
              <span v-else>
                <component :is="crumb.icon" v-if="crumb.icon" class="breadcrumb-icon" />
                {{ crumb.title }}
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <div class="header-right">
          <a-space :size="16">
            <!-- 系统状态指示器 -->
            <a-tooltip title="系统运行正常">
              <div class="status-indicator">
                <div class="status-dot"></div>
                <span class="status-text">系统正常</span>
              </div>
            </a-tooltip>
            
            <!-- 通知铃铛 -->
            <a-tooltip title="通知">
              <a-badge :count="5" size="small">
                <BellOutlined class="header-icon" />
              </a-badge>
            </a-tooltip>
            
            <!-- 用户下拉菜单 -->
            <a-dropdown placement="bottomRight">
              <a class="user-dropdown" @click.prevent>
                <a-avatar class="user-avatar" :style="{ backgroundColor: '#1890ff' }">
                  <template #icon><UserOutlined /></template>
                </a-avatar>
                <span class="user-name">{{ userStore.userInfo?.昵称 || '管理员' }}</span>
                <DownOutlined class="dropdown-arrow" />
              </a>
              <template #overlay>
                <a-menu class="user-menu">
                  <a-menu-item key="profile" @click="goToProfile">
                    <UserOutlined />
                    个人资料
                  </a-menu-item>
                  <a-menu-item key="settings" @click="goToSettings">
                    <SettingOutlined />
                    系统设置
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout" @click="handleLogout" class="logout-item">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </a-layout-header>
      
      <a-layout-content class="admin-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <div class="page-wrapper">
                <component :is="Component" />
              </div>
            </transition>
          </router-view>
        </div>
      </a-layout-content>
      

    </a-layout>
  </a-layout>
</template>

<script setup>
import {computed, onMounted, ref, watch} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {useUserStore} from '../store';
import {message} from 'ant-design-vue';
import {
  BankOutlined,
  BarChartOutlined,
  BellOutlined,
  CloudUploadOutlined,
  CodeSandboxOutlined,
  DashboardOutlined,
  DownOutlined,
  GoldOutlined,
  HomeOutlined,
  LogoutOutlined,
  NotificationOutlined,
  PieChartOutlined,
  ProfileOutlined,
  SettingOutlined,
  UserOutlined
} from '@ant-design/icons-vue';

const collapsed = ref(false);
const selectedKeys = ref([]);
const openKeys = ref([]);

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 图标映射表
const iconMap = {
  'PieChartOutlined': PieChartOutlined,
  'UserOutlined': UserOutlined,
  'NotificationOutlined': NotificationOutlined,
  'ProfileOutlined': ProfileOutlined,
  'SettingOutlined': SettingOutlined,
  'GoldOutlined': GoldOutlined,
  'CodeSandboxOutlined': CodeSandboxOutlined,
  'CloudUploadOutlined': CloudUploadOutlined,
  'HomeOutlined': HomeOutlined,
  'BankOutlined': BankOutlined,
  'BarChartOutlined': BarChartOutlined,
  'DashboardOutlined': DashboardOutlined
};

// 获取菜单路由，过滤掉不需要在菜单中显示的路由
const menuRoutes = computed(() => {
  const adminLayoutRoute = router.options.routes.find(r => r.path === '/');
  if (!adminLayoutRoute?.children) return [];
  
  const routes = adminLayoutRoute.children
    .filter(r => r.meta && r.meta.title && !r.meta.hideInMenu)
    .map(route => {
      // 确保路径格式统一：顶级路由使用完整路径
      const fullPath = route.path.startsWith('/') ? route.path : '/' + route.path;
      
      return {
        ...route,
        path: fullPath, // 修复：统一使用完整路径
        meta: {
          ...route.meta,
          icon: iconMap[route.meta.icon] || route.meta.icon
        },
        children: route.children?.filter(child => child.meta && child.meta.title && !child.meta.hideInMenu)
          .map(child => ({
            ...child,
            meta: {
              ...child.meta,
              icon: iconMap[child.meta.icon] || child.meta.icon
            }
          }))
      };
    });
  
  return routes;
});

// 生成面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(r => r.meta && r.meta.title);
  
  const crumbs = matched.map(r => ({
    title: r.meta.title,
    path: r.path !== route.path ? r.path : '',
    icon: iconMap[r.meta.icon] || r.meta.icon
  }));
  
  // 特殊处理通告编辑页面的面包屑
  if (route.path.startsWith('/announcements/')) {
    // 如果是通告相关页面，确保包含通告管理的面包屑
    const hasNotificationCrumb = crumbs.some(c => c.title === '通告管理');
    if (!hasNotificationCrumb) {
      // 在当前页面前插入通告管理面包屑
      const currentPageCrumb = crumbs.pop(); // 移除当前页面
      crumbs.push({
        title: '通告管理',
        path: '/notifications',
        icon: NotificationOutlined
      });
      if (currentPageCrumb) {
        crumbs.push(currentPageCrumb); // 重新添加当前页面
      }
    }
  }
  
  // 如果第一个不是首页，添加首页面包屑
  if (crumbs.length > 0 && crumbs[0].title !== '仪表盘') {
    crumbs.unshift({
      title: '首页',
      path: '/dashboard',
      icon: HomeOutlined
    });
  }
  
  return crumbs;
});

// 根据路径查找父路径
const getParentPath = (path) => {
  for (const parentRoute of menuRoutes.value) {
    if (parentRoute.children) {
      for (const childRoute of parentRoute.children) {
        const fullChildPath = childRoute.path.startsWith('/') 
          ? childRoute.path 
          : parentRoute.path + '/' + childRoute.path.replace(/^\/+/, '');
        if (fullChildPath === path) {
          return parentRoute.path;
        }
      }
    }
  }
  return null;
};

// 监听路由变化，更新选中状态
watch(() => route.path, (newPath) => {
  // 修复：确保selectedKeys使用正确的路径格式
  selectedKeys.value = [newPath];
  
  const parentPath = getParentPath(newPath);
  if (parentPath) {
    openKeys.value = [parentPath];
  } else {
    // 如果是顶级菜单且没有子菜单，清空openKeys
    const isParentMenu = menuRoutes.value.some(r => 
      r.path === newPath && r.children && r.children.length > 0
    );
    if (!isParentMenu) {
      openKeys.value = [];
    }
  }
}, { immediate: true });

// 处理菜单点击 - 优化路由跳转逻辑
const handleMenuClick = (e) => {
  try {
    // 确保路径格式正确
    const targetPath = e.key;
    
    // 使用router.push进行导航
    router.push(targetPath).catch(err => {
      console.error('路由跳转失败:', err);
      message.error('页面跳转失败，请重试');
    });
  } catch (error) {
    console.error('菜单点击处理失败:', error);
    message.error('菜单操作失败');
  }
};

// 处理退出登录
const handleLogout = () => {
  userStore.logout();
  router.push('/login');
  message.success('已成功退出登录');
};

// 跳转到个人资料
const goToProfile = () => {
  // 暂时没有个人资料页面，提示开发中
  message.info('个人资料页面开发中...');
};

// 跳转到系统设置
const goToSettings = () => {
  router.push('/settings');
};

// 组件挂载时初始化
onMounted(() => {
  const parentPath = getParentPath(route.path);
  if (parentPath) {
    openKeys.value = [parentPath];
  }
});
</script>

<style scoped>
/* 整体布局样式 */
.admin-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
  position: fixed !important;
  left: 0;
  top: 0;
  height: 100vh !important;
  z-index: 1000;
}

.admin-sider :deep(.ant-layout-sider-trigger) {
  background: #001529;
  color: rgba(255, 255, 255, 0.85);
  border-top: 1px solid #313131;
  transition: all 0.2s;
}

.admin-sider :deep(.ant-layout-sider-trigger):hover {
  background: #1890ff;
  color: #fff;
}

/* Logo区域样式 */
.logo {
  height: 64px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-content {
  text-align: center;
  transition: all 0.3s ease;
}

.logo-text, .logo-text-collapsed {
  color: white;
  margin: 0;
  font-weight: 700;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo-text {
  font-size: 18px;
  letter-spacing: 1px;
}

.logo-text-collapsed {
  font-size: 16px;
  letter-spacing: 0.5px;
}

/* 菜单样式 */
.admin-menu {
  border-right: none;
  background: #001529;
  height: calc(100vh - 96px);
  overflow-y: auto;
}

.admin-menu :deep(.ant-menu-item) {
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.admin-menu :deep(.ant-menu-item:hover) {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.admin-menu :deep(.ant-menu-item-selected) {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  color: #fff;
  font-weight: 600;
}

.admin-menu :deep(.ant-menu-submenu-title) {
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.admin-menu :deep(.ant-menu-submenu-title:hover) {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.admin-menu :deep(.ant-menu-submenu-open .ant-menu-submenu-title) {
  color: #1890ff;
}

.admin-menu :deep(.ant-menu-sub) {
  background: #000c17;
}

.admin-menu :deep(.ant-menu-sub .ant-menu-item) {
  margin: 2px 16px;
  padding-left: 32px !important;
  border-radius: 4px;
}

/* 主布局区域 */
.main-layout {
  background: #f0f2f5;
  margin-left: 256px;
  transition: margin-left 0.2s;
}

.main-layout.collapsed {
  margin-left: 64px;
}

/* 头部样式 */
.admin-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 9;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 面包屑样式 */
.breadcrumb {
  margin: 0;
}

.breadcrumb :deep(.ant-breadcrumb-link) {
  color: #666;
  transition: color 0.3s;
}

.breadcrumb :deep(.ant-breadcrumb-link:hover) {
  color: #1890ff;
}

.breadcrumb-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #52c41a;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 头部图标 */
.header-icon {
  font-size: 18px;
  color: #666;
  cursor: pointer;
  transition: color 0.3s;
}

.header-icon:hover {
  color: #1890ff;
}

/* 用户下拉菜单 */
.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background 0.3s;
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.user-dropdown:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.user-avatar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.user-name {
  font-weight: 500;
  color: #333;
}

.dropdown-arrow {
  font-size: 12px;
  color: #999;
  transition: transform 0.3s;
}

.user-dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 用户菜单样式 */
.user-menu {
  min-width: 160px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.user-menu :deep(.ant-menu-item) {
  padding: 8px 16px;
  transition: all 0.3s;
}

.user-menu :deep(.ant-menu-item:hover) {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.logout-item {
  color: #ff4d4f !important;
}

.logout-item:hover {
  background: rgba(255, 77, 79, 0.1) !important;
  color: #ff4d4f !important;
}

/* 内容区域 */
.admin-content {
  margin: 0;
  padding: 0;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100%;
}

.page-wrapper {
  width: 100%;
  height: 100%;
}

/* 页面过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .admin-header {
    padding: 0 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  

  
  .user-name {
    display: none;
  }
  
  .status-text {
    display: none;
  }
}

@media (max-width: 576px) {
  .breadcrumb {
    display: none;
  }
  

}
</style>