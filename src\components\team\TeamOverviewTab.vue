<template>
  <div class="team-overview-container">
    <!-- 移除重复的团队信息头部，因为父组件TeamDetail.vue已经有了 -->

    <!-- 核心统计 -->
    <div class="stats-section">
      <div class="stat-card primary">
        <div class="stat-icon">
          <UserOutlined />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ memberCount }}</div>
          <div class="stat-label">团队成员</div>
          <div class="stat-progress">
            <a-progress
              :percent="memberProgress"
              :show-info="false"
              size="small"
              stroke-color="#1890ff"
            />
            <span class="progress-text">{{ memberCount }}/{{ maxMembers }}</span>
          </div>
        </div>
      </div>

      <div class="stat-card success">
        <div class="stat-icon">
          <TeamOutlined />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ activeMembers }}</div>
          <div class="stat-label">活跃成员</div>
          <div class="stat-subtitle">最近7天内活跃</div>
        </div>
      </div>

      <div class="stat-card warning">
        <div class="stat-icon">
          <CalendarOutlined />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ runningDays }}</div>
          <div class="stat-label">运行天数</div>
          <div class="stat-subtitle">团队创建至今</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-grid">
      <!-- 最新成员 -->
      <div class="content-card">
        <div class="card-header">
          <h3>最新成员</h3>
          <a-button type="text" size="small" @click="loadRecentMembers">
            <ReloadOutlined />
          </a-button>
        </div>
        <div class="card-content">
          <a-spin :spinning="loading">
            <div v-if="recentMembers.length > 0" class="members-list">
              <div
                v-for="member in recentMembers.slice(0, 5)"
                :key="member.用户id"
                class="member-item"
              >
                <a-avatar
                  :size="40"
                  :style="{ backgroundColor: getUserAvatarColor(member.昵称) }"
                >
                  {{ member.昵称?.charAt(0) || 'U' }}
                </a-avatar>
                <div class="member-details">
                  <div class="member-name">{{ member.昵称 || '未知用户' }}</div>
                  <div class="member-role">{{ getRoleDisplayName(member.角色) }}</div>
                  <div class="member-time">{{ formatRelativeTime(member.加入时间) }}加入</div>
                </div>
                <a-tag :color="getRoleColor(member.角色)" size="small">
                  {{ member.角色 }}
                </a-tag>
              </div>
            </div>
            <a-empty v-else description="暂无成员" :image="false" />
          </a-spin>
        </div>
      </div>

      <!-- 角色分布 -->
      <div class="content-card">
        <div class="card-header">
          <h3>角色分布</h3>
        </div>
        <div class="card-content">
          <div v-if="roleDistribution.length > 0" class="role-chart">
            <div
              v-for="role in roleDistribution"
              :key="role.角色"
              class="role-item"
            >
              <div class="role-info">
                <a-tag :color="getRoleColor(role.角色)">{{ role.角色 }}</a-tag>
                <span class="role-count">{{ role.数量 }}人</span>
              </div>
              <div class="role-bar">
                <div
                  class="role-progress"
                  :style="{
                    width: `${role.百分比}%`,
                    backgroundColor: getRoleColor(role.角色)
                  }"
                ></div>
              </div>
              <span class="role-percent">{{ role.百分比 }}%</span>
            </div>
          </div>
          <a-empty v-else description="暂无数据" :image="false" />
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="content-card full-width">
        <div class="card-header">
          <h3>最近活动</h3>
          <a-button type="text" size="small" @click="loadActivities">
            <ReloadOutlined />
          </a-button>
        </div>
        <div class="card-content">
          <a-spin :spinning="activitiesLoading">
            <div v-if="activities.length > 0" class="activities-list">
              <div
                v-for="activity in activities.slice(0, 8)"
                :key="activity.ID || activity.活动时间"
                class="activity-item"
              >
                <div class="activity-dot" :style="{ backgroundColor: getActivityColor(activity.活动类型) }"></div>
                <div class="activity-content">
                  <div class="activity-text">
                    <strong>{{ activity.操作人 }}</strong> {{ activity.活动描述 }}
                    <span v-if="activity.目标用户" class="activity-target">{{ activity.目标用户 }}</span>
                  </div>
                  <div class="activity-time">{{ formatRelativeTime(activity.活动时间) }}</div>
                </div>
              </div>
            </div>
            <a-empty v-else description="暂无活动记录" :image="false" />
          </a-spin>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  UserOutlined,
  CalendarOutlined,
  ReloadOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'
import teamService from '../../services/team'
import { usePermissions } from '../../composables/usePermissions'
import { getRoleColor, getRoleDisplayName } from '../../utils/roleUtils'
import { formatRelativeTime } from '../../utils/teamUtils'

defineOptions({
  name: 'TeamOverviewTab'
})

const props = defineProps({
  team: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['refresh', 'invite-member'])

const { hasPermission } = usePermissions()

// 响应式数据
const loading = ref(false)
const activitiesLoading = ref(false)
const activities = ref([])
const recentMembers = ref([])

// 计算属性
const memberCount = computed(() => props.team.当前成员数 || 0)
const maxMembers = computed(() => props.team.最大成员数 || 100)
const memberProgress = computed(() => Math.round((memberCount.value / maxMembers.value) * 100))
const activeMembers = computed(() => recentMembers.value.filter(m => isRecentlyActive(m)).length)
const runningDays = computed(() => {
  if (!props.team.创建时间) return 0
  const created = new Date(props.team.创建时间)
  const now = new Date()
  return Math.floor((now - created) / (1000 * 60 * 60 * 24))
})

// 移除canInviteMembers计算属性，因为邀请按钮已经在父组件中处理

const roleDistribution = computed(() => {
  if (!recentMembers.value.length) return []

  const roles = {}
  const total = recentMembers.value.length

  recentMembers.value.forEach(member => {
    const role = member.角色 || '成员'
    roles[role] = (roles[role] || 0) + 1
  })

  return Object.entries(roles)
    .map(([角色, 数量]) => ({
      角色,
      数量,
      百分比: Math.round((数量 / total) * 100)
    }))
    .sort((a, b) => b.数量 - a.数量)
})

// 业务方法
const loadActivities = async () => {
  try {
    activitiesLoading.value = true
    const response = await teamService.getTeamActivities({
      团队id: props.team.团队id,
      限制数量: 10
    })

    if (response.status === 100) {
      activities.value = response.data?.活动列表 || []
    }
  } catch (error) {
    console.error('加载活动记录失败:', error)
    activities.value = []
  } finally {
    activitiesLoading.value = false
  }
}

const loadRecentMembers = async () => {
  try {
    loading.value = true
    const response = await teamService.getTeamMembers({
      团队id: props.team.团队id,
      页码: 1,
      每页数量: 10,
      排序方式: '加入时间降序'
    })

    if (response.status === 100) {
      const 成员列表 = response.data?.成员列表 || []
      recentMembers.value = 成员列表.map(member => ({
        ...member,
        昵称: member.昵称 || member.nickname || '未知用户',
        角色: member.角色 || member.职位 || '成员'
      }))
    } else {
      recentMembers.value = []
    }
  } catch (error) {
    console.error('加载最新成员失败:', error)
    recentMembers.value = []
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await Promise.all([
    loadActivities(),
    loadRecentMembers()
  ])
  emit('refresh')
}

// 工具方法
// 移除getTeamColor方法，因为团队头部已经被删除

const getUserAvatarColor = (name) => {
  if (!name) return '#1890ff'
  const colors = [
    '#f56565', '#ed8936', '#ecc94b', '#48bb78',
    '#38b2ac', '#4299e1', '#667eea', '#9f7aea'
  ]
  const index = name.charCodeAt(0) % colors.length
  return colors[index]
}

const getActivityColor = (type) => {
  const colorMap = {
    '创建团队': '#1890ff',
    '加入团队': '#52c41a',
    '邀请成员': '#13c2c2',
    '踢出成员': '#faad14',
    '离开团队': '#f5222d',
    '更新权限': '#722ed1',
    '更新角色': '#eb2f96',
    '成员加入': '#52c41a',
    '成员退出': '#f5222d',
    '权限变更': '#faad14',
    '团队设置': '#1890ff'
  }
  return colorMap[type] || '#1890ff'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const isRecentlyActive = (member) => {
  if (!member.最后活跃时间) return false
  const lastActive = new Date(member.最后活跃时间)
  const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  return lastActive > weekAgo
}

// 监听团队变化
watch(() => props.team.团队id, (newTeamId) => {
  if (newTeamId) {
    loadActivities()
    loadRecentMembers()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.team.团队id) {
    loadActivities()
    loadRecentMembers()
  }
})
</script>

<style scoped>
.team-overview-container {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 移除重复的团队头部样式，因为已经删除了重复的HTML结构 */

/* 统计卡片 */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
  border-left: 4px solid #1890ff;
}

.stat-card.success {
  border-left: 4px solid #52c41a;
}

.stat-card.warning {
  border-left: 4px solid #faad14;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 16px;
  font-size: 20px;
}

.stat-card.primary .stat-icon {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.stat-card.success .stat-icon {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.stat-card.warning .stat-icon {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-subtitle {
  font-size: 12px;
  color: #999;
}

.stat-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.content-card.full-width {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-content {
  padding: 20px 24px 24px;
}

/* 成员列表 */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
  transition: background 0.2s ease;
}

.member-item:hover {
  background: #f0f0f0;
}

.member-details {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.member-role {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.member-time {
  font-size: 12px;
  color: #999;
}

/* 角色分布 */
.role-chart {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
}

.role-count {
  font-size: 13px;
  color: #666;
}

.role-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.role-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.role-percent {
  font-size: 12px;
  color: #999;
  min-width: 35px;
  text-align: right;
}

/* 活动列表 */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.activity-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 6px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 14px;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 4px;
}

.activity-target {
  color: #1890ff;
  font-weight: 500;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .content-card.full-width {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .team-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .team-actions {
    justify-content: flex-end;
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-value {
    font-size: 24px;
  }

  .member-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .role-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .role-bar {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .team-overview-container {
    padding: 0;
  }

  .team-header {
    margin: 0 0 16px 0;
    border-radius: 0;
  }

  .content-grid {
    gap: 16px;
  }

  .content-card {
    border-radius: 0;
    margin: 0 -16px;
  }

  .card-header,
  .card-content {
    padding-left: 16px;
    padding-right: 16px;
  }
}
</style>