"""
客户邀请管理路由
提供客户邀请相关的API接口
主要功能：
1. 发送客户邀请
2. 获取邀请列表
3. 获取邀请详情
4. 获取邀请统计
5. 处理邀请接受逻辑
"""

from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户, 获取当前用户_可选
from 工具.团队工具 import 标准化API响应
from 数据.客户邀请数据 import (
    处理用户注册邀请关联,
    异步创建客户邀请,
    异步获取客户邀请列表,
    异步获取邀请统计,
    异步获取邀请详情,
)
from 日志 import 错误日志器

# 创建客户邀请管理路由
客户邀请管理路由 = APIRouter(tags=["客户邀请管理"])

# =============== Pydantic模型 ===============


class 客户邀请请求(BaseModel):
    """客户邀请请求模型"""

    被邀请人手机号: str = Field(..., description="被邀请人手机号")
    邀请消息: Optional[str] = Field(None, description="邀请消息")
    有效期天数: int = Field(30, description="邀请有效期天数", ge=1, le=365)


class 邀请列表请求(BaseModel):
    """邀请列表查询请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)
    状态筛选: Optional[str] = Field(None, description="状态筛选")


class 邀请详情请求(BaseModel):
    """邀请详情请求模型"""

    邀请码: str = Field(..., description="邀请码")


# =============== 路由端点 ===============


@客户邀请管理路由.post("/create", summary="发送客户邀请")
async def 发送客户邀请接口(
    请求数据: 客户邀请请求, 当前用户: dict = Depends(获取当前用户)
):
    """
    发送客户邀请接口

    功能说明：
    - 验证用户每日邀请次数限制（基于用户表的每日邀约次数字段）
    - 检查被邀请人手机号是否已注册，避免重复邀请
    - 生成32位随机邀请码，确保唯一性
    - 创建邀请记录并生成线上邀请链接
    - 支持更新现有待处理邀请，避免重复记录

    参数：
    - 被邀请人手机号：必填，11位手机号
    - 邀请消息：可选，自定义邀请消息
    - 有效期天数：默认30天，范围1-365天

    返回：
    - 成功：邀请id、邀请码、邀请链接等信息
    - 失败：具体错误信息和状态码
    """
    try:
        结果 = await 异步创建客户邀请(
            邀请人id=当前用户["id"],
            被邀请人手机号=请求数据.被邀请人手机号,
            邀请消息=请求数据.邀请消息,
            有效期天数=请求数据.有效期天数,
        )

        # 检查返回结果的格式
        if "success" in 结果:
            # 旧格式：包含success字段
            if 结果["success"]:
                return 标准化API响应(100, 结果["data"], 结果["message"])
            else:
                # 业务失败，但HTTP状态码仍然是200
                return 标准化API响应(101, None, 结果["message"])
        elif "status" in 结果:
            # 新格式：包含业务状态码
            return 标准化API响应(结果["status"], None, 结果["message"])
        else:
            # 未知格式，默认处理
            return 标准化API响应(500, None, "响应格式异常")

    except Exception as e:
        错误日志器.error(f"发送客户邀请失败: {e}", exc_info=True)
        return 标准化API响应(500, None, "发送邀请失败")


@客户邀请管理路由.post("/list", summary="获取客户邀请列表")
async def 获取客户邀请列表接口(
    请求数据: 邀请列表请求, 当前用户: dict = Depends(获取当前用户)
):
    """
    获取当前用户的客户邀请列表
    - 支持分页查询
    - 支持状态筛选
    """
    try:
        结果 = await 异步获取客户邀请列表(
            邀请人id=当前用户["id"],
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            状态筛选=请求数据.状态筛选,
        )

        if 结果["success"]:
            return 标准化API响应(100, 结果["data"], "获取邀请列表成功")
        else:
            return 标准化API响应(400, None, 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取客户邀请列表失败: {e}", exc_info=True)
        return 标准化API响应(500, None, "获取邀请列表失败")


@客户邀请管理路由.get("/detail/{邀请码}", summary="获取邀请详情")
async def 获取邀请详情接口(
    邀请码: str, 当前登录用户: Optional[dict] = Depends(获取当前用户_可选)
):
    """
    通过邀请码获取邀请详情接口

    功能说明：
    - 支持未登录用户访问，用于邀请页面展示
    - 验证邀请码的有效性和存在性
    - 自动检查邀请状态：待处理、已注册、已过期、已拒绝
    - 实时检查过期时间，自动更新过期状态
    - 检查被邀请人手机号是否已注册，自动更新邀请状态

    参数：
    - 邀请码：32位随机字符串，路径参数

    返回状态码：
    - 100：成功，返回邀请详情
    - 404：邀请不存在或邀请码无效
    - 409：手机号已注册，邀请已失效
    - 410：邀请已过期
    - 500：服务器内部错误
    """
    try:
        结果 = await 异步获取邀请详情(邀请码)

        if 结果["success"]:
            return 标准化API响应(100, 结果["data"], "获取邀请详情成功")
        else:
            # 保持原有的状态码，这些是特殊的业务状态码，前端需要根据不同状态码做不同处理
            return 标准化API响应(结果.get("status", 400), None, 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取邀请详情失败: {e}", exc_info=True)
        return 标准化API响应(500, None, "获取邀请详情失败")


@客户邀请管理路由.get("/statistics", summary="获取邀请统计")
async def 获取邀请统计接口(当前用户: dict = Depends(获取当前用户)):
    """
    获取当前用户的邀请统计信息
    - 各状态邀请数量
    - 今日邀请次数和剩余次数
    """
    try:
        结果 = await 异步获取邀请统计(当前用户["id"])

        if 结果["success"]:
            return 标准化API响应(100, 结果["data"], "获取统计信息成功")
        else:
            return 标准化API响应(400, None, 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取邀请统计失败: {e}", exc_info=True)
        return 标准化API响应(500, None, "获取统计信息失败")


@客户邀请管理路由.post("/cancel/{邀请id}", summary="撤销客户邀请")
async def 撤销客户邀请接口(邀请id: int, 当前用户: dict = Depends(获取当前用户)):
    """
    撤销客户邀请
    - 只能撤销待处理状态的邀请
    - 撤销后邀请状态变为已拒绝
    """
    try:
        from 数据.客户邀请数据 import 异步撤销客户邀请

        结果 = await 异步撤销客户邀请(邀请id, 当前用户["id"])

        if 结果["success"]:
            return 标准化API响应(100, 结果.get("data"), 结果["message"])
        else:
            return 标准化API响应(400, None, 结果["message"])

    except Exception as e:
        错误日志器.error(f"撤销客户邀请失败: {e}", exc_info=True)
        return 标准化API响应(500, None, "撤销邀请失败")


@客户邀请管理路由.post("/resend/{邀请id}", summary="重新发送客户邀请")
async def 重新发送客户邀请接口(邀请id: int, 当前用户: dict = Depends(获取当前用户)):
    """
    重新发送客户邀请
    - 可以重新发送已过期、已拒绝状态的邀请
    - 重新发送后生成新的邀请码和过期时间
    """
    try:
        from 数据.客户邀请数据 import 异步重新发送客户邀请

        结果 = await 异步重新发送客户邀请(邀请id, 当前用户["id"])

        if 结果["success"]:
            return 标准化API响应(100, 结果.get("data"), 结果["message"])
        else:
            return 标准化API响应(400, None, 结果["message"])

    except Exception as e:
        错误日志器.error(f"重新发送客户邀请失败: {e}", exc_info=True)
        return 标准化API响应(500, None, "重新发送邀请失败")


@客户邀请管理路由.post("/process-registration", summary="处理注册邀请关联")
async def 处理注册邀请关联接口(
    手机号: str, 用户id: int, 当前用户: Optional[dict] = Depends(获取当前用户_可选)
):
    """
    处理用户注册时的邀请关联接口

    功能说明：
    - 在用户完成注册后自动调用，建立邀请关系
    - 查找该手机号的最新待处理邀请记录
    - 更新邀请状态为"已注册"，记录注册时间和用户id
    - 在用户表中设置邀请人字段，建立邀请关系
    - 支持邀请奖励机制的后续处理

    调用时机：
    - 用户通过邀请链接注册成功后
    - 前端注册流程中自动调用
    - 也可以手动补充邀请关联关系

    参数：
    - 手机号：注册用户的手机号
    - 用户id：注册成功后的用户id

    返回：
    - 成功：邀请人id和邀请码信息
    - 失败：未找到相关邀请记录或处理失败
    """
    try:
        结果 = await 处理用户注册邀请关联(手机号, 用户id)

        if 结果["success"]:
            return 标准化API响应(100, 结果.get("data"), 结果["message"])
        else:
            return 标准化API响应(400, None, 结果["message"])

    except Exception as e:
        错误日志器.error(f"处理注册邀请关联失败: {e}", exc_info=True)
        return 标准化API响应(500, None, "处理邀请关联失败")


# =============== 邀请页面路由 ===============


@客户邀请管理路由.get("/page/{邀请码}", summary="邀请页面")
async def 邀请页面(邀请码: str):
    """
    邀请页面路由，用于前端显示邀请详情页面
    这个路由主要用于SEO和直接访问
    """
    try:
        # 这里可以返回邀请页面的基本信息
        # 实际的页面渲染由前端处理
        return {
            "邀请码": 邀请码,
            "页面类型": "客户邀请",
            "重定向URL": f"/register?inviteCode={邀请码}",
        }
    except Exception as e:
        错误日志器.error(f"邀请页面访问失败: {e}", exc_info=True)
        return {"error": "页面访问失败"}
