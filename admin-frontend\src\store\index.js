import {defineStore} from 'pinia'

/**
 * 用户状态管理
 */
export const useUserStore = defineStore('user', {
  state: () => ({
    // 安全解析localStorage数据，避免JSON.parse错误
    userInfo: (() => {
      try {
        const stored = localStorage.getItem('userInfo');
        return stored ? JSON.parse(stored) : null;
      } catch (error) {
        console.warn('解析userInfo失败，清除无效数据:', error);
        localStorage.removeItem('userInfo');
        return null;
      }
    })(),
    token: localStorage.getItem('authToken') || null,
    permissions: (() => {
      try {
        const stored = localStorage.getItem('userPermissions');
        return stored ? JSON.parse(stored) : null;
      } catch (error) {
        console.warn('解析userPermissions失败，清除无效数据:', error);
        localStorage.removeItem('userPermissions');
        return null;
      }
    })(),
    tokenExpiry: (() => {
      try {
        const stored = localStorage.getItem('tokenExpiry');
        return stored ? parseInt(stored) : null;
      } catch (error) {
        console.warn('解析tokenExpiry失败，清除无效数据:', error);
        localStorage.removeItem('tokenExpiry');
        return null;
      }
    })(),
    lastActivity: Date.now(),
  }),
  
  getters: {
    isAuthenticated: (state) => !!state.token && state.isTokenValid,
    
    getUserInfo: (state) => state.userInfo,
    
    isAdmin: (state) => {
      return state.userInfo && state.userInfo.roles && state.userInfo.roles.includes('admin');
    },
    
    hasPermission: (state) => (permission) => {
      if (!state.permissions) return false;
      return state.permissions.includes(permission);
    },
    
    isTokenValid: (state) => {
      if (!state.tokenExpiry) return false;
      // 提前5分钟认为token过期，确保有足够时间刷新
      return state.tokenExpiry > (Date.now() + 5 * 60 * 1000);
    },
    
    isSessionExpired: (state) => {
      // 30分钟无活动视为会话过期
      const SESSION_TIMEOUT = 30 * 60 * 1000; // 30分钟
      return Date.now() - state.lastActivity > SESSION_TIMEOUT;
    },
    
    userRoles: (state) => {
      return state.userInfo?.roles || [];
    },
    
    userName: (state) => {
      return state.userInfo?.username || '未登录';
    },
    
    userAvatar: (state) => {
      return state.userInfo?.avatar || '';
    }
  },
  
  actions: {
    /**
     * 登录成功后调用
     * @param {Object} userData 用户数据
     * @param {String} authToken 认证令牌
     * @param {Array} userPermissions 用户权限
     * @param {Number} expiresIn 过期时间(秒)
     */
    loginSuccess(userData, authToken, userPermissions, expiresIn = 86400) {
      this.userInfo = userData;
      this.token = authToken;
      this.permissions = userPermissions;
      
      // 计算token过期时间
      const expiryTime = Date.now() + expiresIn * 1000;
      this.tokenExpiry = expiryTime;
      
      // 安全存储到localStorage
      try {
        localStorage.setItem('authToken', authToken);
        localStorage.setItem('userInfo', JSON.stringify(userData));
        localStorage.setItem('userPermissions', JSON.stringify(userPermissions || []));
        localStorage.setItem('tokenExpiry', expiryTime.toString());
      } catch (error) {
        console.error('存储登录信息到localStorage失败:', error);
        // 即使存储失败，也继续维持内存中的状态
      }
      
      this.updateActivity();
    },
    
    /**
     * 退出登录
     */
    logout() {
      this.userInfo = null;
      this.token = null;
      this.permissions = null;
      this.tokenExpiry = null;
      
      // 清除localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('userPermissions');
      localStorage.removeItem('tokenExpiry');
    },
    
    /**
     * 更新用户活动时间
     */
    updateActivity() {
      this.lastActivity = Date.now();
    },
    
    /**
     * 检查并处理会话状态
     * @returns {Boolean} 会话是否有效
     */
    checkSession() {
      // 如果token无效，直接登出
      if (this.token && !this.isTokenValid) {
        console.warn('Token已过期，自动登出');
        this.logout();
        return false;
      }
      
      // 如果会话过期，直接登出
      if (this.token && this.isSessionExpired) {
        console.warn('会话已过期，自动登出');
        this.logout();
        return false;
      }
      
      // 如果有token，更新活动时间
      if (this.token) {
        this.updateActivity();
      }
      
      return !!this.token;
    },
    
    /**
     * 更新用户信息
     * @param {Object} userData 用户数据
     */
    updateUserInfo(userData) {
      this.userInfo = { ...this.userInfo, ...userData };
      try {
        localStorage.setItem('userInfo', JSON.stringify(this.userInfo));
      } catch (error) {
        console.error('更新用户信息到localStorage失败:', error);
      }
    },
    
    /**
     * 更新用户权限
     * @param {Array} permissions 权限列表
     */
    updatePermissions(permissions) {
      this.permissions = permissions;
      try {
        localStorage.setItem('userPermissions', JSON.stringify(permissions || []));
      } catch (error) {
        console.error('更新用户权限到localStorage失败:', error);
      }
    },
    
    /**
     * 刷新令牌
     * @param {String} newToken 新令牌
     * @param {Number} expiresIn 过期时间(秒)
     */
    refreshToken(newToken, expiresIn = 86400) {
      this.token = newToken;
      
      // 计算新的过期时间
      const expiryTime = Date.now() + expiresIn * 1000;
      this.tokenExpiry = expiryTime;
      
      try {
        localStorage.setItem('authToken', newToken);
        localStorage.setItem('tokenExpiry', expiryTime.toString());
      } catch (error) {
        console.error('刷新令牌到localStorage失败:', error);
      }
      
      this.updateActivity();
    },

    /**
     * 清理无效的localStorage数据
     * 在应用启动时调用，确保数据完整性
     */
    cleanupInvalidStorage() {
      try {
        // 检查并清理可能损坏的localStorage数据
        const keys = ['userInfo', 'userPermissions', 'tokenExpiry', 'authToken'];
        
        keys.forEach(key => {
          const value = localStorage.getItem(key);
          if (value === 'undefined' || value === 'null') {
            console.warn(`清理无效的localStorage项: ${key}`);
            localStorage.removeItem(key);
          }
        });
        
        // 验证JSON数据的有效性
        ['userInfo', 'userPermissions'].forEach(key => {
          const value = localStorage.getItem(key);
          if (value) {
            try {
              JSON.parse(value);
            } catch (error) {
              console.warn(`清理无效的JSON数据: ${key}`, error);
              localStorage.removeItem(key);
            }
          }
        });
      } catch (error) {
        console.error('清理localStorage失败:', error);
      }
    }
  },
});

// 你可以在这里定义更多的 store
// export const useSettingsStore = defineStore('settings', { ... }); 