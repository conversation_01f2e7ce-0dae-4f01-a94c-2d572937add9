<template>
  <div class="current-membership-card">
    <a-card :bordered="false" class="membership-card">
      <div class="membership-header">
        <div class="membership-info">
          <div class="membership-title">
            <component :is="getMembershipIcon()" class="membership-icon" />
            <h4 class="membership-name">{{ membership?.会员名称 || '免费体验版' }}</h4>
            <a-tag :color="getStatusColor()" size="large" class="membership-status">
              {{ getStatusText() }}
            </a-tag>
          </div>
          <p class="membership-description">
            {{ getMembershipDescription() }}
          </p>
        </div>
        
        <div class="membership-actions">
          <a-space>
            <!-- 激活码按钮 -->
            <a-button
              type="default"
              @click="$emit('showActivation')"
              size="small"
            >
              <template #icon>
                <key-outlined />
              </template>
              激活码激活
            </a-button>
          </a-space>
        </div>
      </div>
      
      <!-- 会员到期提醒 -->
      <div v-if="membership && shouldShowExpiryWarning()" class="expiry-warning">
        <a-alert 
          :message="getExpiryWarningMessage()"
          :type="getExpiryWarningType()"
          :description="getExpiryWarningDescription()"
          show-icon
          :style="{ marginBottom: '16px' }"
        />
      </div>
      
      <!-- 会员信息展示 -->
      <div v-if="membership" class="membership-details">
        <div class="details-grid">
          <div class="detail-card">
            <div class="detail-icon">
              <calendar-outlined />
            </div>
            <div class="detail-content">
              <div class="detail-label">到期时间</div>
              <div class="detail-value">{{ formatDate(membership.到期时间) }}</div>
            </div>
          </div>
          
          <div class="detail-card">
            <div class="detail-icon">
              <clock-circle-outlined />
            </div>
            <div class="detail-content">
              <div class="detail-label">剩余天数</div>
              <div class="detail-value" :class="{ 'warning': membership.剩余天数 <= 7, 'danger': membership.剩余天数 <= 3 }">
                {{ membership.剩余天数 }}天
              </div>
            </div>
          </div>
          
          <div class="detail-card">
            <div class="detail-icon">
              <wallet-outlined />
            </div>
            <div class="detail-content">
              <div class="detail-label">月费价格</div>
              <div class="detail-value price">¥{{ membership.每月费用 || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无会员状态 -->
      <div v-else class="no-membership">
        <a-empty 
          :image="Empty.PRESENTED_IMAGE_SIMPLE" 
          description="暂无有效会员"
        >
          <template #description>
            <span class="empty-text">体验基础功能，升级享受更多权益</span>
          </template>
        </a-empty>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  CrownOutlined, 
  KeyOutlined, 
  GoldOutlined, 
  RocketOutlined,
  StarOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  WalletOutlined
} from '@ant-design/icons-vue'
import { Empty } from 'ant-design-vue'

// 组件属性
const props = defineProps({
  membership: {
    type: Object,
    default: null
  }
})

// 组件事件
const emit = defineEmits(['showActivation'])

/**
 * 获取会员图标
 */
const getMembershipIcon = () => {
  if (!props.membership) return StarOutlined
  
  const membershipName = props.membership.会员名称 || ''
  if (membershipName.includes('企业')) return GoldOutlined
  if (membershipName.includes('专业')) return RocketOutlined
  if (membershipName.includes('高级')) return CrownOutlined
  return StarOutlined
}

/**
 * 获取状态颜色
 */
const getStatusColor = () => {
  if (!props.membership) return 'default'
  
  const remainingDays = props.membership.剩余天数 || 0
  if (remainingDays <= 0) return 'error'
  if (remainingDays <= 3) return 'error'
  if (remainingDays <= 7) return 'warning'
  return 'success'
}

/**
 * 获取状态文本
 */
const getStatusText = () => {
  if (!props.membership) return '未开通'
  
  const remainingDays = props.membership.剩余天数 || 0
  if (remainingDays <= 0) return '已过期'
  if (remainingDays <= 3) return '即将过期'
  if (remainingDays <= 7) return '即将到期'
  return '有效'
}

/**
 * 获取会员描述
 */
const getMembershipDescription = () => {
  if (!props.membership) return '体验基础功能，升级享受更多权益'
  
  const membershipName = props.membership.会员名称 || ''
  if (membershipName.includes('企业')) return '享受全功能权益，助力业务增长'
  if (membershipName.includes('专业')) return '专业功能全开放，提升工作效率'
  if (membershipName.includes('高级')) return '高级功能体验，满足进阶需求'
  return `${membershipName}套餐，享受对应权益`
}

/**
 * 是否显示到期提醒
 */
const shouldShowExpiryWarning = () => {
  if (!props.membership) return false
  const remainingDays = props.membership.剩余天数 || 0
  return remainingDays <= 7
}

/**
 * 获取到期提醒消息
 */
const getExpiryWarningMessage = () => {
  if (!props.membership) return ''
  
  const remainingDays = props.membership.剩余天数 || 0
  if (remainingDays <= 0) return '会员已过期'
  return `会员还有${remainingDays}天到期`
}

/**
 * 获取到期提醒类型
 */
const getExpiryWarningType = () => {
  if (!props.membership) return 'info'
  
  const remainingDays = props.membership.剩余天数 || 0
  if (remainingDays <= 0) return 'error'
  if (remainingDays <= 3) return 'error'
  return 'warning'
}

/**
 * 获取到期提醒描述
 */
const getExpiryWarningDescription = () => {
  if (!props.membership) return ''
  
  const remainingDays = props.membership.剩余天数 || 0
  if (remainingDays <= 0) return '请及时续费以继续享受会员权益'
  return '建议提前续费，避免权益中断'
}

/**
 * 格式化日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '日期格式错误'
  }
}
</script>

<style scoped>
.current-membership-card {
  margin-bottom: 24px;
}

.membership-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.membership-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.membership-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.membership-info {
  flex: 1;
}

.membership-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.membership-icon {
  font-size: 20px;
  color: #faad14;
}

.membership-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.membership-status {
  font-weight: 500;
}

.membership-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.membership-actions {
  margin-left: 16px;
}

.expiry-warning {
  margin-bottom: 16px;
}

.membership-details {
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.detail-card {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.detail-card:hover {
  transform: translateY(-2px);
}

.detail-icon {
  font-size: 24px;
  color: #faad14;
  margin-right: 12px;
}

.detail-content {
  flex: 1;
}

.detail-label {
  font-size: 13px;
  color: #999;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.detail-value.price {
  color: #fa8c16;
}

.detail-value.warning {
  color: #fa8c16;
  font-weight: 600;
}

.detail-value.danger {
  color: #f5222d;
  font-weight: 600;
}

.no-membership {
  padding: 40px 0;
  text-align: center;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .membership-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .membership-actions {
    margin-left: 0;
    width: 100%;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .detail-card {
    padding: 16px;
  }
  
  .detail-icon {
    font-size: 20px;
  }
  
  .detail-value {
    font-size: 15px;
  }
}
</style> 