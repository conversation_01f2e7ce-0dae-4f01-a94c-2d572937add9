/**
 * CRM System K6 Stress Testing Script
 * 
 * Features:
 * - Simulate real user behavior
 * - Test core business processes
 * - Real-time system performance monitoring
 * - Safe progressive loading
 */

import http from 'k6/http'
import { check, sleep, group } from 'k6'
import { Rate, Counter, Trend } from 'k6/metrics'

// 内联配置，避免模块导入问题
const testConfig = {
  baseUrl: 'https://invite.limob.cn',
  testAccounts: {
    primary: {
      phone: '***********',
      password: '123456' // 使用配置文件中的密码
    }
  },
  testData: {
    pagination: {
      defaultPageSize: 20
    },
    searchKeywords: ['美妆', '时尚', '生活', '科技', '美食']
  },
  safetyLimits: {
    maxConcurrentUsers: 100
  },
  monitoring: {
    thresholds: {
      'http_req_duration': ['p(95)<500', 'p(99)<1000'],
      'http_req_failed': ['rate<0.01'],
      'http_reqs': ['rate>50']
    }
  }
}

// Custom metrics
const loginSuccessRate = new Rate('login_success_rate')
const talentQueryPerformance = new Trend('talent_query_performance')
const leadsSearchPerformance = new Trend('leads_search_performance')
const businessErrors = new Counter('business_errors')

// Test configuration - 轻量级测试配置
export const options = {
  stages: [
    { duration: '30s', target: 5 },
    { duration: '2m', target: 10 },
    { duration: '30s', target: 0 }
  ],
  thresholds: testConfig.monitoring.thresholds,

  // Safety limits
  discardResponseBodies: true,
  noConnectionReuse: false,

  // Tags for result analysis
  tags: {
    testType: 'crm_stress_test',
    environment: 'production'
  }
}

// Global variables
let authToken = null
let userId = null

/**
 * Test initialization
 */
export function setup() {
  console.log('🚀 Starting CRM System Stress Test')
  console.log(`📊 Target URL: ${testConfig.baseUrl}`)
  console.log(`👥 Max Concurrency: ${testConfig.safetyLimits.maxConcurrentUsers}`)
  
  return {
    startTime: new Date().toISOString()
  }
}

/**
 * Main test function
 */
export default function(data) {
  // User authentication test
  group('User Authentication Flow', () => {
    performLogin()
  })
  
  // Business function tests (only execute after successful login)
  if (authToken) {
    group('Talent Management Features', () => {
      testTalentManagement()
    })
    
    group('Leads Query Features', () => {
      testLeadsSearch()
    })
    
    group('Team Management Features', () => {
      testTeamManagement()
    })
  }
  
  // Simulate user thinking time
  sleep(Math.random() * 3 + 2) // 2-5 seconds random wait
}

/**
 * User login test
 */
function performLogin() {
  const loginData = {
    phone: testConfig.testAccounts.primary.phone,
    password: testConfig.testAccounts.primary.password
  }
  
  const response = http.post(
    `${testConfig.baseUrl}/login`,
    JSON.stringify(loginData),
    {
      headers: {
        'Content-Type': 'application/json'
      },
      tags: { name: 'login' }
    }
  )
  
  const loginSuccess = check(response, {
    'Login status code correct': (r) => r.status === 200,
    'Login response time reasonable': (r) => r.timings.duration < 1000,
    'Returns access token': (r) => {
      try {
        const body = JSON.parse(r.body)
        return body.data && body.data.访问令牌
      } catch {
        return false
      }
    }
  })
  
  loginSuccessRate.add(loginSuccess)
  
  if (loginSuccess && response.status === 200) {
    try {
      const responseData = JSON.parse(response.body)
      authToken = responseData.data?.访问令牌
      userId = responseData.data?.用户id
    } catch (e) {
      businessErrors.add(1)
      console.error('Failed to parse login response:', e)
    }
  } else {
    businessErrors.add(1)
  }
}

/**
 * Talent management feature test
 */
function testTalentManagement() {
  if (!authToken) return
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  }
  
  // Test talent pool query
  const talentPoolResponse = http.post(
    `${testConfig.baseUrl}/kol/public`,
    JSON.stringify({
      页码: 1,
      每页数量: testConfig.testData.pagination.defaultPageSize,
      筛选条件: {}
    }),
    { 
      headers,
      tags: { name: 'talent_pool' }
    }
  )
  
  const talentQuerySuccess = check(talentPoolResponse, {
    'Talent query status correct': (r) => r.status === 200,
    'Talent query response timely': (r) => r.timings.duration < 2000
  })
  
  talentQueryPerformance.add(talentPoolResponse.timings.duration)
  
  if (!talentQuerySuccess) {
    businessErrors.add(1)
  }
  
  sleep(1) // Simulate user browsing time
  
  // Test my talents query
  const myTalentsResponse = http.post(
    `${testConfig.baseUrl}/kol/my-talents`,
    JSON.stringify({
      页码: 1,
      每页数量: 10
    }),
    { 
      headers,
      tags: { name: 'my_talents' }
    }
  )
  
  check(myTalentsResponse, {
    'My talents query successful': (r) => r.status === 200
  })
}

/**
 * Leads search feature test
 */
function testLeadsSearch() {
  if (!authToken) return
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  }
  
  // Randomly select search keyword
  const keyword = testConfig.testData.searchKeywords[
    Math.floor(Math.random() * testConfig.testData.searchKeywords.length)
  ]
  
  const leadsResponse = http.post(
    `${testConfig.baseUrl}/leads/list`,
    JSON.stringify({
      页码: 1,
      每页数量: testConfig.testData.pagination.defaultPageSize,
      筛选_信息值: keyword
    }),
    { 
      headers,
      tags: { name: 'leads_search' }
    }
  )
  
  const searchSuccess = check(leadsResponse, {
    'Leads search status correct': (r) => r.status === 200,
    'Leads search response timely': (r) => r.timings.duration < 1500
  })
  
  leadsSearchPerformance.add(leadsResponse.timings.duration)
  
  if (!searchSuccess) {
    businessErrors.add(1)
  }
}

/**
 * Team management feature test
 */
function testTeamManagement() {
  if (!authToken) return
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  }
  
  // Test get team list
  const teamListResponse = http.post(
    `${testConfig.baseUrl}/team/list`,
    JSON.stringify({
      页码: 1,
      每页数量: 10
    }),
    { 
      headers,
      tags: { name: 'team_list' }
    }
  )
  
  check(teamListResponse, {
    'Team list query successful': (r) => r.status === 200,
    'Team list response timely': (r) => r.timings.duration < 1000
  })
}

/**
 * Test cleanup
 */
export function teardown(data) {
  console.log('🏁 Stress test completed')
  console.log(`⏱️  Test start time: ${data.startTime}`)
  console.log(`⏱️  Test end time: ${new Date().toISOString()}`)
  console.log('📊 Please check test results for detailed report')
}
