<template>
  <div class="membership-management">
    <!-- Header Section -->
    <div class="page-header">
      <h1>💎 会员服务中心</h1>
      <p>选择适合您的会员套餐，开启智能CRM体验</p>
    </div>

    <!-- User Membership Status Card -->
    <div class="membership-status-card">
      <a-card>
        <template #title>
          <crown-outlined />
          当前会员状态
          <a-badge 
            v-if="userMembershipInfo.是否会员" 
            :count="userMembershipInfo.会员数量" 
            :number-style="{ backgroundColor: '#52c41a' }"
            style="margin-left: 8px;"
          />
        </template>
        <template #extra>
          <a-space>
            <!-- 激活码按钮 - 所有用户都可以使用 -->
            <a-button
              type="default"
              @click="showActivationModal = true"
            >
              <template #icon>
                <key-outlined />
              </template>
              激活码激活
            </a-button>
            <!-- 立即开通按钮 - 仅非会员显示 -->
            <a-button
              v-if="!userMembershipInfo.是否会员"
              type="primary"
              @click="scrollToPlans"
            >
              立即开通
            </a-button>
          </a-space>
        </template>
        
        <div class="status-content">
          <!-- 多个会员状态显示 -->
          <div v-if="userMembershipInfo.是否会员 && userMembershipInfo.会员列表?.length > 0" class="multi-member-status">
            <div class="member-summary">
              <a-statistic
                title="有效会员套餐"
                :value="userMembershipInfo.会员数量"
                suffix="个"
                :value-style="{ color: '#3f8600' }"
              />
            </div>
            
            <div class="member-cards-grid">
              <div 
                v-for="(会员, index) in userMembershipInfo.会员列表" 
                :key="index"
                class="member-card"
                :class="{ 'expired': 会员.会员状态 === '已过期' }"
              >
                <div class="member-card-header">
                  <a-tag 
                    :color="会员.会员状态 === '有效' ? 'gold' : 'default'"
                    size="large"
                  >
                    {{ 会员.套餐信息.套餐名称 }}
                  </a-tag>
                  <a-badge 
                    :status="会员.会员状态 === '有效' ? 'success' : 'default'"
                    :text="会员.会员状态"
                  />
                </div>
                
                <div class="member-card-content">
                  <div class="member-info-row">
                    <span class="label">开通时间：</span>
                    <span class="value">{{ formatDateTime(会员.会员信息.开通时间) }}</span>
                  </div>
                  <div class="member-info-row">
                    <span class="label">到期时间：</span>
                    <span class="value">{{ formatDateTime(会员.会员信息.到期时间) }}</span>
                  </div>
                  <div class="member-info-row">
                    <span class="label">剩余天数：</span>
                    <span 
                      class="value"
                      :class="{
                        'text-warning': 会员.会员信息.剩余天数 <= 30 && 会员.会员信息.剩余天数 > 0,
                        'text-danger': 会员.会员信息.剩余天数 <= 0
                      }"
                    >
                      {{ 会员.会员信息.剩余天数 }} 天
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 非会员状态显示 -->
          <div v-else class="non-member-status">
            <div class="status-info">
              <div class="member-level">
                <a-tag color="default" size="large">
                  普通用户
                </a-tag>
              </div>
              <div class="non-member-tips">
                <p>您还不是会员，开通会员享受更多权益！</p>
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- Membership Plans Section -->
    <div id="membership-plans" class="plans-section">
      <h2>🎯 选择您的专属套餐</h2>
      <div class="plans-grid">
        <div 
          v-for="plan in membershipPlans" 
          :key="plan.会员id"
          class="plan-card"
          :class="{ 'recommended': plan.推荐 }"
        >
          <a-card>
            <template #title>
              <div class="plan-title">
                {{ plan.套餐名称 }}
                <a-badge v-if="plan.推荐" count="推荐" />
              </div>
            </template>
            
            <div class="plan-content">
              <!-- Pricing -->
              <div class="pricing-section">
                <div class="price-option">
                  <div class="price">
                    <span class="currency">¥</span>
                    <span class="amount">{{ plan.月费 }}</span>
                    <span class="period">/月</span>
                  </div>
                  <a-button 
                    type="primary" 
                    @click="createOrder(plan.会员id, 'monthly')"
                    :loading="orderLoading"
                  >
                    月付开通
                  </a-button>
                </div>
                
                <div class="price-option yearly" v-if="plan.年费优惠比例 > 0">
                  <div class="price">
                    <span class="currency">¥</span>
                    <span class="amount">{{ plan.年费 }}</span>
                    <span class="period">/年</span>
                    <a-tag color="red" size="small">
                      省{{ plan.年费优惠比例 }}%
                    </a-tag>
                  </div>
                  <a-button 
                    type="primary" 
                    @click="createOrder(plan.会员id, 'yearly')"
                    :loading="orderLoading"
                  >
                    年付开通
                  </a-button>
                </div>
              </div>
              
              <!-- Features -->
              <div class="features-section">
                <h4>🎁 专享特权</h4>
                <ul class="features-list">
                  <li v-for="feature in plan.特色功能" :key="feature">
                    <check-circle-outlined />
                    {{ feature }}
                  </li>
                </ul>
              </div>
            </div>
          </a-card>
        </div>
      </div>
    </div>

    <!-- Order History Section -->
    <div class="orders-section">
      <h2>📋 我的订单</h2>
      <a-table 
        :columns="orderColumns" 
        :data-source="orderList" 
        :pagination="{ pageSize: 10 }"
        :loading="orderListLoading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getOrderStatusColor(record.订单状态)">
              {{ record.订单状态 }}
            </a-tag>
          </template>
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button 
                size="small" 
                @click="queryOrderStatus(record.订单号)"
                :loading="queryLoading[record.订单号]"
              >
                查询状态
              </a-button>
              <a-button 
                v-if="record.订单状态 === '待支付'" 
                size="small" 
                danger
                @click="cancelOrder(record.订单号)"
                :loading="cancelLoading[record.订单号]"
              >
                取消订单
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 激活码模态框 -->
    <a-modal
      v-model:open="showActivationModal"
      title="激活码激活"
      :footer="null"
      width="500px"
      centered
    >
      <div class="activation-modal-content">
        <!-- 激活说明 -->
        <a-alert
          :message="userMembershipInfo.是否会员 ? '使用激活码延长会员时间' : '使用激活码快速激活会员'"
          :description="userMembershipInfo.是否会员 ?
            '输入有效的激活码可以延长您的会员时间，激活码对应的天数将添加到您当前的到期时间。' :
            '输入有效的激活码即可立即激活对应的会员权限，无需支付费用。'"
          type="info"
          show-icon
          style="margin-bottom: 24px;"
        />

        <!-- 激活码输入表单 -->
        <a-form
          ref="activationFormRef"
          :model="activationForm"
          :rules="activationRules"
          layout="vertical"
          @finish="handleActivation"
        >
          <a-form-item
            label="激活码"
            name="code"
            help="请输入16位激活码"
          >
            <a-input
              v-model:value="activationForm.code"
              size="large"
              placeholder="请输入激活码"
              :maxlength="16"
              allow-clear
            >
              <template #prefix>
                <key-outlined />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item style="margin-bottom: 0;">
            <a-space style="width: 100%; justify-content: flex-end;">
              <a-button @click="showActivationModal = false">
                取消
              </a-button>
              <a-button
                type="primary"
                html-type="submit"
                :loading="activationLoading"
              >
                <template #icon>
                  <rocket-outlined />
                </template>
                立即激活
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- Payment QR Code Modal -->
    <a-modal
      v-model:open="paymentModalVisible"
      title="扫码支付"
      :footer="null"
      centered
      width="400px"
    >
      <div class="payment-modal-content">
        <div class="order-info">
          <h3>{{ currentOrder.会员名称 }}</h3>
          <p>订单金额：<strong>¥{{ currentOrder.订单金额 }}</strong></p>
          <p>订单号：{{ currentOrder.订单号 }}</p>
        </div>
        
        <div class="qr-code-container">
          <div v-if="currentOrder.支付二维码" class="qr-code">
            <qrcode-vue 
              :value="currentOrder.支付二维码" 
              :size="200"
              level="M"
            />
          </div>
          <p class="payment-tips">请使用微信扫描二维码完成支付</p>
        </div>
        
        <div class="payment-actions">
          <a-button @click="paymentModalVisible = false">取消支付</a-button>
          <a-button 
            type="primary" 
            @click="checkPaymentStatus"
            :loading="paymentCheckLoading"
          >
            已完成支付
          </a-button>
        </div>
        
        <div class="payment-timeout">
          <p>订单有效期：{{ currentOrder.过期时间 }}</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  CrownOutlined,
  CheckCircleOutlined,
  KeyOutlined,
  RocketOutlined
} from '@ant-design/icons-vue'
import QrcodeVue from 'qrcode.vue'
import { orderService, authAPI } from '@/services'

/**
 * 页面数据定义
 * Define page data
 */
const membershipPlans = ref([])
const userMembershipInfo = ref({
  是否会员: false,
  会员状态: '未开通',
  会员数量: 0,
  会员列表: []
})
const orderList = ref([])
const currentOrder = ref({})

/**
 * 加载状态
 * Loading states
 */
const plansLoading = ref(false)
const orderLoading = ref(false)
const orderListLoading = ref(false)
const paymentCheckLoading = ref(false)
const activationLoading = ref(false)

/**
 * 激活码相关状态
 * Activation code related states
 */
const showActivationModal = ref(false)
const activationFormRef = ref()
const activationForm = reactive({
  code: ''
})

/**
 * 激活码表单验证规则
 * Activation form validation rules
 */
const activationRules = {
  code: [
    { required: true, message: '请输入激活码', trigger: 'blur' },
    {
      min: 16,
      max: 16,
      message: '激活码必须为16位字符',
      trigger: 'blur'
    }
  ]
}
const queryLoading = reactive({})
const cancelLoading = reactive({})

/**
 * 弹窗状态
 * Modal states
 */
const paymentModalVisible = ref(false)

/**
 * API服务实例
 * API service instances
 * 使用项目中已有的orderService替代不存在的useApiService
 */

/**
 * 表格列定义
 * Table column definitions
 */
const orderColumns = [
  {
    title: '订单号',
    dataIndex: '订单号',
    key: 'orderNumber',
    width: 200
  },
  {
    title: '会员套餐',
    dataIndex: '会员名称',
    key: 'membershipName'
  },
  {
    title: '订单金额',
    dataIndex: '订单金额',
    key: 'amount',
    render: (amount) => `¥${amount}`
  },
  {
    title: '订单状态',
    dataIndex: '订单状态',
    key: 'status'
  },
  {
    title: '创建时间',
    dataIndex: '创建时间',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'actions',
    width: 180
  }
]

/**
 * 页面初始化
 * Page initialization
 */
onMounted(async () => {
  await loadMembershipPlans()
  await loadUserMembershipInfo()
  await loadOrderList()
})

/**
 * 加载会员套餐列表
 * Load membership plans
 */
const loadMembershipPlans = async () => {
  try {
    plansLoading.value = true
    const response = await orderService.getMembershipPlans()
    
    if (response.status === 100) {
      membershipPlans.value = response.data?.套餐列表 || []
    } else {
      message.error('获取套餐列表失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('加载套餐列表失败:', error)
    message.error('加载套餐列表失败')
  } finally {
    plansLoading.value = false
  }
}

/**
 * 加载用户会员信息
 * Load user membership info
 */
const loadUserMembershipInfo = async () => {
  try {
    console.log('开始加载用户会员信息...')
    const response = await orderService.getUserMembership()
    console.log('用户会员信息接口响应:', response)

    if (response.status === 100) {
      const data = response.data || {}
      console.log('解析的会员数据:', data)

      // 适配后端返回的数据结构
      userMembershipInfo.value = {
        是否会员: data.是否会员 || false,
        会员状态: data.会员状态 || '未开通',
        会员数量: data.会员数量 || 0,
        会员列表: data.会员列表 || []
      }

      console.log('更新后的用户会员信息:', userMembershipInfo.value)
    } else {
      console.error('获取会员信息失败:', response.message)
      message.error('获取会员信息失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('加载用户会员信息失败:', error)
    message.error('加载用户会员信息失败')
  }
}

/**
 * 加载订单列表
 * Load order list
 */
const loadOrderList = async () => {
  try {
    orderListLoading.value = true
    const response = await orderService.getUserOrders()
    
    if (response.status === 100) {
      orderList.value = response.data?.订单列表 || []
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    message.error('加载订单列表失败')
  } finally {
    orderListLoading.value = false
  }
}

/**
 * 处理激活码激活
 * Handle activation code activation
 */
const handleActivation = async () => {
  try {
    activationLoading.value = true

    // 检查用户认证状态
    const token = localStorage.getItem('crm_token') || document.cookie.split('; ').find(row => row.startsWith('token='))?.split('=')[1]
    if (!token) {
      message.error('请先登录后再使用激活码')
      showActivationModal.value = false
      return
    }

    console.log('开始激活码激活，激活码:', activationForm.code)

    // 调用激活接口
    const response = await authAPI.activateAccount({
      code: activationForm.code
    })

    console.log('激活接口响应:', response)

    // 检查激活结果
    if (response.status === 200 || response.status === 100) {
      // 根据用户当前状态显示不同的成功消息
      const successMessage = userMembershipInfo.value.是否会员
        ? '激活成功！您的会员时间已延长'
        : '激活成功！您的会员权限已生效'

      message.success(successMessage)

      // 关闭模态框
      showActivationModal.value = false

      // 清空表单
      activationForm.code = ''
      activationFormRef.value?.resetFields()

      // 重新加载用户会员信息 - 确保及时更新状态
      await loadUserMembershipInfo()
      
      // 如果激活成功，也刷新订单列表（可能有相关的激活记录）
      await loadOrderList()

    } else {
      throw new Error(response.message || '激活失败')
    }

  } catch (error) {
    console.error('激活失败详细错误:', error)

    // 检查是否是认证错误
    if (error.message && (error.message.includes('登录') || error.message.includes('认证') || error.message.includes('token'))) {
      message.error('登录状态已过期，请重新登录后再试')
      showActivationModal.value = false
    } else {
      message.error(error.message || '激活失败，请检查激活码是否正确')
    }
  } finally {
    activationLoading.value = false
  }
}

/**
 * 创建订单
 * Create order
 */
const createOrder = async (membershipId, billingCycle) => {
  try {
    orderLoading.value = true
    
    const response = await orderService.createOrder(membershipId, billingCycle)
    
    if (response.status === 100) {
      currentOrder.value = response.data || {}
      paymentModalVisible.value = true
      message.success('订单创建成功，请扫码支付')
    } else {
      message.error('创建订单失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    message.error('创建订单失败')
  } finally {
    orderLoading.value = false
  }
}

/**
 * 查询订单状态
 * Query order status
 */
const queryOrderStatus = async (orderNumber) => {
  try {
    queryLoading[orderNumber] = true
    
    const response = await orderService.queryOrder(orderNumber)
    
    if (response.status === 100) {
      const status = response.data?.订单状态
      message.success(`订单状态: ${status}`)
      
      // 刷新订单列表
      await loadOrderList()
      
      // 如果支付成功，立即刷新会员信息
      if (status === '已支付') {
        await loadUserMembershipInfo()
        message.success('会员状态已更新！')
      }
    } else {
      message.error('查询订单失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('查询订单状态失败:', error)
    message.error('查询订单状态失败')
  } finally {
    queryLoading[orderNumber] = false
  }
}

/**
 * 取消订单
 * Cancel order
 */
const cancelOrder = async (orderNumber) => {
  try {
    cancelLoading[orderNumber] = true
    
    const response = await orderService.cancelOrder(orderNumber)
    
    if (response.status === 100) {
      message.success('订单取消成功')
      await loadOrderList()
    } else {
      message.error('取消订单失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('取消订单失败:', error)
    message.error('取消订单失败')
  } finally {
    cancelLoading[orderNumber] = false
  }
}

/**
 * 检查支付状态
 * Check payment status
 */
const checkPaymentStatus = async () => {
  if (!currentOrder.value.订单号) return
  
  try {
    paymentCheckLoading.value = true
    
    const response = await orderService.queryOrder(currentOrder.value.订单号)
    
    if (response.status === 100) {
      const status = response.data?.订单状态
      
      if (status === '已支付') {
        message.success('支付成功！会员权限已生效')
        paymentModalVisible.value = false
        
        // 立即刷新会员信息和订单列表
        await Promise.all([
          loadUserMembershipInfo(),
          loadOrderList()
        ])
      } else {
        message.warning('订单尚未支付，请继续支付')
      }
    }
  } catch (error) {
    console.error('检查支付状态失败:', error)
    message.error('检查支付状态失败')
  } finally {
    paymentCheckLoading.value = false
  }
}

/**
 * 滚动到套餐区域
 * Scroll to plans section
 */
const scrollToPlans = () => {
  nextTick(() => {
    const element = document.getElementById('membership-plans')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  })
}

/**
 * 获取订单状态颜色
 * Get order status color
 */
const getOrderStatusColor = (status) => {
  const colorMap = {
    '待支付': 'orange',
    '已支付': 'green',
    '已取消': 'red',
    '已退款': 'purple'
  }
  return colorMap[status] || 'default'
}

/**
 * 格式化日期时间
 * Format datetime
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  
  // 如果是字符串，转换为Date对象
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
  
  // 检查是否是有效日期
  if (isNaN(date.getTime())) return '-'
  
  // 格式化为 YYYY-MM-DD HH:mm
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.membership-management {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  font-size: 16px;
  color: #666;
}

.membership-status-card {
  margin-bottom: 32px;
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.member-level {
  margin-bottom: 16px;
}

.member-details p {
  margin: 8px 0;
  color: #666;
}

.text-warning {
  color: #faad14;
  font-weight: bold;
}

.text-danger {
  color: #ff4d4f;
  font-weight: bold;
}

.non-member-tips p {
  color: #999;
  font-style: italic;
}

/* 多会员状态显示样式 */
.multi-member-status {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.member-summary {
  display: flex;
  justify-content: center;
  padding: 16px;
  background: linear-gradient(135deg, #f6f8fa 0%, #e9ecef 100%);
  border-radius: 8px;
}

.member-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.member-card {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.member-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.member-card.expired {
  background: #f8f9fa;
  border-color: #dee2e6;
  opacity: 0.8;
}

.member-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.member-card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.member-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.member-info-row .label {
  color: #666;
  font-weight: 500;
}

.member-info-row .value {
  color: #333;
  font-weight: 600;
}

.non-member-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.non-member-status .status-info {
  text-align: center;
}

.non-member-status .member-level {
  margin-bottom: 16px;
}

.plans-section {
  margin-bottom: 32px;
}

.plans-section h2 {
  text-align: center;
  margin-bottom: 24px;
  font-size: 24px;
  color: #333;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.plan-card {
  position: relative;
  transition: transform 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-4px);
}

.plan-card.recommended {
  border: 2px solid #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.plan-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
}

.plan-content {
  padding: 16px 0;
}

.pricing-section {
  margin-bottom: 24px;
}

.price-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.price-option.yearly {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.price {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 16px;
  color: #666;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 4px;
}

.period {
  font-size: 14px;
  color: #666;
}

.features-section h4 {
  margin-bottom: 12px;
  color: #333;
}

.features-list {
  list-style: none;
  padding: 0;
}

.features-list li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.features-list li .anticon {
  color: #52c41a;
  margin-right: 8px;
}

.orders-section h2 {
  margin-bottom: 16px;
  font-size: 20px;
  color: #333;
}

.payment-modal-content {
  text-align: center;
}

.order-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.order-info h3 {
  margin-bottom: 8px;
  color: #333;
}

.order-info p {
  margin: 4px 0;
  color: #666;
}

.qr-code-container {
  margin-bottom: 24px;
}

.qr-code {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.payment-tips {
  color: #666;
  font-size: 14px;
}

.payment-actions {
  margin-bottom: 16px;
}

.payment-actions .ant-btn {
  margin: 0 8px;
}

.payment-timeout {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .membership-management {
    padding: 16px;
  }
  
  .plans-grid {
    grid-template-columns: 1fr;
  }
  
  .price-option {
    flex-direction: column;
    gap: 12px;
  }
  
  .status-content {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style> 