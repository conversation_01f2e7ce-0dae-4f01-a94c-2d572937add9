<template>
  <div class="register-container">
    <div class="register-wrapper">
      <div class="register-card">
        <div class="register-header">
          <h1 class="title">
            {{ isCustomInviteRegistration ? '受邀注册' :
               isInvitedRegistration ? '接受邀请注册' : '欢迎注册' }}
          </h1>
          <p class="subtitle">
            加入灵邀AI达人管家
          </p>

          <!-- 邀请信息显示 -->
          <div v-if="isCustomInviteRegistration && promoterInfo" class="invite-info">
            <div class="invite-card">
              <div class="invite-header" @click="toggleInviteDetails">
                <UserOutlined class="invite-icon" />
                <span class="invite-title">您收到了专属邀请</span>
                <span class="toggle-icon">
                  <DownOutlined v-if="showInviteDetails" />
                  <RightOutlined v-else />
                </span>
              </div>

              <!-- 简化显示 -->
              <div v-if="!showInviteDetails" class="invite-summary">
                <span class="summary-text">来自 </span>
                <span class="inviter-name-simple">{{ promoterInfo.推广用户昵称 }}</span>
                <span class="summary-text"> 的邀请</span>
                <a-tag v-if="promoterInfo.可以绑定" color="green" size="small" class="benefit-hint">
                  有权益
                </a-tag>
              </div>

              <!-- 详细信息 -->
              <div v-if="showInviteDetails" class="invite-content">
                <div class="inviter-intro">
                  <span class="intro-text">来自</span>
                  <span class="inviter-name">{{ promoterInfo.推广用户昵称 }}</span>
                  <span class="intro-text">的邀请</span>
                  <a-tag v-if="promoterInfo.会员名称" color="blue" class="member-badge">
                    {{ promoterInfo.会员名称 }}
                  </a-tag>
                </div>
                <div class="benefit-status">
                  <a-tag
                    :color="promoterInfo.可以绑定 ? 'success' : 'orange'"
                    class="benefit-tag"
                  >
                    <CheckCircleOutlined v-if="promoterInfo.可以绑定" />
                    <ExclamationCircleOutlined v-else />
                    {{ promoterInfo.可以绑定 ? '可享受专属权益' : '暂无特殊权益' }}
                  </a-tag>
                </div>
                <div class="benefit-description">
                  {{ promoterInfo.可以绑定 ? '注册成功后将获得专属服务支持和优先权益' : '邀请人暂未开通会员服务，无法提供额外权益' }}
                </div>
              </div>
            </div>
          </div>
        </div>



        <a-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          layout="vertical"
          class="register-form"
          @finish="handleRegister"
        >
          <!-- 手机号输入 -->
          <a-form-item name="phone" label="手机号">
            <a-input
              v-model:value="registerForm.phone"
              size="large"
              placeholder="请输入手机号"
              :prefix="h(PhoneOutlined)"
              @input="handlePhoneInput"
            />
          </a-form-item>

          <!-- 验证码输入 -->
          <a-form-item name="verification_code" label="验证码">
            <div class="code-input-group">
              <a-input
                v-model:value="registerForm.verification_code"
                size="large"
                placeholder="请输入验证码"
                :prefix="h(SafetyOutlined)"
                style="flex: 1; margin-right: 10px;"
              />
              <a-button
                size="large"
                :disabled="!canSendCode || countdown > 0"
                :loading="sendingCode"
                @click="handleSendCode"
                class="code-button"
              >
                {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
              </a-button>
            </div>
          </a-form-item>

          <!-- 密码输入 -->
          <a-form-item name="password" label="密码">
            <a-input-password
              v-model:value="registerForm.password"
              size="large"
              placeholder="请输入密码 (至少6位)"
              :prefix="h(LockOutlined)"
              @input="handlePasswordInput"
            />
            <div v-if="passwordStrengthMessage" class="password-strength">
              {{ passwordStrengthMessage }}
            </div>
          </a-form-item>

          <!-- 确认密码输入 -->
          <a-form-item name="confirmPassword" label="确认密码">
            <a-input-password
              v-model:value="registerForm.confirmPassword"
              size="large"
              placeholder="请再次输入密码"
              :prefix="h(LockOutlined)"
            />
          </a-form-item>

          <!-- 注册按钮 -->
          <a-form-item>
            <a-button
              type="primary"
              size="large"
              html-type="submit"
              :loading="registering"
              block
              class="register-button"
            >
              立即注册
            </a-button>
          </a-form-item>

          <!-- 登录链接 -->
          <div class="login-link">
            <span>已有账户？</span>
            <router-link to="/login" class="link">
              立即登录
            </router-link>
          </div>
        </a-form>

        <!-- 版权信息 -->
        <div class="footer-info">
          <p>&copy; 2024 灵邀AI达人管家. All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { authAPI, validatePassword, validatePhone, validateVerificationCode } from '@/services'
import {
  CheckCircleOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  LockOutlined,
  PhoneOutlined,
  RightOutlined,
  SafetyOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, h, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import customerInvitationService from '../services/customerInvitationService.js'
import { userAPI } from '../services/user.js'
import { useUserStore } from '../store/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单数据
const registerForm = ref({
  phone: '',
  verification_code: '',
  password: '',
  confirmPassword: ''
})

// 表单引用
const registerFormRef = ref()

// 状态管理
const registering = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)

// 邀请相关状态
const inviteCode = ref('')
const customInviteCode = ref('')  // 新增：自定义邀请码
const isInvitedRegistration = ref(false)
const isCustomInviteRegistration = ref(false)  // 新增：是否通过自定义邀请码注册
const promoterInfo = ref(null)  // 新增：推广用户信息
const showInviteDetails = ref(false) // 控制邀请详情的显示

// 组件挂载时检查是否有预填参数
onMounted(() => {
  if (route.query.phone) {
    registerForm.value.phone = route.query.phone
  }

  // 检查是否是通过邀请链接注册
  if (route.query.inviteCode) {
    inviteCode.value = route.query.inviteCode
    isInvitedRegistration.value = true
    console.log('通过邀请链接注册，邀请码:', inviteCode.value)
  }

  // 检查是否是通过自定义邀请码注册
  if (route.query.customInviteCode) {
    customInviteCode.value = route.query.customInviteCode
    isCustomInviteRegistration.value = true
    console.log('通过自定义邀请码注册，邀请码:', customInviteCode.value)

    // 验证自定义邀请码
    validateCustomInviteCode()
  }
})

// 计算属性
const canSendCode = computed(() => {
  return validatePhone(registerForm.value.phone)
})

const passwordStrengthMessage = ref('')

// 表单验证规则
const registerRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { 
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        if (!validatePhone(value)) {
          return Promise.reject(new Error('请输入正确的手机号格式'))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  verification_code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        if (!validateVerificationCode(value)) {
          return Promise.reject(new Error('验证码格式不正确'))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        const validation = validatePassword(value)
        if (!validation.isValid) {
          return Promise.reject(new Error(validation.errors.join(', ')))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()
        if (value !== registerForm.value.password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 处理手机号输入
const handlePhoneInput = () => {
  // 清除验证码（手机号改变时）
  if (registerForm.value.verification_code) {
    registerForm.value.verification_code = ''
  }
}

// 处理密码输入
const handlePasswordInput = () => {
  const password = registerForm.value.password
  if (!password) {
    passwordStrengthMessage.value = ''
    return
  }
  
  const validation = validatePassword(password)
  if (validation.isValid) {
    passwordStrengthMessage.value = '密码强度：良好'
  } else {
    passwordStrengthMessage.value = `密码要求：${validation.errors.join(', ')}`
  }
}

// 切换邀请详情显示
const toggleInviteDetails = () => {
  showInviteDetails.value = !showInviteDetails.value
}

// 验证自定义邀请码
const validateCustomInviteCode = async () => {
  if (!customInviteCode.value) return

  try {
    console.log('验证自定义邀请码:', customInviteCode.value)

    const response = await userAPI.validateCustomInviteCode({
      自定义邀请码: customInviteCode.value
    })

    if (response && response.status === 100) {
      promoterInfo.value = response.data
      console.log('邀请码验证成功，推广用户信息:', promoterInfo.value)

      if (promoterInfo.value.可以绑定) {
        message.success(`邀请码有效！推广用户：${promoterInfo.value.推广用户昵称}`)
      } else {
        message.warning('邀请码有效，但推广用户不是会员，无法建立推广关系')
      }
    } else {
      const errorMsg = response?.message || '邀请码验证失败'
      message.error(errorMsg)
      isCustomInviteRegistration.value = false
      customInviteCode.value = ''
    }
  } catch (error) {
    console.error('验证自定义邀请码失败:', error)
    message.error('验证邀请码失败，请稍后重试')
    isCustomInviteRegistration.value = false
    customInviteCode.value = ''
  }
}

// 发送验证码
const handleSendCode = async () => {
  if (!canSendCode.value) {
    message.error('请先输入正确的手机号')
    return
  }

  try {
    sendingCode.value = true
    
    await authAPI.sendSmsCode({
      phone: registerForm.value.phone,
      type: 'register'
    })
    
    message.success('验证码已发送，请查收短信')
    
    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    message.error(error.message || '发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 处理注册
const handleRegister = async () => {
  try {
    registering.value = true

    // 构建注册请求参数，包含邀请码（如果存在）
    const registerParams = {
      phone: registerForm.value.phone,
      password: registerForm.value.password,
      verification_code: registerForm.value.verification_code
    }

    // 如果是通过邀请注册，添加邀请码参数到注册请求中
    if (isInvitedRegistration.value && inviteCode.value) {
      registerParams.invite_code = inviteCode.value
      console.log('注册请求包含邀请码:', inviteCode.value)
    }

    // 使用用户store的注册方法，支持自动登录
    const result = await userStore.register(registerParams)

    if (result.success) {
      message.success('注册成功！')

      // 处理自定义邀请码关联
      if (isCustomInviteRegistration.value && customInviteCode.value && result.user?.id) {
        try {
          console.log('处理自定义邀请码注册关联')
          const associationResult = await userAPI.processCustomInviteRegistration({
            手机号: registerForm.value.phone,
            用户id: result.user.id,
            自定义邀请码: customInviteCode.value
          })

          if (associationResult && associationResult.status === 100) {
            console.log('自定义邀请码关联成功:', associationResult.data)
            message.success(`推广关系建立成功！推广用户：${associationResult.data.推广用户昵称}`)
          } else {
            console.warn('自定义邀请码关联失败:', associationResult)
            // 不影响注册流程，只记录错误
          }
        } catch (error) {
          console.error('自定义邀请码关联处理失败:', error)
          // 不影响注册流程，只记录错误
        }
      }

      // 如果后端注册接口未处理邀请关联（作为备用机制），前端再次处理
      if (isInvitedRegistration.value && inviteCode.value && result.user?.id) {
        try {
          console.log('执行前端邀请关联备用处理')
          await customerInvitationService.processRegistrationInvitation(
            registerForm.value.phone,
            result.user.id
          )
          console.log('前端邀请关联处理成功')
        } catch (error) {
          console.error('前端邀请关联处理失败:', error)
          // 不影响注册流程，只记录错误
        }
      }

      if (result.autoLogin) {
        // 注册后自动登录成功，跳转到redirect页面或默认页面
        const redirect = route.query.redirect || '/dashboard'
        message.success('自动登录成功，即将跳转...')
        await router.push(redirect)
      } else {
        // 注册成功但需要手动登录
        message.info('请使用手机号和密码登录')
    router.push({
      path: '/login',
          query: {
            phone: registerForm.value.phone,
            redirect: route.query.redirect
          }
    })
      }
    } else {
      message.error(result.message || '注册失败，请检查信息后重试')
    }
    
  } catch (error) {
    console.error('注册失败:', error)
    message.error(error.message || '注册失败，请检查信息后重试')
  } finally {
    registering.value = false
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-wrapper {
  width: 100%;
  max-width: 400px;
}

.register-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.register-form {
  margin-bottom: 24px;
}

.code-input-group {
  display: flex;
  align-items: center;
}

.code-button {
  min-width: 120px;
  border-color: #667eea;
  color: #667eea;
}

.code-button:hover {
  border-color: #764ba2;
  color: #764ba2;
}

.password-strength {
  font-size: 12px;
  margin-top: 4px;
  color: #666;
}

.register-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.register-button:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.login-link {
  text-align: center;
  color: #666;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  margin-left: 4px;
}

.link:hover {
  color: #764ba2;
}

.footer-info {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.footer-info p {
  color: #999;
  font-size: 12px;
  margin: 0;
}

/* 邀请信息样式 - 优化设计，减少营销感 */
.invite-info {
  margin: 16px 0;
  display: flex;
  justify-content: center;
}

.invite-card {
  background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%);
  border: 1px solid #e0f2fe;
  border-radius: 8px;
  padding: 18px;
  width: 100%;
  max-width: 380px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.invite-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.invite-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
  padding: 6px 8px 10px 8px;
  border-bottom: 1px solid #e0f2fe;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 4px;
}

.invite-header:hover {
  background-color: rgba(8, 145, 178, 0.03);
}

.invite-icon {
  font-size: 16px;
  color: #0891b2;
  margin-right: 8px;
}

.invite-title {
  font-size: 15px;
  font-weight: 500;
  color: #0f172a;
}

.invite-content {
  space-y: 10px;
}

.inviter-intro {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
  justify-content: center;
}

.intro-text {
  color: #64748b;
  font-size: 14px;
}

.inviter-name {
  color: #0f172a;
  font-size: 15px;
  font-weight: 500;
  padding: 2px 8px;
  background: rgba(8, 145, 178, 0.1);
  border-radius: 4px;
}

.member-badge {
  font-size: 11px;
  border-radius: 3px;
  margin-left: 4px;
}

.benefit-status {
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}

.benefit-tag {
  font-size: 12px;
  padding: 3px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.benefit-description {
  color: #64748b;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid rgba(8, 145, 178, 0.1);
}

/* 新增样式 - 切换图标和简化显示 */
.toggle-icon {
  color: #64748b;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.invite-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 4px;
  padding: 4px 0;
}

.summary-text {
  color: #64748b;
  font-size: 13px;
}

.inviter-name-simple {
  color: #0f172a;
  font-size: 14px;
  font-weight: 500;
  padding: 1px 6px;
  background: rgba(8, 145, 178, 0.08);
  border-radius: 3px;
}

.benefit-hint {
  font-size: 10px;
  margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-card {
    padding: 24px;
    margin: 10px;
  }

  .title {
    font-size: 24px;
  }

  .code-button {
    min-width: 100px;
    font-size: 12px;
  }

  .invite-card {
    padding: 14px;
    margin: 0 8px;
    max-width: 100%;
  }

  .inviter-intro {
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .invite-title {
    font-size: 14px;
  }

  .inviter-name {
    font-size: 14px;
  }

  .benefit-description {
    font-size: 11px;
    padding: 6px 8px;
  }
}
</style> 