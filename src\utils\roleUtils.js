/**
 * 角色处理工具库
 * 统一管理角色相关的显示逻辑、颜色映射、图标等
 */

/**
 * 简化的角色类型枚举（只有三个固定角色）
 */
export const ROLE_TYPES = {
  FOUNDER: 'founder',    // 创始人/创建者
  LEADER: 'leader',      // 负责人
  MEMBER: 'member'       // 成员
}

/**
 * 简化的角色配置（直接使用中文）
 */
export const ROLE_CONFIG = {
  '创建者': { color: 'gold', icon: '👑', level: 100 },
  '负责人': { color: 'orange', icon: '🎯', level: 80 },
  '成员': { color: 'default', icon: '👤', level: 20 }
}

/**
 * 获取角色显示名称（简化版，直接返回中文角色名）
 * @param {string|Object} role - 角色字符串或角色对象
 * @returns {string} 角色显示名称
 */
export function getRoleDisplayName(role) {
  // 处理字符串类型的角色
  if (typeof role === 'string') {
    // 如果已经是中文角色名，直接返回
    if (ROLE_CONFIG[role]) {
      return role
    }
    
    // 处理英文映射到中文
    const englishToChinese = {
      'founder': '创建者',
      'leader': '负责人', 
      'member': '成员'
    }
    
    return englishToChinese[role] || role
  }
  
  // 处理对象类型的角色
  if (typeof role === 'object' && role !== null) {
    const roleType = role.角色类型 || role.我的角色类型 || role.角色 || role.我的角色
    return getRoleDisplayName(roleType)
  }
  
  return '成员'
}

/**
 * 获取角色颜色（简化版）
 * @param {string|Object} role - 角色字符串或角色对象
 * @returns {string} 角色对应的颜色
 */
export function getRoleColor(role) {
  const roleName = getRoleDisplayName(role)
  return ROLE_CONFIG[roleName]?.color || 'default'
}

/**
 * 获取角色图标（简化版）
 * @param {string|Object} role - 角色字符串或角色对象
 * @returns {string} 角色对应的图标
 */
export function getRoleIcon(role) {
  const roleName = getRoleDisplayName(role)
  return ROLE_CONFIG[roleName]?.icon || '👤'
}

/**
 * 判断角色级别（简化版）
 * @param {string|Object} role - 角色类型
 * @returns {number} 角色级别（数字越大权限越高）
 */
export function getRoleLevel(role) {
  const roleName = getRoleDisplayName(role)
  return ROLE_CONFIG[roleName]?.level || 0
}

/**
 * 比较两个角色的权限级别
 * @param {string} role1 - 角色1
 * @param {string} role2 - 角色2
 * @returns {number} 1表示role1权限更高，-1表示role2权限更高，0表示相等
 */
export function compareRoleLevel(role1, role2) {
  const level1 = getRoleLevel(role1)
  const level2 = getRoleLevel(role2)
  
  if (level1 > level2) return 1
  if (level1 < level2) return -1
  return 0
}

/**
 * 检查角色是否有管理权限（简化版）
 * @param {string|Object} role - 角色类型
 * @returns {boolean} 是否有管理权限
 */
export function hasManagementPermission(role) {
  const roleName = getRoleDisplayName(role)
  return ['创建者', '负责人'].includes(roleName)
}

/**
 * 获取简化的角色选项列表
 * @param {boolean} includeFounder - 是否包含创建者选项（通常不包含）
 * @returns {Array} 角色选项数组
 */
export function getRoleOptions(includeFounder = false) {
  const standardRoles = [
    { 
      value: '成员', 
      label: `👤 成员`,
      level: 20
    },
    { 
      value: '负责人', 
      label: `🎯 负责人`,
      level: 80
    }
  ]
  
  // 如果需要包含创建者选项（极少数情况）
  if (includeFounder) {
    standardRoles.push({
      value: '创建者',
      label: `👑 创建者`,
      level: 100
    })
  }
  
  return standardRoles.sort((a, b) => b.level - a.level)
} 