"""
LangChain智能体错误处理器 - 统一的错误处理机制
提供标准化的错误处理和日志记录功能
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
import traceback

# 获取日志器
智能体错误日志器 = logging.getLogger("LangChain智能体错误处理器")


class 智能体错误处理器:
    """统一的智能体错误处理器"""

    @staticmethod
    def _构建标准错误响应(
        error_type: str,
        error_message: str,
        fallback_action: str = "使用默认值",
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """构建标准错误响应格式"""
        response = {
            "success": False,
            "error_type": error_type,
            "error_message": error_message,
            "fallback_action": fallback_action,
            "timestamp": datetime.now().isoformat()
        }
        
        if additional_data:
            response.update(additional_data)
            
        return response

    @staticmethod
    def 处理RAG检索错误(error: Exception, 智能体id: int) -> Dict[str, Any]:
        """处理RAG检索相关错误"""
        try:
            error_message = str(error)
            智能体错误日志器.error(
                f"RAG检索错误 - 智能体id: {智能体id}, 错误: {error_message}",
                extra={
                    "智能体id": 智能体id,
                    "错误类型": "RAG检索错误",
                    "堆栈": traceback.format_exc()
                }
            )
            
            return 智能体错误处理器._构建标准错误响应(
                error_type="RAG_RETRIEVAL_ERROR",
                error_message=f"RAG检索失败: {error_message}",
                fallback_action="返回空上下文",
                additional_data={
                    "智能体id": 智能体id,
                    "建议": "检查知识库连接和配置"
                }
            )
            
        except Exception as e:
            智能体错误日志器.critical(f"错误处理器自身异常: {str(e)}")
            return 智能体错误处理器._构建标准错误响应(
                error_type="ERROR_HANDLER_FAILURE",
                error_message="错误处理器异常",
                fallback_action="使用最小化错误响应"
            )

    @staticmethod
    def 处理JSON格式化错误(error: Exception, 原始内容: str) -> Dict[str, Any]:
        """处理JSON格式化相关错误"""
        try:
            error_message = str(error)
            智能体错误日志器.error(
                f"JSON格式化错误: {error_message}",
                extra={
                    "错误类型": "JSON格式化错误",
                    "原始内容长度": len(原始内容),
                    "内容预览": 原始内容[:200] if 原始内容 else "空内容",
                    "堆栈": traceback.format_exc()
                }
            )
            
            return 智能体错误处理器._构建标准错误响应(
                error_type="JSON_FORMAT_ERROR",
                error_message=f"JSON格式化失败: {error_message}",
                fallback_action="返回纯文本格式",
                additional_data={
                    "原始内容长度": len(原始内容),
                    "建议": "检查JSON Schema配置"
                }
            )
            
        except Exception as e:
            智能体错误日志器.critical(f"JSON错误处理器异常: {str(e)}")
            return 智能体错误处理器._构建标准错误响应(
                error_type="ERROR_HANDLER_FAILURE",
                error_message="JSON错误处理器异常",
                fallback_action="返回基础文本响应"
            )

    @staticmethod
    def 处理工具调用错误(error: Exception, 工具名称: str, 用户id: int) -> Dict[str, Any]:
        """处理工具调用相关错误"""
        try:
            error_message = str(error)
            智能体错误日志器.error(
                f"工具调用错误 - 工具: {工具名称}, 用户id: {用户id}, 错误: {error_message}",
                extra={
                    "工具名称": 工具名称,
                    "用户id": 用户id,
                    "错误类型": "工具调用错误",
                    "堆栈": traceback.format_exc()
                }
            )
            
            return 智能体错误处理器._构建标准错误响应(
                error_type="TOOL_CALL_ERROR",
                error_message=f"工具调用失败: {error_message}",
                fallback_action="跳过工具调用",
                additional_data={
                    "工具名称": 工具名称,
                    "用户id": 用户id,
                    "建议": "检查工具权限和配置"
                }
            )
            
        except Exception as e:
            智能体错误日志器.critical(f"工具错误处理器异常: {str(e)}")
            return 智能体错误处理器._构建标准错误响应(
                error_type="ERROR_HANDLER_FAILURE",
                error_message="工具错误处理器异常",
                fallback_action="禁用工具调用"
            )

    @staticmethod
    def 处理链构建错误(error: Exception, 链类型: str) -> Dict[str, Any]:
        """处理链构建相关错误"""
        try:
            error_message = str(error)
            智能体错误日志器.error(
                f"链构建错误 - 链类型: {链类型}, 错误: {error_message}",
                extra={
                    "链类型": 链类型,
                    "错误类型": "链构建错误",
                    "堆栈": traceback.format_exc()
                }
            )
            
            return 智能体错误处理器._构建标准错误响应(
                error_type="CHAIN_BUILD_ERROR",
                error_message=f"链构建失败: {error_message}",
                fallback_action="使用基础对话链",
                additional_data={
                    "链类型": 链类型,
                    "建议": "检查模型配置和提示词模板"
                }
            )
            
        except Exception as e:
            智能体错误日志器.critical(f"链构建错误处理器异常: {str(e)}")
            return 智能体错误处理器._构建标准错误响应(
                error_type="ERROR_HANDLER_FAILURE",
                error_message="链构建错误处理器异常",
                fallback_action="使用最简单的对话模式"
            )

    @staticmethod
    def 处理通用错误(error: Exception, 操作描述: str, 上下文: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理通用错误"""
        try:
            error_message = str(error)
            智能体错误日志器.error(
                f"通用错误 - 操作: {操作描述}, 错误: {error_message}",
                extra={
                    "操作描述": 操作描述,
                    "错误类型": "通用错误",
                    "上下文": 上下文 or {},
                    "堆栈": traceback.format_exc()
                }
            )
            
            return 智能体错误处理器._构建标准错误响应(
                error_type="GENERAL_ERROR",
                error_message=f"{操作描述}失败: {error_message}",
                fallback_action="使用默认处理",
                additional_data={
                    "操作描述": 操作描述,
                    "上下文": 上下文 or {}
                }
            )
            
        except Exception as e:
            智能体错误日志器.critical(f"通用错误处理器异常: {str(e)}")
            return 智能体错误处理器._构建标准错误响应(
                error_type="ERROR_HANDLER_FAILURE",
                error_message="通用错误处理器异常",
                fallback_action="最小化错误处理"
            )

    @staticmethod
    def 记录成功操作(操作描述: str, 详细信息: Optional[Dict[str, Any]] = None):
        """记录成功操作的日志"""
        智能体错误日志器.info(
            f"操作成功: {操作描述}",
            extra={
                "操作描述": 操作描述,
                "详细信息": 详细信息 or {},
                "时间戳": datetime.now().isoformat()
            }
        )
