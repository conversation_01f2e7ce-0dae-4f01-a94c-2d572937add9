import logging

from .日志配置 import 应用日志器 as 系统日志器

# 根据系统负载自动调整设置
try:
    import psutil

    CPU核心数 = psutil.cpu_count(logical=False) or 1
    系统日志器.info(f"实时日志系统性能配置已根据系统负载调整 (CPU核心数: {CPU核心数})")
except ImportError:
    pass

# 日志级别映射
日志级别映射 = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
}

# 日志类型列表
有效日志类型 = ["系统", "错误", "接口", "数据库", "安全"]


# 简化的日志管理器，移除WebSocket功能
class 简化日志管理器:
    """基础日志管理功能"""

    def __init__(self):
        # 基础设置
        self.已初始化 = False

    async def 启动(self):
        """启动日志系统"""
        if not self.已初始化:
            系统日志器.info("简化日志管理器已启动")
            self.已初始化 = True

    async def 关闭(self):
        """关闭日志系统"""
        if self.已初始化:
            系统日志器.info("简化日志管理器已关闭")
            self.已初始化 = False


# 创建日志管理器实例
实时日志管理器 = 简化日志管理器()


# 初始化函数
async def 初始化实时日志系统():
    """初始化实时日志系统"""
    # 启动日志管理器
    await 实时日志管理器.启动()
    系统日志器.info("实时日志系统已初始化")
