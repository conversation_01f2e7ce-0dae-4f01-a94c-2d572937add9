<template>
  <div class="douyin-product-detail">
    <div v-if="product" class="product-content">
      <!-- 商品基本信息 -->
      <div class="basic-info-section">
        <div class="section-header">
          <h3>商品信息</h3>
          <a-space>
            <a-button size="small" @click="refreshProduct">刷新</a-button>
            <a-button size="small" @click="viewOnDouyin" type="primary">查看原商品</a-button>
          </a-space>
        </div>

        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="商品名称">
            {{ product.商品名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="商品ID">
            {{ product.商品ID }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(product.状态)">
              {{ product.状态 }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="价格">
            ¥{{ product.当前价格 || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="销量">
            {{ product.销量 || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="库存">
            {{ product.库存 || '未知' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>

    <a-empty v-else description="暂无商品信息" />
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'

/**
 * 组件属性定义
 */
const props = defineProps({
  product: {
    type: Object,
    default: () => null
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['refresh'])

/**
 * 获取状态对应的颜色
 */
const getStatusColor = (status) => {
  const colorMap = {
    '在售': 'green',
    '缺货': 'orange',
    '下架': 'red',
    '停售': 'gray'
  }
  return colorMap[status] || 'blue'
}

/**
 * 刷新商品数据
 */
const refreshProduct = () => {
  message.success('商品数据已刷新')
  emit('refresh')
}

/**
 * 在抖音中查看商品
 */
const viewOnDouyin = () => {
  message.info('跳转到抖音商品页面')
}

// 组件名称定义
defineOptions({
  name: 'DouyinProductDetail'
})
</script>

<style scoped>
.douyin-product-detail {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1d;
}

.basic-info-section {
  margin-bottom: 24px;
}
</style> 