"""
通告管理路由
提供通告相关的API接口
主要功能：
1. 获取通告列表
2. 获取通告详情
3. 创建通告
4. 更新通告
5. 删除通告
"""

from typing import List, Optional

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户
from 数据.通告数据 import (
    异步创建通告,
    异步删除通告,
    异步更新通告,
    异步获取通告列表,
    异步获取通告详情,
)
from 数据模型.响应模型 import 统一响应模型
from 日志 import 错误日志器

# 创建通告管理路由
通告管理路由 = APIRouter(tags=["通告管理"])

# ==================== 数据模型 ====================


class 通告创建请求(BaseModel):
    """通告创建请求模型"""

    标题: str = Field(..., description="通告标题")
    类型: str = Field(default="通知", description="通告类型")
    重要性: int = Field(default=1, description="重要性（1-5）")
    操作标识: int = Field(default=0, description="操作标识")
    排序: int = Field(default=0, description="排序")
    已发布: bool = Field(default=False, description="是否已发布")
    内容: Optional[List[dict]] = Field(default=[], description="通告内容")
    开始时间: Optional[str] = Field(None, description="开始时间")
    结束时间: Optional[str] = Field(None, description="结束时间")


class 通告更新请求(通告创建请求):
    """通告更新请求模型"""

    通告id: int = Field(..., description="通告id")


class 通告列表请求(BaseModel):
    """通告列表请求模型"""

    页码: int = Field(default=1, description="页码")
    页面大小: int = Field(default=10, description="每页大小")
    类型筛选: Optional[str] = Field(None, description="类型筛选")
    状态筛选: Optional[str] = Field(None, description="状态筛选")


# ==================== 路由接口 ====================


@通告管理路由.post("/list", summary="获取通告列表")
async def 获取通告列表接口(
    请求数据: 通告列表请求, 当前用户: dict = Depends(获取当前用户)
):
    """
    获取通告列表接口

    功能说明：
    - 支持分页查询
    - 支持按类型和状态筛选
    - 返回通告基本信息列表

    参数：
    - 页码：页码，从1开始
    - 页面大小：每页返回的记录数
    - 类型筛选：可选，按通告类型筛选
    - 状态筛选：可选，按发布状态筛选

    返回：
    - 成功：通告列表和分页信息
    - 失败：错误信息
    """
    try:
        结果 = await 异步获取通告列表(
            页码=请求数据.页码,
            页面大小=请求数据.页面大小,
            类型筛选=请求数据.类型筛选,
            状态筛选=请求数据.状态筛选,
        )

        if 结果 and 结果.get("通告列表") is not None:
            return 统一响应模型(status=100, data=结果, message="获取通告列表成功")
        else:
            return 统一响应模型(status=101, data=None, message="获取通告列表失败")

    except Exception as e:
        错误日志器.error(f"获取通告列表失败: {e}", exc_info=True)
        return 统一响应模型(status=500, data=None, message="获取通告列表失败")


@通告管理路由.post("/detail", summary="获取通告详情")
async def 获取通告详情接口_POST(
    请求数据: dict = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取通告详情接口 (POST方式)

    功能说明：
    - 通过POST请求获取指定通告的详细信息
    - 支持管理前端的API调用方式

    参数：
    - 通告id：要获取的通告id

    返回：
    - 成功：通告详细信息
    - 失败：错误信息
    """
    try:
        通告id = 请求数据.get("通告id")
        if not 通告id:
            return 统一响应模型(status=400, data=None, message="缺少通告id")

        # 确保通告id是整数类型
        try:
            通告id = int(通告id)
        except (ValueError, TypeError):
            return 统一响应模型(status=400, data=None, message="通告id格式错误")

        结果 = await 异步获取通告详情(通告id)

        if 结果:
            return 统一响应模型(status=100, data=结果, message="获取通告详情成功")
        else:
            return 统一响应模型(status=404, data=None, message="通告不存在")

    except Exception as e:
        错误日志器.error(f"获取通告详情失败: {e}", exc_info=True)
        return 统一响应模型(status=500, data=None, message="获取通告详情失败")


@通告管理路由.get("/detail/{通告id}", summary="获取通告详情")
async def 获取通告详情接口_GET(通告id: int, 当前用户: dict = Depends(获取当前用户)):
    """
    获取通告详情接口

    功能说明：
    - 根据通告id获取详细信息
    - 包含完整的通告内容和元数据

    参数：
    - 通告id：通告的唯一标识

    返回：
    - 成功：完整的通告信息
    - 失败：错误信息
    """
    try:
        结果 = await 异步获取通告详情(通告id)

        if 结果:
            return 统一响应模型(status=100, data=结果, message="获取通告详情成功")
        else:
            return 统一响应模型(status=404, data=None, message="通告不存在")

    except Exception as e:
        错误日志器.error(f"获取通告详情失败: {e}", exc_info=True)
        return 统一响应模型(status=500, data=None, message="获取通告详情失败")


@通告管理路由.post("/create", summary="创建通告")
async def 创建通告接口(请求数据: 通告创建请求, 当前用户: dict = Depends(获取当前用户)):
    """
    创建通告接口

    功能说明：
    - 创建新的通告记录
    - 支持富文本内容
    - 支持定时发布

    参数：
    - 标题：通告标题
    - 类型：通告类型
    - 内容：通告内容（支持多种格式）
    - 其他配置参数

    返回：
    - 成功：新创建的通告信息
    - 失败：错误信息
    """
    try:
        结果 = await 异步创建通告(
            标题=请求数据.标题,
            类型=请求数据.类型,
            重要性=请求数据.重要性,
            操作标识=请求数据.操作标识,
            排序=请求数据.排序,
            已发布=请求数据.已发布,
            内容=请求数据.内容,
            开始时间=请求数据.开始时间,
            结束时间=请求数据.结束时间,
            创建者ID=当前用户["id"],
        )

        if 结果["success"]:
            return 统一响应模型(status=100, data=结果["data"], message=结果["message"])
        else:
            return 统一响应模型(status=101, data=None, message=结果["message"])

    except Exception as e:
        错误日志器.error(f"创建通告失败: {e}", exc_info=True)
        return 统一响应模型(status=500, data=None, message="创建通告失败")


@通告管理路由.post("/update", summary="更新通告")
async def 更新通告接口(请求数据: 通告更新请求, 当前用户: dict = Depends(获取当前用户)):
    """
    更新通告接口

    功能说明：
    - 更新现有通告的信息
    - 支持部分字段更新
    - 保留创建时间等元数据

    参数：
    - 通告id：要更新的通告id
    - 其他要更新的字段

    返回：
    - 成功：更新后的通告信息
    - 失败：错误信息
    """
    try:
        结果 = await 异步更新通告(
            通告id=请求数据.通告id,
            标题=请求数据.标题,
            类型=请求数据.类型,
            重要性=请求数据.重要性,
            操作标识=请求数据.操作标识,
            排序=请求数据.排序,
            已发布=请求数据.已发布,
            内容=请求数据.内容,
            开始时间=请求数据.开始时间,
            结束时间=请求数据.结束时间,
            更新者ID=当前用户["id"],
        )

        if 结果:
            return 统一响应模型(status=100, data=结果, message="更新通告成功")
        else:
            return 统一响应模型(status=404, data=None, message="通告不存在或更新失败")

    except Exception as e:
        错误日志器.error(f"更新通告失败: {e}", exc_info=True)
        return 统一响应模型(status=500, data=None, message="更新通告失败")


@通告管理路由.delete("/delete/{通告id}", summary="删除通告")
async def 删除通告接口(通告id: int, 当前用户: dict = Depends(获取当前用户)):
    """
    删除通告接口

    功能说明：
    - 删除指定的通告记录
    - 软删除，保留数据用于审计

    参数：
    - 通告id：要删除的通告id

    返回：
    - 成功：删除确认信息
    - 失败：错误信息
    """
    try:
        结果 = await 异步删除通告(通告id, 当前用户["id"])

        if 结果["success"]:
            return 统一响应模型(status=100, data=None, message=结果["message"])
        else:
            return 统一响应模型(status=404, data=None, message=结果["message"])

    except Exception as e:
        错误日志器.error(f"删除通告失败: {e}", exc_info=True)
        return 统一响应模型(status=500, data=None, message="删除通告失败")
