<template>
  <div class="store-page product-management-page">
    <!-- 页面头部 -->
    <div class="store-page-header">
      <h1 class="store-page-title">
        <appstore-outlined class="store-title-icon" />
        产品管理
      </h1>
      <p class="store-page-description">管理您的产品信息，支持智能识别和手动录入</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <ProductManagementModule />
    </div>
  </div>
</template>

<script setup>
import { AppstoreOutlined } from '@ant-design/icons-vue'
import ProductManagementModule from '@/components/store/ProductManagementModule.vue'
import '@/assets/css/store-common.css'

defineOptions({
  name: 'ProductManagement'
})
</script>

<style scoped>
/* 页面特有样式 */
.page-content {
  background: #ffffff;
}
</style>
