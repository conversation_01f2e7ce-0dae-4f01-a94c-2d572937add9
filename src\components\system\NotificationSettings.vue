<template>
  <div class="notification-settings">
    <a-card title="通知设置" :bordered="false" class="settings-card">
      <!-- 推送通知设置 -->
      <div class="notification-section">
        <h3 class="section-title">
          <BellOutlined class="title-icon" />
          推送通知
        </h3>
        
        <div class="setting-item">
          <div class="setting-info">
            <h4>系统通知</h4>
            <p>接收系统重要通知和公告</p>
          </div>
          <a-switch 
            v-model:checked="settings.systemNotification"
            @change="handleSettingChange('systemNotification', $event)"
          />
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <h4>邮件通知</h4>
            <p>通过邮件接收重要信息</p>
          </div>
          <a-switch 
            v-model:checked="settings.emailNotification"
            @change="handleSettingChange('emailNotification', $event)"
          />
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <h4>短信通知</h4>
            <p>通过短信接收紧急通知</p>
          </div>
          <a-switch 
            v-model:checked="settings.smsNotification"
            @change="handleSettingChange('smsNotification', $event)"
          />
        </div>
      </div>

      <!-- 业务通知设置 */
      <div class="notification-section">
        <h3 class="section-title">
          <MessageOutlined class="title-icon" />
          业务通知
        </h3>

        <div class="setting-item">
          <div class="setting-info">
            <h4>达人管理通知</h4>
            <p>达人状态变更、合作进展等通知</p>
          </div>
          <a-switch 
            v-model:checked="settings.talentNotification"
            @change="handleSettingChange('talentNotification', $event)"
          />
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <h4>团队协作通知</h4>
            <p>团队成员动态、任务分配等通知</p>
          </div>
          <a-switch 
            v-model:checked="settings.teamNotification"
            @change="handleSettingChange('teamNotification', $event)"
          />
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <h4>财务通知</h4>
            <p>付费提醒、账单信息等通知</p>
          </div>
          <a-switch 
            v-model:checked="settings.financialNotification"
            @change="handleSettingChange('financialNotification', $event)"
          />
        </div>
      </div>

      <!-- 通知时间设置 -->
      <div class="notification-section">
        <h3 class="section-title">
          <ClockCircleOutlined class="title-icon" />
          通知时间
        </h3>

        <div class="setting-item">
          <div class="setting-info">
            <h4>免打扰时段</h4>
            <p>在指定时间段内不接收非紧急通知</p>
          </div>
          <a-switch 
            v-model:checked="settings.quietHours.enabled"
            @change="handleQuietHoursChange"
          />
        </div>

        <div v-if="settings.quietHours.enabled" class="time-range-setting">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-time-picker
                v-model:value="settings.quietHours.startTime"
                format="HH:mm"
                placeholder="开始时间"
                @change="handleTimeChange('startTime', $event)"
              />
            </a-col>
            <a-col :span="12">
              <a-time-picker
                v-model:value="settings.quietHours.endTime"
                format="HH:mm"
                placeholder="结束时间"
                @change="handleTimeChange('endTime', $event)"
              />
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="actions">
        <a-button type="primary" @click="handleSave" :loading="saving">
          保存设置
        </a-button>
        <a-button @click="handleReset" style="margin-left: 8px;">
          重置默认
        </a-button>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  BellOutlined,
  MessageOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'

// 通知设置组件 - 管理用户的各种通知偏好设置
defineOptions({
  name: 'NotificationSettings'
})

// 定义事件
const emit = defineEmits(['save'])

// 响应式数据
const saving = ref(false)

// 通知设置数据
const settings = reactive({
  systemNotification: true,
  emailNotification: true,
  smsNotification: false,
  talentNotification: true,
  teamNotification: true,
  financialNotification: true,
  quietHours: {
    enabled: false,
    startTime: dayjs('22:00', 'HH:mm'),
    endTime: dayjs('08:00', 'HH:mm')
  }
})

// 处理单个设置项变化
const handleSettingChange = (key, value) => {
  console.log(`通知设置变化: ${key} = ${value}`)
  // 这里可以实时保存或者标记为待保存状态
}

// 处理免打扰时段开关变化
const handleQuietHoursChange = (enabled) => {
  console.log(`免打扰时段${enabled ? '开启' : '关闭'}`)
}

// 处理时间变化
const handleTimeChange = (timeType, time) => {
  console.log(`${timeType}时间变化:`, time)
}

// 保存设置
const handleSave = async () => {
  saving.value = true
  try {
    // 这里应该调用API保存通知设置
    // await saveNotificationSettings(settings)
    
    message.success('通知设置保存成功')
    
    // 触发保存事件
    emit('save', 'notifications', settings)
  } catch (error) {
    console.error('保存通知设置失败:', error)
    message.error('保存设置失败，请重试')
  } finally {
    saving.value = false
  }
}

// 重置为默认设置
const handleReset = () => {
  Object.assign(settings, {
    systemNotification: true,
    emailNotification: true,
    smsNotification: false,
    talentNotification: true,
    teamNotification: true,
    financialNotification: true,
    quietHours: {
      enabled: false,
      startTime: dayjs('22:00', 'HH:mm'),
      endTime: dayjs('08:00', 'HH:mm')
    }
  })
  
  message.success('已重置为默认设置')
}

// 加载用户通知设置
const loadSettings = async () => {
  try {
    // 这里应该调用API获取用户的通知设置
    // const data = await getUserNotificationSettings()
    // Object.assign(settings, data)
    
    console.log('加载通知设置')
  } catch (error) {
    console.error('加载通知设置失败:', error)
    message.error('加载设置失败')
  }
}

// 组件挂载时加载设置
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.notification-settings {
  background: #f5f5f5;
}

.settings-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-section {
  margin-bottom: 32px;
}

.notification-section:last-child {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.title-icon {
  margin-right: 8px;
  color: #1890ff;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.setting-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.time-range-setting {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.actions {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .time-range-setting {
    margin-top: 12px;
  }
  
  .actions {
    text-align: center;
  }
}
</style> 