<template>
  <div class="user-details-container">
    <ErrorBoundary>
      <!-- 页面头部导航 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <a-button 
              type="text" 
              :icon="h(ArrowLeftOutlined)" 
              @click="返回用户列表"
              class="back-btn"
            >
              返回用户列表
            </a-button>
            <h1>
              <UserOutlined />
              用户详情
            </h1>
          </div>
          <div class="header-actions">
            <a-space size="large">
              <a-button 
                type="primary" 
                :icon="h(EditOutlined)"
                @click="编辑用户信息"
                :loading="loading"
              >
                编辑用户
              </a-button>
            </a-space>
          </div>
        </div>
      </div>

      <!-- 用户基本信息卡片 -->
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :md="8">
          <a-card title="基本信息" class="user-info-card">
            <div class="user-avatar-section">
              <a-avatar 
                :size="120" 
                :src="用户信息.avatar" 
                :style="{ backgroundColor: 获取头像颜色(用户信息.昵称) }"
                class="user-avatar"
              >
                {{ 用户信息.昵称?.charAt(0)?.toUpperCase() }}
              </a-avatar>
              <div class="user-basic-info">
                <h2>{{ 用户信息.昵称 || '未知用户' }}</h2>
                <a-tag
                  :color="获取状态颜色(用户信息.状态)"
                  class="status-tag"
                >
                  {{ 获取状态文本(用户信息.状态) }}
                </a-tag>
                <a-tag 
                  :color="用户信息.is_admin ? 'blue' : 'default'"
                  class="admin-tag"
                >
                  {{ 用户信息.is_admin ? '管理员' : '普通用户' }}
                </a-tag>
              </div>
            </div>

            <a-descriptions :column="1" class="user-descriptions">
              <a-descriptions-item label="用户id">
                <a-tag color="purple">{{ 用户信息.id }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="邮箱">
                <a-space>
                  <MailOutlined />
                  {{ 用户信息.邮箱 || '-' }}
                </a-space>
              </a-descriptions-item>
              <a-descriptions-item label="手机号">
                <a-space>
                  <PhoneOutlined />
                  {{ 用户信息.手机号 || '-' }}
                </a-space>
              </a-descriptions-item>
              <a-descriptions-item label="用户等级">
                <a-tag color="gold">Lv.{{ 用户信息.level || 1 }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="注册时间">
                <a-space>
                  <CalendarOutlined />
                  {{ 格式化日期(用户信息.created_at) }}
                </a-space>
              </a-descriptions-item>
              <a-descriptions-item label="最后登录">
                <a-space>
                  <ClockCircleOutlined />
                  {{ 格式化日期(用户信息.last_login_time) || '从未登录' }}
                </a-space>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <a-col :xs="24" :md="16">
          <!-- 用户统计信息 -->
          <a-card title="统计信息" class="stats-card" style="margin-bottom: 24px;">
            <a-row :gutter="[16, 16]">
              <a-col :xs="12" :sm="6">
                <a-statistic 
                  title="用户等级" 
                  :value="用户信息.level || 1" 
                  :value-style="{ color: '#1890ff' }"
                  prefix="Lv."
                />
              </a-col>
              <a-col :xs="12" :sm="6">
                <a-statistic 
                  title="账户状态" 
                  :value="获取状态文本(用户信息.status)" 
                  :value-style="{ color: 获取状态颜色(用户信息.status) === 'green' ? '#52c41a' : '#f5222d' }"
                />
              </a-col>
              <a-col :xs="12" :sm="6">
                <a-statistic 
                  title="管理员权限" 
                  :value="用户信息.is_admin ? '是' : '否'" 
                  :value-style="{ color: 用户信息.is_admin ? '#faad14' : '#8c8c8c' }"
                />
              </a-col>
              <a-col :xs="12" :sm="6">
                <a-statistic 
                  title="注册天数" 
                  :value="计算注册天数(用户信息.created_at)" 
                  :value-style="{ color: '#722ed1' }"
                  suffix="天"
                />
              </a-col>
            </a-row>
          </a-card>

          <!-- 关联店铺信息 -->
          <a-card title="关联店铺" class="shops-card" style="margin-bottom: 24px;">
            <a-spin :spinning="店铺加载中">
              <div v-if="用户店铺列表.length > 0">
                <a-list
                  :data-source="用户店铺列表"
                  :grid="{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-card size="small" hoverable>
                        <template #cover>
                          <div class="shop-avatar-container">
                            <a-avatar
                              :size="60"
                              :src="item.店铺头像"
                              :alt="item.店铺名称"
                              class="shop-avatar"
                            >
                              <template #icon><ShopOutlined /></template>
                            </a-avatar>
                          </div>
                        </template>
                        <a-card-meta>
                          <template #title>
                            <a-tooltip :title="item.店铺名称">
                              <div class="shop-name">{{ item.店铺名称 }}</div>
                            </a-tooltip>
                          </template>
                          <template #description>
                            <div class="shop-info">
                              <div>ID: {{ item.店铺标识 }}</div>
                              <div>关联时间: {{ item.创建时间 }}</div>
                              <a-tag color="green" size="small">{{ item.关联状态 }}</a-tag>
                            </div>
                          </template>
                        </a-card-meta>
                      </a-card>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
              <a-empty v-else description="该用户暂未关联任何店铺" />
            </a-spin>
          </a-card>

          <!-- 详细信息Tab页 -->
          <a-card class="details-tabs-card">
            <a-empty description="更多详细信息功能开发中..." />
          </a-card>
        </a-col>
      </a-row>

      <!-- 编辑用户模态框 -->
      <a-modal
        v-model:open="编辑模态框显示"
        title="编辑用户信息"
        @ok="确认编辑用户"
        @cancel="取消编辑用户"
        :confirm-loading="编辑确认加载中"
        width="600px"
      >
        <a-form
          ref="编辑表单引用"
          :model="编辑表单数据"
          layout="vertical"
        >
          <a-form-item
            name="昵称"
            label="用户昵称"
            :rules="[{ required: true, message: '请输入用户昵称' }]"
          >
            <a-input v-model:value="编辑表单数据.昵称" placeholder="请输入用户昵称" />
          </a-form-item>
          <a-form-item
            name="邮箱"
            label="邮箱地址"
            :rules="[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]"
          >
            <a-input v-model:value="编辑表单数据.邮箱" placeholder="请输入邮箱地址" />
          </a-form-item>
          <a-form-item name="手机号" label="手机号码">
            <a-input v-model:value="编辑表单数据.手机号" placeholder="请输入手机号码" />
          </a-form-item>
          <a-form-item name="is_admin" label="管理员权限">
            <a-switch 
              v-model:checked="编辑表单数据.is_admin" 
              checked-children="管理员" 
              un-checked-children="普通用户" 
            />
          </a-form-item>
          <a-form-item name="level" label="用户等级">
            <a-input-number 
              v-model:value="编辑表单数据.level" 
              :min="1" 
              :max="100" 
              placeholder="用户等级"
              style="width: 100%"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <LoadingSpinner v-if="loading" type="page" tip="正在加载用户详情..." />
    </ErrorBoundary>
  </div>
</template>

<script setup>
import { h, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  ArrowLeftOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  EditOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
  ShopOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import superAdminService from '../../services/superAdminService';
import { useSuperAdminRequest } from '../../composables/useApiRequest';
import ErrorBoundary from '../../components/common/ErrorBoundary.vue';
import LoadingSpinner from '../../components/common/LoadingSpinner.vue';
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const route = useRoute();
const router = useRouter();
const { loading, 执行API请求 } = useSuperAdminRequest();

// ==================== 数据状态管理 ====================

// 用户基本信息
const 用户信息 = ref({
  id: null,
  昵称: '',
  邮箱: '',
  手机号: '',
  状态: 'active',
  is_admin: false,
  level: 1,
  created_at: null,
  last_login_time: null
});

// ==================== 编辑用户模态框 ====================

const 编辑模态框显示 = ref(false);
const 编辑确认加载中 = ref(false);
const 编辑表单引用 = ref();
const 编辑表单数据 = reactive({
  昵称: '',
  邮箱: '',
  手机号: '',
  is_admin: false,
  level: 1
});

// ==================== 店铺相关状态 ====================

const 用户店铺列表 = ref([]);
const 店铺加载中 = ref(false);

// ==================== 工具函数 ====================

// 获取头像颜色
const 获取头像颜色 = (昵称) => {
  const 颜色列表 = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068', '#108ee9'];
  const 索引 = (昵称 || '').charCodeAt(0) % 颜色列表.length;
  return 颜色列表[索引];
};

// 获取状态颜色
const 获取状态颜色 = (status) => {
  const 颜色映射 = {
    'active': 'green',
    'inactive': 'orange',
    'banned': 'red'
  };
  return 颜色映射[status] || 'default';
};

// 获取状态文本
const 获取状态文本 = (status) => {
  const 文本映射 = {
    'active': '正常',
    'inactive': '未激活',
    'banned': '已禁用'
  };
  return 文本映射[status] || '未知';
};

// 格式化日期
const 格式化日期 = (dateString) => {
  if (!dateString) return '-';
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
};

// 计算注册天数
const 计算注册天数 = (创建时间) => {
  if (!创建时间) return 0;
  return dayjs().diff(dayjs(创建时间), 'day');
};

// ==================== API调用方法 ====================

// 获取用户详情
const 获取用户详情 = async () => {
  const 用户id = route.params.userId;
  if (!用户id) {
    message.error('用户id无效');
    返回用户列表();
    return;
  }

  await 执行API请求(async () => {
    const response = await superAdminService.getUserDetail(用户id);
    
    if (superAdminService.isSuccess(response)) {
      const 用户数据 = superAdminService.getData(response);
      用户信息.value = { ...用户信息.value, ...用户数据 };
      
      // 填充编辑表单数据
      Object.assign(编辑表单数据, {
        昵称: 用户数据.昵称 || '',
        邮箱: 用户数据.邮箱 || '',
        手机号: 用户数据.手机号 || '',
        is_admin: 用户数据.is_admin || false,
        level: 用户数据.level || 1
      });

      // 获取用户关联店铺
      await 获取用户店铺();
    } else {
      throw new Error(superAdminService.getMessage(response) || '获取用户详情失败');
    }
  }, {
    onError: (error) => {
      console.error('获取用户详情失败:', error);
      message.error('获取用户详情失败');
    }
  });
};

// 获取用户关联店铺
const 获取用户店铺 = async () => {
  const 用户id = route.params.userId;
  if (!用户id) return;

  店铺加载中.value = true;

  await 执行API请求(async () => {
    const response = await superAdminService.获取用户店铺数据(用户id);

    if (superAdminService.isSuccess(response)) {
      const 店铺数据 = superAdminService.getData(response);
      用户店铺列表.value = 店铺数据.列表 || [];
      console.log('✅ 用户店铺获取成功:', 店铺数据);
    } else {
      throw new Error(superAdminService.getMessage(response) || '获取用户店铺失败');
    }
  }, {
    onError: (error) => {
      console.error('获取用户店铺失败:', error);
      message.error('获取用户店铺失败');
    },
    onFinally: () => {
      店铺加载中.value = false;
    }
  });
};

// ==================== 事件处理方法 ====================

// 返回用户列表
const 返回用户列表 = () => {
  router.push('/users');
};

// 编辑用户信息
const 编辑用户信息 = () => {
  编辑模态框显示.value = true;
};

// 确认编辑用户
const 确认编辑用户 = async () => {
  try {
    await 编辑表单引用.value.validate();
    编辑确认加载中.value = true;
    
    const 更新数据 = {
      用户id: 用户信息.value.id,
      ...编辑表单数据
    };
    
    const response = await superAdminService.updateUser(更新数据);
    
    if (superAdminService.isSuccess(response)) {
      message.success('用户信息更新成功');
      编辑模态框显示.value = false;
      获取用户详情(); // 重新获取用户信息
    } else {
      throw new Error(superAdminService.getMessage(response));
    }
  } catch (error) {
    if (!(error && error.errorFields)) {
      console.error('更新用户信息失败:', error);
      message.error('更新用户信息失败: ' + error.message);
    }
  } finally {
    编辑确认加载中.value = false;
  }
};

// 取消编辑用户
const 取消编辑用户 = () => {
  编辑模态框显示.value = false;
};

// ==================== 生命周期 ====================

onMounted(() => {
  获取用户详情();
});

</script>

<style scoped>
.user-details-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
}

.back-btn {
  color: #666;
}

.back-btn:hover {
  color: #1890ff;
}

.user-info-card {
  height: fit-content;
}

.user-avatar-section {
  text-align: center;
  margin-bottom: 24px;
}

.user-avatar {
  margin-bottom: 16px;
}

.user-basic-info h2 {
  margin: 8px 0;
  color: #262626;
}

.status-tag,
.admin-tag {
  margin: 4px;
}

.user-descriptions {
  margin-top: 24px;
}

.stats-card .ant-statistic {
  text-align: center;
}

.details-tabs-card {
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-details-container {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
  }
}

/* 店铺相关样式 */
.shops-card {
  margin-bottom: 24px;
}

.shop-avatar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: #fafafa;
}

.shop-avatar {
  border: 2px solid #e8e8e8;
}

.shop-name {
  font-weight: 500;
  color: #262626;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shop-info {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

.shop-info > div {
  margin-bottom: 4px;
}

.shop-info > div:last-child {
  margin-bottom: 0;
}
</style>