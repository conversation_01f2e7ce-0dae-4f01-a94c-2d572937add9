/**
 * CRM前端统一配置管理
 * 集中管理所有环境配置、API配置和应用配置
 * 
 * 功能特性：
 * - 环境自适应配置
 * - API端点统一管理
 * - 应用常量集中定义
 * - 配置验证和默认值
 */

/**
 * 环境配置
 */
export const ENV_CONFIG = {
  // 当前环境
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  MODE: import.meta.env.MODE || 'development',
  
  // 是否为开发环境
  IS_DEV: import.meta.env.DEV || false,
  IS_PROD: import.meta.env.PROD || false,
  
  // 基础URL配置
  BASE_URL: import.meta.env.BASE_URL || '/',
  
  // API基础URL
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '',
  
  // 其他环境变量
  API_TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
  DEV_MODE: import.meta.env.VITE_DEV_MODE === 'true'
}

/**
 * API配置
 */
export const API_CONFIG = {
  // 基础URL获取函数
  getBaseURL: () => {
    // 根据构建模式自动选择
    if (ENV_CONFIG.IS_DEV) {
      console.log('🔧 使用开发环境API地址: http://localhost:8000')
      return 'http://localhost:8000'
    } else {
      // 生产环境强制使用正确的后端API地址
      const prodUrl = 'https://invite.limob.cn'
      console.log('🔧 使用生产环境API地址:', prodUrl)
      console.log('🔧 环境变量API地址:', ENV_CONFIG.API_BASE_URL)
      console.log('🔧 IS_PROD:', ENV_CONFIG.IS_PROD)
      console.log('🔧 IS_DEV:', ENV_CONFIG.IS_DEV)
      return prodUrl
    }
  },
  
  // 超时配置
  TIMEOUT: {
    DEFAULT: ENV_CONFIG.API_TIMEOUT,
    SHORT: 5000,    // 短超时：用于简单查询
    MEDIUM: 15000,  // 中等超时：用于一般操作
    LONG: 60000,    // 长超时：用于复杂操作（上传、导出等）
    UPLOAD: 120000  // 上传超时：用于文件上传
  },
  
  // 重试配置
  RETRY: {
    MAX_RETRIES: 2,
    RETRY_DELAY: 1000,
    RETRYABLE_ERRORS: ['ECONNABORTED', 'NETWORK_ERROR', 'TIMEOUT']
  },
  
  // 长超时路径配置
  LONG_TIMEOUT_PATHS: [
    '/kol/edit-my-talent',
    '/team/talent',
    '/user/upload',
    '/upload/',
    '/export/',
    '/import/'
  ]
}

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/user/login',
    REGISTER: '/register',
    LOGOUT: '/logout',
    SMS_CODE: '/sms_code',
    RESET_PASSWORD: '/password/forgot/reset',
    CHANGE_PASSWORD: '/update_password',
    GET_PERMISSIONS: '/get_permission',
    ACTIVATE_ACCOUNT: '/activate_code'
  },
  
  // 用户相关
  USER: {
    GET_INFO: '/get_permission',
    GET_ORDERS: '/get_order',
    GET_FULL_INFO: '/full-info',
    SET_NICKNAME: '/users/set_nickname',
    UPLOAD_AVATAR: '/upload/avatar',
    FIND_BY_PHONE: '/find-by-phone',
    SMART_SEARCH: '/smart-search',
    SUBMIT_CONTACT: '/submit_contact',
    INVITE_KOL: '/invite_kol',
    BIND_SHOP: '/bind_shop',
    GET_PROMOTED_USERS: '/users/promoted_list',
    GET_COMPANY_USERS: '/company-users'
  },
  
  // 团队相关
  TEAM: {
    BASE: '/team',
    USER_TEAMS: '/team/user/teams',
    USER_STATS: '/team/user/stats',
    DETAIL: '/team/detail',
    CREATE: '/team/create',
    DISSOLVE: '/team/dissolve',
    LEAVE: '/team/leave',
    SMART_LEAVE: '/team/smart-leave',
    TRANSFER_OWNERSHIP: '/team/transfer-ownership',
    COMPANY_LIST: '/team/company/list',
    COMPANY_CREATE: '/team/company/create'
  },
  
  // KOL/达人相关
  KOL: {
    BASE: '/kol',
    SEARCH: '/kol/search',
    DETAIL: '/kol/detail',
    MY_TALENTS: '/kol/my-talents',
    TEAM_TALENTS: '/kol/team-talents'
  },
  
  // 微信相关
  WECHAT: {
    BASE: '/wechat',
    TALENTS: '/wechat/talents',
    SEARCH: '/wechat/search'
  },
  
  // 客户相关
  CUSTOMER: {
    BASE: '/customer',
    INVITATION: '/customer/invitation'
  },
  
  // 工作空间相关
  WORKSPACE: {
    BASE: '/workspace'
  },
  
  // 订单相关
  ORDER: {
    BASE: '/order'
  }
}

/**
 * 应用配置
 */
export const APP_CONFIG = {
  // 应用信息
  NAME: 'CRM系统',
  VERSION: '1.0.0',
  DESCRIPTION: 'CRM客户关系管理系统',
  
  // 分页配置
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
    SHOW_SIZE_CHANGER: true,
    SHOW_QUICK_JUMPER: true,
    SHOW_TOTAL: true
  },
  
  // 表格配置
  TABLE: {
    DEFAULT_SIZE: 'middle',
    SCROLL_X: 'max-content',
    BORDERED: true,
    SHOW_HEADER: true
  },
  
  // 表单配置
  FORM: {
    LABEL_COL: { span: 6 },
    WRAPPER_COL: { span: 18 },
    LAYOUT: 'horizontal',
    VALIDATE_TRIGGER: 'blur'
  },
  
  // 消息配置
  MESSAGE: {
    DURATION: 3, // 秒
    MAX_COUNT: 3,
    TOP: 24
  },
  
  // 通知配置
  NOTIFICATION: {
    DURATION: 4.5, // 秒
    PLACEMENT: 'topRight',
    MAX_COUNT: 5
  },
  
  // 上传配置
  UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ACCEPTED_TYPES: {
      IMAGE: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      DOCUMENT: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      EXCEL: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
    }
  }
}

/**
 * 业务常量
 */
export const BUSINESS_CONSTANTS = {
  // 用户状态
  USER_STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    PENDING: 'pending',
    BANNED: 'banned'
  },
  
  // 用户类型
  USER_TYPE: {
    NORMAL: 'normal',
    VIP: 'vip',
    ADMIN: 'admin',
    KOL: 'kol'
  },
  
  // 联系方式类型
  CONTACT_TYPE: {
    WECHAT: '微信',
    QQ: 'QQ',
    EMAIL: '邮箱',
    PHONE: '电话'
  },
  
  // 验证码类型
  SMS_TYPE: {
    REGISTER: '注册',
    RESET_PASSWORD: '重置密码',
    LOGIN: '登录'
  },
  
  // 团队角色
  TEAM_ROLE: {
    OWNER: 'owner',
    ADMIN: 'admin',
    MEMBER: 'member',
    GUEST: 'guest'
  },
  
  // 平台类型
  PLATFORM_TYPE: {
    DOUYIN: 'douyin',
    WECHAT: 'wechat',
    XIAOHONGSHU: 'xiaohongshu',
    WEIBO: 'weibo'
  }
}

/**
 * 错误码配置
 */
export const ERROR_CODES = {
  // 成功状态码
  SUCCESS: [0, 1, 100, 200],
  
  // 认证错误
  AUTH_ERROR: [401, 502, 503],
  
  // 权限错误
  PERMISSION_ERROR: [403],
  
  // 资源不存在
  NOT_FOUND: [404],
  
  // 验证错误
  VALIDATION_ERROR: [400, 422],
  
  // 服务器错误
  SERVER_ERROR: [500, 502, 503, 504]
}

/**
 * 本地存储键名配置
 */
export const STORAGE_KEYS = {
  // 认证相关
  TOKEN: 'crm_token',
  USER_INFO: 'crm_user_info',
  PERMISSIONS: 'crm_permissions',
  SESSION_ACTIVE: 'crm_session_active',
  
  // 用户偏好
  THEME: 'crm_theme',
  LANGUAGE: 'crm_language',
  SIDEBAR_COLLAPSED: 'crm_sidebar_collapsed',
  
  // 业务数据缓存
  RECENT_SEARCHES: 'crm_recent_searches',
  FAVORITE_TALENTS: 'crm_favorite_talents',
  TEAM_CACHE: 'crm_team_cache'
}

/**
 * 路由配置
 */
export const ROUTE_CONFIG = {
  // 公开路由（无需认证）
  PUBLIC_ROUTES: [
    '/login',
    '/register',
    '/forgot-password',
    '/invitation'
  ],
  
  // 认证路由（需要登录）
  AUTH_ROUTES: [
    '/dashboard',
    '/talents',
    '/team',
    '/workspace',
    '/profile'
  ],
  
  // 默认重定向
  DEFAULT_REDIRECT: '/dashboard',
  LOGIN_REDIRECT: '/login'
}

/**
 * 获取完整的API URL
 * @param {string} endpoint - API端点
 * @returns {string} 完整的API URL
 */
export const getApiUrl = (endpoint) => {
  const baseURL = API_CONFIG.getBaseURL()
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseURL}${cleanEndpoint}`
}

/**
 * 获取配置值
 * @param {string} path - 配置路径，如 'API_CONFIG.TIMEOUT.DEFAULT'
 * @param {any} defaultValue - 默认值
 * @returns {any} 配置值
 */
export const getConfig = (path, defaultValue = null) => {
  try {
    const keys = path.split('.')
    let value = window
    
    // 从全局配置对象中查找
    const configObjects = {
      ENV_CONFIG,
      API_CONFIG,
      API_ENDPOINTS,
      APP_CONFIG,
      BUSINESS_CONSTANTS,
      ERROR_CODES,
      STORAGE_KEYS,
      ROUTE_CONFIG
    }
    
    if (configObjects[keys[0]]) {
      value = configObjects[keys[0]]
      for (let i = 1; i < keys.length; i++) {
        if (value && typeof value === 'object' && keys[i] in value) {
          value = value[keys[i]]
        } else {
          return defaultValue
        }
      }
      return value
    }
    
    return defaultValue
  } catch (error) {
    console.warn(`获取配置失败: ${path}`, error)
    return defaultValue
  }
}

/**
 * 验证配置完整性
 */
export const validateConfig = () => {
  const errors = []
  
  // 验证必要的环境变量
  if (!ENV_CONFIG.API_BASE_URL && ENV_CONFIG.IS_PROD) {
    errors.push('生产环境缺少 VITE_API_BASE_URL 配置')
  }
  
  // 验证API超时配置
  if (API_CONFIG.TIMEOUT.DEFAULT < 1000) {
    errors.push('API超时时间配置过短')
  }
  
  if (errors.length > 0) {
    console.error('❌ 配置验证失败:', errors)
    return false
  }
  
  console.log('✅ 配置验证通过')
  return true
}

// 在开发环境下验证配置
if (ENV_CONFIG.IS_DEV) {
  validateConfig()
}

// 默认导出所有配置
export default {
  ENV_CONFIG,
  API_CONFIG,
  API_ENDPOINTS,
  APP_CONFIG,
  BUSINESS_CONSTANTS,
  ERROR_CODES,
  STORAGE_KEYS,
  ROUTE_CONFIG,
  getApiUrl,
  getConfig,
  validateConfig
}
