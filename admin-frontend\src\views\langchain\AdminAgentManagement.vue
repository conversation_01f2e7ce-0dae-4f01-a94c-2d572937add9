<template>
  <div class="admin-agent-management">
    <div class="page-header">
      <div class="header-content">
        <h1>LangChain智能体管理</h1>
        <p>管理员视图 - 创建、配置和分配智能体给用户</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="跳转创建页面">
          <template #icon><PlusOutlined /></template>
          创建智能体
        </a-button>
        <a-button @click="刷新列表">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
        <a-button @click="重新加载所有智能体">
          <template #icon><ReloadOutlined /></template>
          重新加载配置
        </a-button>
        <a-button @click="查看统计">
          <template #icon><BarChartOutlined /></template>
          统计信息
        </a-button>
        <a-button @click="查看系统调试信息" type="dashed">
          <template #icon><BugOutlined /></template>
          系统调试
        </a-button>
        <a-button @click="检查数据一致性" type="dashed">
          <template #icon><SettingOutlined /></template>
          数据检查
        </a-button>

        <!-- 批量操作 -->
        <a-dropdown v-if="选中的智能体列表.length > 0" :disabled="批量操作中">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batch-enable" @click="批量切换启用状态(true)">
                <template #icon><PlayCircleOutlined /></template>
                批量启用 ({{ 选中的智能体列表.length }})
              </a-menu-item>
              <a-menu-item key="batch-disable" @click="批量切换启用状态(false)">
                <template #icon><PauseCircleOutlined /></template>
                批量禁用 ({{ 选中的智能体列表.length }})
              </a-menu-item>
            </a-menu>
          </template>
          <a-button :loading="批量操作中" type="primary" ghost>
            <template #icon><SettingOutlined /></template>
            批量操作 ({{ 选中的智能体列表.length }})
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <a-card class="search-card" size="small">
      <a-row :gutter="16">
        <a-col :span="5">
          <a-input
            v-model:value="搜索条件.搜索关键词"
            placeholder="搜索智能体名称"
            @press-enter="搜索智能体"
          >
            <template #prefix><SearchOutlined /></template>
          </a-input>
        </a-col>
        <!-- {{ AURA-X: Delete - 移除智能体类型筛选. Approval: 寸止(ID:1732456800). }} -->
        <a-col :span="3">
          <a-select
            v-model:value="搜索条件.是否启用"
            placeholder="启用状态"
            allow-clear
            @change="搜索智能体"
          >
            <a-select-option :value="true">已启用</a-select-option>
            <a-select-option :value="false">已禁用</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-select
            v-model:value="搜索条件.是否公开"
            placeholder="公开状态"
            allow-clear
            @change="搜索智能体"
          >
            <a-select-option :value="true">公开</a-select-option>
            <a-select-option :value="false">私有</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-button type="primary" @click="搜索智能体">搜索</a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 智能体列表 -->
    <a-card class="list-card">
      <a-table
        :columns="表格列定义"
        :data-source="智能体列表"
        :loading="加载中"
        :pagination="分页配置"
        @change="处理表格变化"
        row-key="智能体id"
        :customRow="自定义行属性"
        :row-selection="行选择配置"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'agent_info'">
            <div class="agent-info">
              <a-avatar :size="32" style="background-color: #1890ff">
                {{ record.智能体名称.charAt(0) }}
              </a-avatar>
              <div class="agent-details">
                <div class="agent-name">{{ record.智能体名称 }}</div>
                <div class="agent-id">ID: {{ record.智能体id }}</div>
                <div class="agent-desc">{{ record.智能体描述 }}</div>
              </div>
            </div>
          </template>

          <!-- {{ AURA-X: Delete - 移除智能体类型列显示. Approval: 寸止(ID:1732456800). }} -->

          <template v-else-if="column.key === 'is_enabled'">
            <a-switch
              :checked="Boolean(record.是否启用)"
              :loading="record._切换启用状态中"
              @change="(checked) => 切换智能体启用状态(record, checked)"
              checked-children="启用"
              un-checked-children="禁用"
              size="small"
            />
          </template>

          <template v-else-if="column.key === 'is_public'">
            <div class="public-status-control">
              <a-switch
                :checked="Boolean(record.是否公开)"
                :loading="record._切换公开状态中"
                @change="(checked) => 切换智能体公开状态(record, checked)"
                checked-children="公开"
                un-checked-children="私有"
                size="small"
              />
            </div>
          </template>

          <template v-else-if="column.key === 'usage_stats'">
            <div class="usage-stats">
              <div class="stat-item">
                <span class="stat-label">对话:</span>
                <span class="stat-value">{{ record.使用统计?.对话次数 || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">用户:</span>
                <span class="stat-value">{{ record.使用统计?.用户数量 || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最后使用:</span>
                <span class="stat-time">{{ 格式化相对时间(record.使用统计?.最后使用时间) }}</span>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'assigned_users'">
            <a-statistic
              :value="record.分配用户数"
              :value-style="{ fontSize: '14px' }"
            />
          </template>

          <template v-else-if="column.key === 'model_name'">
            <a-tag color="purple">{{ record.模型名称 }}</a-tag>
          </template>

          <template v-else-if="column.key === 'created_time'">
            {{ 格式化时间(record.创建时间) }}
          </template>

          <template v-else-if="column.key === 'actions'">
            <a-space>
              <!-- 主要操作 -->
              <a-button
                type="primary"
                size="small"
                @click.stop="跳转编辑页面(record)"
                title="编辑智能体配置"
              >
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>

              <!-- 用户管理操作 -->
              <a-dropdown @click.stop>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="assign" @click="分配给用户(record)">
                      <template #icon><UserAddOutlined /></template>
                      分配用户
                    </a-menu-item>
                    <a-menu-item key="view-assignments" @click="查看分配情况(record)">
                      <template #icon><TeamOutlined /></template>
                      分配情况
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small" title="用户管理">
                  <template #icon><UserOutlined /></template>
                  用户 <DownOutlined />
                </a-button>
              </a-dropdown>

              <!-- 系统操作 -->
              <a-dropdown @click.stop>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="usage-stats" @click="查看智能体使用统计(record)">
                      <template #icon><BarChartOutlined /></template>
                      使用统计
                    </a-menu-item>

                    <a-menu-divider />
                    <a-menu-item key="reload" @click="重新加载智能体配置(record.智能体id)">
                      <template #icon><ReloadOutlined /></template>
                      重新加载配置
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger @click="确认删除智能体(record)">
                      <template #icon><DeleteOutlined /></template>
                      删除智能体
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small" title="系统操作">
                  <template #icon><SettingOutlined /></template>
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>



    <!-- 优化的分配用户对话框 -->
    <a-modal
      v-model:open="分配对话框显示"
      title="分配智能体给用户"
      width="900px"
      :confirm-loading="分配中"
      @ok="确认分配用户"
      @cancel="取消分配"
      :ok-text="选中用户列表.length > 0 ? `分配给 ${选中用户列表.length} 个用户` : '确认分配'"
      :ok-button-props="{ disabled: 选中用户列表.length === 0 }"
    >
      <div v-if="当前分配智能体">
        <!-- 智能体信息卡片 -->
        <a-card size="small" style="margin-bottom: 16px">
          <template #title>
            <div class="agent-card-title">
              <a-avatar :size="32" style="background-color: #1890ff; margin-right: 12px">
                {{ 当前分配智能体.智能体名称.charAt(0) }}
              </a-avatar>
              <div>
                <div class="agent-name">{{ 当前分配智能体.智能体名称 }}</div>
                <div class="agent-meta">
                  <a-tag :color="Boolean(当前分配智能体.是否公开) ? 'green' : 'orange'" size="small">
                    {{ Boolean(当前分配智能体.是否公开) ? '公开' : '私有' }}
                  </a-tag>
                  <span class="user-count">已分配: {{ 当前分配智能体.分配用户数 }} 用户</span>
                </div>
              </div>
            </div>
          </template>
        </a-card>

        <a-form layout="vertical">
          <!-- 搜索用户区域 -->
          <a-form-item label="搜索用户">
            <a-input-group compact>
              <a-input
                v-model:value="用户搜索关键词"
                placeholder="输入用户昵称、手机号或邮箱搜索"
                style="width: calc(100% - 80px)"
                @press-enter="搜索用户"
                allow-clear
              />
              <a-button
                type="primary"
                @click="搜索用户"
                :loading="搜索用户中"
                style="width: 80px"
              >
                搜索
              </a-button>
            </a-input-group>
            <div v-if="搜索用户列表.length === 0 && 用户搜索关键词" style="margin-top: 8px">
              <a-empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                description="未找到匹配的用户，请尝试其他关键词"
                :image-style="{ height: '40px' }"
              />
            </div>
          </a-form-item>

          <!-- 分配配置 -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="权限级别">
                <a-select v-model:value="分配表单.权限级别" style="width: 100%">
                  <a-select-option value="只读权限">
                    <div class="permission-option">
                      <span class="permission-name">只读权限</span>
                      <span class="permission-desc">仅可查看和使用智能体</span>
                    </div>
                  </a-select-option>
                  <a-select-option value="读写权限">
                    <div class="permission-option">
                      <span class="permission-name">读写权限</span>
                      <span class="permission-desc">可使用智能体并查看对话历史</span>
                    </div>
                  </a-select-option>
                  <a-select-option value="管理权限">
                    <div class="permission-option">
                      <span class="permission-name">管理权限</span>
                      <span class="permission-desc">完全控制权限，包括配置修改</span>
                    </div>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="分配类型">
                <a-select v-model:value="分配表单.分配类型" style="width: 100%">
                  <a-select-option value="个人分配">
                    <div class="assignment-type-option">
                      <span class="assignment-type-name">个人分配</span>
                      <span class="assignment-type-desc">转移智能体所有权，变为私有</span>
                    </div>
                  </a-select-option>
                  <a-select-option value="共享分配">
                    <div class="assignment-type-option">
                      <span class="assignment-type-name">共享分配</span>
                      <span class="assignment-type-desc">添加使用权限，保持公开状态</span>
                    </div>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="备注信息">
            <a-textarea
              v-model:value="分配表单.备注"
              :rows="2"
              placeholder="可选：添加分配备注信息"
              show-count
              :maxlength="200"
            />
          </a-form-item>

          <!-- 用户选择区域 -->
          <a-form-item>
            <template #label>
              <div class="user-selection-label">
                <span>选择用户</span>
                <a-tag v-if="选中用户列表.length > 0" color="blue">
                  已选择 {{ 选中用户列表.length }} 个用户
                </a-tag>
              </div>
            </template>

            <a-table
              :columns="优化的用户表格列"
              :data-source="搜索用户列表"
              :loading="搜索用户中"
              :pagination="用户分页配置"
              :row-selection="用户选择配置"
              @change="处理用户表格变化"
              row-key="id"
              size="small"
              :scroll="{ y: 300 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'user_info'">
                  <div class="user-info-enhanced">
                    <a-avatar :size="32" style="background-color: #52c41a">
                      {{ record.昵称.charAt(0) }}
                    </a-avatar>
                    <div class="user-details-enhanced">
                      <div class="user-name">{{ record.昵称 }}</div>
                      <div class="user-contact">
                        <span v-if="record.手机号">📱 {{ record.手机号 }}</span>
                        <span v-if="record.邮箱" :style="{ marginLeft: record.手机号 ? '8px' : '0' }">
                          📧 {{ record.邮箱 }}
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else-if="column.key === 'user_status'">
                  <a-badge
                    :status="record.状态 === 'active' ? 'success' : 'default'"
                    :text="record.状态 === 'active' ? '活跃' : '非活跃'"
                  />
                </template>
                <template v-else-if="column.key === 'assigned_agents'">
                  <a-statistic
                    :value="record.分配智能体数 || 0"
                    :value-style="{ fontSize: '14px', color: '#1890ff' }"
                  />
                </template>
                <template v-else-if="column.key === 'last_active'">
                  <span class="last-active">
                    {{ 格式化时间(record.最后活跃时间) || '从未使用' }}
                  </span>
                </template>
              </template>
            </a-table>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 分配情况抽屉 -->
    <a-drawer
      v-model:open="分配情况抽屉显示"
      title="智能体分配情况"
      width="800"
      placement="right"
    >
      <div v-if="当前查看智能体">
        <a-descriptions :column="2" bordered style="margin-bottom: 16px">
          <a-descriptions-item label="智能体名称">
            {{ 当前查看智能体.智能体名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="分配用户数">
            {{ 当前查看智能体.分配用户数 }}
          </a-descriptions-item>
          <a-descriptions-item label="是否公开" :span="2">
            <a-tag :color="Boolean(当前查看智能体.是否公开) ? 'green' : 'orange'">
              {{ Boolean(当前查看智能体.是否公开) ? '公开' : '私有' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <a-table
          :columns="分配情况表格列"
          :data-source="分配情况列表"
          :loading="加载分配情况中"
          :pagination="分配情况分页配置"
          @change="处理分配情况表格变化"
          row-key="id"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'user_info'">
              <div class="user-info">
                <a-avatar :size="24">{{ record.昵称 ? record.昵称.charAt(0) : '?' }}</a-avatar>
                <div class="user-details">
                  <div class="user-name">{{ record.昵称 || '未知用户' }}</div>
                  <div class="user-contact">{{ record.手机号 || record.邮箱 || '无联系方式' }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'permission_level'">
              <a-tag :color="获取权限颜色(record.权限级别)">
                {{ 格式化权限级别(record.权限级别) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="record.状态 === '启用' ? 'green' : 'red'">
                {{ record.状态 || '未知' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-popconfirm
                title="确定要撤销这个用户的智能体分配吗？"
                @confirm="撤销分配(record.id)"
              >
                <a-button type="link" size="small" danger>
                  撤销分配
                </a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
      </div>
    </a-drawer>

    <!-- 统计信息抽屉 -->
    <a-drawer
      v-model:open="统计抽屉显示"
      title="智能体统计信息"
      width="600"
      placement="right"
    >
      <div v-if="统计信息">
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :span="12">
            <a-statistic
              title="总智能体数"
              :value="统计信息.智能体统计?.总智能体数 || 0"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="12">
            <a-statistic
              title="运行中数量"
              :value="统计信息.智能体统计?.运行中数量 || 0"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :span="12">
            <a-statistic
              title="公开智能体"
              :value="统计信息.智能体统计?.公开智能体数 || 0"
              :value-style="{ color: '#faad14' }"
            />
          </a-col>
          <a-col :span="12">
            <a-statistic
              title="总分配数"
              :value="统计信息.分配统计?.总分配数 || 0"
              :value-style="{ color: '#722ed1' }"
            />
          </a-col>
        </a-row>

        <a-divider />

        <h3>热门智能体 (近30天)</h3>
        <a-list
          :data-source="统计信息.热门智能体 || []"
          size="small"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  {{ item.智能体名称 }}
                </template>
                <template #description>
                  对话次数: {{ item.对话次数 }} | 使用用户: {{ item.使用用户数 }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-drawer>

    <!-- 系统调试信息抽屉 -->
    <a-drawer
      v-model:open="调试抽屉显示"
      title="系统调试信息"
      width="800px"
      placement="right"
    >
      <div v-if="调试信息">
        <a-card title="数据库统计" size="small" style="margin-bottom: 16px">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic
                title="数据库总智能体数"
                :value="调试信息.数据库统计?.总数 || 0"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="12">
              <a-statistic
                title="运行中智能体数"
                :value="调试信息.数据库统计?.运行中数量 || 0"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
          </a-row>
        </a-card>

        <a-card title="智能体工厂状态" size="small" style="margin-bottom: 16px">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic
                title="工厂初始化状态"
                :value="调试信息.工厂状态?.工厂已初始化 ? '已初始化' : '未初始化'"
                :value-style="{ color: 调试信息.工厂状态?.工厂已初始化 ? '#52c41a' : '#ff4d4f' }"
              />
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="内存池智能体数量"
                :value="调试信息.工厂状态?.内存池智能体数量 || 0"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="8">
              <a-statistic
                title="用户映射数量"
                :value="调试信息.工厂状态?.用户映射数量 || 0"
                :value-style="{ color: '#722ed1' }"
              />
            </a-col>
          </a-row>
          <div style="margin-top: 16px">
            <p><strong>初始化时间:</strong> {{ 调试信息.工厂状态?.初始化时间 || '未知' }}</p>
          </div>
        </a-card>

        <a-card title="数据一致性检查" size="small" style="margin-bottom: 16px">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-alert
                :message="`缺失的智能体: ${调试信息.数据不一致?.缺失的智能体?.length || 0} 个`"
                :type="调试信息.数据不一致?.缺失的智能体?.length > 0 ? 'warning' : 'success'"
                show-icon
              />
              <div v-if="调试信息.数据不一致?.缺失的智能体?.length > 0" style="margin-top: 8px">
                <a-tag v-for="id in 调试信息.数据不一致.缺失的智能体" :key="id" color="orange">
                  ID: {{ id }}
                </a-tag>
              </div>
            </a-col>
            <a-col :span="12">
              <a-alert
                :message="`多余的智能体: ${调试信息.数据不一致?.多余的智能体?.length || 0} 个`"
                :type="调试信息.数据不一致?.多余的智能体?.length > 0 ? 'error' : 'success'"
                show-icon
              />
              <div v-if="调试信息.数据不一致?.多余的智能体?.length > 0" style="margin-top: 8px">
                <a-tag v-for="id in 调试信息.数据不一致.多余的智能体" :key="id" color="red">
                  ID: {{ id }}
                </a-tag>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <a-card title="内存池智能体详情" size="small" style="margin-bottom: 16px">
          <a-table
            :data-source="调试信息.内存池智能体 || []"
            :columns="[
              { title: 'ID', dataIndex: '智能体id', width: 80 },
              { title: '名称', dataIndex: '智能体名称', width: 150 },
              { title: '状态', dataIndex: '状态', width: 100 },
              { title: '对话次数', dataIndex: '对话次数', width: 100 }
            ]"
            :pagination="false"
            size="small"
          />
        </a-card>

        <a-card title="服务层状态" size="small">
          <a-statistic
            title="服务层初始化状态"
            :value="调试信息.服务层状态?.服务层已初始化 ? '已初始化' : '未初始化'"
            :value-style="{ color: 调试信息.服务层状态?.服务层已初始化 ? '#52c41a' : '#ff4d4f' }"
          />
        </a-card>

        <div style="margin-top: 24px; text-align: center">
          <a-button type="primary" @click="查看系统调试信息">
            刷新调试信息
          </a-button>
          <a-button style="margin-left: 8px" @click="重新加载所有智能体">
            重新加载所有智能体
          </a-button>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal, Empty } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  BarChartOutlined,
  BugOutlined,
  EyeOutlined,
  EditOutlined,
  UserAddOutlined,
  TeamOutlined,
  UserOutlined,
  DownOutlined,
  SettingOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons-vue'
import { adminLangChainService } from '@/services'
import langchainService from '@/services/langchainService'
import * as LangChainUtils from '@/utils/langchainUtils'

const router = useRouter()

// 响应式数据
const 智能体列表 = ref([])
const 加载中 = ref(false)
const 分配对话框显示 = ref(false)
const 分配情况抽屉显示 = ref(false)
const 统计抽屉显示 = ref(false)
const 调试抽屉显示 = ref(false)
const 分配中 = ref(false)
const 搜索用户中 = ref(false)
const 加载分配情况中 = ref(false)
const 批量操作中 = ref(false)
const 选中的智能体列表 = ref([])

// 当前操作的智能体
const 当前分配智能体 = ref(null)
const 当前查看智能体 = ref(null)
const 统计信息 = ref(null)
const 调试信息 = ref(null)

// 搜索条件
const 搜索条件 = reactive({
  搜索关键词: '',
  是否启用: null,
  是否公开: null
})

// 分页配置
const 分页配置 = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})



// 优化的分配表单
const 分配表单 = reactive({
  权限级别: '读写权限',
  分配类型: '个人分配',
  备注: ''
})

// 用户搜索
const 用户搜索关键词 = ref('')
const 搜索用户列表 = ref([])
const 选中用户列表 = ref([])

// 分配情况
const 分配情况列表 = ref([])

// 用户分页配置
const 用户分页配置 = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  size: 'small'
})

// 分配情况分页配置
const 分配情况分页配置 = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  size: 'small'
})

// 表格列定义
const 表格列定义 = [
  {
    title: '智能体信息',
    key: 'agent_info',
    width: 250
  },
  {
    title: '类型',
    key: 'agent_type',
    width: 120
  },
  {
    title: '启用状态',
    key: 'is_enabled',
    width: 100
  },
  {
    title: '公开状态',
    key: 'is_public',
    width: 100
  },
  {
    title: '使用统计',
    key: 'usage_stats',
    width: 150
  },
  {
    title: '分配用户数',
    key: 'assigned_users',
    width: 100
  },
  {
    title: '模型',
    key: 'model_name',
    width: 120
  },
  {
    title: '创建时间',
    key: 'created_time',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 用户表格列
const 用户表格列 = [
  {
    title: '用户信息',
    key: 'user_info',
    width: 200
  },
  {
    title: '已分配智能体',
    key: 'assigned_agents',
    width: 120
  },
  {
    title: '注册时间',
    dataIndex: '注册时间',
    width: 150
  }
]

// 行选择配置
const 行选择配置 = {
  selectedRowKeys: 选中的智能体列表,
  onChange: (selectedRowKeys, selectedRows) => {
    选中的智能体列表.value = selectedRowKeys
  },
  getCheckboxProps: (record) => ({
    disabled: record.状态 === '初始化中', // 初始化中的智能体不能批量操作
    name: record.智能体名称,
  }),
}

// 优化的用户表格列（用于分配对话框）
const 优化的用户表格列 = [
  {
    title: '用户信息',
    key: 'user_info',
    width: 220,
    fixed: 'left'
  },
  {
    title: '状态',
    key: 'user_status',
    width: 80,
    align: 'center'
  },
  {
    title: '已分配智能体',
    key: 'assigned_agents',
    width: 100,
    align: 'center'
  },
  {
    title: '最后活跃',
    key: 'last_active',
    width: 120,
    align: 'center'
  }
]

// 分配情况表格列
const 分配情况表格列 = [
  {
    title: '用户信息',
    key: 'user_info',
    width: 150
  },
  {
    title: '权限级别',
    key: 'permission_level',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '使用次数',
    dataIndex: '使用次数',
    width: 80
  },
  {
    title: '分配时间',
    dataIndex: '分配时间',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 100
  }
]

// 用户选择配置
const 用户选择配置 = {
  selectedRowKeys: 选中用户列表,
  onChange: (selectedRowKeys) => {
    选中用户列表.value = selectedRowKeys
  }
}

// 工具方法 - 使用统一的 langchainUtils.js 中的方法
const 格式化权限级别 = LangChainUtils.格式化权限级别
const 获取权限颜色 = LangChainUtils.获取权限级别颜色
const 格式化时间 = LangChainUtils.格式化时间

// 格式化相对时间
const 格式化相对时间 = (时间) => {
  if (!时间) return '从未使用'

  const now = new Date()
  const time = new Date(时间)
  const diff = now - time

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return 格式化时间(时间)
}

// 数据缓存
const 列表数据缓存 = new Map()
const 缓存过期时间 = 30 * 1000 // 30秒

// 主要方法
const 加载智能体列表 = async (强制刷新 = false) => {
  try {
    加载中.value = true

    // 生成缓存键
    const 缓存键 = JSON.stringify({
      页码: 分页配置.current,
      每页数量: 分页配置.pageSize,
      搜索条件
    })

    // 检查缓存
    const 缓存数据 = 列表数据缓存.get(缓存键)
    const 现在 = Date.now()

    if (!强制刷新 && 缓存数据 && (现在 - 缓存数据.时间戳) < 缓存过期时间) {
      智能体列表.value = 缓存数据.智能体列表
      分页配置.total = 缓存数据.总数量

      // 仍然加载统计数据，因为这个更新频繁
      await 加载智能体使用统计()
      return
    }

    const response = await adminLangChainService.获取所有智能体列表({
      页码: 分页配置.current,
      每页数量: 分页配置.pageSize,
      ...搜索条件
    })

    if (response.status === 100) {
      const 智能体列表数据 = response.data.智能体列表 || []
      const 总数量 = response.data.总数量 || 0

      智能体列表.value = 智能体列表数据
      分页配置.total = 总数量

      // 更新缓存
      列表数据缓存.set(缓存键, {
        智能体列表: 智能体列表数据,
        总数量,
        时间戳: 现在
      })

      // 为每个智能体加载使用统计
      await 加载智能体使用统计()
    } else {
      message.error(response.message || '获取智能体列表失败')
    }
  } catch (error) {
    console.error('加载智能体列表失败:', error)
    message.error('加载智能体列表失败')
  } finally {
    加载中.value = false
  }
}

// 加载智能体使用统计
const 加载智能体使用统计 = async () => {
  try {
    // 获取所有智能体的使用统计
    const 智能体ids = 智能体列表.value.map(agent => agent.智能体id)

    if (智能体ids.length === 0) return

    const response = await langchainService.获取使用统计数据({
      智能体id列表: 智能体ids,
      统计维度: '总计'
    })

    if (response.status === 100 && response.data.智能体排行) {
      // 将统计数据合并到智能体列表中
      智能体列表.value = 智能体列表.value.map(agent => {
        const 统计数据 = response.data.智能体排行.find(stat => stat.智能体id === agent.智能体id)
        return {
          ...agent,
          使用统计: 统计数据 ? {
            对话次数: 统计数据.对话次数 || 0,
            用户数量: 统计数据.用户数量 || 0,
            最后使用时间: 统计数据.最后使用时间
          } : {
            对话次数: 0,
            用户数量: 0,
            最后使用时间: null
          }
        }
      })
    }
  } catch (error) {
    console.error('加载智能体使用统计失败:', error)
    // 不显示错误消息，因为这是辅助功能
  }
}

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 防抖搜索
const 防抖搜索智能体 = debounce(() => {
  分页配置.current = 1
  加载智能体列表()
}, 500)

const 搜索智能体 = () => {
  防抖搜索智能体()
}

const 刷新列表 = () => {
  // 清除缓存，强制刷新
  列表数据缓存.clear()
  加载智能体列表(true)
}

const 跳转创建页面 = () => {
  router.push('/langchain/agents/create')
}

const 取消创建 = () => {
  创建对话框显示.value = false
  编辑模式.value = false
  当前编辑智能体.value = null

  // 重置表单
  Object.assign(创建表单, {
    智能体名称: '',
    智能体描述: '',
    智能体类型: 'basic_chat',
    模型名称: 'qwen-turbo',
    系统提示词: '你是一个友好、专业的AI助手，请根据用户的问题提供准确、有用的回答。',
    用户提示词: '',
    角色设定: '',
    行为规范: '',
    温度参数: 0.7,
    最大令牌数: 4000,
    记忆窗口大小: 10,
    启用rag: false,
    知识库列表: [],
    工具列表: [],
    输出格式: 'text',
    是否公开: 1,
    标签: []
  })
}

const 验证创建表单 = () => {
  const 错误列表 = []

  if (!创建表单.智能体名称?.trim()) {
    错误列表.push('智能体名称不能为空')
  } else if (创建表单.智能体名称.length > 100) {
    错误列表.push('智能体名称不能超过100个字符')
  }

  if (!创建表单.系统提示词?.trim()) {
    错误列表.push('系统提示词不能为空')
  } else if (创建表单.系统提示词.length > 2000) {
    错误列表.push('系统提示词不能超过2000个字符')
  }

  if (创建表单.智能体描述 && 创建表单.智能体描述.length > 500) {
    错误列表.push('智能体描述不能超过500个字符')
  }

  if (创建表单.温度参数 < 0 || 创建表单.温度参数 > 2) {
    错误列表.push('温度参数必须在0-2之间')
  }

  if (创建表单.最大令牌数 < 100 || 创建表单.最大令牌数 > 32000) {
    错误列表.push('最大令牌数必须在100-32000之间')
  }

  if (创建表单.记忆窗口大小 < 1 || 创建表单.记忆窗口大小 > 50) {
    错误列表.push('记忆窗口大小必须在1-50之间')
  }

  return 错误列表
}

const 确认创建智能体 = async () => {
  try {
    // 表单验证
    const 验证错误 = 验证创建表单()
    if (验证错误.length > 0) {
      message.error(验证错误[0])
      return
    }

    创建中.value = true

    // 准备提交数据
    const 自定义配置 = {
      是否公开: 创建表单.是否公开,
      知识库列表: 创建表单.知识库列表,
      工具列表: 创建表单.工具列表,
      标签: 创建表单.标签
    }

    const 提交数据 = {
      智能体名称: 创建表单.智能体名称.trim(),
      智能体描述: 创建表单.智能体描述?.trim() || '',
      智能体类型: 创建表单.智能体类型,
      模型名称: 创建表单.模型名称,
      系统提示词: 创建表单.系统提示词.trim(),
      用户提示词: 创建表单.用户提示词?.trim() || '',
      角色设定: 创建表单.角色设定?.trim() || '',
      行为规范: 创建表单.行为规范?.trim() || '',
      温度参数: 创建表单.温度参数,
      最大令牌数: 创建表单.最大令牌数,
      记忆窗口大小: 创建表单.记忆窗口大小,
      启用rag: 创建表单.启用rag,
      输出格式: 创建表单.输出格式,
      自定义配置: 自定义配置
    }

    let response
    if (编辑模式.value) {
      // 编辑模式：更新智能体
      response = await adminLangchainService.更新智能体(当前编辑智能体.value.智能体id, 提交数据)
    } else {
      // 创建模式：创建新智能体
      response = await adminLangchainService.管理员创建智能体(提交数据)
    }

    if (response.status === 100) {
      const 操作类型 = 编辑模式.value ? '更新' : '创建'
      message.success(`智能体 "${提交数据.智能体名称}" ${操作类型}成功`)
      创建对话框显示.value = false
      取消创建()
      加载智能体列表()
    } else {
      const 操作类型 = 编辑模式.value ? '更新' : '创建'
      message.error(response.message || `${操作类型}智能体失败`)
    }
  } catch (error) {
    const 操作类型 = 编辑模式.value ? '更新' : '创建'
    console.error(`${操作类型}智能体失败:`, error)
    if (error.response?.data?.message) {
      message.error(error.response.data.message)
    } else {
      message.error(`${操作类型}智能体失败，请稍后重试`)
    }
  } finally {
    创建中.value = false
  }
}

// 优化的分配给用户方法
const 分配给用户 = (智能体) => {
  console.log('🎯 开始分配智能体给用户:', 智能体)
  当前分配智能体.value = 智能体
  分配对话框显示.value = true

  // 重置分配表单 - 使用完整的表单结构
  Object.assign(分配表单, {
    权限级别: '读写权限',
    分配类型: '个人分配',
    备注: ''
  })

  // 重置用户选择状态
  选中用户列表.value = []
  搜索用户列表.value = []
  用户搜索关键词.value = ''

  // 重置分页
  用户分页配置.current = 1
  用户分页配置.total = 0
}

// 优化的搜索用户方法
const 搜索用户 = async () => {
  const 关键词 = 用户搜索关键词.value.trim()
  if (!关键词) {
    message.warning('请输入搜索关键词（用户昵称、手机号或邮箱）')
    return
  }

  try {
    搜索用户中.value = true
    console.log('🔍 搜索用户:', 关键词)

    const response = await adminLangChainService.搜索用户({
      关键词: 关键词,
      页码: 用户分页配置.current,
      每页数量: 用户分页配置.pageSize
    })

    if (response.status === 100) {
      const 用户列表 = response.data.用户列表 || []
      搜索用户列表.value = 用户列表
      用户分页配置.total = response.data.总数量 || 0

      console.log(`✅ 搜索到 ${用户列表.length} 个用户`)

      if (用户列表.length === 0) {
        message.info('未找到匹配的用户，请尝试其他关键词')
      }
    } else {
      message.error(response.message || '搜索用户失败')
      搜索用户列表.value = []
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    message.error('搜索用户失败，请稍后重试')
    搜索用户列表.value = []
  } finally {
    搜索用户中.value = false
  }
}

// 优化的确认分配用户方法
const 确认分配用户 = async () => {
  if (选中用户列表.value.length === 0) {
    message.warning('请选择要分配的用户')
    return
  }

  // 验证分配类型和用户数量
  if (分配表单.分配类型 === '个人分配' && 选中用户列表.value.length > 1) {
    message.warning('个人分配只能选择一个用户，请重新选择或改为共享分配')
    return
  }

  // 获取选中用户的详细信息用于确认
  const 选中用户信息 = 搜索用户列表.value.filter(user =>
    选中用户列表.value.includes(user.id)
  )

  const 用户名称列表 = 选中用户信息.map(user => user.昵称).join('、')

  // 显示确认对话框
  Modal.confirm({
    title: '确认分配智能体',
    content: `确定要将智能体 "${当前分配智能体.value.智能体名称}" 分配给以下 ${选中用户列表.value.length} 个用户吗？\n\n${用户名称列表}\n\n权限级别：${分配表单.权限级别}\n分配类型：${分配表单.分配类型}`,
    okText: '确认分配',
    cancelText: '取消',
    onOk: async () => {
      try {
        分配中.value = true
        console.log('🎯 执行智能体分配:', {
          智能体: 当前分配智能体.value.智能体名称,
          用户数量: 选中用户列表.value.length,
          用户id列表: 选中用户列表.value,
          用户id类型: 选中用户列表.value.map(id => typeof id),
          权限级别: 分配表单.权限级别,
          分配类型: 分配表单.分配类型
        })

        // 调用正确的API
        const response = await adminLangChainService.分配智能体给用户({
          智能体id: 当前分配智能体.value.智能体id,
          分配数据: {
            用户id列表: 选中用户列表.value.map(id => parseInt(id)),
            权限级别: 分配表单.权限级别,
            分配类型: 分配表单.分配类型,
            备注: 分配表单.备注 || `管理员分配 - ${new Date().toLocaleString()}`
          }
        })

        if (response.status === 100) {
          const 成功数量 = response.data.成功分配?.length || 0
          const 失败数量 = response.data.失败分配?.length || 0
          const 跳过数量 = response.data.已分配用户id?.length || 0

          let 消息内容 = `智能体分配完成！`
          if (成功数量 > 0) 消息内容 += ` 成功分配: ${成功数量} 个用户`
          if (跳过数量 > 0) 消息内容 += ` 已存在: ${跳过数量} 个用户`
          if (失败数量 > 0) 消息内容 += ` 失败: ${失败数量} 个用户`

          if (失败数量 > 0) {
            message.warning(消息内容)
            // 显示失败详情弹窗
            const 失败详情 = response.data.失败分配.map(item =>
              `• 用户id ${item.用户id}: ${item.错误}`
            ).join('\n')

            Modal.warning({
              title: '分配结果详情',
              content: h('div', { style: 'white-space: pre-line' }, [
                h('p', `✅ 成功分配：${成功数量} 个用户`),
                跳过数量 > 0 ? h('p', `⚠️ 已存在分配：${跳过数量} 个用户`) : null,
                h('p', `❌ 分配失败：${失败数量} 个用户`),
                h('div', { style: 'margin-top: 12px; padding: 8px; background: #fff2f0; border-radius: 4px' }, [
                  h('strong', '失败原因：'),
                  h('div', { style: 'margin-top: 4px; font-family: monospace' }, 失败详情)
                ])
              ]),
              width: 600
            })
          } else if (跳过数量 > 0) {
            message.info(消息内容)
          } else {
            message.success(消息内容)
          }

          // 关闭对话框并刷新列表
          分配对话框显示.value = false
          await 加载智能体列表()

        } else {
          message.error(response.message || '分配智能体失败')
        }
      } catch (error) {
        console.error('分配智能体失败:', error)
        message.error('分配智能体失败，请稍后重试')
      } finally {
        分配中.value = false
      }
    }
  })
}

const 取消分配 = () => {
  分配对话框显示.value = false
  当前分配智能体.value = null
}

const 查看分配情况 = async (智能体) => {
  当前查看智能体.value = 智能体
  分配情况抽屉显示.value = true
  await 加载分配情况()
}

const 加载分配情况 = async () => {
  try {
    加载分配情况中.value = true

    const response = await adminLangChainService.获取用户智能体分配列表({
      智能体id: 当前查看智能体.value.智能体id,
      页码: 分配情况分页配置.current,
      每页数量: 分配情况分页配置.pageSize
    })

    if (response.status === 100) {
      分配情况列表.value = response.data.分配列表 || []
      分配情况分页配置.total = response.data.总数量 || 0
    } else {
      message.error(response.message || '获取分配情况失败')
    }
  } catch (error) {
    console.error('获取分配情况失败:', error)
    message.error('获取分配情况失败')
  } finally {
    加载分配情况中.value = false
  }
}

const 撤销分配 = async (分配id) => {
  try {
    const response = await adminLangChainService.撤销用户智能体分配(分配id)

    if (response.status === 100) {
      message.success('分配撤销成功')
      await 加载分配情况()
      加载智能体列表()
    } else {
      message.error(response.message || '撤销分配失败')
    }
  } catch (error) {
    console.error('撤销分配失败:', error)
    message.error('撤销分配失败')
  }
}

const 跳转编辑页面 = (智能体) => {
  console.log('🔍 跳转编辑页面 - 智能体数据:', 智能体)
  console.log('🔍 跳转编辑页面 - 智能体id:', 智能体.智能体id)
  router.push(`/langchain/agents/${智能体.智能体id}/edit`)
}

// 查看智能体详情 - 跳转到编辑页面
const 查看智能体详情 = (智能体) => {
  console.log('🔍 查看智能体详情:', 智能体)
  router.push(`/langchain/agents/${智能体.智能体id}/edit`)
}

// 确认删除智能体 - 使用Modal确认
const 确认删除智能体 = (智能体) => {
  Modal.confirm({
    title: '确认删除智能体',
    content: `确定要删除智能体 "${智能体.智能体名称}" 吗？删除后所有相关数据将被清除，此操作不可恢复！`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => 删除智能体(智能体.智能体id)
  })
}

const 删除智能体 = async (智能体id) => {
  try {
    const response = await adminLangChainService.删除智能体(智能体id)

    if (response.status === 100) {
      message.success('智能体删除成功')
      加载智能体列表()
    } else {
      message.error(response.message || '删除智能体失败')
    }
  } catch (error) {
    console.error('删除智能体失败:', error)
    message.error('删除智能体失败')
  }
}

// 切换智能体公开状态
const 切换智能体公开状态 = async (智能体, 新状态) => {
  try {
    // 设置加载状态
    智能体._切换公开状态中 = true

    // 转换为数字类型：true -> 1, false -> 0
    const 数字状态 = 新状态 ? 1 : 0

    const response = await adminLangChainService.更新智能体(智能体.智能体id, {
      是否公开: 数字状态
    })

    if (response.status === 100) {
      // 更新本地数据为数字类型
      智能体.是否公开 = 数字状态
      message.success(`智能体已${新状态 ? '设为公开' : '设为私有'}`)
    } else {
      message.error(response.message || '更新公开状态失败')
      // 恢复原状态 - 保持数字类型
      智能体.是否公开 = 新状态 ? 0 : 1
    }
  } catch (error) {
    console.error('切换公开状态失败:', error)
    message.error('切换公开状态失败')
    // 恢复原状态 - 保持数字类型
    智能体.是否公开 = 新状态 ? 0 : 1
  } finally {
    // 清除加载状态
    智能体._切换公开状态中 = false
  }
}

// 切换智能体启用状态
const 切换智能体启用状态 = async (智能体, 新状态) => {
  try {
    // 设置加载状态
    智能体._切换启用状态中 = true

    const response = await adminLangChainService.切换智能体启用状态(智能体.智能体id, 新状态)

    if (response.status === 100) {
      // 更新本地数据
      智能体.是否启用 = 新状态
      message.success(`智能体已${新状态 ? '启用' : '禁用'}`)
    } else {
      message.error(response.message || '更新启用状态失败')
      // 恢复原状态
      智能体.是否启用 = !新状态
    }
  } catch (error) {
    console.error('切换启用状态失败:', error)
    message.error('切换启用状态失败')
    // 恢复原状态
    智能体.是否启用 = !新状态
  } finally {
    // 清除加载状态
    智能体._切换启用状态中 = false
  }
}

const 重新加载智能体配置 = async (智能体id) => {
  try {
    const response = await adminLangChainService.重新加载智能体配置(智能体id)

    if (response.status === 100) {
      message.success('智能体配置重新加载成功')
    } else {
      message.error(response.message || '重新加载智能体配置失败')
    }
  } catch (error) {
    console.error('重新加载智能体配置失败:', error)
    message.error('重新加载智能体配置失败')
  }
}

const 重新加载所有智能体 = async () => {
  try {
    const response = await adminLangChainService.重新加载所有智能体()

    if (response.status === 100) {
      message.success('所有智能体配置重新加载成功')
    } else {
      message.error(response.message || '重新加载所有智能体失败')
    }
  } catch (error) {
    console.error('重新加载所有智能体失败:', error)
    message.error('重新加载所有智能体失败')
  }
}



// 批量切换启用状态
const 批量切换启用状态 = async (目标状态) => {
  if (选中的智能体列表.value.length === 0) {
    message.warning('请先选择要操作的智能体')
    return
  }

  const 操作文本 = 目标状态 ? '启用' : '禁用'
  const 选中的智能体 = 智能体列表.value.filter(item =>
    选中的智能体列表.value.includes(item.智能体id)
  )

  Modal.confirm({
    title: `批量${操作文本}智能体`,
    content: h('div', [
      h('p', `确定要${操作文本}以下 ${选中的智能体.length} 个智能体吗？`),
      h('div', { style: 'margin: 12px 0; max-height: 200px; overflow-y: auto; padding: 8px; background: #f5f5f5; border-radius: 4px' },
        选中的智能体.map(agent =>
          h('div', { style: 'margin: 4px 0' }, `• ${agent.智能体名称} (${agent.是否启用 ? '已启用' : '已禁用'})`)
        )
      ),
      h('div', { style: 'margin-top: 12px; padding: 8px; background: #f6ffed; border-radius: 4px; border-left: 3px solid #52c41a' }, [
        h('strong', '批量操作说明：'),
        h('ul', { style: 'margin: 4px 0 0 16px; padding: 0' }, [
          h('li', '启用：智能体可以分配给用户使用'),
          h('li', '禁用：智能体暂时停用，无法分配给新用户')
        ])
      ])
    ]),
    okText: `确认${操作文本}`,
    cancelText: '取消',
    width: 600,
    onOk: async () => {
      批量操作中.value = true
      let 成功数量 = 0
      let 失败数量 = 0
      const 失败列表 = []

      try {
        for (const 智能体id of 选中的智能体列表.value) {
          try {
            const response = await adminLangChainService.切换智能体启用状态(智能体id, 目标状态)

            if (response.status === 100) {
              成功数量++
            } else {
              失败数量++
              const 智能体名称 = 选中的智能体.find(a => a.智能体id === 智能体id)?.智能体名称 || `ID:${智能体id}`
              失败列表.push(`${智能体名称}: ${response.message}`)
            }
          } catch (error) {
            失败数量++
            const 智能体名称 = 选中的智能体.find(a => a.智能体id === 智能体id)?.智能体名称 || `ID:${智能体id}`
            失败列表.push(`${智能体名称}: ${error.message}`)
          }
        }

        // 显示结果
        if (成功数量 > 0 && 失败数量 === 0) {
          message.success(`批量${操作文本}成功，共处理 ${成功数量} 个智能体`)
        } else if (成功数量 > 0 && 失败数量 > 0) {
          message.warning(`批量${操作文本}部分成功：成功 ${成功数量} 个，失败 ${失败数量} 个`)
          Modal.warning({
            title: '批量操作结果',
            content: h('div', [
              h('p', `✅ 成功：${成功数量} 个智能体`),
              h('p', `❌ 失败：${失败数量} 个智能体`),
              h('div', { style: 'margin-top: 12px; padding: 8px; background: #fff2f0; border-radius: 4px' }, [
                h('strong', '失败详情：'),
                h('div', { style: 'margin-top: 4px; font-family: monospace' },
                  失败列表.map(item => h('div', item))
                )
              ])
            ]),
            width: 500
          })
        } else {
          message.error(`批量${操作文本}失败`)
        }

        // 清空选择并刷新列表
        选中的智能体列表.value = []
        await 加载智能体列表()

      } catch (error) {
        console.error('批量操作失败:', error)
        message.error('批量操作失败')
      } finally {
        批量操作中.value = false
      }
    }
  })
}

const 查看统计 = async () => {
  try {
    const response = await adminLangChainService.获取智能体统计信息()

    if (response.status === 100) {
      统计信息.value = response.data
      统计抽屉显示.value = true
    } else {
      message.error(response.message || '获取统计信息失败')
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    message.error('获取统计信息失败')
  }
}

// 查看智能体使用统计
const 查看智能体使用统计 = (智能体) => {
  // 跳转到使用统计页面，并传入智能体id参数
  router.push({
    path: '/admin/langchain/usage-statistics',
    query: {
      智能体id: 智能体.智能体id,
      智能体名称: 智能体.智能体名称
    }
  })
}

// 数据一致性检查
const 检查数据一致性 = async () => {
  try {
    Modal.info({
      title: '数据一致性检查',
      content: h('div', [
        h('p', '正在检查系统数据一致性...'),
        h('div', { style: 'margin: 12px 0; padding: 8px; background: #f6ffed; border-radius: 4px' }, [
          h('strong', '检查项目：'),
          h('ul', { style: 'margin: 4px 0 0 16px; padding: 0' }, [
            h('li', '智能体状态数据完整性'),
            h('li', '用户分配关系有效性'),
            h('li', '权限级别数据规范性'),
            h('li', '分配状态数据一致性')
          ])
        ]),
        h('p', { style: 'color: #1890ff' }, '检查完成后将显示详细报告...')
      ]),
      width: 500,
      onOk: async () => {
        // 这里可以调用后端API进行数据检查
        message.info('数据一致性检查功能开发中...')
      }
    })
  } catch (error) {
    console.error('数据一致性检查失败:', error)
    message.error('检查失败')
  }
}

const 查看系统调试信息 = async () => {
  try {
    const response = await adminLangChainService.获取智能体工厂调试信息()

    if (response.status === 100) {
      调试信息.value = response.data
      调试抽屉显示.value = true
    } else {
      message.error(response.message || '获取调试信息失败')
    }
  } catch (error) {
    console.error('获取调试信息失败:', error)
    message.error('获取调试信息失败')
  }
}

const 处理表格变化 = (pagination) => {
  分页配置.current = pagination.current
  分页配置.pageSize = pagination.pageSize
  加载智能体列表()
}

// 优化的行属性 - 只在非操作列区域响应点击
const 自定义行属性 = (record) => {
  return {
    style: { cursor: 'pointer' },
    onClick: (event) => {
      // 检查点击的目标元素，避免在操作按钮区域和开关控件触发行点击
      const target = event.target
      const isActionButton = target.closest('.ant-btn') ||
                            target.closest('.ant-popconfirm') ||
                            target.closest('.ant-space')

      // 检查是否点击了公开状态开关
      const isPublicSwitch = target.closest('.ant-switch') ||
                           target.closest('.public-status-control')

      // 如果点击的是操作按钮区域或公开状态开关，不执行行点击逻辑
      if (isActionButton || isPublicSwitch) {
        event.stopPropagation()
        return
      }

      // 跳转到智能体编辑页面
      console.log('🔍 行点击 - 智能体数据:', record)
      console.log('🔍 行点击 - 智能体id:', record.智能体id)
      router.push(`/langchain/agents/${record.智能体id}/edit`)
    }
  }
}

const 处理用户表格变化 = (pagination) => {
  用户分页配置.current = pagination.current
  用户分页配置.pageSize = pagination.pageSize
  搜索用户()
}

const 处理分配情况表格变化 = (pagination) => {
  分配情况分页配置.current = pagination.current
  分配情况分页配置.pageSize = pagination.pageSize
  加载分配情况()
}

// ==================== 辅助方法 ====================
// 注意：格式化方法已在文件开头通过 LangChainUtils 引入，无需重复定义

// 生命周期
onMounted(() => {
  加载智能体列表()
})
</script>

<style scoped>
.admin-agent-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 16px;
}

.list-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.agent-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.agent-details {
  flex: 1;
}

.agent-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.agent-id {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.agent-desc {
  font-size: 12px;
  color: #666;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.user-contact {
  font-size: 12px;
  color: #8c8c8c;
}

.param-help {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 状态标签样式 */
:deep(.ant-badge-status-success) {
  background-color: #52c41a;
}

:deep(.ant-badge-status-processing) {
  background-color: #1890ff;
}

:deep(.ant-badge-status-warning) {
  background-color: #faad14;
}

:deep(.ant-badge-status-error) {
  background-color: #ff4d4f;
}

/* 抽屉内容样式 */
:deep(.ant-drawer-body) {
  padding: 16px;
}

/* 模态框样式 */
:deep(.ant-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-agent-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .search-card :deep(.ant-row) {
    flex-direction: column;
    gap: 12px;
  }

  .search-card :deep(.ant-col) {
    width: 100% !important;
  }

  .agent-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .agent-details {
    width: 100%;
  }

  .agent-desc {
    max-width: none;
    white-space: normal;
  }
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
  font-weight: 600;
}

/* 列表项样式 */
:deep(.ant-list-item-meta-title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.ant-list-item-meta-description) {
  font-size: 12px;
  color: #8c8c8c;
}

/* 表单样式优化 */
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-slider-mark-text) {
  font-size: 12px;
}

/* 标签样式 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
}

/* 按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
}

/* 分页样式 */
:deep(.ant-pagination) {
  margin-top: 16px;
  text-align: right;
}

/* 优化的分配对话框样式 */
.agent-card-title {
  display: flex;
  align-items: center;
}

.agent-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.agent-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.user-count {
  font-size: 12px;
  color: #8c8c8c;
}

.permission-option {
  display: flex;
  flex-direction: column;
}

.permission-name {
  font-weight: 500;
  color: #262626;
}

.permission-desc {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.user-selection-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info-enhanced {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details-enhanced {
  flex: 1;
}

.user-details-enhanced .user-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.user-details-enhanced .user-contact {
  font-size: 12px;
  color: #8c8c8c;
}

.last-active {
  font-size: 12px;
  color: #8c8c8c;
}

/* 表格行悬停样式 - 提示用户可点击 */
:deep(.ant-table-tbody > tr:hover) {
  background-color: #f5f5f5 !important;
  transition: background-color 0.2s ease;
}

:deep(.ant-table-tbody > tr) {
  transition: background-color 0.2s ease;
}

/* 加载状态样式 */
:deep(.ant-spin-container) {
  min-height: 200px;
}

/* 使用统计样式 */
.usage-stats {
  font-size: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #8c8c8c;
  margin-right: 8px;
}

.stat-value {
  font-weight: 500;
  color: #1890ff;
}

.stat-time {
  font-weight: 500;
  color: #52c41a;
}

/* 空状态样式 */
:deep(.ant-empty) {
  margin: 40px 0;
}

/* 公开状态控制样式 */
.public-status-control {
  display: flex;
  align-items: center;
  justify-content: center;
}

.public-status-control .ant-switch {
  min-width: 60px;
}

/* 分配类型选择样式 */
.assignment-type-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.assignment-type-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.assignment-type-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
}

/* 权限级别选择样式 */
.permission-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.permission-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.permission-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
}

.public-status-control .ant-switch-checked {
  background-color: #52c41a;
}

.public-status-control .ant-switch:not(.ant-switch-checked) {
  background-color: #fa8c16;
}
</style>
