"""
权限管理相关的Pydantic模型
包含权限管理、角色管理等相关的请求和响应模型
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field


# =============== 枚举定义 ===============

class 权限操作类型枚举(str, Enum):
    """权限操作类型"""
    add = "add"
    remove = "remove"
    replace = "replace"

class 权限状态枚举(str, Enum):
    """权限状态"""
    启用 = "启用"
    禁用 = "禁用"

class 权限变更操作枚举(str, Enum):
    """权限变更操作类型"""
    角色权限更新 = "角色权限更新"
    成员角色变更 = "成员角色变更"
    成员权限变更 = "成员权限变更"
    岗位创建 = "岗位创建"
    岗位更新 = "岗位更新"
    岗位删除 = "岗位删除"



# =============== 权限相关请求模型 ===============

class 用户团队权限状态请求(BaseModel):
    """检查用户在团队中的权限状态请求"""
    团队id: int = Field(..., description="团队id")
    用户id: Optional[int] = Field(None, description="用户id，为空时检查当前登录用户")

class 更新角色权限请求(BaseModel):
    """更新角色权限请求"""
    团队id: int = Field(..., description="团队id")
    角色: str = Field(..., description="角色类型或自定义岗位ID")
    权限列表: List[str] = Field(..., description="权限代码列表")

class 批量更新角色请求(BaseModel):
    """批量更新成员角色请求"""
    团队id: int = Field(..., description="团队id")
    成员id列表: List[int] = Field(..., description="成员用户id列表")
    角色: str = Field(..., description="要设置的角色")

class 更新成员权限请求(BaseModel):
    """更新单个成员权限请求"""
    团队id: int = Field(..., description="团队id")
    用户id: int = Field(..., description="要更新权限的用户id")
    权限列表: List[str] = Field(..., description="要设置的权限代码列表")
    操作: 权限操作类型枚举 = Field(权限操作类型枚举.replace, description="操作类型：add添加、remove移除、replace替换")


class 获取权限变更日志请求(BaseModel):
    """获取权限变更日志请求"""
    团队id: int = Field(..., description="团队id")
    页码: int = Field(1, ge=1, description="页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    操作类型: Optional[str] = Field(None, description="操作类型筛选")
    目标用户id: Optional[int] = Field(None, description="目标用户id筛选")

class 获取所有权限列表请求(BaseModel):
    """获取所有权限列表请求"""
    pass  # 空请求体，为了统一POST格式




# =============== 权限相关响应模型 ===============

class 权限基础模型(BaseModel):
    """权限基础模型"""
    id: Optional[int] = Field(None, description="权限ID")
    权限代码: str = Field(..., description="权限代码")
    权限名称: str = Field(..., description="权限名称")
    权限描述: Optional[str] = Field(None, description="权限描述")
    权限分类: Optional[str] = Field(None, description="权限分类")
    是否系统权限: bool = Field(False, description="是否为系统权限")
    排序: int = Field(0, description="显示排序")
    状态: 权限状态枚举 = Field(权限状态枚举.启用, description="权限状态")




class 用户团队权限状态模型(BaseModel):
    """用户在团队中的权限状态模型"""
    用户id: int = Field(..., description="用户id")
    团队id: int = Field(..., description="团队id")
    用户姓名: Optional[str] = Field(None, description="用户姓名")
    团队名称: Optional[str] = Field(None, description="团队名称")
    用户角色: Optional[str] = Field(None, description="用户在团队中的角色")
    成员状态: Optional[str] = Field(None, description="用户在团队中的状态")
    权限列表: List[str] = Field([], description="用户拥有的权限代码列表")
    角色权限: List[str] = Field([], description="角色权限列表")
    个人权限: List[str] = Field([], description="个人独立权限列表")
    是否团队创建者: bool = Field(False, description="是否为团队创建者")
    是否团队负责人: bool = Field(False, description="是否为团队负责人")
    加入时间: Optional[datetime] = Field(None, description="加入团队时间")

class 权限变更日志模型(BaseModel):
    """权限变更日志模型"""
    id: Optional[int] = Field(None, description="日志ID")
    团队id: int = Field(..., description="团队id")
    操作人ID: Optional[int] = Field(None, description="操作人ID")
    操作人姓名: Optional[str] = Field(None, description="操作人姓名")
    操作类型: 权限变更操作枚举 = Field(..., description="操作类型")
    操作描述: str = Field(..., description="操作描述")
    目标用户id: Optional[int] = Field(None, description="目标用户id")
    目标用户姓名: Optional[str] = Field(None, description="目标用户姓名")
    变更前数据: Optional[Dict[str, Any]] = Field(None, description="变更前的数据")
    变更后数据: Optional[Dict[str, Any]] = Field(None, description="变更后的数据")
    操作时间: Optional[datetime] = Field(None, description="操作时间")
    IP地址: Optional[str] = Field(None, description="操作IP地址")


# =============== 列表响应模型 ===============

class 权限列表响应模型(BaseModel):
    """权限列表响应模型"""
    list: List[权限基础模型] = Field(..., description="权限列表")



class 权限变更日志列表响应模型(BaseModel):
    """权限变更日志列表响应模型"""
    list: List[权限变更日志模型] = Field(..., description="权限变更日志列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: Optional[int] = Field(None, description="总页数")


# =============== 操作结果模型 ===============

class 批量权限操作结果模型(BaseModel):
    """批量权限操作结果模型"""
    成功数量: int = Field(..., description="成功操作的数量")
    失败数量: int = Field(..., description="失败操作的数量")
    总数量: int = Field(..., description="总操作数量")
    失败详情: Optional[List[Dict[str, Any]]] = Field(None, description="失败操作的详情") 