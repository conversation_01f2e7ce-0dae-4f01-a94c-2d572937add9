# 邀约系统管理前端

一个基于 Vue 3 + Ant Design Vue 的现代化管理后台系统，提供完整的用户管理、通知管理、系统监控等功能。

## 🚀 项目特性

### 核心功能
- 📊 **仪表盘** - 系统运行状态和数据统计展示
- 👥 **用户管理** - 完整的用户CRUD操作和详情查看
- 📢 **通告管理** - 系统通知和公告的发布管理
- 🔍 **日志管理** - API调用日志、文件日志和实时日志监控
- ⚙️ **系统设置** - 系统参数配置和邮件服务设置
- 🧪 **接口测试** - 内置API测试平台
- 📥 **数据导入** - 线索批量导入功能

### 技术亮点
- 🎨 **现代化UI** - 优雅的界面设计和丰富的交互动画
- 📱 **响应式布局** - 完美适配桌面端和移动端
- 🔒 **权限控制** - 基于角色的访问控制
- 🔄 **实时数据** - WebSocket支持和自动刷新
- 🚦 **状态管理** - Pinia状态管理和持久化
- 🔧 **API集成** - 统一的API服务层和错误处理

## 🛠️ 技术栈

### 前端框架
- **Vue 3.4.0** - 采用Composition API，提供更好的TypeScript支持
- **Vite 5.2.0** - 快速的构建工具，支持热更新
- **Vue Router 4.3.0** - 路由管理
- **Pinia 2.1.0** - 状态管理

### UI组件库
- **Ant Design Vue 4.1.0** - 企业级UI组件库
- **@ant-design/icons-vue 7.0.0** - 图标库

### 工具库
- **Axios 1.6.0** - HTTP客户端
- **Day.js 1.11.0** - 日期处理
- **ECharts** - 图表库
- **xlsx 0.18.5** - Excel文件处理

## 📁 项目结构

```
admin-frontend/
├── public/                     # 静态资源
├── src/                       # 源代码
│   ├── components/            # 公共组件
│   │   ├── common/            # 通用组件
│   │   └── api-testing/       # 接口测试组件
│   ├── composables/           # 组合式函数
│   │   ├── useApiService.js   # API服务Hook
│   │   └── useApi.js          # 通用API Hook
│   ├── layouts/               # 布局组件
│   │   └── AdminLayout.vue    # 管理后台主布局
│   ├── router/                # 路由配置
│   │   └── index.js           # 路由定义
│   ├── services/              # API服务层
│   │   ├── api.js             # Axios配置
│   │   └── apiService.js      # API服务类
│   ├── store/                 # 状态管理
│   │   └── index.js           # Pinia store
│   ├── views/                 # 页面组件
│   │   ├── Dashboard.vue      # 仪表盘
│   │   ├── UserManagement.vue # 用户管理
│   │   ├── NotificationManagement.vue # 通告管理
│   │   ├── SystemSettings.vue # 系统设置
│   │   └── logs/              # 日志管理页面
│   ├── utils/                 # 工具函数
│   ├── App.vue                # 根组件
│   └── main.js                # 应用入口
├── vite.config.js             # Vite配置
├── package.json               # 项目依赖
└── README.md                  # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd admin-frontend
npm install
```

### 开发环境启动
```bash
npm run dev
```

### 生产构建
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 🎯 核心功能介绍

### 1. 用户管理系统
- **用户列表** - 分页展示、搜索筛选、批量操作
- **用户详情** - 完整信息展示、关联店铺、登录历史
- **用户操作** - 新增、编辑、删除、状态切换

### 2. 仪表盘监控
- **系统概览** - 核心指标统计和趋势图表
- **实时监控** - CPU、内存、磁盘使用率
- **活动日志** - 最近系统活动和操作记录

### 3. 通告管理
- **内容编辑** - 富文本编辑和HTML支持
- **发布控制** - 定时发布、状态管理
- **权限设置** - 重要性级别、排序设置

### 4. 日志系统
- **API调用日志** - 详细的接口调用记录和性能统计
- **文件日志查看** - 系统日志文件在线查看
- **实时日志** - WebSocket实时日志推送

### 5. 系统设置
- **基础配置** - 站点信息、维护模式
- **邮件服务** - SMTP配置和测试
- **API设置** - 速率限制、超时配置

## 🔧 API服务架构

### API服务映射
```javascript
const apiServiceMap = {
  'dashboard': dashboardApi,        // 仪表盘数据
  'userManagement': userManagementApi,  // 用户管理
  'announcement': announcementApi,   // 通告管理
  'logManagement': logManagementApi, // 日志管理
  'auth': authApi                    // 认证服务
};
```

### 使用示例
```javascript
// 在组件中使用API服务
const { executeRequest, loading, error } = useApiService('userManagement')

// 调用API方法
const responseData = await executeRequest('getUserList', [params])
if (responseData.status === 100) {
  // 处理成功响应
  const data = responseData.message
}
```

## 🎨 UI设计特色

### 导航栏设计
- **渐变Logo** - 毛玻璃效果，支持收缩状态
- **智能菜单** - 图标映射、悬停效果、选中高亮
- **面包屑导航** - 智能路径生成，支持图标显示

### 头部功能区
- **系统状态指示器** - 实时系统状态，带脉冲动画
- **通知中心** - 消息徽章提醒
- **用户菜单** - 个人设置、退出登录

### 页面过渡
- **淡入淡出** - 页面切换动画
- **加载状态** - 统一的加载指示器
- **错误边界** - 优雅的错误处理

## 🔒 权限控制

### 路由守卫
```javascript
// 全局前置守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  const isAuthenticated = userStore.isAuthenticated
  
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isAuthenticated) {
      next({ name: 'login', query: { redirect: to.fullPath } })
    } else {
      next()
    }
  } else {
    next()
  }
})
```

### 状态管理
- **用户认证** - JWT Token管理和自动续期
- **权限缓存** - 本地存储和状态同步
- **会话控制** - 自动登出和活动检测

## 📊 性能优化

### 构建优化
- **代码分割** - 按路由自动分割
- **懒加载** - 组件和路由懒加载
- **Tree Shaking** - 移除未使用代码
- **资源压缩** - 图片和代码压缩

### 运行时优化
- **虚拟滚动** - 大数据列表优化
- **防抖节流** - 搜索和输入优化
- **缓存策略** - API响应缓存
- **并行加载** - 数据并行获取

## 🧪 开发指南

### 添加新页面
1. 在 `src/views/` 创建页面组件
2. 在 `src/router/index.js` 添加路由配置
3. 在 `AdminLayout.vue` 的菜单中添加导航项

### 创建API服务
1. 在 `src/services/apiService.js` 创建服务类
2. 在 `apiServiceMap` 中注册服务
3. 使用 `useApiService` Hook调用API

### 样式开发
- 使用 scoped CSS 避免样式冲突
- 遵循 Ant Design 设计规范
- 使用 CSS 变量保持一致性

## 🐛 常见问题

### 开发环境问题
**Q: 启动时端口被占用**
```bash
# 更改端口
npm run dev -- --port 3001
```

**Q: 热更新不生效**
```bash
# 清除缓存重启
rm -rf node_modules/.vite
npm run dev
```

### 生产环境问题
**Q: 构建后路由404**
- 检查服务器配置，确保支持 History 模式
- 配置 fallback 到 index.html

**Q: API跨域问题**
- 开发环境在 `vite.config.js` 配置代理
- 生产环境确保后端配置 CORS

## 📈 更新日志

### v1.2.0 (最新)
- ✅ 导航栏UI完全重构，支持渐变效果和动画
- ✅ 系统状态指示器和通知中心
- ✅ 面包屑导航优化，支持图标显示
- ✅ 所有页面API调用统一重构
- ✅ 用户详情页面数据加载优化
- ✅ 代码引用错误修复和性能优化

### v1.1.0
- ✅ API服务层重构，统一数据处理
- ✅ 错误处理机制完善
- ✅ 用户管理功能优化
- ✅ 系统设置页面完善

### v1.0.0
- ✅ 基础框架搭建
- ✅ 核心功能实现
- ✅ 基础UI组件

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查浏览器控制台错误信息
3. 查看 `前端优化记录.md` 了解最新改动
4. 联系技术支持团队

---

**项目维护者**: AI Assistant  
**最后更新**: 2024年  
**项目版本**: v1.2.0 