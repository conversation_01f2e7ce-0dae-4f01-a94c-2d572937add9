#!/usr/bin/env python3
"""
修复接口日志表的id字段为现代化自增字段
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 上一级目录是项目根目录
sys.path.insert(0, project_root)

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器

async def 修复接口日志表():
    """修复接口日志表的id字段为现代化自增字段"""
    try:
        应用日志器.info("开始修复接口日志表的id字段...")
        
        # 检查当前表结构
        检查SQL = """
        SELECT column_name, data_type, is_nullable, column_default 
        FROM information_schema.columns 
        WHERE table_name = '接口日志表' AND column_name = 'id'
        """
        
        结果 = await 异步连接池实例.执行查询(检查SQL)
        if 结果:
            应用日志器.info(f"当前id字段信息: {结果[0]}")
        
        # 开始事务修复
        async with 异步连接池实例.获取连接() as conn:
            async with conn.transaction():
                # 1. 备份现有数据（如果有的话）
                备份SQL = """
                CREATE TEMP TABLE 接口日志表_备份 AS 
                SELECT 用户id, ip地址, 请求路径, 请求方法, 状态码, 耗时, 创建时间, 
                       错误信息, 堆栈跟踪, 请求头, 请求参数, 请求体
                FROM 接口日志表
                """
                await conn.execute(备份SQL)
                应用日志器.info("已备份现有数据到临时表")
                
                # 2. 删除原表
                await conn.execute("DROP TABLE 接口日志表")
                应用日志器.info("已删除原表")
                
                # 3. 重新创建表，使用现代化的自增id
                创建表SQL = """
                CREATE TABLE 接口日志表 (
                    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                    用户id INTEGER,
                    ip地址 VARCHAR(45) NOT NULL,
                    请求路径 VARCHAR(500) NOT NULL,
                    请求方法 VARCHAR(10) NOT NULL,
                    状态码 INTEGER NOT NULL,
                    耗时 REAL NOT NULL,
                    创建时间 TIMESTAMP NOT NULL DEFAULT NOW(),
                    错误信息 TEXT,
                    堆栈跟踪 TEXT,
                    请求头 TEXT,
                    请求参数 TEXT,
                    请求体 TEXT
                )
                """
                await conn.execute(创建表SQL)
                应用日志器.info("已重新创建表，使用现代化自增id")
                
                # 4. 恢复数据（如果有的话）
                恢复SQL = """
                INSERT INTO 接口日志表 (用户id, ip地址, 请求路径, 请求方法, 状态码, 耗时, 创建时间, 
                                    错误信息, 堆栈跟踪, 请求头, 请求参数, 请求体)
                SELECT 用户id, ip地址, 请求路径, 请求方法, 状态码, 耗时, 创建时间, 
                       错误信息, 堆栈跟踪, 请求头, 请求参数, 请求体
                FROM 接口日志表_备份
                """
                result = await conn.execute(恢复SQL)
                应用日志器.info(f"已恢复数据，影响行数: {result}")
                
                # 5. 创建索引以提高查询性能
                索引SQL = [
                    "CREATE INDEX idx_接口日志表_用户id ON 接口日志表(用户id)",
                    "CREATE INDEX idx_接口日志表_创建时间 ON 接口日志表(创建时间)",
                    "CREATE INDEX idx_接口日志表_请求路径 ON 接口日志表(请求路径)",
                    "CREATE INDEX idx_接口日志表_状态码 ON 接口日志表(状态码)"
                ]
                
                for sql in 索引SQL:
                    try:
                        await conn.execute(sql)
                        应用日志器.info(f"已创建索引: {sql}")
                    except Exception as e:
                        应用日志器.warning(f"创建索引失败（可能已存在）: {e}")
        
        # 验证修复结果
        验证SQL = """
        SELECT column_name, data_type, is_nullable, column_default 
        FROM information_schema.columns 
        WHERE table_name = '接口日志表' AND column_name = 'id'
        """
        
        验证结果 = await 异步连接池实例.执行查询(验证SQL)
        if 验证结果:
            应用日志器.info(f"修复后id字段信息: {验证结果[0]}")
        
        应用日志器.info("✅ 接口日志表修复完成！")
        return True
        
    except Exception as e:
        应用日志器.error(f"修复接口日志表失败: {e}", exc_info=True)
        return False

async def main():
    """主函数"""
    try:
        # 初始化连接池
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 执行修复
        成功 = await 修复接口日志表()

        if 成功:
            print("✅ 接口日志表修复成功！")
        else:
            print("❌ 接口日志表修复失败！")
            sys.exit(1)

    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # 关闭连接池
        try:
            await 异步连接池实例.关闭连接池()
        except Exception as e:
            print(f"关闭连接池时发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
