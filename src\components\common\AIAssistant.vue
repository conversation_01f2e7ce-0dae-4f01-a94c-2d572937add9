<template>
  <div class="ai-assistant-container">
    <!-- 可拖拽的悬浮触发按钮 -->
    <div 
      v-if="!showChatDialog" 
      class="ai-floating-button"
      @click="handleButtonClick"
      @mousedown="startDragButton"
      :class="{ 'has-unread': hasUnreadMessage }"
      ref="floatingButton"
    >
      <!-- 可爱的AI图标 -->
      <div class="ai-avatar">
        <div class="ai-face">
          <div class="ai-eyes">
            <div class="eye left-eye"></div>
            <div class="eye right-eye"></div>
          </div>
          <div class="ai-mouth"></div>
        </div>
        <div class="ai-glow"></div>
      </div>
      <span v-if="hasUnreadMessage" class="unread-badge">{{ unreadCount }}</span>
    </div>

    <!-- 可拖拽调整大小的对话窗口 -->
    <div 
      v-if="showChatDialog" 
      class="ai-chat-dialog"
      ref="chatDialog"
    >
      <!-- 可拖拽的标题栏 -->
      <div 
        class="chat-header"
        @mousedown="startDragDialog"
        :class="{ 'dragging': isDraggingDialog }"
      >
        <div class="header-title">
          <div class="ai-model-info">
            <div class="ai-model-avatar">
              {{ currentAIModel.名称?.charAt(0) || 'AI' }}
            </div>
            <div class="ai-model-details">
              <span class="ai-model-name">{{ currentAIModel.名称 || 'AI智能助手' }}</span>
              <span class="ai-model-type">{{ aiModeText }}</span>
            </div>
          </div>
          <span class="drag-hint">拖拽移动</span>
        </div>
        <div class="header-actions">
          <!-- AI模型切换按钮 -->
          <button @click="showModelSelector = !showModelSelector" class="header-btn" title="切换AI模型">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </button>
          <button @click="clearConversation" class="header-btn" :disabled="isClearing" :title="isClearing ? '正在清空...' : '清空对话和AI记忆'">
            <svg v-if="!isClearing" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
            </svg>
            <div v-else class="loading-spinner-small"></div>
          </button>
          <button @click="toggleChat" class="header-btn" title="关闭">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

      <!-- AI模型选择器 -->
      <div v-if="showModelSelector" class="ai-model-selector">
        <div class="model-selector-header">
          <span>选择AI模型</span>
          <button @click="showModelSelector = false" class="close-selector">×</button>
        </div>
        <div class="model-options">
          <!-- 默认模式选项 -->
          <div
            class="model-option"
            :class="{ active: aiMode === 'coze' }"
            @click="switchAIMode('coze')"
          >
            <div class="model-option-avatar">默</div>
            <div class="model-option-info">
              <div class="model-option-name">默认模式</div>
              <div class="model-option-desc">Coze AI智能助手</div>
            </div>
            <div class="model-option-status">
              <span v-if="aiMode === 'coze'" class="status-active">使用中</span>
              <span v-if="aiMode === 'coze'" class="power-cost">{{ currentAIModel.算力消耗 || 1 }}算力/次</span>
            </div>
          </div>

          <!-- 测试模式选项 -->
          <div
            class="model-option"
            :class="{ active: aiMode === 'qianwen' }"
            @click="switchAIMode('qianwen')"
          >
            <div class="model-option-avatar">测</div>
            <div class="model-option-info">
              <div class="model-option-name">测试模式</div>
              <div class="model-option-desc">通义千问测试接口</div>
            </div>
            <div class="model-option-status">
              <span v-if="aiMode === 'qianwen'" class="status-active">使用中</span>
            </div>
          </div>
        </div>
      </div>
      </div>
      
      <!-- 调整大小的拖拽手柄 -->
      <div class="resize-handles">
        <!-- 右下角调整大小 -->
        <div 
          class="resize-handle resize-se" 
          @mousedown="(e) => startResizeSE(e)"
        ></div>
        <!-- 右边调整宽度 -->
        <div 
          class="resize-handle resize-e" 
          @mousedown="(e) => startResizeE(e)"
        ></div>
        <!-- 下边调整高度 -->
        <div 
          class="resize-handle resize-s" 
          @mousedown="(e) => startResizeS(e)"
        ></div>
      </div>

      <!-- 消息列表 -->
      <div class="chat-messages" ref="chatMessages">
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-icon">👋</div>
          <div class="welcome-text">
            <h3>欢迎使用AI智能助手！</h3>
            <p>我可以帮助您：</p>
            <ul>
              <li>解答系统使用问题</li>
              <li>指导操作流程</li>
              <li>提供功能说明</li>
            </ul>
          </div>
        </div>

        <div 
          v-for="message in messages" 
          :key="message.id" 
          class="message-item"
          :class="[`message-${message.role}`, { 'message-error': message.isError }]"
        >
          <div class="message-content">
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="typing-indicator">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div class="typing-text">AI正在思考...</div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div v-if="quickActions.length > 0" class="quick-actions">
        <button 
          v-for="action in quickActions" 
          :key="action.id"
          @click="sendQuickMessage(action.message)"
          class="quick-action-btn"
          :disabled="isTyping"
        >
          {{ action.label }}
        </button>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <textarea
            v-model="currentMessage"
            @keydown="handleKeyDown"
            placeholder="输入您的问题..."
            rows="1"
            ref="messageInput"
            :disabled="isTyping"
            maxlength="500"
          ></textarea>
          <button 
            @click="sendMessage" 
            :disabled="!currentMessage.trim() || isTyping"
            class="send-button"
          >
            <svg v-if="!isTyping" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
            </svg>
            <div v-else class="loading-spinner"></div>
          </button>
        </div>
        <div class="input-footer">
          <span class="char-count">{{ currentMessage.length }}/500</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import aiService from '@/services/aiService'
import api from '@/services/api'

// 响应式数据
const showChatDialog = ref(false)
const messages = ref([])
const currentMessage = ref('')
const isTyping = ref(false)
const hasUnreadMessage = ref(false)
const unreadCount = ref(0)

// AI模型相关状态
const aiMode = ref('coze') // 'qianwen' | 'coze' - 默认使用默认模式
const showModelSelector = ref(false)
const currentAIModel = ref({
  名称: '默认模式',
  描述: 'Coze AI智能助手',
  头像: '',
  算力消耗: 1
})


// 清空状态
const isClearing = ref(false)

// 计算属性：AI模式文本
const aiModeText = ref('默认模式')

// 更新AI模式文本
const updateAIModeText = () => {
  if (aiMode.value === 'qianwen') {
    aiModeText.value = '测试模式'
  } else {
    aiModeText.value = '默认模式'
  }
}

// DOM引用
const chatMessages = ref(null)
const messageInput = ref(null)
const floatingButton = ref(null)
const chatDialog = ref(null)

// 拖拽和调整大小相关状态（简化）
const isDraggingDialog = ref(false)
const isResizing = ref(false)
const resizeDirection = ref('')

// 拖拽状态 - 记录拖拽过程中的坐标和尺寸信息
const dragState = ref({
  startX: 0,        // 鼠标起始X坐标
  startY: 0,        // 鼠标起始Y坐标
  initialX: 0,      // 元素起始X位置
  initialY: 0,      // 元素起始Y位置
  initialWidth: 0,  // 元素起始宽度
  initialHeight: 0  // 元素起始高度
})

// 快捷操作
const quickActions = ref([
  { id: '1', label: '系统功能介绍', message: '请介绍一下系统的主要功能' },
  { id: '2', label: '如何操作', message: '如何使用这个系统？' },
  { id: '3', label: '常见问题', message: '有哪些常见问题？' },
  { id: '4', label: '联系客服', message: '如何联系技术支持？' }
])

// 切换对话窗口
const toggleChat = () => {
  showChatDialog.value = !showChatDialog.value
  
  if (showChatDialog.value) {
    hasUnreadMessage.value = false
    unreadCount.value = 0
    nextTick(() => {
      scrollToBottom()
      focusInput()
    })
  }
}

// 拖拽状态管理 - 区分拖拽和点击
let hasMoved = false

/**
 * 处理悬浮按钮点击事件
 * 只有在没有拖拽动作时才触发对话框切换
 */
const handleButtonClick = (e) => {
  // 如果刚刚有拖拽动作，不触发点击
  if (hasMoved) {
    return
  }
  
  // 切换对话框显示状态
  toggleChat()
}

/**
 * 开始拖拽悬浮按钮
 * 记录初始位置，用于区分拖拽和点击
 */
const startDragButton = (e) => {
  e.preventDefault()
  
  // 重置拖拽状态
  hasMoved = false
  
  // 获取按钮当前在屏幕上的真实位置
  const rect = floatingButton.value.getBoundingClientRect()
  
  // 保存鼠标起始位置和按钮起始位置
  dragState.value.startX = e.clientX
  dragState.value.startY = e.clientY
  dragState.value.initialX = rect.left
  dragState.value.initialY = rect.top
  
  // 绑定拖拽事件监听器
  document.addEventListener('mousemove', dragButton)
  document.addEventListener('mouseup', stopDragButton)
}

/**
 * 执行悬浮按钮拖拽
 * 使用直接DOM操作确保流畅性
 */
const dragButton = (e) => {
  // 计算鼠标移动距离
  const deltaX = e.clientX - dragState.value.startX
  const deltaY = e.clientY - dragState.value.startY
  
  // 只有移动距离超过阈值才算真正的拖拽
  if (Math.abs(deltaX) > 3 || Math.abs(deltaY) > 3) {
    hasMoved = true
    
    // 计算新位置，确保不超出屏幕边界
    const newX = Math.max(0, Math.min(window.innerWidth - 60, dragState.value.initialX + deltaX))
    const newY = Math.max(0, Math.min(window.innerHeight - 60, dragState.value.initialY + deltaY))
    
    // 直接操作DOM样式，避免Vue响应式更新带来的性能损耗
    if (floatingButton.value) {
      floatingButton.value.style.left = newX + 'px'
      floatingButton.value.style.top = newY + 'px'
    }
  }
}

/**
 * 停止拖拽悬浮按钮
 * 清理事件监听器，延迟重置状态
 */
const stopDragButton = () => {
  // 移除拖拽事件监听器
  document.removeEventListener('mousemove', dragButton)
  document.removeEventListener('mouseup', stopDragButton)
  
  // 延迟重置拖拽状态，防止立即触发点击事件
  setTimeout(() => {
    hasMoved = false
  }, 50)
}

/**
 * 开始拖拽对话窗口
 * 只允许在标题栏区域拖拽，避免误触
 */
const startDragDialog = (e) => {
  // 防止在按钮区域触发拖拽
  if (e.target.closest('.header-btn')) return
  
  e.preventDefault()
  isDraggingDialog.value = true
  
  // 获取对话窗口当前位置
  const rect = chatDialog.value.getBoundingClientRect()
  
  // 记录拖拽起始状态
  dragState.value.startX = e.clientX
  dragState.value.startY = e.clientY
  dragState.value.initialX = rect.left
  dragState.value.initialY = rect.top
  
  // 绑定拖拽事件
  document.addEventListener('mousemove', dragDialog)
  document.addEventListener('mouseup', stopDragDialog)
}

/**
 * 执行对话窗口拖拽
 * 直接操作DOM确保流畅性，添加边界检测
 */
const dragDialog = (e) => {
  if (!isDraggingDialog.value) return
  
  // 计算移动偏移量
  const deltaX = e.clientX - dragState.value.startX
  const deltaY = e.clientY - dragState.value.startY
  
  // 获取窗口当前宽度，用于边界检测
  const currentWidth = chatDialog.value ? chatDialog.value.offsetWidth : 380
  
  // 计算新位置，确保不超出屏幕边界
  const newX = Math.max(0, Math.min(window.innerWidth - currentWidth, dragState.value.initialX + deltaX))
  const newY = Math.max(0, Math.min(window.innerHeight - 60, dragState.value.initialY + deltaY))
  
  // 直接更新DOM位置
  if (chatDialog.value) {
    chatDialog.value.style.left = newX + 'px'
    chatDialog.value.style.top = newY + 'px'
  }
}

/**
 * 停止拖拽对话窗口
 * 清理事件监听器，重置状态
 */
const stopDragDialog = () => {
  isDraggingDialog.value = false
  document.removeEventListener('mousemove', dragDialog)
  document.removeEventListener('mouseup', stopDragDialog)
}

/**
 * 通用的调整大小开始函数
 * @param {string} direction - 调整方向 (se/e/s)
 */
const startResize = (direction) => (e) => {
  e.preventDefault()
  e.stopPropagation()
  
  // 设置调整状态
  isResizing.value = true
  resizeDirection.value = direction
  
  // 获取当前窗口尺寸
  const rect = chatDialog.value.getBoundingClientRect()
  
  // 记录起始状态
  dragState.value.startX = e.clientX
  dragState.value.startY = e.clientY
  dragState.value.initialWidth = rect.width
  dragState.value.initialHeight = rect.height
  
  // 绑定调整事件
  document.addEventListener('mousemove', resizeDialog)
  document.addEventListener('mouseup', stopResize)
}

// 创建各方向的调整函数
const startResizeSE = startResize('se')  // 右下角
const startResizeE = startResize('e')    // 右边
const startResizeS = startResize('s')    // 下边

/**
 * 执行窗口大小调整
 * 直接操作DOM样式，确保流畅性
 */
const resizeDialog = (e) => {
  if (!isResizing.value || !chatDialog.value) return
  
  // 计算尺寸变化
  const deltaX = e.clientX - dragState.value.startX
  const deltaY = e.clientY - dragState.value.startY
  
  // 水平方向调整（右边和右下角）
  if (resizeDirection.value.includes('e')) {
    const newWidth = Math.max(300, Math.min(800, dragState.value.initialWidth + deltaX))
    chatDialog.value.style.width = newWidth + 'px'
  }
  
  // 垂直方向调整（下边和右下角）
  if (resizeDirection.value.includes('s')) {
    const newHeight = Math.max(400, Math.min(600, dragState.value.initialHeight + deltaY))
    chatDialog.value.style.height = newHeight + 'px'
  }
}

/**
 * 停止调整大小
 * 清理状态和事件监听器
 */
const stopResize = () => {
  isResizing.value = false
  resizeDirection.value = ''
  
  document.removeEventListener('mousemove', resizeDialog)
  document.removeEventListener('mouseup', stopResize)
}

/**
 * 切换AI模式
 */
const switchAIMode = async (mode) => {
  if (mode === aiMode.value) return

  const previousMode = aiMode.value

  try {
    aiMode.value = mode
    updateAIModeText()

    // 根据模式设置AI模型信息
    if (mode === 'qianwen') {
      currentAIModel.value = {
        名称: '测试模式',
        描述: '通义千问测试接口',
        头像: '',
        算力消耗: 0
      }
    } else {
      // 切换到默认模式，使用用户AI信息表中类型为"默认"的配置
      currentAIModel.value = {
        名称: '默认模式',
        描述: 'Coze AI智能助手（自动使用默认配置）',
        头像: '',
        算力消耗: 1
      }
    }

    showModelSelector.value = false

    // 清空对话记录
    if (confirm('切换AI模型将清空当前对话记录，是否继续？')) {
      messages.value = []
      await clearContext()
    } else {
      // 用户取消，恢复原模式
      aiMode.value = previousMode
      updateAIModeText()
    }

  } catch (error) {
    console.error('切换AI模型失败:', error)
    // 恢复原来的模式
    aiMode.value = previousMode
    updateAIModeText()
  }
}



/**
 * 发送用户消息给AI助手
 */
const sendMessage = async () => {
  if (!currentMessage.value.trim() || isTyping.value) return

  const userMessage = {
    role: 'user',
    content: currentMessage.value.trim(),
    timestamp: new Date(),
    id: generateMessageId()
  }

  messages.value.push(userMessage)
  currentMessage.value = ''
  isTyping.value = true

  try {
    await nextTick()
    scrollToBottom()

    // 根据AI模式调用不同的接口
    const response = aiMode.value === 'qianwen'
      ? await aiService.sendMessage(userMessage.content)
      : await sendCozeMessage(userMessage.content)

    // 添加AI回复
    messages.value.push({
      role: 'assistant',
      content: response.content,
      timestamp: new Date(),
      id: generateMessageId()
    })

    // 设置未读提醒
    if (!showChatDialog.value) {
      hasUnreadMessage.value = true
      unreadCount.value++
    }

  } catch (error) {
    console.error('AI对话错误:', error)

    messages.value.push({
      role: 'assistant',
      content: `抱歉，${error.message || '我现在遇到了一些问题，请稍后重试。'}`,
      timestamp: new Date(),
      id: generateMessageId(),
      isError: true
    })

  } finally {
    isTyping.value = false
    await nextTick()
    scrollToBottom()
  }
}

/**
 * 发送消息到默认模式 (Coze AI)
 */
const sendCozeMessage = async (content) => {
  try {
    const requestData = {
      内容: content,
      我方微信号: 'ai_chat_system',
      对方微信号: 'ai_assistant_user',
      最近聊天内容: null
    }

    console.log('发送Coze AI请求:', requestData)

    const response = await api.post('/ai/chat', requestData)

    console.log('Coze AI响应:', response)

    if (response.status === 100) {
      // 解析AI回复内容
      let aiContent = ''
      if (response.data?.ai) {
        if (typeof response.data.ai === 'string') {
          aiContent = response.data.ai
        } else if (response.data.ai.消息数组?.length > 0) {
          aiContent = response.data.ai.消息数组
            .filter(msg => msg.消息类型 === '文本')
            .map(msg => msg.内容)
            .join('\n')
        }
      }

      return {
        content: aiContent || '收到您的消息',
        timestamp: new Date()
      }
    } else {
      throw new Error(response.message || '默认模式响应异常')
    }
  } catch (error) {
    console.error('默认模式调用失败:', error)
    throw new Error(error.response?.data?.message || '默认模式服务暂时不可用，请稍后重试')
  }
}

/**
 * 生成唯一的消息ID
 * 结合时间戳和随机字符串确保唯一性
 */
const generateMessageId = () => {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`
}

/**
 * 发送预设的快捷消息
 * @param {string} message - 要发送的快捷消息内容
 */
const sendQuickMessage = async (message) => {
  if (isTyping.value) return
  
  currentMessage.value = message
  await sendMessage()
}

/**
 * 清空对话记录
 */
const clearConversation = async () => {
  if (isClearing.value) return

  const confirmMessage = aiMode.value === 'coze'
    ? '确定要清空所有对话记录吗？这将清除AI的记忆上下文，无法恢复。'
    : '确定要清空所有对话记录吗？'

  if (confirm(confirmMessage)) {
    isClearing.value = true

    try {
      // 先清空本地消息
      messages.value = []

      // 根据AI模式清空不同的上下文
      await clearContext()

      console.log('对话记录和上下文已清空')

      // 显示成功提示
      if (aiMode.value === 'coze') {
        console.log('✅ Coze AI记忆已清除，开始全新对话')
      } else {
        console.log('✅ 通义千问上下文已清除')
      }

    } catch (error) {
      console.error('清空对话失败:', error)

      // 显示错误提示但保持本地清空状态
      const errorMsg = error.message || '清空失败，请稍后重试'
      console.error('❌ ' + errorMsg)

    } finally {
      isClearing.value = false
    }
  }
}

/**
 * 清空上下文（根据当前AI模式）
 */
const clearContext = async () => {
  try {
    if (aiMode.value === 'qianwen') {
      // 清空通义千问上下文
      await aiService.clearContext()
    } else {
      // 清空Coze AI会话上下文
      await clearCozeContext()
    }
  } catch (error) {
    console.error('清空上下文失败:', error)
    throw error
  }
}

/**
 * 清空Coze AI会话上下文
 */
const clearCozeContext = async () => {
  try {
    const requestData = {
      我方微信号: 'ai_chat_system',
      对方微信号: 'ai_assistant_user'
    }

    console.log('清空Coze会话上下文:', requestData)

    const response = await api.post('/ai/clear_context', requestData)

    if (response.status === 100) {
      console.log('Coze会话上下文清空成功:', response.data)
      return response.data
    } else {
      throw new Error(response.message || 'Coze会话上下文清空失败')
    }
  } catch (error) {
    console.error('清空Coze会话上下文失败:', error)
    if (error.response?.status === 404) {
      console.log('未找到相关会话，可能是首次使用')
      return { message: '未找到相关会话' }
    }
    throw new Error(error.response?.data?.message || 'Coze会话上下文清空服务暂时不可用')
  }
}

/**
 * 处理输入框键盘事件
 * Enter键发送消息，Shift+Enter换行
 */
const handleKeyDown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

/**
 * 滚动消息列表到底部
 * 用于显示最新消息
 */
const scrollToBottom = () => {
  if (chatMessages.value) {
    nextTick(() => {
      chatMessages.value.scrollTop = chatMessages.value.scrollHeight
    })
  }
}

/**
 * 聚焦到消息输入框
 * 打开对话框时自动聚焦
 */
const focusInput = () => {
  if (messageInput.value) {
    messageInput.value.focus()
  }
}

/**
 * 格式化消息内容
 * 支持基本的Markdown语法和换行
 */
const formatMessage = (content) => {
  return content
    .replace(/\n/g, '<br>')                        // 换行转换
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
    .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
}

/**
 * 格式化时间显示
 * 根据时间差显示相对时间或具体时间
 */
const formatTime = (timestamp) => {
  const diff = Date.now() - new Date(timestamp).getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 处理窗口大小变化
 * 对话框显示时自动滚动到底部
 */
const handleResize = () => {
  if (showChatDialog.value) {
    nextTick(() => scrollToBottom())
  }
}

/**
 * 组件挂载后的初始化
 * 设置窗口大小变化监听，初始化AI配置
 */
onMounted(async () => {
  window.addEventListener('resize', handleResize)

  // 初始化AI模式文本
  updateAIModeText()


})

/**
 * 组件卸载时的清理工作
 * 移除所有事件监听器，防止内存泄露
 */
onUnmounted(() => {
  // 清理窗口大小变化监听
  window.removeEventListener('resize', handleResize)
  
  // 清理所有拖拽相关的事件监听器
  document.removeEventListener('mousemove', dragButton)
  document.removeEventListener('mouseup', stopDragButton)
  document.removeEventListener('mousemove', dragDialog)
  document.removeEventListener('mouseup', stopDragDialog)
  document.removeEventListener('mousemove', resizeDialog)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped>
.ai-assistant-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.ai-floating-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: grab;
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  transition: transform 0.2s ease;
  user-select: none;
}

.ai-floating-button:hover {
  transform: scale(1.1);
}

.ai-floating-button.has-unread {
  animation: pulse 2s infinite;
}

.ai-floating-button.dragging {
  cursor: grabbing;
  transform: scale(1.05);
  z-index: 1001;
}

/* 可爱的AI头像样式 */
.ai-avatar {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.ai-face {
  position: relative;
  width: 36px;
  height: 36px;
}

.ai-eyes {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.eye {
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  animation: blink 3s infinite ease-in-out;
}

.ai-mouth {
  width: 12px;
  height: 6px;
  border: 2px solid white;
  border-top: none;
  border-radius: 0 0 12px 12px;
  margin: 0 auto;
}

.ai-glow {
  position: absolute;
  inset: -3px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.3;
  filter: blur(6px);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes blink {
  0%, 90%, 100% { transform: scaleY(1); }
  95% { transform: scaleY(0.1); }
}

@keyframes glow {
  from { opacity: 0.3; transform: scale(1); }
  to { opacity: 0.6; transform: scale(1.1); }
}

.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.ai-chat-dialog {
  position: fixed;
  right: 40px;
  top: 100px;
  width: 380px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1000;
  user-select: none;
}

.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab;
  position: relative;
  z-index: 1001;
  border-radius: 12px 12px 0 0;
}

.chat-header.dragging {
  cursor: grabbing;
}

.drag-hint {
  font-size: 11px;
  opacity: 0.7;
  margin-left: 8px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
}

.ai-model-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ai-model-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  background-size: cover;
  background-position: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.ai-model-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.ai-model-name {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.ai-model-type {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.header-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner-small {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* AI模型选择器样式 */
.ai-model-selector {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-height: 350px;
  overflow-y: auto;
  z-index: 1000;
  min-width: 320px;
}

.model-selector-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.close-selector {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-selector:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.05);
}

.model-options {
  padding: 12px 0;
}

.model-option {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 4px solid transparent;
  margin: 4px 0;
}

.model-option:hover {
  background: #f8f9fa;
  transform: translateX(2px);
}

.model-option.active {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-left-color: #2196F3;
  box-shadow: inset 0 1px 3px rgba(33, 150, 243, 0.1);
}

.model-option-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 18px;
  background-size: cover;
  background-position: center;
  border: 3px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.model-option:hover .model-option-avatar {
  transform: scale(1.05);
}

.model-option-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.model-option-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
  line-height: 1.2;
}

.model-option-desc {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.model-option-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
  min-width: 80px;
}

.status-active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.power-cost {
  color: #FF9800;
  font-size: 12px;
  font-weight: 500;
  background: rgba(255, 152, 0, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .ai-model-selector {
    min-width: 280px;
    max-height: 300px;
  }

  .model-option {
    padding: 12px 16px;
    gap: 12px;
  }

  .model-option-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .model-option-name {
    font-size: 14px;
  }

  .model-option-desc {
    font-size: 12px;
  }

  .model-selector-header {
    padding: 12px 16px;
    font-size: 14px;
  }
}

/* 滚动条样式优化 */
.ai-model-selector::-webkit-scrollbar {
  width: 6px;
}

.ai-model-selector::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ai-model-selector::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ai-model-selector::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message {
  text-align: center;
  padding: 20px;
  color: #666;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-text h3 {
  margin-bottom: 12px;
  color: #333;
}

.welcome-text ul {
  text-align: left;
  margin-top: 12px;
}

.message-item {
  display: flex;
  max-width: 85%;
}

.message-user {
  align-self: flex-end;
}

.message-user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-left: auto;
}

.message-assistant .message-content {
  background: #f5f5f5;
  color: #333;
}

.message-error .message-content {
  background: #ffe6e6;
  color: #d63031;
  border-left: 3px solid #d63031;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.message-text {
  line-height: 1.4;
  margin-bottom: 4px;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 18px;
  max-width: 120px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

.typing-text {
  font-size: 12px;
  color: #666;
}

.quick-actions {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  border-top: 1px solid #eee;
}

.quick-action-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-action-btn:hover:not(:disabled) {
  background: #e9ecef;
}

.quick-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chat-input {
  border-top: 1px solid #eee;
  padding: 16px;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 10px 16px;
  resize: none;
  outline: none;
  font-family: inherit;
  line-height: 1.4;
}

.input-container textarea:focus {
  border-color: #667eea;
}

.send-button {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.input-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

.char-count {
  font-size: 11px;
  color: #999;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 调整大小手柄样式 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  pointer-events: all;
  background: transparent;
}

.resize-handle.resize-se {
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  cursor: se-resize;
}

.resize-handle.resize-se::after {
  content: '';
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-bottom: 6px solid #999;
  transition: border-bottom-color 0.2s;
}

.resize-handle.resize-e {
  top: 30px;
  right: 0;
  bottom: 30px;
  width: 6px;
  cursor: e-resize;
  border-right: 2px solid transparent;
  transition: border-right-color 0.2s;
}

.resize-handle.resize-e:hover {
  border-right-color: #999;
}

.resize-handle.resize-s {
  bottom: 0;
  left: 30px;
  right: 30px;
  height: 6px;
  cursor: s-resize;
  border-bottom: 2px solid transparent;
  transition: border-bottom-color 0.2s;
}

.resize-handle.resize-s:hover {
  border-bottom-color: #999;
}

.resize-handle:hover::after {
  border-bottom-color: #999;
}

@media (max-width: 768px) {
  .ai-chat-dialog {
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0;
    bottom: 0;
    border-radius: 0;
  }

  .resize-handles {
    display: none;
  }

  .drag-hint {
    display: none;
  }
}
</style> 