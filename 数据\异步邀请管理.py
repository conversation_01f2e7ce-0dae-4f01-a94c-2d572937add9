"""
异步邀请管理数据库操作函数 - PostgreSQL版本
提供团队邀请相关的数据库操作功能
主要功能：1. 通过手机号邀请成员（支持已注册和未注册用户）
2. 重复邀请智能处理（更新现有邀请而非报错）
3. 邀请状态跟踪（新建、已存在、已更新）
4. 邀请链接生成和管理
5. 邀请记录查询和管理

新增改进（2024-12）：
- 当检测到待处理邀请时，返回现有邀请信息而不是失败
- 支持邀请状态区分（new/existing/updated）
- 自动更新邀请过期时间，确保邀请有效
- 优化用户体验，减少重复操作困扰
"""

import secrets
import string
from typing import Optional, List, Dict, Any

from 数据.用户 import (
    根据手机号获取用户信息 as 异步获取用户_电话,
    创建半注册用户 as 异步创建半注册用户,
    用户是否已注册_电话 as 异步用户是否已注册_电话
)
# 导入数据库连接和日志
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器

# 邀请状态常量（适配现有数据库枚举值）
INVITATION_STATUS = {
    "PENDING": "邀请待处理",
    "ACCEPTED": "邀请已接受",
    "REJECTED": "邀请已拒绝",
    "EXPIRED": "邀请已过期",
    "CANCELLED": "邀请已取消"
}

# 邀请创建状态常量
INVITATION_CREATION_STATUS = {
    "new": "新建邀请",
    "existing": "已存在邀请", 
    "updated": "已更新邀请"
}

def 处理邀请创建异常(e: Exception, context: str) -> Dict[str, Any]:
    """统一处理邀请创建过程中的异常"""
    错误消息 = str(e)
    if "generator" in 错误消息.lower() or "didn't stop after athrow" in 错误消息:
        错误日志器.error(f"{context}时检测到生成器错误: {错误消息}", exc_info=True)
        return {"success": False, "message": "数据库连接不稳定，请稍后重试"}
    elif "connection" in 错误消息.lower() or "timeout" in 错误消息.lower():
        错误日志器.error(f"{context}时检测到连接错误: {错误消息}", exc_info=True)
        return {"success": False, "message": "网络连接不稳定，请稍后重试"}
    错误日志器.error(f"{context}失败: {错误消息}", exc_info=True)
    return {"success": False, "message": f"{context}失败: {错误消息}"}

def 解析备注信息(备注: str) -> Dict[str, Any]:
    """从备注字符串中解析手机号、邀请令牌和权限列表"""
    手机号 = None
    邀请令牌 = None
    权限列表 = []
    if 备注:
        部分列表 = 备注.split('|')
        for 部分 in 部分列表:
            if 部分.startswith('手机号:'):
                手机号 = 部分.replace('手机号:', '')
            elif 部分.startswith('令牌:'):
                邀请令牌 = 部分.replace('令牌:', '')
            elif 部分.startswith('权限:'):
                权限文本 = 部分.replace('权限:', '')
                if 权限文本:
                    权限列表 = 权限文本.split(',')
    return {"手机号": 手机号, "邀请令牌": 邀请令牌, "权限列表": 权限列表}

def 生成邀请令牌(长度: int = 32) -> str:
    """生成唯一的邀请令牌"""
    字符集 = string.ascii_letters + string.digits
    return ''.join(secrets.choice(字符集) for _ in range(长度))

async def 异步通过手机号邀请成员(
    手机号: str,
    团队id: int,
    邀请人id: int,
    角色: str = "成员",
    权限列表: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    通过手机号邀请成员加入团队
    
    Args:
        手机号: 被邀请人的手机号
        团队id: 团队id
        邀请人id: 邀请人的用户id
        角色: 角色名称，默认为"成员"
        权限列表: 权限列表，可选
        
    Returns:
        Dict: 包含邀请结果的字典
    """
    try:
        数据库日志器.info(f"开始处理邀请：手机号={手机号}, 团队id={团队id}, 邀请人id={邀请人id}")
        
        # 验证输入参数
        if not 手机号 or not 团队id or not 邀请人id:
            return {
                "success": False,
                "message": "手机号、团队id和邀请人id不能为空"
            }
        
        # 检查用户注册状态
        用户数据 = await 异步获取用户_电话(手机号)
        用户已完成注册 = await 异步用户是否已注册_电话(手机号)
        
        if 用户数据 and 用户已完成注册:
            # 用户已完成注册，发送直接邀请
            数据库日志器.info(f"用户 {手机号} 已完成注册，发送直接邀请")
            return await 异步创建直接邀请(
                被邀请用户id=用户数据['id'],
                团队id=团队id,
                邀请人id=邀请人id,
                角色=角色,
                权限列表=权限列表
            )
        else:
            # 用户未完成注册或不存在
            if 用户数据:
                # 用户存在但未完成注册（半注册状态）
                数据库日志器.info(f"用户 {手机号} 存在但未完成注册，使用现有用户id")
                用户id = 用户数据['id']
            else:
                # 完全未注册，创建半注册用户
                数据库日志器.info(f"用户 {手机号} 完全未注册，创建半注册用户记录")
                try:
                    用户id = await 异步创建半注册用户(手机号)
                    if not 用户id or 用户id <= 0:
                        错误日志器.error(f"创建半注册用户失败：返回无效用户id ({用户id}) for 手机号 {手机号}")
                        return {
                            "success": False,
                            "message": "创建用户记录失败，请稍后重试"
                        }
                    数据库日志器.info(f"成功创建半注册用户，手机号: {手机号}, 用户id: {用户id}")
                except Exception as create_error:
                    错误日志器.error(f"创建半注册用户异常: {create_error}", exc_info=True)
                    return {
                        "success": False,
                        "message": f"创建用户记录失败: {str(create_error)}"
                    }
            
            # 验证用户id有效性
            if not 用户id or 用户id <= 0:
                错误日志器.error(f"用户id无效: {用户id}, 手机号: {手机号}")
                return {
                    "success": False,
                    "message": "用户id无效，无法创建邀请"
                }
            
            # 创建直接邀请（使用半注册用户id）
            数据库日志器.info(f"为半注册用户 {手机号}（用户id: {用户id}）创建邀请")
            邀请结果 = await 异步创建直接邀请(
                被邀请用户id=用户id,
                团队id=团队id,
                邀请人id=邀请人id,
                角色=角色,
                权限列表=权限列表
            )
            
            if not 邀请结果.get("success"):
                错误日志器.error(f"创建邀请失败，手机号: {手机号}, 用户id: {用户id}, 错误: {邀请结果.get('message')}")
            
            return 邀请结果
            
    except Exception as e:
        return 处理邀请创建异常(e, "通过手机号邀请成员")


async def 异步创建直接邀请(
    被邀请用户id: int,
    团队id: int,
    邀请人id: int,
    角色: str = "成员",
    权限列表: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    创建直接邀请（用户已存在的情况）

    Args:
        被邀请用户id: 被邀请用户的ID
        团队id: 团队id
        邀请人id: 邀请人的用户id
        角色: 角色名称，默认为"成员"
        权限列表: 权限列表，可选

    Returns:
        Dict: 包含邀请结果的字典
    """
    try:
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        数据库日志器.info(f"创建直接邀请：被邀请用户id={被邀请用户id}, 团队id={团队id}, 邀请人id={邀请人id}")

        # 验证输入参数
        if not 被邀请用户id or not 团队id or not 邀请人id:
            return {
                "success": False,
                "message": "被邀请用户id、团队id和邀请人id不能为空"
            }

        # 检查是否已存在邀请
        检查SQL = """
        SELECT id, 状态, 邀请令牌, 过期时间
        FROM 团队邀请表
        WHERE 被邀请用户id = $1 AND 团队id = $2 AND 状态 = '邀请待处理'
        """

        现有邀请 = await 异步连接池实例.执行查询(检查SQL, (被邀请用户id, 团队id))

        if 现有邀请:
            # 更新现有邀请的过期时间
            邀请id = 现有邀请[0]['id']
            邀请令牌 = 现有邀请[0]['邀请令牌']

            更新SQL = """
            UPDATE 团队邀请表
            SET 过期时间 = EXTRACT(EPOCH FROM NOW() + INTERVAL '7 days'),
                角色 = $1,
                权限列表 = $2,
                更新时间 = CURRENT_TIMESTAMP
            WHERE id = $3
            """

            权限字符串 = ','.join(权限列表) if 权限列表 else None
            await 异步连接池实例.执行更新(更新SQL, (角色, 权限字符串, 邀请id))

            数据库日志器.info(f"更新现有邀请成功：邀请id={邀请id}")

            return {
                "success": True,
                "message": "邀请已更新",
                "status": "updated",
                "invitation_id": 邀请id,
                "invitation_token": 邀请令牌
            }
        else:
            # 创建新邀请
            邀请令牌 = 生成邀请令牌()

            插入SQL = """
            INSERT INTO 团队邀请表 (
                团队id, 邀请人id, 被邀请用户id, 角色, 权限列表,
                邀请令牌, 状态, 过期时间, 创建时间, 更新时间
            ) VALUES (
                $1, $2, $3, $4, $5, $6, '邀请待处理',
                EXTRACT(EPOCH FROM NOW() + INTERVAL '7 days'),
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            ) RETURNING id
            """

            权限字符串 = ','.join(权限列表) if 权限列表 else None
            结果 = await 异步连接池实例.执行查询(
                插入SQL,
                (团队id, 邀请人id, 被邀请用户id, 角色, 权限字符串, 邀请令牌)
            )

            if 结果:
                邀请id = 结果[0]['id']
                数据库日志器.info(f"创建新邀请成功：邀请id={邀请id}")

                return {
                    "success": True,
                    "message": "邀请创建成功",
                    "status": "new",
                    "invitation_id": 邀请id,
                    "invitation_token": 邀请令牌
                }
            else:
                return {
                    "success": False,
                    "message": "创建邀请失败"
                }

    except Exception as e:
        return 处理邀请创建异常(e, "创建直接邀请")


async def 异步获取团队邀请列表(
    团队id: int,
    页码: int = 1,
    每页数量: int = 10,
    状态筛选: Optional[str] = None,
    操作者ID: Optional[int] = None
) -> Dict[str, Any]:
    """获取团队邀请列表"""
    try:
        # 构建基础查询SQL
        基础SQL = """
        SELECT
            ti.id as 邀请id,
            ti.邀请令牌,
            ti.团队id,
            ti.邀请人id,
            ti.被邀请人手机号,
            ti.角色,
            ti.状态,
            ti.创建时间,
            ti.过期时间,
            ti.备注,
            u1.昵称 as 邀请人昵称,
            u1.手机号 as 邀请人手机号,
            u2.昵称 as 被邀请人昵称,
            t.团队名称
        FROM 团队邀请表 ti
        LEFT JOIN 用户表 u1 ON ti.邀请人id = u1.id
        LEFT JOIN 用户表 u2 ON ti.被邀请人手机号 = u2.手机号
        LEFT JOIN 团队表 t ON ti.团队id = t.id
        WHERE ti.团队id = $1
        """

        查询参数: List[Any] = [团队id]
        参数计数 = 1

        # 添加状态筛选
        if 状态筛选:
            参数计数 += 1
            基础SQL += f" AND ti.状态 = ${参数计数}"
            查询参数.append(状态筛选)

        # 添加排序
        基础SQL += " ORDER BY ti.创建时间 DESC"

        # 查询总数
        计数SQL = 基础SQL.replace("SELECT ti.id as 邀请id,ti.邀请令牌,ti.团队id,ti.邀请人id,ti.被邀请人手机号,ti.角色,ti.状态,ti.创建时间,ti.过期时间,ti.备注,u1.昵称 as 邀请人昵称,u1.手机号 as 邀请人手机号,u2.昵称 as 被邀请人昵称,t.团队名称", "SELECT COUNT(*) as total")
        总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(查询参数))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 分页查询
        偏移量 = (页码 - 1) * 每页数量
        参数计数 += 1
        分页SQL = 基础SQL + f" LIMIT ${参数计数}"
        查询参数.append(每页数量)
        参数计数 += 1
        分页SQL += f" OFFSET ${参数计数}"
        查询参数.append(偏移量)

        邀请列表 = await 异步连接池实例.执行查询(分页SQL, tuple(查询参数))

        # 格式化邀请列表
        格式化邀请列表 = []
        for 邀请 in 邀请列表:
            格式化邀请列表.append({
                "邀请id": 邀请["邀请id"],
                "邀请令牌": 邀请["邀请令牌"],
                "团队id": 邀请["团队id"],
                "团队名称": 邀请["团队名称"],
                "邀请人id": 邀请["邀请人id"],
                "邀请人昵称": 邀请["邀请人昵称"],
                "邀请人手机号": 邀请["邀请人手机号"],
                "被邀请人手机号": 邀请["被邀请人手机号"],
                "被邀请人昵称": 邀请["被邀请人昵称"],
                "角色": 邀请["角色"],
                "状态": 邀请["状态"],
                "创建时间": 邀请["创建时间"],
                "过期时间": 邀请["过期时间"],
                "备注": 邀请["备注"]
            })

        数据库日志器.info(f"获取团队邀请列表成功: 团队id={团队id}, 总数={总数}")

        return {
            "success": True,
            "data": {
                "邀请列表": 格式化邀请列表,
                "总数": 总数,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": (总数 + 每页数量 - 1) // 每页数量
            }
        }

    except Exception as e:
        错误日志器.error(f"获取团队邀请列表失败: 团队id={团队id}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "message": f"获取邀请列表失败: {str(e)}"
        }


async def 异步处理邀请(
    邀请令牌: str,
    操作: str,
    用户id: int,
    备注: Optional[str] = None,
    当前登录用户: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """处理团队邀请（接受/拒绝）"""
    try:
        # 查找邀请记录
        查询SQL = """
        SELECT
            ti.id as 邀请id,
            ti.团队id,
            ti.邀请人id,
            ti.被邀请人手机号,
            ti.角色,
            ti.状态,
            ti.过期时间,
            t.团队名称
        FROM 团队邀请表 ti
        LEFT JOIN 团队表 t ON ti.团队id = t.id
        WHERE ti.邀请令牌 = $1
        """

        邀请记录 = await 异步连接池实例.执行查询(查询SQL, (邀请令牌,))

        if not 邀请记录:
            return {
                "success": False,
                "status": 404,
                "message": "邀请不存在或邀请码无效"
            }

        邀请 = 邀请记录[0]

        # 检查邀请状态
        if 邀请["状态"] != "邀请待处理":
            return {
                "success": False,
                "status": 400,
                "message": f"邀请已{邀请['状态']}，无法重复处理"
            }

        # 检查是否过期
        from datetime import datetime
        if 邀请["过期时间"] and datetime.now() > 邀请["过期时间"]:
            # 更新邀请状态为已过期
            await 异步连接池实例.执行更新(
                "UPDATE 团队邀请表 SET 状态 = $1 WHERE id = $2",
                ("邀请已过期", 邀请["邀请id"])
            )
            return {
                "success": False,
                "status": 410,
                "message": "邀请已过期"
            }

        if 操作 == "accept":
            # 接受邀请 - 将用户加入团队
            from 数据.团队成员数据 import 加入团队

            加入结果 = await 加入团队(
                用户id=用户id,
                团队id=邀请["团队id"],
                职位=邀请["角色"],
                邀请人id=邀请["邀请人id"]
            )

            if 加入结果["success"]:
                # 更新邀请状态
                await 异步连接池实例.执行更新(
                    "UPDATE 团队邀请表 SET 状态 = $1, 处理时间 = CURRENT_TIMESTAMP WHERE id = $2",
                    ("邀请已接受", 邀请["邀请id"])
                )

                数据库日志器.info(f"用户 {用户id} 接受邀请加入团队 {邀请['团队id']} 成功")

                return {
                    "success": True,
                    "message": f"成功加入团队 {邀请['团队名称']}",
                    "data": {
                        "团队id": 邀请["团队id"],
                        "团队名称": 邀请["团队名称"],
                        "角色": 邀请["角色"]
                    }
                }
            else:
                return {
                    "success": False,
                    "status": 400,
                    "message": f"加入团队失败: {加入结果['message']}"
                }

        elif 操作 == "reject":
            # 拒绝邀请
            await 异步连接池实例.执行更新(
                "UPDATE 团队邀请表 SET 状态 = $1, 处理时间 = CURRENT_TIMESTAMP, 备注 = $2 WHERE id = $3",
                ("邀请已拒绝", 备注 or "用户拒绝邀请", 邀请["邀请id"])
            )

            数据库日志器.info(f"用户 {用户id} 拒绝邀请加入团队 {邀请['团队id']}")

            return {
                "success": True,
                "message": "已拒绝邀请"
            }
        else:
            return {
                "success": False,
                "status": 400,
                "message": "无效的操作类型"
            }

    except Exception as e:
        错误日志器.error(f"处理邀请失败: 邀请令牌={邀请令牌}, 操作={操作}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "status": 500,
            "message": f"处理邀请失败: {str(e)}"
        }


async def 异步获取邀请详情(
    邀请令牌: str,
    当前登录用户: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """获取邀请详情"""
    try:
        # 查询邀请详情
        查询SQL = """
        SELECT
            ti.id as 邀请id,
            ti.邀请令牌,
            ti.团队id,
            ti.邀请人id,
            ti.被邀请人手机号,
            ti.角色,
            ti.状态,
            ti.创建时间,
            ti.过期时间,
            ti.备注,
            t.团队名称,
            t.团队描述,
            t.团队代码,
            u.昵称 as 邀请人昵称,
            u.手机号 as 邀请人手机号
        FROM 团队邀请表 ti
        LEFT JOIN 团队表 t ON ti.团队id = t.id
        LEFT JOIN 用户表 u ON ti.邀请人id = u.id
        WHERE ti.邀请令牌 = $1
        """

        邀请记录 = await 异步连接池实例.执行查询(查询SQL, (邀请令牌,))

        if not 邀请记录:
            return {
                "success": False,
                "status": 404,
                "message": "邀请不存在或邀请码无效"
            }

        邀请 = 邀请记录[0]

        # 检查是否过期
        from datetime import datetime
        是否过期 = 邀请["过期时间"] and datetime.now() > 邀请["过期时间"]

        if 是否过期 and 邀请["状态"] == "邀请待处理":
            # 自动更新过期状态
            await 异步连接池实例.执行更新(
                "UPDATE 团队邀请表 SET 状态 = $1 WHERE id = $2",
                ("邀请已过期", 邀请["邀请id"])
            )
            邀请["状态"] = "邀请已过期"

        # 检查被邀请人是否已注册
        被邀请人信息 = await 异步获取用户_电话(邀请["被邀请人手机号"])

        数据库日志器.info(f"获取邀请详情成功: 邀请令牌={邀请令牌}")

        return {
            "success": True,
            "data": {
                "邀请id": 邀请["邀请id"],
                "邀请令牌": 邀请["邀请令牌"],
                "团队信息": {
                    "团队id": 邀请["团队id"],
                    "团队名称": 邀请["团队名称"],
                    "团队描述": 邀请["团队描述"],
                    "团队代码": 邀请["团队代码"]
                },
                "邀请人信息": {
                    "邀请人id": 邀请["邀请人id"],
                    "邀请人昵称": 邀请["邀请人昵称"],
                    "邀请人手机号": 邀请["邀请人手机号"]
                },
                "被邀请人手机号": 邀请["被邀请人手机号"],
                "被邀请人已注册": bool(被邀请人信息),
                "角色": 邀请["角色"],
                "状态": 邀请["状态"],
                "创建时间": 邀请["创建时间"],
                "过期时间": 邀请["过期时间"],
                "是否过期": 是否过期,
                "是否有效": 邀请["状态"] == "邀请待处理" and not 是否过期,
                "备注": 邀请["备注"]
            }
        }

    except Exception as e:
        错误日志器.error(f"获取邀请详情失败: 邀请令牌={邀请令牌}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "status": 500,
            "message": f"获取邀请详情失败: {str(e)}"
        }


async def 异步生成注册邀请链接(
    团队id: int,
    手机号: str,
    邀请人id: int,
    角色: str = "成员",
    权限列表: Optional[List[str]] = None,
    有效期天数: int = 7
) -> Dict[str, Any]:
    """生成注册邀请链接"""
    try:
        # 生成邀请令牌
        邀请令牌 = 生成邀请令牌()

        # 计算过期时间
        from datetime import datetime, timedelta
        过期时间 = datetime.now() + timedelta(days=有效期天数)

        # 构建备注信息
        备注信息 = f"手机号:{手机号}|令牌:{邀请令牌}"
        if 权限列表:
            备注信息 += f"|权限:{','.join(权限列表)}"

        # 插入邀请记录
        插入SQL = """
        INSERT INTO 团队邀请表
        (邀请令牌, 团队id, 邀请人id, 被邀请人手机号, 角色, 状态, 创建时间, 过期时间, 备注)
        VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, $7, $8)
        RETURNING id
        """

        插入结果 = await 异步连接池实例.执行插入(
            插入SQL,
            (邀请令牌, 团队id, 邀请人id, 手机号, 角色, "邀请待处理", 过期时间, 备注信息)
        )

        if 插入结果:
            # 生成邀请链接
            邀请链接 = f"/team/invite?token={邀请令牌}"

            数据库日志器.info(f"生成注册邀请链接成功: 团队id={团队id}, 手机号={手机号}")

            return {
                "success": True,
                "message": "邀请链接生成成功",
                "data": {
                    "邀请令牌": 邀请令牌,
                    "邀请链接": 邀请链接,
                    "过期时间": 过期时间,
                    "有效期天数": 有效期天数
                }
            }
        else:
            return {
                "success": False,
                "message": "生成邀请链接失败"
            }

    except Exception as e:
        错误日志器.error(f"生成注册邀请链接失败: 团队id={团队id}, 手机号={手机号}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "message": f"生成邀请链接失败: {str(e)}"
        }


async def 异步撤销邀请(
    邀请令牌: str,
    操作人ID: int
) -> Dict[str, Any]:
    """撤销邀请"""
    try:
        # 查找邀请记录
        查询SQL = """
        SELECT id, 状态, 邀请人id, 团队id
        FROM 团队邀请表
        WHERE 邀请令牌 = $1
        """

        邀请记录 = await 异步连接池实例.执行查询(查询SQL, (邀请令牌,))

        if not 邀请记录:
            return {
                "success": False,
                "message": "邀请不存在"
            }

        邀请 = 邀请记录[0]

        # 检查权限（只有邀请人可以撤销）
        if 邀请["邀请人id"] != 操作人ID:
            return {
                "success": False,
                "message": "只有邀请人可以撤销邀请"
            }

        # 检查状态
        if 邀请["状态"] != "邀请待处理":
            return {
                "success": False,
                "message": f"邀请已{邀请['状态']}，无法撤销"
            }

        # 更新邀请状态
        更新SQL = """
        UPDATE 团队邀请表
        SET 状态 = $1, 处理时间 = CURRENT_TIMESTAMP
        WHERE id = $2
        """

        更新成功 = await 异步连接池实例.执行更新(更新SQL, ("邀请已取消", 邀请["id"]))

        if 更新成功:
            数据库日志器.info(f"撤销邀请成功: 邀请令牌={邀请令牌}, 操作人={操作人ID}")
            return {
                "success": True,
                "message": "邀请已撤销"
            }
        else:
            return {
                "success": False,
                "message": "撤销邀请失败"
            }

    except Exception as e:
        错误日志器.error(f"撤销邀请失败: 邀请令牌={邀请令牌}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "message": f"撤销邀请失败: {str(e)}"
        }


async def 异步重新发送邀请(
    邀请令牌: str,
    操作人ID: int
) -> Dict[str, Any]:
    """重新发送邀请"""
    try:
        # 查找邀请记录
        查询SQL = """
        SELECT id, 状态, 邀请人id, 团队id, 被邀请人手机号, 角色, 过期时间
        FROM 团队邀请表
        WHERE 邀请令牌 = $1
        """

        邀请记录 = await 异步连接池实例.执行查询(查询SQL, (邀请令牌,))

        if not 邀请记录:
            return {
                "success": False,
                "message": "邀请不存在"
            }

        邀请 = 邀请记录[0]

        # 检查权限（只有邀请人可以重新发送）
        if 邀请["邀请人id"] != 操作人ID:
            return {
                "success": False,
                "message": "只有邀请人可以重新发送邀请"
            }

        # 检查状态（只有已过期或已拒绝的邀请可以重新发送）
        if 邀请["状态"] not in ["邀请已过期", "邀请已拒绝"]:
            return {
                "success": False,
                "message": f"当前状态为{邀请['状态']}，无法重新发送"
            }

        # 更新邀请状态和过期时间
        from datetime import datetime, timedelta
        新过期时间 = datetime.now() + timedelta(days=7)

        更新SQL = """
        UPDATE 团队邀请表
        SET 状态 = $1, 过期时间 = $2, 处理时间 = NULL
        WHERE id = $3
        """

        更新成功 = await 异步连接池实例.执行更新(
            更新SQL,
            ("邀请待处理", 新过期时间, 邀请["id"])
        )

        if 更新成功:
            数据库日志器.info(f"重新发送邀请成功: 邀请令牌={邀请令牌}, 操作人={操作人ID}")
            return {
                "success": True,
                "message": "邀请已重新发送",
                "data": {
                    "邀请令牌": 邀请令牌,
                    "新过期时间": 新过期时间
                }
            }
        else:
            return {
                "success": False,
                "message": "重新发送邀请失败"
            }

    except Exception as e:
        错误日志器.error(f"重新发送邀请失败: 邀请令牌={邀请令牌}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "message": f"重新发送邀请失败: {str(e)}"
        }


async def 异步撤销邀请_通过ID(
    邀请id: int,
    操作人ID: int
) -> Dict[str, Any]:
    """通过邀请id撤销邀请"""
    try:
        # 查找邀请记录
        查询SQL = """
        SELECT id, 状态, 邀请人id, 团队id, 邀请令牌
        FROM 团队邀请表
        WHERE id = $1
        """

        邀请记录 = await 异步连接池实例.执行查询(查询SQL, (邀请id,))

        if not 邀请记录:
            return {
                "success": False,
                "message": "邀请不存在"
            }

        邀请 = 邀请记录[0]

        # 检查权限（只有邀请人可以撤销）
        if 邀请["邀请人id"] != 操作人ID:
            return {
                "success": False,
                "message": "只有邀请人可以撤销邀请"
            }

        # 检查状态
        if 邀请["状态"] != "邀请待处理":
            return {
                "success": False,
                "message": f"邀请已{邀请['状态']}，无法撤销"
            }

        # 更新邀请状态
        更新SQL = """
        UPDATE 团队邀请表
        SET 状态 = $1, 处理时间 = CURRENT_TIMESTAMP
        WHERE id = $2
        """

        更新成功 = await 异步连接池实例.执行更新(更新SQL, ("邀请已取消", 邀请id))

        if 更新成功:
            数据库日志器.info(f"撤销邀请成功: 邀请id={邀请id}, 操作人={操作人ID}")
            return {
                "success": True,
                "message": "邀请已撤销"
            }
        else:
            return {
                "success": False,
                "message": "撤销邀请失败"
            }

    except Exception as e:
        错误日志器.error(f"撤销邀请失败: 邀请id={邀请id}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "message": f"撤销邀请失败: {str(e)}"
        }


async def 异步重新发送邀请_通过ID(
    邀请id: int,
    操作人ID: int
) -> Dict[str, Any]:
    """通过邀请id重新发送邀请"""
    try:
        # 查找邀请记录
        查询SQL = """
        SELECT id, 状态, 邀请人id, 团队id, 被邀请人手机号, 角色, 过期时间, 邀请令牌
        FROM 团队邀请表
        WHERE id = $1
        """

        邀请记录 = await 异步连接池实例.执行查询(查询SQL, (邀请id,))

        if not 邀请记录:
            return {
                "success": False,
                "message": "邀请不存在"
            }

        邀请 = 邀请记录[0]

        # 检查权限（只有邀请人可以重新发送）
        if 邀请["邀请人id"] != 操作人ID:
            return {
                "success": False,
                "message": "只有邀请人可以重新发送邀请"
            }

        # 检查状态（只有待处理的邀请可以重新发送）
        if 邀请["状态"] != "邀请待处理":
            return {
                "success": False,
                "message": f"当前状态为{邀请['状态']}，无法重新发送"
            }

        # 更新邀请的过期时间
        from datetime import datetime, timedelta
        新过期时间 = datetime.now() + timedelta(days=7)

        更新SQL = """
        UPDATE 团队邀请表
        SET 过期时间 = $1, 更新时间 = CURRENT_TIMESTAMP
        WHERE id = $2
        """

        更新成功 = await 异步连接池实例.执行更新(
            更新SQL,
            (新过期时间, 邀请id)
        )

        if 更新成功:
            数据库日志器.info(f"重新发送邀请成功: 邀请id={邀请id}, 操作人={操作人ID}")
            return {
                "success": True,
                "message": "邀请已重新发送",
                "data": {
                    "邀请令牌": 邀请["邀请令牌"],
                    "新过期时间": 新过期时间
                }
            }
        else:
            return {
                "success": False,
                "message": "重新发送邀请失败"
            }

    except Exception as e:
        错误日志器.error(f"重新发送邀请失败: 邀请id={邀请id}, 错误={e}", exc_info=True)
        return {
            "success": False,
            "message": f"重新发送邀请失败: {str(e)}"
        }
