<template>
  <div class="request-params-form">
    <a-form layout="vertical" :model="currentParamsData">
      <!-- 历史参数选择下拉菜单 -->
      <div v-if="parameterHistoryForCurrentApi.length > 0" style="margin-bottom: 16px;">
        <a-select
          v-model:value="selectedHistoryEntryId"
          placeholder="从历史记录加载参数..."
          style="width: 100%;"
          allow-clear
          @change="handleLoadParamsFromHistory"
        >
          <a-select-option v-for="entry in parameterHistoryForCurrentApi" :key="entry.id" :value="entry.id">
            <div>
              <span style="font-weight: bold;">{{ entry.formattedTimestamp }}</span>
              <div style="font-size: 0.85em; color: #777; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                {{ entry.preview }}
              </div>
            </div>
          </a-select-option>
        </a-select>
      </div>
      
      <a-row :gutter="16">
        <template v-for="param in apiParams" :key="param.key">
          <a-col :span="getColSpan(param)">
            <a-form-item 
              :name="param.key"
              :required="param.required"
              :rules="generateAntdValidationRules(param)"
              :extra="param.description || param.placeholder" 
            >
              <template #label>
                <a-tooltip placement="topLeft">
                  <template #title>
                    <div style="white-space: pre-line;">{{ formatParamDetailsForTooltip(param) }}</div>
                  </template>
                  <span>
                    {{ param.key }}
                    <a-tag color="blue" size="small" style="margin-left: 8px;">{{ getTypeDisplayText(param) }}</a-tag>
                  </span>
                </a-tooltip>
              </template>

              <!-- 字符串, URL, Email (oasFormat will refine these further if needed by specific components) -->
              <a-input 
                v-if="param.type === 'string' || param.type === 'url' || param.type === 'email'" 
                v-model:value="currentParamsData[param.key]" 
                :placeholder="`示例: ${getExampleValue(param)}`"
                allow-clear
              />
              <!-- 密码 -->
              <a-input-password
                v-else-if="param.type === 'password'"
                v-model:value="currentParamsData[param.key]"
                :placeholder="`示例: ${getExampleValue(param)}`"
                allow-clear
              />
              <!-- 数字 (整数或浮点数) -->
              <a-input-number 
                v-else-if="param.type === 'number' || param.type === 'integer'" 
                v-model:value="currentParamsData[param.key]" 
                :placeholder="`示例: ${getExampleValue(param)}`"
                :precision="param.oasType === 'integer' ? 0 : undefined"
                style="width: 100%;"
              />
              <!-- 布尔值 -->
              <a-switch 
                v-else-if="param.type === 'boolean'" 
                v-model:checked="currentParamsData[param.key]" 
              />
              <!-- 嵌套对象 -->
              <div v-else-if="param.type === 'object' && param.children && param.children.length > 0" class="nested-object">
                <a-card :title="param.name" size="small" class="nested-object-card">
                  <nested-object-form
                    :fields="param.children"
                    :value="currentParamsData[param.key] || {}"
                    @change="(val) => handleNestedObjectChange(param.key, val)"
                  />
                </a-card>
              </div>
              <!-- 数组 -->
              <div v-else-if="param.type === 'array' && param.itemSchema" class="nested-array">
                 <a-card :title="param.name" size="small" class="nested-array-card">
                  <nested-array-form
                    :item-schema="param.itemSchema"
                    :array-validations="param.validations"
                    :value="currentParamsData[param.key] || []"
                    @change="(val) => handleNestedArrayChange(param.key, val)"
                  />
                </a-card>
              </div>
              <!-- 文本, JSON字符串, 或无子/项定义的复杂类型 (回退到textarea or JSON editor) -->
              <template v-else-if="param.type === 'text' || (param.type === 'object' && (!param.children || param.children.length === 0)) || (param.type === 'array' && !param.itemSchema)">
                <div v-if="param.type === 'object' || param.type === 'array'" class="json-editor-container">
                  <json-editor-vue3
                    v-model="currentParamsData[param.key]"
                    :mode="'code'"
                    :modes="['code', 'tree', 'view', 'text']"
                    :mainMenuBar="true"
                    :navigationBar="false"
                    :statusBar="false"
                    :askToFormat="true"
                    :readOnly="false"
                    :schema="getJsonSchemaForParam(param)"
                    @json-change="(json) => handleJsonEditorChange(param.key, json)"
                    @json-error="(error) => handleJsonEditorError(param.key, error)" 
                  />
                   <small v-if="jsonErrorMessages[param.key]" class="json-editor-error-message">
                    JSON错误: {{ jsonErrorMessages[param.key] }}
                  </small>
                </div>
                <a-textarea 
                  v-else
                  v-model:value="currentParamsData[param.key]"
                  :placeholder="param.placeholder || `请输入 ${param.name}`"
                  :rows="3"
                  allow-clear
                />
              </template>
              <!-- 日期选择 -->
              <a-date-picker
                v-else-if="param.type === 'date'"
                v-model:value="currentParamsData[param.key]"
                style="width: 100%;"
                valueFormat="YYYY-MM-DD"
              />
              <!-- 日期时间选择 -->
              <a-date-picker
                v-else-if="param.type === 'datetime'"
                v-model:value="currentParamsData[param.key]"
                show-time
                style="width: 100%;"
                valueFormat="YYYY-MM-DD HH:mm:ss"
              />
              <!-- 下拉选择 (基于 param.options，这部分通常来自 enum) -->
              <a-select
                v-else-if="param.options && param.options.length > 0"
                v-model:value="currentParamsData[param.key]"
                :placeholder="param.placeholder || `请选择 ${param.name}`"
                style="width: 100%;"
                allow-clear
              >
                <a-select-option v-for="option in param.options" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
              <!-- 未知类型 -->
              <a-input 
                v-else
                v-model:value="currentParamsData[param.key]" 
                :placeholder="`未知类型 (${param.type}) - ${param.name}`"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </template>
      </a-row>
      <a-empty v-if="!apiParams || apiParams.length === 0" description="该接口无需请求参数" />
    </a-form>
  </div>
</template>

<script setup>
import {computed, reactive, ref, toRaw, watch} from 'vue';
import {
  Card as ACard,
  Col as ACol,
  DatePicker as ADatePicker,
  Empty as AEmpty,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  InputPassword as AInputPassword,
  message,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
  Switch as ASwitch,
  Textarea as ATextarea
} from 'ant-design-vue';
import {useApiTestingModule} from '@/store/apiTestingModule';
import NestedObjectForm from './params-form/NestedObjectForm.vue';
import NestedArrayForm from './params-form/NestedArrayForm.vue';
import JsonEditorVue3 from 'json-editor-vue3';
import 'jsoneditor/dist/jsoneditor.min.css';

const store = useApiTestingModule();

const selectedApi = computed(() => store.getSelectedApiDetails);
const apiParams = computed(() => selectedApi.value?.params || []);

// 历史参数相关
const MAX_PARAM_HISTORY_ITEMS = 10; // 最多显示10条历史参数
const MAX_PREVIEW_LENGTH = 50; // 参数预览最大长度
const selectedHistoryEntryId = ref(undefined); // 用于 select 的 v-model

// 计算当前API的历史参数记录
const parameterHistoryForCurrentApi = computed(() => {
  if (!store.selectedApiId) {
    return [];
  }
  const historyForApi = store.requestHistory.filter(
    (item) => item.apiId === store.selectedApiId && item.params && typeof item.params === 'object'
  );

  // 去重并格式化
  const uniqueParamSets = new Map();
  historyForApi.forEach(item => {
    const paramsString = JSON.stringify(item.params);
    if (!uniqueParamSets.has(paramsString)) {
      uniqueParamSets.set(paramsString, {
        id: item.id, // 使用原始历史ID作为唯一键
        timestamp: item.timestamp,
        params: item.params,
        // 创建一个简短的预览文本
        preview: Object.entries(item.params)
                  .map(([k, v]) => `${k}:${String(v).substring(0,15)}${String(v).length > 15 ? '...' : ''}`)
                  .join(', ')
                  .substring(0, MAX_PREVIEW_LENGTH) + (Object.entries(item.params).join(', ').length > MAX_PREVIEW_LENGTH ? '...' : ''),
        // 格式化时间戳
        formattedTimestamp: new Date(item.timestamp).toLocaleString()
      });
    }
  });
  
  // 按时间戳降序排序并截取
  return Array.from(uniqueParamSets.values())
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    .slice(0, MAX_PARAM_HISTORY_ITEMS);
});

// 从历史记录加载参数
const handleLoadParamsFromHistory = (historyEntryId) => {
  if (!historyEntryId) return;
  const entry = parameterHistoryForCurrentApi.value.find(e => e.id === historyEntryId);
  if (entry && entry.params) {
    store.loadParamsFromHistory(entry.params);
    message.success(`已加载历史参数 (${entry.formattedTimestamp})`);
  }
};

// 使用 reactive 来包装 currentParams，以便深层属性的更改能被正确侦听
// 同时，当 selectedApi 变化时，需要重新初始化它
const currentParamsData = reactive({});
const jsonErrorMessages = reactive({}); // To store JSON validation errors from the editor

watch(() => store.currentParams, (newParams) => {
  // 当 store 中的 currentParams 变化时（例如通过 selectApi action 初始化）
  // 更新本地的 currentParamsData
  Object.keys(currentParamsData).forEach(key => delete currentParamsData[key]);
  Object.keys(jsonErrorMessages).forEach(key => delete jsonErrorMessages[key]); // Clear errors too
  Object.assign(currentParamsData, newParams || {});
}, { immediate: true, deep: true });

// 监听 currentParamsData 的变化，并同步回 store
// 使用 toRaw 避免循环依赖，并确保我们只在用户实际更改时才提交
watch(currentParamsData, (newData) => {
  const rawNewData = toRaw(newData);
  // 简单的比较，如果需要更精确的深比较，可以使用lodash.isEqual
  if (JSON.stringify(rawNewData) !== JSON.stringify(toRaw(store.currentParams))) {
      Object.keys(rawNewData).forEach(key => {
        store.updateParam(key, rawNewData[key]);
      });
  }
}, { deep: true });

// 处理嵌套对象变更
const handleNestedObjectChange = (key, value) => {
  currentParamsData[key] = value;
};

// 处理嵌套数组变更
const handleNestedArrayChange = (key, value) => {
  currentParamsData[key] = value;
};

// Handlers for json-editor-vue3
const handleJsonEditorChange = (key, jsonValue) => {
  // The editor directly provides the JS object/array, not a string if valid JSON
  currentParamsData[key] = jsonValue;
  jsonErrorMessages[key] = null; // Clear error on valid change
  // No need to call store.updateParam here, as the watch(currentParamsData) will handle it.
};

const handleJsonEditorError = (key, error) => {
  console.warn(`JSON editor error for ${key}:`, error);
  // The editor might still emit a string value on error, or the last valid object.
  // We store the error message to display it.
  // Check if the error is a schema validation error and format it
  if (Array.isArray(error)) { // JSON Schema validation errors are usually an array
    jsonErrorMessages[key] = error.map(err => `${err.path}: ${err.message}`).join('; ');
  } else if (error.message) {
    jsonErrorMessages[key] = error.message;
  } else {
    jsonErrorMessages[key] = 'JSON格式或结构无效';
  }
};

// Function to get the JSON schema for a parameter if available
const getJsonSchemaForParam = (param) => {
  if (param && param.oasOriginalSchema) {
    // console.log(`Providing schema for param ${param.key}:`, param.oasOriginalSchema);
    return param.oasOriginalSchema;
  }
  return undefined; // Return undefined if no schema is available
};

// 根据参数类型获取列布局宽度
const getColSpan = (param) => {
  // 对于复杂类型或多行输入，使用整行
  if (param.type === 'object' || param.type === 'array' || param.type === 'text') {
    return 24;
  }
  // 对于日期选择器，使用半行
  if (['date', 'datetime'].includes(param.type)) {
    return 12;
  }
  // 默认半行
  return 12;
};

// 获取类型显示文本
const getTypeDisplayText = (param) => {
  if (!param) return 'unknown';

  // 优先使用 OpenAPI 类型
  if (param.oasType) {
    let typeText = param.oasType;
    if (param.oasFormat) {
      typeText += `(${param.oasFormat})`;
    }
    return typeText;
  }

  // 使用映射后的类型
  return param.type || 'string';
};

// 生成参数示例值
const getExampleValue = (param) => {
  if (!param) return '';

  // 如果有默认值，使用默认值
  if (param.defaultValue !== undefined) {
    return typeof param.defaultValue === 'object'
      ? JSON.stringify(param.defaultValue)
      : String(param.defaultValue);
  }

  // 如果有枚举值，使用第一个
  if (param.validations && param.validations.enum && param.validations.enum.length > 0) {
    return String(param.validations.enum[0]);
  }

  // 根据类型生成示例
  switch (param.type) {
    case 'string':
      if (param.oasFormat === 'email') return '<EMAIL>';
      if (param.oasFormat === 'uri') return 'https://example.com';
      if (param.oasFormat === 'date') return '2024-01-01';
      if (param.oasFormat === 'date-time') return '2024-01-01T12:00:00Z';
      return '示例文本';
    case 'integer':
      return '123';
    case 'number':
      return '123.45';
    case 'boolean':
      return 'true';
    case 'array':
      return '[示例项目]';
    case 'object':
      return '{"示例": "值"}';
    default:
      return '示例值';
  }
};

// Helper function to format parameter details for tooltip
const formatParamDetailsForTooltip = (param) => {
  if (!param) return '';
  const details = [];
  details.push(`名称: ${param.name} (${param.key})`);
  if (param.isPathParam) {
    details.push('位置: 路径参数 (Path)');
  } else if (param.isQueryParam) {
    details.push('位置: 查询参数 (Query)');
  }
  if (param.oasType) {
    details.push(`类型: ${param.oasType}${param.oasFormat ? ` (${param.oasFormat})` : ''}`);
  }
  if (param.description) {
    details.push(`描述: ${param.description}`);
  }
  if (param.required) {
    details.push('是否必填: 是');
  }
  if (param.defaultValue !== undefined) {
    details.push(`默认值: ${JSON.stringify(param.defaultValue)}`);
  }

  if (param.validations) {
    const vRules = [];
    if (param.validations.pattern) vRules.push(`模式: ${param.validations.pattern}`);
    if (param.validations.minLength !== undefined) vRules.push(`最小长度: ${param.validations.minLength}`);
    if (param.validations.maxLength !== undefined) vRules.push(`最大长度: ${param.validations.maxLength}`);
    if (param.validations.minimum !== undefined) vRules.push(`最小值: ${param.validations.minimum}`);
    if (param.validations.maximum !== undefined) vRules.push(`最大值: ${param.validations.maximum}`);
    if (param.validations.exclusiveMinimum !== undefined) vRules.push(`排他最小值: ${param.validations.exclusiveMinimum}`);
    if (param.validations.exclusiveMaximum !== undefined) vRules.push(`排他最大值: ${param.validations.exclusiveMaximum}`);
    if (param.validations.multipleOf !== undefined) vRules.push(`倍数: ${param.validations.multipleOf}`);
    if (param.validations.enum) vRules.push(`可选值: ${param.validations.enum.join(', ')}`);
    // Array specific validations (these might be better displayed near the array itself)
    if (param.type === 'array') {
        if (param.validations.minItems !== undefined) vRules.push(`最少项目数: ${param.validations.minItems}`);
        if (param.validations.maxItems !== undefined) vRules.push(`最多项目数: ${param.validations.maxItems}`);
        if (param.validations.uniqueItems) vRules.push(`项目需唯一`);
    }

    if (vRules.length > 0) {
      details.push(`校验规则:\n  - ${vRules.join('\n  - ')}`);
    }
  }
  return details.join('\n\n');
};

// 确保validateJsonOnBlur函数存在，并可能需要根据新的param结构调整
// This function might become less relevant for object/array types now handled by json-editor-vue3
// but can still be useful for param.type === 'json_string' if we ever introduce that explicitly.
const validateJsonOnBlur = (param, event) => {
  if (!param || !(param.type === 'object' || param.type === 'array' || param.type === 'json_string')) {
    return;
  }
  // If this param is handled by json-editor-vue3, its internal validation is preferred.
  if (param.type === 'object' && (!param.children || param.children.length === 0)) return;
  if (param.type === 'array' && !param.itemSchema) return;

  const value = event.target.value;
  if (value) {
    try {
      JSON.parse(value);
    } catch (e) {
      message.error(`${param.name} (${param.key}) 的JSON格式无效: ${e.message}`);
    }
  }
};

// Helper function to generate Ant Design Vue validation rules
const generateAntdValidationRules = (param) => {
  if (!param) return [];
  const rules = [];

  // Required rule (Ant Design handles this if :name is set and model value is tracked)
  // However, explicitly adding it can provide custom messages or ensure it works standalone.
  if (param.required) {
    rules.push({ required: true, message: `${param.name} 是必填项` });
  }

  // Type-specific rules from param.type (mapped from oasFormat or basic type)
  if (param.type === 'email') {
    rules.push({ type: 'email', message: '请输入有效的邮箱地址' });
  }
  if (param.type === 'url') {
    rules.push({ type: 'url', message: '请输入有效的URL地址' });
  }

  // Validations from param.validations (parsed from OpenAPI schema)
  if (param.validations) {
    const vals = param.validations;
    if (vals.minLength !== undefined) {
      rules.push({ min: vals.minLength, message: `长度不能少于 ${vals.minLength} 个字符`, trigger: 'blur' });
    }
    if (vals.maxLength !== undefined) {
      rules.push({ max: vals.maxLength, message: `长度不能超过 ${vals.maxLength} 个字符`, trigger: 'blur' });
    }
    if (vals.pattern) {
      try {
        const regex = new RegExp(vals.pattern);
        rules.push({ pattern: regex, message: `必须符合模式: ${vals.pattern}`, trigger: 'blur' });
      } catch (e) {
        console.warn(`Invalid regex pattern for ${param.key}: ${vals.pattern}`, e);
      }
    }
    if (vals.minimum !== undefined) {
      rules.push({ type: param.oasType === 'integer' ? 'integer' : 'number', min: vals.minimum, message: `数值不能小于 ${vals.minimum}`, trigger: 'blur' });
    }
    if (vals.maximum !== undefined) {
      rules.push({ type: param.oasType === 'integer' ? 'integer' : 'number', max: vals.maximum, message: `数值不能大于 ${vals.maximum}`, trigger: 'blur' });
    }
    if (vals.enum && Array.isArray(vals.enum)) {
      rules.push({
        type: param.oasType === 'array' ? 'array' : undefined, // For array of enums, validator checks items
        validator: (rule, value) => {
          if (value === undefined || value === null || value === '') return Promise.resolve(); // Allow empty if not required
          const enumValues = vals.enum;
          if (param.type === 'array') { // If param itself is an array of enums
            if (!Array.isArray(value)) return Promise.reject('必须是一个数组');
            for (const item of value) {
              if (!enumValues.includes(item)) {
                return Promise.reject(`${item} 不在允许的值列表中: [${enumValues.join(', ')}]`);
              }
            }
          } else { // Single value enum
            if (!enumValues.includes(value)) {
              return Promise.reject(`${value} 不在允许的值列表中: [${enumValues.join(', ')}]`);
            }
          }
          return Promise.resolve();
        },
        trigger: 'change',
      });
    }

    // Array-specific validations for the array field itself
    if (param.type === 'array') {
      if (vals.minItems !== undefined) {
        rules.push({ type: 'array', min: vals.minItems, message: `至少需要 ${vals.minItems} 个项目`, trigger: 'change' });
      }
      if (vals.maxItems !== undefined) {
        rules.push({ type: 'array', max: vals.maxItems, message: `最多允许 ${vals.maxItems} 个项目`, trigger: 'change' });
      }
      if (vals.uniqueItems && param.itemSchema) { // uniqueItems only makes sense if we can check items
        rules.push({
          type: 'array',
          validator: (rule, value) => {
            if (!Array.isArray(value) || value.length === 0) return Promise.resolve();
            const seen = new Set();
            for (const item of value) {
              // For complex objects, stringify to check uniqueness. Simple values can be checked directly.
              const comparableItem = (typeof item === 'object' && item !== null) ? JSON.stringify(item) : item;
              if (seen.has(comparableItem)) {
                return Promise.reject('数组中的项目必须是唯一的');
              }
              seen.add(comparableItem);
            }
            return Promise.resolve();
          },
          trigger: 'change',
        });
      }
    }
  }
  return rules;
};

</script>

<style scoped>
.request-params-form {
  padding: 8px 0;
}

.nested-object, .nested-array {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
}

.nested-object-card, .nested-array-card {
  background-color: #fafafa;
}

.ant-form-item {
  margin-bottom: 12px; /* 减少表单项间距 */
}
.ant-form-item-extra {
    font-size: 12px;
    color: rgba(0,0,0,.45);
    min-height: 18px; /* 避免没有描述时跳动 */
}

.json-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

/* Scoped styles for json-editor-vue3 might be needed if global ones conflict */
/* For example, to ensure it fits well within the form item */
:deep(.jsoneditor-vue) {
  height: 200px; /* Default height, adjust as needed */
}
:deep(.jsoneditor-menu) {
  background-color: #f5f5f5; /* Match Ant Design's aesthetic a bit */
  border-bottom: 1px solid #e8e8e8;
}

.json-editor-error-message {
  color: red;
  font-size: 0.85em;
  margin-top: 4px;
}
</style> 