<template>
  <div class="response-display">
    <div v-if="content?.access_token && type === 'body'" class="token-actions">
      <a-button type="primary" @click="saveToken">
        <template #icon><save-outlined /></template>
        保存为认证Token
      </a-button>
    </div>
    
    <div class="json-display" v-if="isJsonContent">
      <pre v-highlightjs><code class="json">{{ formattedContent }}</code></pre>
    </div>
    <div class="headers-display" v-else-if="type === 'headers'">
      <div v-for="(value, key) in content" :key="key" class="header-row">
        <b>{{ key }}:</b> {{ value }}
      </div>
      <div v-if="!content || Object.keys(content).length === 0" class="no-headers">
        No headers
      </div>
    </div>
    <div class="raw-display" v-else>
      <pre>{{ typeof content === 'object' ? JSON.stringify(content, null, 2) : content }}</pre>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue';
import {Button as AButton, message} from 'ant-design-vue';
import {SaveOutlined} from '@ant-design/icons-vue';
import {useApiTestingModule} from '@/store/apiTestingModule';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

// 定义组件属性
const props = defineProps({
  content: { type: [Object, Array, String, Number], default: null }, // 响应内容，可以是对象、数组、字符串或数字
  type: { type: String, default: 'body' }, // 显示类型：'body', 'headers', 'raw'
});

// 获取API测试状态管理模块
const store = useApiTestingModule();

// 判断是否为JSON内容（用于语法高亮显示）
const isJsonContent = computed(() => {
  return props.type === 'body' && props.content !== null && typeof props.content === 'object';
});

// 格式化JSON内容显示
const formattedContent = computed(() => {
  if (!props.content) return '';
  try {
    return JSON.stringify(props.content, null, 2);
  } catch (e) {
    console.error('JSON格式化错误:', e);
    return String(props.content);
  }
});

// 代码高亮指令 - 用于对JSON代码进行语法高亮
const vHighlightjs = {
  mounted(el) {
    const blocks = el.querySelectorAll('pre code');
    blocks.forEach(block => {
      hljs.highlightElement(block);
    });
  },
  updated(el) {
    const blocks = el.querySelectorAll('pre code');
    blocks.forEach(block => {
      hljs.highlightElement(block);
    });
  }
};

// 保存认证Token到全局状态
const saveToken = () => {
  if (!props.content?.access_token) return;
  
  const success = store.saveTokenFromResponse({ data: props.content });
  if (success) {
    message.success('认证Token已保存');
  } else {
    message.error('保存Token失败，响应中可能没有有效的Token');
  }
};
</script>

<style scoped>
.response-display {
  width: 100%;
}

.token-actions {
  margin-bottom: 16px;
}

.json-display pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.header-row {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  word-break: break-all;
}

.header-row:last-child {
  border-bottom: none;
}

.no-headers {
  color: #999;
  font-style: italic;
}

.raw-display pre {
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}
</style> 