
from datetime import datetime
from typing import Dict, List, Optional

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client

import config


# 导入统一日志系统

class 腾讯云存储服务:
    """腾讯云对象存储COS服务封装类"""
    def __init__(self):
        """初始化腾讯云对象存储服务"""
        # 从配置文件获取腾讯云配置
        self.secret_id = config.腾讯云配置.get('secret_id', '')
        self.secret_key = config.腾讯云配置.get('secret_key', '')
        self.region = config.腾讯云配置.get('region', 'ap-guangzhou')  # 默认广州区域
        self.bucket = config.腾讯云配置.get('bucket', '')
        
        # 初始化COS客户端
        cos_config = CosConfig(
            Region=self.region,
            SecretId=self.secret_id,
            SecretKey=self.secret_key
        )
        self.client = CosS3Client(cos_config)
    
    async def 获取文件列表(self, 前缀: str = '') -> List[Dict]:
        """
        获取腾讯云COS中指定前缀的文件列表及其MD5信息
        
        Args:
            前缀: 文件前缀路径，例如 'update/'
            
        Returns:
            文件信息列表，每个文件包含路径、大小、MD5等信息
        """
        try:
            # 使用同步API获取文件列表
            response = self.client.list_objects(
                Bucket=self.bucket,
                Prefix=前缀,
                MaxKeys=1000  # 最大获取数量
            )
            
            文件列表 = []
            if 'Contents' in response:
                for item in response['Contents']:
                    # 获取文件的ETag (通常是MD5的引用形式)
                    etag = item.get('ETag', '').strip('"')  # 去除ETag两端的引号
                    
                    文件信息 = {
                        '路径': item.get('Key', ''),
                        '大小': item.get('Size', 0),
                        'MD5': etag,
                        '最后修改时间': item.get('LastModified', '').strftime('%Y-%m-%d %H:%M:%S') if isinstance(item.get('LastModified'), datetime) else item.get('LastModified', '')
                    }
                    文件列表.append(文件信息)
            
            return 文件列表
        except Exception as e:
            print(f"获取文件列表异常: {str(e)}")
            return []
    
    async def 获取文件下载链接(self, 文件路径: str, 过期时间: int = 3600) -> Optional[str]:
        """
        获取腾讯云COS中指定文件的临时下载链接
        
        Args:
            文件路径: COS中的文件路径
            过期时间: 链接有效期（秒），默认1小时
            
        Returns:
            临时下载链接，如果生成失败则返回None
        """
        try:
            # 生成预签名URL
            response = self.client.get_presigned_url(
                Method='GET',
                Bucket=self.bucket,
                Key=文件路径,
                Expired=过期时间
            )
            
            return response
        except Exception as e:
            print(f"获取文件下载链接异常: {str(e)}")
            return None
    
    async def 批量获取文件下载链接(self, 文件路径列表: List[str], 过期时间: int = 3600) -> Dict[str, str]:
        """
        批量获取多个文件的临时下载链接
        
        Args:
            文件路径列表: COS中的文件路径列表
            过期时间: 链接有效期（秒），默认1小时
            
        Returns:
            文件路径与下载链接的映射字典
        """
        结果 = {}
        for 路径 in 文件路径列表:
            链接 = await self.获取文件下载链接(路径, 过期时间)
            if 链接:
                结果[路径] = 链接
        
        return 结果

# 创建全局实例
腾讯云存储服务实例 = 腾讯云存储服务()



