<template>
  <div class="invitation-list-container">
    <!-- 筛选器组件 -->
    <InvitationFilters
      :refresh-loading="loading"
      :total="pagination.total"
      :initial-filters="filters"
      @refresh="handleRefresh"
      @filter-change="handleFilterChange"
    />

    <!-- 邀请列表组件 -->
    <InvitationList
      :invitation-list="invitationList"
      :loading="loading"
      :pagination="pagination"
      :action-loading="actionLoading"
      @table-change="handleTableChange"
      @view-link="handleViewInviteLink"
      @resend="handleResendInvitation"
      @view-detail="handleViewDetail"
      @action-success="handleActionSuccess"
    />

    <!-- 邀请链接详情弹窗 -->
    <a-modal
      v-model:open="inviteLinkModalVisible"
      title="邀请链接详情"
      :footer="null"
      width="600px"
      centered
    >
      <div v-if="selectedInvitation" class="invite-link-detail">
        <div class="invite-basic-info">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="被邀请人">
              {{ selectedInvitation.被邀请人姓名 || '未注册用户' }}
            </a-descriptions-item>
            <a-descriptions-item label="手机号">
              {{ selectedInvitation.手机号 }}
            </a-descriptions-item>
            <a-descriptions-item label="邀请状态">
              <a-tag :color="getStatusColor(selectedInvitation.状态)">
                {{ getStatusText(selectedInvitation.状态) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="邀请角色">
              {{ selectedInvitation.职位 || '成员' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <a-divider>邀请链接</a-divider>

        <div class="invite-link-section">
          <div class="link-display">
            <a-input
              :value="currentInviteLink"
              readonly
              size="large"
              style="font-family: monospace;"
            >
              <template #suffix>
                <a-button 
                  type="link" 
                  @click="copyInviteLink"
                  :loading="copyLoading"
                >
                  <CopyOutlined />
                  复制
                </a-button>
              </template>
            </a-input>
          </div>

          <div class="link-info">
            <a-alert
              message="分享说明"
              description="请将此邀请链接发送给被邀请人，他们可以通过此链接直接加入团队。"
              type="info"
              show-icon
              style="margin-top: 16px;"
            />
          </div>

          <div class="link-actions">
            <a-space>
              <a-button @click="inviteLinkModalVisible = false">关闭</a-button>
              <a-button type="primary" @click="copyAndCloseLink">
                复制链接并关闭
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 邀请详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="邀请详情"
      :footer="null"
      width="600px"
    >
      <div v-if="selectedInvitation" class="invitation-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="被邀请人">
            {{ selectedInvitation.被邀请人姓名 || '未注册用户' }}
          </a-descriptions-item>
          <a-descriptions-item label="手机号">
            {{ selectedInvitation.手机号 }}
          </a-descriptions-item>
          <a-descriptions-item label="邀请状态">
            <a-tag :color="getStatusColor(selectedInvitation.状态)">
              {{ getStatusText(selectedInvitation.状态) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="邀请角色">
            {{ selectedInvitation.职位 || '成员' }}
          </a-descriptions-item>
          <a-descriptions-item label="邀请人">
            {{ selectedInvitation.邀请人姓名 }}
          </a-descriptions-item>
          <a-descriptions-item label="最后操作时间">
            {{ formatDate(selectedInvitation.更新时间 || selectedInvitation.创建时间) }}
          </a-descriptions-item>
          <a-descriptions-item label="邀请消息" :span="2">
            {{ selectedInvitation.邀请消息 || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 重发邀请确认弹窗 -->
    <a-modal
      v-model:open="resendModalVisible"
      title="重新发送邀请"
      @ok="confirmResendInvitation"
      :confirm-loading="resendLoading"
    >
      <div>
        <p>确定要重新发送邀请给 <strong>{{ selectedInvitation?.被邀请人姓名 || selectedInvitation?.手机号 }}</strong> 吗？</p>
        <p style="color: #666; font-size: 14px;">重发后将生成新的邀请链接，邀请有效期将重新计算。</p>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { CopyOutlined } from '@ant-design/icons-vue'
import teamService from '../../services/team'
import { formatDate } from '../../utils/teamUtils'
import InvitationFilters from './InvitationFilters.vue'
import InvitationList from './InvitationList.vue'

defineOptions({
  name: 'InvitationListTab'
})

const props = defineProps({
  team: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['refresh'])

// 响应式数据
const loading = ref(false)
const invitationList = ref([])
const actionLoading = ref({})
const detailModalVisible = ref(false)
const resendModalVisible = ref(false)
const inviteLinkModalVisible = ref(false)
const selectedInvitation = ref(null)
const resendLoading = ref(false)
const copyLoading = ref(false)
const currentInviteLink = ref('')

// 筛选条件
const filters = reactive({
  状态: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 重发邀请相关状态（已简化，无需表单）

// 计算属性
const teamId = computed(() => props.team?.团队id || props.team?.id)

// 方法定义

/**
 * 加载邀请列表
 */
const loadInvitationList = async () => {
  if (!teamId.value) return

  try {
    loading.value = true
    const response = await teamService.getTeamInvitations({
      团队id: teamId.value,
      页码: pagination.current,
      每页数量: pagination.pageSize,
      状态: filters.状态
    })

    if ([100, 0, 1].includes(response.status)) {
      const data = response.data || response.message
      invitationList.value = data?.邀请列表 || data?.list || []
      pagination.total = data?.总数 || data?.total || 0
    } else {
      throw new Error(response.message || '获取邀请列表失败')
    }
  } catch (error) {
    console.error('加载邀请列表失败:', error)
    message.error('加载邀请列表失败: ' + error.message)
    invitationList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 刷新列表
 */
const handleRefresh = async () => {
  await loadInvitationList()
  message.success('邀请列表已刷新')
}

/**
 * 筛选条件变化处理
 */
const handleFilterChange = (newFilters) => {
  // 更新筛选条件
  Object.assign(filters, newFilters)
  pagination.current = 1
  loadInvitationList()
}

/**
 * 表格变化处理
 */
const handleTableChange = (paginationParams) => {
  pagination.current = paginationParams.current
  pagination.pageSize = paginationParams.pageSize
  loadInvitationList()
}

/**
 * 处理重发邀请
 */
const handleResendInvitation = (invitation) => {
  selectedInvitation.value = invitation
  resendModalVisible.value = true
}

/**
 * 确认重发邀请
 */
const confirmResendInvitation = async () => {
  if (!selectedInvitation.value) return

  try {
    resendLoading.value = true
    const response = await teamService.resendInvitation({
      邀请id: selectedInvitation.value.id
    })

    if ([100, 0, 1].includes(response.status)) {
      message.success('邀请重发成功，邀请时间已更新')
      resendModalVisible.value = false
      
      // 立即刷新列表并重置到第一页以显示最新的邀请
      pagination.current = 1
      await loadInvitationList()
      
      // 通知父组件刷新相关数据
      emit('refresh')
    } else {
      throw new Error(response.message || '重发邀请失败')
    }
  } catch (error) {
    console.error('重发邀请失败:', error)
    message.error('重发邀请失败: ' + error.message)
  } finally {
    resendLoading.value = false
  }
}

/**
 * 处理操作成功回调
 */
const handleActionSuccess = async (result) => {
  // 刷新列表
          await loadInvitationList()
          // 通知父组件刷新相关数据
          emit('refresh')
}

/**
 * 查看邀请详情
 */
const handleViewDetail = (invitation) => {
  selectedInvitation.value = invitation
  detailModalVisible.value = true
}

// 处理查看邀请链接
const handleViewInviteLink = async (invitation) => {
  try {
    selectedInvitation.value = invitation
    
    // 优先使用后端返回的邀请确认链接
    if (invitation.邀请确认链接) {
      currentInviteLink.value = invitation.邀请确认链接
    } else if (invitation.邀请令牌) {
      // 基于前端域名和邀请令牌生成链接，使用路径参数格式
      const frontendUrl = window.location.origin
      currentInviteLink.value = `${frontendUrl}/invitation/${invitation.邀请令牌}`
    } else {
      // 如果没有邀请令牌，尝试从备注中提取
      if (invitation.备注 && invitation.备注.includes('令牌:')) {
        const tokenMatch = invitation.备注.match(/令牌:([^|]+)/)
        if (tokenMatch) {
          const frontendUrl = window.location.origin
          currentInviteLink.value = `${frontendUrl}/invitation/${tokenMatch[1]}`
        } else {
          message.warning('该邀请暂无可用链接')
          return
        }
      } else {
        message.warning('该邀请暂无可用链接')
        return
      }
    }
    
    inviteLinkModalVisible.value = true
  } catch (error) {
    console.error('获取邀请链接失败:', error)
    message.error('获取邀请链接失败')
  }
}

// 复制邀请链接
const copyInviteLink = async () => {
  try {
    copyLoading.value = true
    await navigator.clipboard.writeText(currentInviteLink.value)
    message.success('邀请链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级处理：创建临时输入框进行复制
    const textArea = document.createElement('textarea')
    textArea.value = currentInviteLink.value
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
      message.success('邀请链接已复制到剪贴板')
    } catch (fallbackError) {
      message.error('复制失败，请手动复制链接')
    }
    document.body.removeChild(textArea)
  } finally {
    copyLoading.value = false
  }
}

// 复制链接并关闭弹窗
const copyAndCloseLink = async () => {
  await copyInviteLink()
  inviteLinkModalVisible.value = false
}

// 状态处理函数
/**
 * 获取邀请状态对应的标签颜色
 * @param {string} status - 邀请状态
 * @returns {string} Ant Design标签颜色
 */
const getStatusColor = (status) => {
  const statusColorMap = {
    '待处理': 'warning',
    '邀请待处理': 'warning',
    '已接受': 'success',
    '已拒绝': 'error',
    '已拒绝邀请': 'error',
    '已过期': 'default',
    '已撤销': 'default',
    '已移除': 'default'
  }
  return statusColorMap[status] || 'default'
}

/**
 * 获取邀请状态对应的显示文本
 * @param {string} status - 邀请状态
 * @returns {string} 显示文本
 */
const getStatusText = (status) => {
  const statusTextMap = {
    '待处理': '待处理',
    '邀请待处理': '待处理',
    '已接受': '已接受',
    '已拒绝': '已拒绝',
    '已拒绝邀请': '已拒绝',
    '已过期': '已过期',
    '已撤销': '已撤销',
    '已移除': '已移除'
  }
  return statusTextMap[status] || status || '未知'
}

// 监听团队变化
watch(() => props.team, (newTeam) => {
  if (newTeam?.团队id || newTeam?.id) {
    loadInvitationList()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (teamId.value) {
    loadInvitationList()
  }
})
</script>

<style scoped>
.invitation-list-container {
  padding: 16px;
}

.invitation-detail {
  padding: 16px 0;
}

.invite-link-detail {
  padding: 16px 0;
}

.invite-basic-info {
  margin-bottom: 16px;
}

.invite-link-section {
  text-align: center;
}

.link-display {
  margin: 16px 0;
  padding: 16px;
  background-color: #f6f6f6;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.link-info {
  margin: 16px 0;
}

.link-actions {
  margin-top: 24px;
  text-align: center;
}
</style> 