/**
 * 团队服务统一入口
 * 整合所有团队相关的API服务，提供统一的调用接口
 * 重构后的版本：模块化、优雅、高效
 */

// 导入各功能模块
import { teamBasicService } from './team/teamBasic'
import { teamMemberService } from './team/teamMember'
import { teamPermissionService } from './team/teamPermission'
import { teamInvitationService } from './team/teamInvitation'

/**
 * 团队服务统一导出
 * 为了保持向后兼容性，保留原有的函数名称
 */
export const teamService = {
  // ==================== 团队基础功能 ====================
  
  /**
   * 获取用户团队列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getUserTeams: teamBasicService.getUserTeams,

  /**
   * 获取用户团队统计
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getUserTeamStats: teamBasicService.getUserTeamStats,

  /**
   * 获取用户最近活动
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getUserRecentActivities: teamBasicService.getUserRecentActivities,

  /**
   * 获取团队详情
   * @param {number} teamId - 团队id
   * @param {Object} options - 选项
   * @returns {Promise} API响应
   */
  getTeamDetail: teamBasicService.getTeamDetail,

  /**
   * 创建团队
   * @param {Object} params - 团队信息
   * @returns {Promise} API响应
   */
  createTeam: teamBasicService.createTeam,

  /**
   * 解散团队
   * @param {number} teamId - 团队id
   * @returns {Promise} API响应
   */
  dissolveTeam: teamBasicService.dissolveTeam,

  /**
   * 退出团队
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  leaveTeam: teamBasicService.leaveTeam,

  /**
   * 获取公司列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getCompanyList: teamBasicService.getCompanyList,

  /**
   * 创建公司
   * @param {Object} params - 公司信息
   * @returns {Promise} API响应
   */
  createCompany: teamBasicService.createCompany,

  // ==================== 团队成员功能 ====================

  /**
   * 获取团队成员列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getTeamMembers: teamMemberService.getTeamMembers,

  /**
   * 通过手机号邀请成员
   * @param {Object} params - 邀请参数
   * @returns {Promise} API响应
   */
  inviteByPhone: teamMemberService.inviteByPhone,

  /**
   * 批量邀请成员
   * @param {Object} params - 邀请参数
   * @returns {Promise} API响应
   */
  inviteMembers: teamMemberService.inviteMembers,

  /**
   * 移除团队成员
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  removeMember: teamMemberService.removeMember,

  /**
   * 设置成员职位
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  setMemberPosition: teamMemberService.setMemberPosition,

  /**
   * 批量更新成员角色
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  batchUpdateMemberRole: teamMemberService.batchUpdateMemberRole,

  /**
   * 获取成员详细权限
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  getMemberDetailedPermissions: teamMemberService.getMemberDetailedPermissions,

  /**
   * 搜索用户
   * @param {Object} params - 搜索参数
   * @returns {Promise} API响应
   */
  searchUsers: teamMemberService.searchUsers,

  // ==================== 团队权限功能 (重构后) ====================

  /**
   * 检查用户在团队中的权限状态
   * @param {Object} params - 查询参数 { 团队id, 用户id }
   * @returns {Promise} API响应
   */
  getUserTeamPermissionStatus: teamPermissionService.getUserTeamPermissionStatus,

  /**
   * 获取团队所有可用权限列表
   * @returns {Promise} API响应
   */
  getPermissionList: teamPermissionService.getPermissionList,

  /**
   * 更新成员权限
   * @param {Object} params - 参数 { 团队id, 用户id, 权限列表 }
   * @returns {Promise} API响应
   */
  updateMemberPermissions: teamPermissionService.updateMemberPermissions,

  /**
   * 获取权限变更日志
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getPermissionLogs: teamPermissionService.getPermissionLogs,

  // ==================== 团队邀请功能 ====================

  /**
   * 获取团队邀请列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getTeamInvitations: teamInvitationService.getTeamInvitations,

  /**
   * 撤销邀请
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  revokeInvitation: teamInvitationService.revokeInvitation,

  /**
   * 重发邀请
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  resendInvitation: teamInvitationService.resendInvitation,

  /**
   * 获取邀请详情
   * @param {string} token - 邀请令牌
   * @returns {Promise} API响应
   */
  getInvitationDetails: teamInvitationService.getInvitationDetails,

  /**
   * 处理邀请（接受或拒绝）
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  processInvitation: teamInvitationService.processInvitation,

  /**
   * 获取团队统计信息
   * @param {number} teamId - 团队id
   * @returns {Promise} API响应
   */
  getTeamStats: teamInvitationService.getTeamStats,

  /**
   * 获取团队活动记录
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getTeamActivities: teamInvitationService.getTeamActivities,

  /**
   * 转移团队所有权
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  transferOwnership: teamInvitationService.transferOwnership,

  // ==================== 兼容性别名 ====================
  // 为了保持向后兼容性，提供一些常用的别名

  /**
   * 获取所有权限分类（别名）
   * @returns {Promise} API响应
   */
  getAllPermissions: teamPermissionService.getTeamPermissions,

  /**
   * 获取权限分类（别名）
   * @returns {Promise} API响应
   */
  getPermissionCategories: teamPermissionService.getTeamPermissions,

  /**
   * 检查权限（别名）
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  hasPermission: teamPermissionService.checkPermission,

  /**
   * 批量检查权限（别名）
   * @param {Object} params - 参数
   * @returns {Promise} API响应
   */
  batchCheckPermissions: teamPermissionService.checkMultiplePermissions,

  // 为 members 命名空间分配成员相关服务
  members: { ...teamMemberService },

  // 为 permissions 命名空间分配权限相关服务
  permissions: {
    getUserTeamPermissionStatus: teamPermissionService.getUserTeamPermissionStatus,
    getPermissionList: teamPermissionService.getPermissionList,
    updateMemberPermissions: teamPermissionService.updateMemberPermissions,
    getPermissionLogs: teamPermissionService.getPermissionLogs
  },
  
  // 为 invitations 命名空间分配邀请相关服务
  invitations: { ...teamInvitationService }
}

// 默认导出
export default teamService

/**
 * 导出各个模块，供需要单独使用的场景
 */
export {
  teamBasicService,
  teamMemberService,
  teamPermissionService,
  teamInvitationService
} 