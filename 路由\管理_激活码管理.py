"""
管理员激活码管理API路由
提供激活码管理相关的API接口
主要功能：
1. 激活码类型管理
2. 激活码生成
3. 激活码列表查询
4. 激活码详情查看
5. 激活码删除管理
6. 激活码统计分析
"""

import csv
import io
from typing import List, Optional

from fastapi import APIRouter, Depends, Response
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前管理员用户
from 数据.管理_统一入口 import (
    # 新增的激活码类型管理函数
    异步创建激活码类型,
    异步删除激活码,
    异步删除激活码类型,
    异步批量删除激活码,
    异步批量生成激活码,
    异步更新激活码类型,
    异步获取激活码列表,
    异步获取激活码类型列表,
    异步获取激活码类型列表带统计,
    异步获取激活码类型详情,
    异步获取激活码统计,
    异步获取激活码详情,
)
from 数据模型.响应模型 import 统一响应模型
from 日志 import 安全日志器, 接口日志器, 错误日志器

# 创建激活码管理路由
激活码管理路由 = APIRouter(prefix="/admin/activation-codes", tags=["管理员-激活码管理"])

# ==================== 数据模型 ====================


class 激活码生成请求(BaseModel):
    数量: int = Field(..., ge=1, le=1000, description="生成数量，1-1000")
    类型id: int = Field(..., description="激活码类型id")
    是否为一次性激活: int = Field(
        0, description="激活码类型：0=一次性激活码，1=永久激活码"
    )
    备注: str = Field("", max_length=200, description="备注信息")


class 激活码列表查询请求(BaseModel):
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: str = Field("", description="搜索关键词")
    类型id: Optional[int] = Field(None, description="激活码类型id")
    状态: Optional[str] = Field(None, description="激活码状态 (unused/used)")
    开始日期: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    结束日期: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")


class 批量删除请求(BaseModel):
    激活码id列表: List[int] = Field(..., description="激活码id列表")


class 激活码类型创建请求(BaseModel):
    名称: str = Field(..., min_length=1, max_length=50, description="类型名称")
    描述: str = Field(..., min_length=1, max_length=200, description="类型描述")
    价格: float = Field(..., ge=0, description="类型价格")
    会员表id: int = Field(..., description="关联的会员表id")
    会员天数: int = Field(..., ge=1, le=3650, description="会员天数，1-3650天")


class 激活码类型更新请求(BaseModel):
    名称: str = Field(..., min_length=1, max_length=50, description="类型名称")
    描述: str = Field(..., min_length=1, max_length=200, description="类型描述")
    价格: float = Field(..., ge=0, description="类型价格")
    会员表id: int = Field(..., description="关联的会员表id")
    会员天数: int = Field(..., ge=1, le=3650, description="会员天数，1-3650天")


class 激活码类型列表查询请求(BaseModel):
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页数量")
    搜索关键词: str = Field("", description="搜索关键词")


# ==================== 激活码类型管理 ====================


@激活码管理路由.get("/types", summary="获取激活码类型列表")
async def 获取激活码类型列表接口(当前用户: dict = Depends(获取当前管理员用户)):
    """获取激活码类型列表"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求激活码类型列表")

        类型列表 = await 异步获取激活码类型列表()
        return 统一响应模型.成功(数据=类型列表, 消息="获取激活码类型列表成功")

    except Exception as e:
        错误日志器.error(f"获取激活码类型列表失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取激活码类型列表失败")


@激活码管理路由.post("/types/list", summary="获取激活码类型列表（带统计）")
async def 获取激活码类型列表带统计接口(
    请求数据: 激活码类型列表查询请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取激活码类型列表，包含统计信息"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求激活码类型列表（带统计）")

        列表结果 = await 异步获取激活码类型列表带统计(
            page=请求数据.page, size=请求数据.size, 搜索关键词=请求数据.搜索关键词
        )

        return 统一响应模型.成功(数据=列表结果, 消息="获取激活码类型列表成功")

    except Exception as e:
        错误日志器.error(f"获取激活码类型列表带统计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取激活码类型列表失败")


@激活码管理路由.post("/types", summary="创建激活码类型")
async def 创建激活码类型接口(
    请求数据: 激活码类型创建请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """创建激活码类型"""
    try:
        接口日志器.info(
            f"管理员 {当前用户.get('id')} 请求创建激活码类型: {请求数据.名称}"
        )

        创建结果 = await 异步创建激活码类型(
            名称=请求数据.名称,
            描述=请求数据.描述,
            价格=请求数据.价格,
            会员表id=请求数据.会员表id,
            会员天数=请求数据.会员天数,
        )

        if 创建结果.get("success"):
            安全日志器.info(
                f"管理员 {当前用户.get('id')} 成功创建激活码类型: {请求数据.名称}"
            )
            return 统一响应模型.成功(
                数据=创建结果.get("data"),
                消息=创建结果.get("message", "激活码类型创建成功"),
            )
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=创建结果.get("message", "创建激活码类型失败")
            )

    except Exception as e:
        错误日志器.error(f"创建激活码类型失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="创建激活码类型失败")


@激活码管理路由.get("/types/{类型id}", summary="获取激活码类型详情")
async def 获取激活码类型详情接口(
    类型id: int, 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取激活码类型详情"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求激活码类型详情: {类型id}")

        详情结果 = await 异步获取激活码类型详情(类型id)

        if 详情结果:
            return 统一响应模型.成功(数据=详情结果, 消息="获取激活码类型详情成功")
        else:
            return 统一响应模型.失败(状态码=404, 消息="激活码类型不存在")

    except Exception as e:
        错误日志器.error(f"获取激活码类型详情失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取激活码类型详情失败")


@激活码管理路由.put("/types/{类型id}", summary="更新激活码类型")
async def 更新激活码类型接口(
    类型id: int,
    请求数据: 激活码类型更新请求,
    当前用户: dict = Depends(获取当前管理员用户),
):
    """更新激活码类型"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求更新激活码类型: {类型id}")

        更新结果 = await 异步更新激活码类型(
            类型id=类型id,
            名称=请求数据.名称,
            描述=请求数据.描述,
            价格=请求数据.价格,
            会员表id=请求数据.会员表id,
            会员天数=请求数据.会员天数,
        )

        if 更新结果.get("success"):
            安全日志器.info(f"管理员 {当前用户.get('id')} 成功更新激活码类型: {类型id}")
            return 统一响应模型.成功(
                数据=更新结果.get("data"),
                消息=更新结果.get("message", "激活码类型更新成功"),
            )
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=更新结果.get("message", "更新激活码类型失败")
            )

    except Exception as e:
        错误日志器.error(f"更新激活码类型失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="更新激活码类型失败")


@激活码管理路由.delete("/types/{类型id}", summary="删除激活码类型")
async def 删除激活码类型接口(类型id: int, 当前用户: dict = Depends(获取当前管理员用户)):
    """删除激活码类型"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求删除激活码类型: {类型id}")

        删除结果 = await 异步删除激活码类型(类型id)

        if 删除结果.get("success"):
            安全日志器.info(f"管理员 {当前用户.get('id')} 成功删除激活码类型: {类型id}")
            return 统一响应模型.成功(消息=删除结果.get("message", "激活码类型删除成功"))
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=删除结果.get("message", "删除激活码类型失败")
            )

    except Exception as e:
        错误日志器.error(f"删除激活码类型失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="删除激活码类型失败")


# ==================== 激活码生成 ====================


@激活码管理路由.post("/generate", summary="生成激活码")
async def 生成激活码接口(
    请求数据: 激活码生成请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """批量生成激活码"""
    try:
        接口日志器.info(
            f"管理员 {当前用户.get('id')} 请求生成激活码: 数量={请求数据.数量}, 类型id={请求数据.类型id}, 激活码类型={'一次性' if 请求数据.是否为一次性激活 == 0 else '永久'}"
        )

        生成结果 = await 异步批量生成激活码(
            请求数据.数量, 请求数据.类型id, 请求数据.备注, 请求数据.是否为一次性激活
        )

        # 处理不同的返回字段名
        if 生成结果.get("成功", 生成结果.get("success", False)):
            安全日志器.info(
                f"管理员 {当前用户.get('id')} 成功生成 {请求数据.数量} 个激活码"
            )
            return 统一响应模型.成功(
                数据=生成结果.get("codes", 生成结果.get("激活码列表", [])),
                消息=生成结果.get("消息", 生成结果.get("message", "激活码生成成功")),
            )
        else:
            错误消息 = 生成结果.get("消息", 生成结果.get("message", "生成激活码失败"))
            接口日志器.warning(f"生成激活码失败: {错误消息}")
            return 统一响应模型.失败(状态码=400, 消息=错误消息)

    except Exception as e:
        错误日志器.error(f"生成激活码异常: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="生成激活码失败")


# ==================== 激活码列表管理 ====================


@激活码管理路由.post("/list", summary="获取激活码列表")
async def 获取激活码列表接口(
    请求数据: 激活码列表查询请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取激活码列表，支持分页、搜索和筛选"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求激活码列表")

        列表结果 = await 异步获取激活码列表(
            page=请求数据.page,
            size=请求数据.size,
            搜索关键词=请求数据.搜索关键词,
            类型id=请求数据.类型id,
            状态=请求数据.状态,
            开始日期=请求数据.开始日期,
            结束日期=请求数据.结束日期,
        )

        return 统一响应模型.成功(数据=列表结果, 消息="获取激活码列表成功")

    except Exception as e:
        错误日志器.error(f"获取激活码列表失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取激活码列表失败")


# ==================== 激活码详情管理 ====================


@激活码管理路由.get("/{激活码id}", summary="获取激活码详情")
async def 获取激活码详情接口(
    激活码id: int, 当前用户: dict = Depends(获取当前管理员用户)
):
    """获取激活码详情"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求激活码详情: {激活码id}")

        详情结果 = await 异步获取激活码详情(激活码id)

        if 详情结果:
            return 统一响应模型.成功(数据=详情结果, 消息="获取激活码详情成功")
        else:
            return 统一响应模型.失败(状态码=404, 消息="激活码不存在")

    except Exception as e:
        错误日志器.error(f"获取激活码详情失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取激活码详情失败")


# ==================== 激活码删除管理 ====================


@激活码管理路由.delete("/{激活码id}", summary="删除激活码")
async def 删除激活码接口(激活码id: int, 当前用户: dict = Depends(获取当前管理员用户)):
    """删除单个激活码"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求删除激活码: {激活码id}")

        删除结果 = await 异步删除激活码(激活码id)

        if 删除结果.get("success"):
            安全日志器.info(f"管理员 {当前用户.get('id')} 成功删除激活码: {激活码id}")
            return 统一响应模型.成功(消息=删除结果.get("message", "激活码删除成功"))
        else:
            return 统一响应模型.失败(
                状态码=400, 消息=删除结果.get("message", "删除激活码失败")
            )

    except Exception as e:
        错误日志器.error(f"删除激活码失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="删除激活码失败")


@激活码管理路由.post("/batch-delete", summary="批量删除激活码")
async def 批量删除激活码接口(
    请求数据: 批量删除请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """批量删除激活码"""
    try:
        接口日志器.info(
            f"管理员 {当前用户.get('id')} 请求批量删除激活码: {len(请求数据.激活码id列表)} 个"
        )

        删除结果 = await 异步批量删除激活码(请求数据.激活码id列表)

        if 删除结果.get("success"):
            安全日志器.info(f"管理员 {当前用户.get('id')} 成功批量删除激活码")
            return 统一响应模型.成功(消息=删除结果.get("message", "批量删除激活码成功"))
        else:
            return 统一响应模型.失败(
                状态码=400,
                消息=删除结果.get("message", "批量删除激活码失败"),
                数据=删除结果.get("details", []),
            )

    except Exception as e:
        错误日志器.error(f"批量删除激活码失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="批量删除激活码失败")


# ==================== 激活码统计分析 ====================


@激活码管理路由.get("/statistics/overview", summary="获取激活码统计")
async def 获取激活码统计接口(当前用户: dict = Depends(获取当前管理员用户)):
    """获取激活码统计信息"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求激活码统计")

        统计结果 = await 异步获取激活码统计()
        return 统一响应模型.成功(数据=统计结果, 消息="获取激活码统计成功")

    except Exception as e:
        错误日志器.error(f"获取激活码统计失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="获取激活码统计失败")


# ==================== 激活码导出功能 ====================


@激活码管理路由.post("/export", summary="导出激活码数据")
async def 导出激活码数据接口(
    请求数据: 激活码列表查询请求, 当前用户: dict = Depends(获取当前管理员用户)
):
    """导出激活码数据为CSV文件"""
    try:
        接口日志器.info(f"管理员 {当前用户.get('id')} 请求导出激活码数据")

        # 获取所有符合条件的激活码数据
        导出查询参数 = {
            "page": 1,
            "size": 10000,  # 导出大量数据
            "搜索关键词": 请求数据.搜索关键词,
            "类型id": 请求数据.类型id,
            "状态": 请求数据.状态,
            "开始日期": 请求数据.开始日期,
            "结束日期": 请求数据.结束日期,
        }

        列表结果 = await 异步获取激活码列表(**导出查询参数)

        if not 列表结果 or not 列表结果.get("list"):
            return 统一响应模型.失败(状态码=400, 消息="没有可导出的数据")

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        headers = [
            "ID",
            "激活码",
            "类型名称",
            "类型描述",
            "会员天数",
            "状态",
            "用户昵称",
            "备注",
            "创建时间",
            "使用时间",
        ]
        writer.writerow(headers)

        # 写入数据
        for item in 列表结果["list"]:
            row = [
                item.get("id", ""),
                item.get("激活码", ""),
                item.get("类型名称", ""),
                item.get("类型描述", ""),
                item.get("会员天数", ""),
                item.get("状态", ""),
                item.get("用户昵称", ""),
                item.get("备注", ""),
                item.get("创建时间", ""),
                item.get("使用时间", ""),
            ]
            writer.writerow(row)

        # 获取CSV内容
        csv_content = output.getvalue()
        output.close()

        # 创建响应
        response = Response(
            content=csv_content.encode("utf-8-sig"),  # 使用UTF-8 BOM以支持中文
            media_type="text/csv",
            headers={
                "Content-Disposition": f'attachment; filename="激活码数据_{请求数据.搜索关键词 or "全部"}_{请求数据.状态 or "全部状态"}.csv"'
            },
        )

        安全日志器.info(f"管理员 {当前用户.get('id')} 成功导出激活码数据")
        return response

    except Exception as e:
        错误日志器.error(f"导出激活码数据失败: {e}", exc_info=True)
        return 统一响应模型.失败(状态码=500, 消息="导出激活码数据失败")
