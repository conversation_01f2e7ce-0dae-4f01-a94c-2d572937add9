<template>
  <div class="loading-container" :class="containerClass">
    <a-spin 
      :spinning="spinning" 
      :size="size" 
      :tip="tip"
      :delay="delay"
      :class="spinClass"
    >
      <slot v-if="!spinning" />
      <div v-else-if="showPlaceholder" class="loading-placeholder">
        <div class="placeholder-content" :style="placeholderStyle">
          <!-- 占位内容 -->
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import {computed} from 'vue';

const props = defineProps({
  // 是否显示加载状态
  spinning: {
    type: Boolean,
    default: true
  },
  // 加载提示文字
  tip: {
    type: String,
    default: '加载中...'
  },
  // 加载图标大小
  size: {
    type: String,
    default: 'default', // small, default, large
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  // 延迟显示加载效果的时间（毫秒）
  delay: {
    type: Number,
    default: 0
  },
  // 容器类型
  type: {
    type: String,
    default: 'default', // default, fullscreen, inline, card
    validator: (value) => ['default', 'fullscreen', 'inline', 'card'].includes(value)
  },
  // 是否显示占位内容
  showPlaceholder: {
    type: Boolean,
    default: false
  },
  // 占位内容高度
  placeholderHeight: {
    type: String,
    default: '200px'
  },
  // 自定义容器样式类
  customClass: {
    type: String,
    default: ''
  }
});

// 计算容器样式类
const containerClass = computed(() => {
  const classes = [`loading-${props.type}`];
  if (props.customClass) {
    classes.push(props.customClass);
  }
  return classes;
});

// 计算加载器样式类
const spinClass = computed(() => {
  return {
    'loading-spin-fullscreen': props.type === 'fullscreen',
    'loading-spin-card': props.type === 'card'
  };
});

// 计算占位内容样式
const placeholderStyle = computed(() => {
  return {
    height: props.placeholderHeight,
    backgroundColor: '#f5f5f5',
    borderRadius: '4px',
    animation: 'pulse 1.5s ease-in-out infinite'
  };
});
</script>

<style scoped>
/* 默认容器样式 */
.loading-default {
  position: relative;
  min-height: 100px;
}

/* 全屏加载样式 */
.loading-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 行内加载样式 */
.loading-inline {
  display: inline-block;
  vertical-align: middle;
}

/* 卡片加载样式 */
.loading-card {
  position: relative;
  min-height: 200px;
  background-color: #fff;
  border-radius: 6px;
  padding: 20px;
}

/* 全屏加载器样式 */
.loading-spin-fullscreen {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 卡片加载器样式 */
.loading-spin-card {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 占位内容样式 */
.loading-placeholder {
  width: 100%;
  height: 100%;
}

.placeholder-content {
  width: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-card {
    padding: 16px;
    min-height: 150px;
  }
}
</style>