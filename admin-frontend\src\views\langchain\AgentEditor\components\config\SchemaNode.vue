<template>
  <div class="schema-node" :style="{ marginLeft: `${level * 20}px` }">
    <div class="node-header" @click="toggleExpanded">
      <span class="expand-icon" v-if="hasChildren">
        <CaretRightOutlined v-if="!expanded" />
        <CaretDownOutlined v-else />
      </span>
      <span class="node-name">{{ node.name }}</span>
      <a-tag :color="getTypeColor(node.type)" size="small">
        {{ getTypeLabel(node.type) }}
      </a-tag>
      <a-tag v-if="node.required" color="red" size="small">必需</a-tag>
    </div>
    
    <div v-if="node.description" class="node-description">
      {{ node.description }}
    </div>

    <div v-if="expanded && hasChildren" class="node-children">
      <template v-if="node.type === 'object' && node.properties">
        <SchemaNode
          v-for="(prop, key) in node.properties"
          :key="key"
          :node="{
            name: key,
            type: prop.type || 'string',
            description: prop.description,
            required: node.schema?.required?.includes(key) || false,
            properties: prop.properties,
            items: prop.items,
            schema: prop
          }"
          :level="level + 1"
        />
      </template>
      
      <template v-else-if="node.type === 'array' && node.items">
        <SchemaNode
          :node="{
            name: '[items]',
            type: node.items.type || 'string',
            description: node.items.description,
            required: false,
            properties: node.items.properties,
            items: node.items.items,
            schema: node.items
          }"
          :level="level + 1"
        />
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { CaretRightOutlined, CaretDownOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  node: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  }
})

// 展开状态
const expanded = ref(true)

// 是否有子节点
const hasChildren = computed(() => {
  return (props.node.type === 'object' && props.node.properties && Object.keys(props.node.properties).length > 0) ||
         (props.node.type === 'array' && props.node.items)
})

// 类型标签映射
const typeLabels = {
  'string': '字符串',
  'number': '数字',
  'integer': '整数',
  'boolean': '布尔值',
  'array': '数组',
  'object': '对象'
}

// 类型颜色映射
const typeColors = {
  'string': 'blue',
  'number': 'green',
  'integer': 'green',
  'boolean': 'orange',
  'array': 'purple',
  'object': 'cyan'
}

// 获取类型标签
const getTypeLabel = (type) => {
  return typeLabels[type] || type
}

// 获取类型颜色
const getTypeColor = (type) => {
  return typeColors[type] || 'default'
}

// 切换展开状态
const toggleExpanded = () => {
  if (hasChildren.value) {
    expanded.value = !expanded.value
  }
}
</script>

<style scoped>
.schema-node {
  margin-bottom: 4px;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.node-header:hover {
  background-color: #f5f5f5;
}

.expand-icon {
  width: 16px;
  display: flex;
  justify-content: center;
  color: #8c8c8c;
}

.node-name {
  font-weight: 500;
  color: #262626;
}

.node-description {
  margin-left: 32px;
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
  font-style: italic;
}

.node-children {
  margin-top: 4px;
}
</style>
